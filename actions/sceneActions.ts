"use server";

import axios from "axios";

import { AtInfo, Scene } from "@/types";
import prisma from "@/utils/prisma";


const convertData = (data: any): any => {
  return JSON.parse(
    JSON.stringify(data, (key, value) => {
      if (typeof value === "bigint") {
        return value.toString();
      }
      if (key === "repayMethod" && typeof value === "string") {
        const parsedInt = parseInt(value, 10);

        return isNaN(parsedInt) ? value : parsedInt;
      }

      return value;
    }),
  );
};

export async function getScenesByUserId(
  userId?: number,
): Promise<Scene[] | { error: string }> {
  if (!userId) {
    console.error("Server Action (getScenesByUserId): userId is required.");

    return { error: "用户ID是必需的。" };
  }

  console.log(
    `Server Action (getScenesByUserId): Fetching scenes for userId: ${userId}`,
  );

  try {
    const scenes = await prisma.scene.findMany({
      where: {
        userId: userId,
      },
    });

    const serializedScenes = convertData(scenes);

    console.log(
      `Server Action (getScenesByUserId): Found ${serializedScenes.length} scenes for user ${userId}.`,
    );

    return serializedScenes as Scene[];
  } catch (error) {
    console.error(
      `Server Action (getScenesByUserId): Error fetching scenes for user ${userId}:`,
      error,
    );

    return { error: "获取场景数据时发生错误。" };
  }
}

// 根据sceneId查询scene
export async function getSceneById(
  sceneId: number,
): Promise<Scene | { error: string }> {
  if (!sceneId) {
    console.error("Server Action (getSceneById): sceneId is required.");

    return { error: "场景ID是必需的。" };
  }

  console.log(
    `Server Action (getSceneById): Fetching scene with ID: ${sceneId}`,
  );

  try {
    const scene = await prisma.scene.findUnique({
      where: {
        id: sceneId,
      },
    });

    if (!scene) {
      console.error(
        `Server Action (getSceneById): Scene with ID ${sceneId} not found.`,
      );

      return { error: "未找到场景。" };
    }

    const serializedScene = convertData(scene);

    console.log(
      `Server Action (getSceneById): Found scene with ID ${sceneId}.`,
    );

    return serializedScene as Scene;
  } catch (error) {
    console.error(
      `Server Action (getSceneById): Error fetching scene with ID ${sceneId}:`,
      error,
    );

    return { error: "获取场景数据时发生错误。" };
  }
}

export async function getAllScenes(): Promise<Scene[] | { error: string }> {
  console.log("Server Action (getAllScenes): Fetching all scenes.");
  try {
    const scenes = await prisma.scene.findMany();
    const serializedScenes = convertData(scenes);

    console.log(
      `Server Action (getAllScenes): Found ${serializedScenes.length} total scenes.`,
    );

    return serializedScenes as Scene[];
  } catch (error) {
    console.error(
      "Server Action (getAllScenes): Error fetching all scenes:",
      error,
    );

    return { error: "获取所有场景数据时发生错误。" };
  }
}

export async function createScene(
  sceneData: Scene,
): Promise<Scene | { error: string }> {
  try {
    if (!sceneData.name || !sceneData.userId) {
      return { error: "场景名称和用户 ID 是必需的。" };
    }
    const newScene = await prisma.scene.create({
      data: sceneData,
    });

    return newScene;
  } catch (error) {
    console.error("创建场景时出错:", error); // 在服务器端记录错误
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === "P2002") {
        return { error: "具有此标识符的场景已存在。" };
      }
    }

    return { error: "创建场景失败，发生意外错误。" };
  }
}

export async function updateScene(
  scene: Scene,
): Promise<Scene | { error: string }> {
  try {
    const updatedScene = await prisma.scene.update({
      where: { id: scene.id },
      data: scene,
    });

    return updatedScene;
  } catch (error) {
    console.error("更新场景时出错:", error);

    return { error: "更新场景失败，发生意外错误。" };
  }
}

export async function deleteSceneById(
  sceneId: number,
): Promise<{ success: boolean } | { error: string }> {
  try {
    await prisma.scene.delete({
      where: { id: sceneId },
    });

    return { success: true };
  } catch (error) {
    console.error("删除场景时出错:", error);

    return { error: "删除场景失败，发生意外错误。" };
  }
}

export async function getAtByCode(
  code: string,
): Promise<Scene | { error: string }> {
  try {
    if (!code) {
      return { error: "code值不能为空" };
    }

    const atParam = {
      grant_type: "authorization_code",
      code: code,
      redirect_uri: "honorid://redirect_url",
      client_id: "220619456",
      client_secret:
        "7360bc3fe7396e913fd7d8c78e350452c98357370a3506a058ab4e725eb33392",
    };

    console.log("获取AT入参是:" + atParam.code);

    const atInfo = await axios.post<AtInfo>(
      "https://hnoauth-login-test-drcn.cloud.honor.com/oauth2/v3/token",
      atParam,
      {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      },
    );

    console.log("获取at的返回为：" + atInfo.data.access_token);

    return atInfo.data.access_token;
  } catch (error) {
    console.error("获取AT出错:", error); // 在服务器端记录错误

    return { error: "获取AT失败，发生意外错误。" + error };
  }
}
