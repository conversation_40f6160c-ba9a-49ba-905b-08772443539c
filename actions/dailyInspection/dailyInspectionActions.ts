"use server";

import { parse } from "csv-parse/sync";
import { InspectionData } from "@/types/dailyInspection/dailyInspection";
import prisma from "@/utils/prisma";

export async function uploadInspectionData(
  formData: FormData,
): Promise<{ data: InspectionData[] } | { error: string }> {
  try {
    const file = formData.get("file") as File;
    if (!file) {
      return { error: "No file uploaded" };
    }

    const fileContent = await file.text();
    const records = parse(fileContent, {
      columns: true,
      skip_empty_lines: true,
    });

    const processedData = records.map((record: any) => ({
      interface: record["出错接口"],
      errorCode: record["错误码"],
      errorMessage: record["失败原因"],
      originalErrorCode: record[" 原始错误码"],
      originalErrorMessage: record["原始错误原因"],
      count: parseInt(record["次数"], 10),
      uniqueCount: parseInt(record["去重后的次数"], 10),
      traceId: record["traceId"],
    }));

    return { data: processedData };
  } catch (error) {
    console.error("Upload error:", error);
    return { error: "Failed to process file" };
  }
}

export async function saveInspectionData(
  data: InspectionData,
): Promise<{ success: boolean } | { error: string }> {
  try {
    await prisma.daily_inspection.create({
      data: {
        id: data.id || "",
        interface: data.interface,
        errorCode: data.errorCode,
        errorMessage: data.errorMessage,
        originalErrorCode: data.originalErrorCode,
        originalErrorMessage: data.originalErrorMessage,
        count: data.count,
        uniqueCount: data.uniqueCount,
        traceId: data.traceId,
        processedBy: data.processedBy,
        processReason: data.processReason,
        processMeasures: data.processMeasures,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Save error:", error);
    return { error: "Failed to save processed data" };
  }
}

export async function getInspectionHistory(): Promise<
  { data: InspectionData[] } | { error: string }
> {
  try {
    const historyData = await prisma.daily_inspection.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });

    return { data: historyData };
  } catch (error) {
    console.error("Error fetching history data:", error);
    return { error: "Failed to fetch history data" };
  }
}

// 更新
export async function updateInspectionData(
  data: InspectionData,
): Promise<{ success: boolean } | { error: string }> {
  try {
    await prisma.daily_inspection.update({
      where: {
        id: data.id,
      },
      data: {
        interface: data.interface,
        errorCode: data.errorCode,
        errorMessage: data.errorMessage,
        originalErrorCode: data.originalErrorCode,
        originalErrorMessage: data.originalErrorMessage,
        count: data.count,
        uniqueCount: data.uniqueCount,
        traceId: data.traceId,
        processedBy: data.processedBy,
        processReason: data.processReason,
        processMeasures: data.processMeasures,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Update error:", error);
    return { error: "Failed to update processed data" };
  }
}

export async function deleteInspectionData(
  id: string,
): Promise<{ success: boolean } | { error: string }> {
  try {
    await prisma.daily_inspection.delete({
      where: {
        id,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Delete error:", error);
    return { error: "Failed to delete processed data" };
  }
}

export async function fetchInspectionData(
  startTimestamp: number,
  endTimestamp: number,
) {
  try {
    const url = `https://aiopsinner-drcn.cloud.hihonor.com/grafana/api/datasources/proxy/uid/GdgQIYI4z/?query=SELECT event as "出错接口", failCode AS "错误码", failReason AS "失败原因", sourceFailCode AS " 原始错误码", sourceFailReason AS "原始错误原因", sum(count) as "次数", length(arrayDistinct(groupArray(userId))) AS "去重后的次数", arrayDistinct(groupArray(5)(traceId)) as "traceId" FROM aiops_dist_prd.aiops_collect_100000091 WHERE s = \'wallet-loan-service\' AND supplierId = \'\' AND time>=date_add(HOUR, -8, toDateTime(${startTimestamp})) and time<=date_add(HOUR, -8, toDateTime(${endTimestamp})) AND flag = \'F\' GROUP BY event, failCode, failReason, sourceFailCode, sourceFailReason ORDER BY "去重后的次数" DESC FORMAT JSON`;

    const grafanaCookie =
      process.env.GRAFANA_SESSION_COOKIE ||
      "grafana_session=559c0c968f3a5f0fefafafa4bf48e93f";

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Cookie: grafanaCookie,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return { success: true, data: data.data || [] };
  } catch (error) {
    console.error("Fetch inspection data error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
