"use server";

import { CommonResponse } from "@/actions/cp/notify/common";
import { encryptRequest } from "@/utils/callBackDecrypt";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";

export interface notifyLoanApplyParamType {
  userId: string; // 荣耀方用户id
  outOrderNo: string; // 渠道方借款申请流水号
  applyNo: string; // 荣耀侧借款申请订单号
  applyStatus: number; // 申请状态，2-成功，3-失败，4-风控拒绝，5-取消
  refuseMsg?: string; // 拒绝原因，失败或拒绝是必传
  refuseMsgData?: string; // 拒绝具体原因，失败或拒绝是必传
  loanSource?: string; // 借款来源，appId
  applyTime: number; // 借款申请时间，毫秒
  institutionNames?: string; // 机构名称，多个资方以"&"分割
  loanInfo?: LoanInfoParam; // 借款信息（成功时需传）
}

export interface LoanInfoParam {
  repayMethod: number; // 还款方式，1-等额本息(灵活还)，2-等额本金(灵活还)，3-先息后本(灵活还)，4-等额本息(按期还)
  loanTime?: number; // 放款成功的时间，毫秒
  effectiveDate: string; // 起息日期，yyyyMMdd
  dayRate?: string; // 日利率，0.06（年化/360 *100）保留6位数
  monthRate?: string; // 月利率
  apr: string; // 年利率，0.18（年化）
  loanAmount: number; // 借款金额，单位：分
  totalTerm: number; // 借款期数
  couponNo?: string; // 优惠券id
  loanUse?: string; // 借款用途
  bankCardId: string; // 绑卡ID
}

const devUrl =
  "http://card-dev-drcn.inner.wallet.hihonorcloud.com/loan/third/notify";
// const devUrl = "http://localhost:18007/loan/third/notify";
const testUrl = "http://wallet-api-test-drcn.cloud.honor.com/loan/third/notify";

export async function notifyLoanApply(
  userId: number,
  params: notifyLoanApplyParamType,
  env: string,
): Promise<CommonResponse> {
  const url = `${process.env.CALLBACK_URL}/loan/third/notify`;
  const res = await fetch(url, {
    method: "POST",
    headers: {
      appId: "mock",
      "Content-Type": "application/json",
    },
    body: encryptRequest(params, "loan.apply.notify"),
  });

  const responseData = await res.json();

  await addInterfaceLog({
    userId,
    interfaceName: "loan.apply.notify",
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(responseData),
  });

  return responseData;
}
