"use server";

import { CommonResponse } from "@/actions/cp/notify/common";
import { encryptRequest } from "@/utils/callBackDecrypt";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";

// 授信变更通知请求参数
export interface CreditChangeNotifyRequest {
  userId: string; // 荣耀方用户id
  applyNo: string; // 荣耀侧授信申请流水号
  outOrderNo: string; // 渠道方授信流水号
  limitAdjustInfo?: LimitAdjustInfoDto; // 额度调整，有额度调整时必传
  rateAdjustInfos?: RateAdjustInfoDto[]; // 利率调整，有利率调整时必传
}
// 额度调整信息
export interface LimitAdjustInfoDto {
  limitChangeType: 1 | 2; // 1-提额或者临额调整，2-降低额度
  availableLimit: number; // 可用固定额度，单位：分
  beforeCreditLimit: number; // 调额前的固定额度，单位：分
  tempCreditLimit?: number; // 临额授信额度
  tempAvailableLimit?: number; // 临额可⽤额度
  beforeTempCreditLimit?: number; // 调整前临额授信额度
  beforeTempAvailableLimit?: number; // 调整前临额可⽤额度
  tempLimitValidTime?: string; // 临额有效期，yyyy-MM-ddHH:mm:ss
  remainLimit: number; // 剩余额度（临额+固额），单位：分
  totalAmount: number; // 总额度（临额+固额），单位：分
  beforeRemainLimit: number; // 调整前剩余额度（临额+固额），单位：分
  beforeTotalAmount: number; // 调整前总额度（临额+固额），单位：分
  adjustTime: string; // 调额时间，yyyy-MM-dd HH:mm:ss
}

// 利率调整信息
export interface RateAdjustInfoDto {
  rateChangeType: 1 | 2; // 1-调高利率，2-调低利率
  repayMethod: number; // 1-等额本息(灵活还)，2-等额本金(灵活还)，3-先息后本(灵活还)，4-等额本息(按期还)
  apr: string; // 年利率
  dayRate: string; // 日利率
  monthRate?: string; // 月利率
  beforeApr: string; // 调价前年利率
  beforeDayRate: string; // 调价前日利率
  beforeMonthRate?: string; // 调价前月利率
  tempApr?: string; // 临价年利率
  tempDayRate?: string; // 临价日利率
  tempMonthRate?: string; // 月利率
  beforeTempApr?: string; // 调整前临价年利率
  beforeTempDayRate?: string; // 调整前临价日利率
  tempPriceDueTime?: string; // 临价截止时间，示例：2023-09-23 23:59:59
  adjustTime: string; // 调价时间，yyyy-MM-dd HH:mm:ss
}

const devUrl =
  "http://card-dev-drcn.inner.wallet.hihonorcloud.com/loan/third/notify";
// const devUrl = "http://localhost:18007/loan/third/notify";
const testUrl = "http://wallet-api-test-drcn.cloud.honor.com/loan/third/notify";

export async function notifyCreditChange(
  userId: number,
  params: CreditChangeNotifyRequest,
  env: string,
): Promise<CommonResponse> {
  const url = `${process.env.CALLBACK_URL}/loan/third/notify`;
  const res = await fetch(url, {
    method: "POST",
    headers: {
      appId: "mock",
      "Content-Type": "application/json",
    },
    body: encryptRequest(params, "credit.change.notify"),
  });

  const responseData = await res.json();

  await addInterfaceLog({
    userId,
    interfaceName: "credit.change.notify",
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(responseData),
  });

  return responseData;
}
