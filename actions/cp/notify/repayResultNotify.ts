"use server";

import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { encryptRequest } from "@/utils/callBackDecrypt";
import { CommonResponse } from "@/actions/cp/notify/common";

export interface notifyRepayResultParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 荣耀侧还款交易流水号，代扣通知无 */
  repayNo?: string;
  /** 渠道方还款交易订单号 */
  outRepayNo: string;
  /** 荣耀侧借款申请订单号 */
  loanApplyNo: string;
  /** 渠道方借款申请流水号 */
  outOrderNo: string;
  /**
   * 还款来源
   * 代扣通知为OTHER
   * 正常还款通知为appId
   * 合作伙伴应用内正常还款通知为PARTNER_IN-APP_REPAYMENT
   */
  repaySource: string;
  /** 还款状态，2-还款成功，4-还款失败 */
  repayStatus: number;
  /** 返回失败原因（失败必传） */
  repayResult?: string;
  /** 还款金额,单位：分 */
  repayAmount: number;
  /** 还款类型，1- 提前还款 2-正常还款 */
  repayType: number;
  /** 优惠券减免金额，单位：分 */
  reductionAmount?: number;
  /** 还款时间，毫秒 */
  repayTime?: number;
  /** 还款期次明细 */
  repayTerms?: RepayTermDto[];
}

// 还款期次明细
export interface RepayTermDto {
  /** 期次 */
  termNo: number;
  /** 本期还款总额,单位：分 */
  termAmount: number;
  /** 本期还款本金，单位：分 */
  termPrincipal: number;
  /** 本期还款利息，单位：分 */
  termInterest: number;
  /** 本期还款优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期还款罚息，单位：分 */
  termPenalty: number;
  /** 本期还款本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期还款利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期还款逾期费，单位：分 */
  termOverdueFee: number;
  /** 本期还款违约金，单位：分 */
  termViolateFee: number;
  /** 本期还款服务费，单位：分 */
  termServiceFee: number;
  /** 逾期天数（可选） */
  overdueDays?: number;
}

const devUrl =
  "http://card-dev-drcn.inner.wallet.hihonorcloud.com/loan/third/notify";
// const devUrl = "http://localhost:18007/loan/third/notify";
const testUrl = "http://wallet-api-test-drcn.cloud.honor.com/loan/third/notify";

export async function notifyRepayResult(
  userId: number,
  params: notifyRepayResultParamType,
  env: string,
): Promise<CommonResponse> {
  const url = `${process.env.CALLBACK_URL}/loan/third/notify`;
  const res = await fetch(url, {
    method: "POST",
    headers: {
      appId: "mock",
      "Content-Type": "application/json",
    },
    body: encryptRequest(params, "repay.result.notify"),
  });

  const responseData = await res.json();

  await addInterfaceLog({
    userId,
    interfaceName: "repay.result.notify",
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(responseData),
  });

  return responseData;
}
