"use server";

import { encryptRequest } from "@/utils/callBackDecrypt";
import { CommonResponse } from "@/actions/cp/notify/common";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";

export interface notifyCouponParamType {
  userId: string; // 荣耀方用户id
  couponNo: string; // 优惠券id
  couponBatchNo: string; // 优惠券批次号
  couponType: number; // 券类型，1: 放款券; 2: 还款券
  supplier: string; // 服务商
  startTime: number; // 券有效期开始时间，毫秒
  endTime: number; // 券有效期结束时间，毫秒
  couponName: string; // 优惠券名称
}

const devUrl =
  "http://card-dev-drcn.inner.wallet.hihonorcloud.com/loan/third/notify";
// const devUrl = "http://localhost:18007/loan/third/notify";
const testUrl = "http://wallet-api-test-drcn.cloud.honor.com/loan/third/notify";

export async function notifyCoupon(
  userId: number,
  params: notifyCouponParamType,
  env: string,
): Promise<CommonResponse> {
  const url = `${process.env.CALLBACK_URL}/loan/third/notify`;
  const res = await fetch(url, {
    method: "POST",
    headers: {
      appId: "mock",
      "Content-Type": "application/json",
    },
    body: encryptRequest(params, "coupon.notify"),
  });

  console.log(`couponCallback url: ${url}, res: ${res}`);

  const responseData = await res.json();

  await addInterfaceLog({
    userId,
    interfaceName: "coupon.notify",
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(responseData),
  });

  return responseData;
}
