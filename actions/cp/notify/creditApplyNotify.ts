"use server";

import { encryptRequest } from "@/utils/callBackDecrypt";
import { CommonResponse } from "@/actions/cp/notify/common";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";

export interface notifyCreditApplyParamType {
  userId: string; // 荣耀方用户id
  applyNo: string; // 荣耀侧授信申请流水号
  outOrderNo: string; // 渠道方授信流水号
  applyStatus: number; // 授信结果，1-审核中，2-通过，3-拒绝
  refuseCode?: string; // 拒绝原因码，拒绝时必传
  refuseMsg?: string; // 拒绝原因说明，拒绝时必传
  refuseMsgData?: string; // 拒绝原因说明，拒绝时必传
  refuseControlTime?: number; // 授信拒绝管控期，单位：秒
  identity?: number; // 新老客标识，1-老客，2-新客
  creditLimit: number; // 授信总额度，单位分（授信通过时返回）
  originCreditLimit?: number; // 原授信总额度，单位分
  // 额度类型字段缺少字段名，建议补充字段名
  // limitType?: number;             // 额度类型，1-循环额度 （请根据实际字段名补充）
  remainLimit: number; // 可用额度，单位分（授信通过时返回）
  limitExpireDate?: string; // 额度失效日期，格式yyyyMMdd
  greyExpireTime?: number; // 额度管控截止时间，时间戳(秒)
  totalAvailableLimit: number; // 总可用额度（临额+固额总和），单位分
  totalCreditLimit: number; // 总授信额度（临额+固额总和），单位分
  dayRate: string; // 日利率，示例：0.065 【即0.065%】
  monthRate: string; // 月利率
  apr: string; // 年利率，示例：23.4 【即23.4%】
  maxLoan: number; // 单笔借款最大金额，单位分
  minLoan: number; // 单笔借款最小金额，单位分
  tempLimitInfo?: TempLimitInfo; // 临额相关信息，有临额时必传
  applyTime: number; // 授信申请时间，毫秒
}

export interface TempLimitInfo {
  tempLimitValidTime: string; // 临额有效期，示例：2023-09-23 23:59:59
  tempCreditLimit: number; // 临额授信额度
  tempAvailableLimit: number; // 临额可用额度
  tempPriceValidTime: string; // 临价截止时间，示例：2023-09-23 23:59:59
  tempDayRate: string; // 临价日利率，示例：0.065 【即0.065%】
  tempApr: string; // 临价年利率，示例：23.4 【即23.4%】
}

const devUrl =
  "http://card-dev-drcn.inner.wallet.hihonorcloud.com/loan/third/notify";
// const devUrl = "http://localhost:18007/loan/third/notify";
const testUrl = "http://wallet-api-test-drcn.cloud.honor.com/loan/third/notify";

export async function notifyCreditApply(
  userId: number,
  params: notifyCreditApplyParamType,
  env: string,
): Promise<CommonResponse> {
  const url = `${process.env.CALLBACK_URL}/loan/third/notify`;
  const res = await fetch(url, {
    method: "POST",
    headers: {
      appId: "mock",
      "Content-Type": "application/json",
    },
    body: encryptRequest(params, "credit.apply.notify"),
  });

  const responseData = await res.json();

  await addInterfaceLog({
    userId,
    interfaceName: "credit.apply.notify",
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(responseData),
  });

  return responseData;
}
