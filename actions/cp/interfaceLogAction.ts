"use server";

import prisma from "@/utils/prisma";

// 新增一条接口日志
export async function addInterfaceLog({
    userId,
    interfaceName,
    requestData,
    responseData,
  }:{userId: number, interfaceName: string, requestData: string, responseData: string}) {
  try {
    await prisma.interface_log.create({
      data: {
        userId: userId,
        interfaceName: interfaceName,
        requestData: requestData,
        responseData: responseData,
        createTime: new Date(),
      },
    });
  } catch (error) {
    console.error("Error logging interface action:", error);
  }
}

// 查询用户的所有日志
export async function getUserInterfaceLogs(userId: number) {
  try {
    const logs = await prisma.interface_log.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        createTime: "desc",
      },
    });
    return logs;
  } catch (error) {
    console.error("Error fetching user interface logs:", error);
    return [];
  }
}

