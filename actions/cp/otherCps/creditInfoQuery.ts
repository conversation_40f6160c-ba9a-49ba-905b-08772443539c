"use server";

import { decryptByPrivateKeyForQf, decryptForQf, encryptRequestForQf } from "@/utils/qf/decrypt";

export async function creditInfoQuery(userId: string, targetUrl: string) {
  const response = await fetch(targetUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: encryptRequestForQf({ userId }, "credit.info.query"),
  });

  const responseData = await response.json();

  const key = responseData.key;
  const decryptKey = decryptByPrivateKeyForQf(key);
  const EncryptParams = responseData.data;
  const decryptParams = JSON.parse(decryptForQf(EncryptParams, decryptKey));
  console.log("responseData", decryptParams);
  return decryptParams;
}