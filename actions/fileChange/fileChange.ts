"use server";

import JS<PERSON><PERSON> from "jszip";
import prisma from "@/utils/prisma";
import { diffLines } from "diff";

// 上传Action
export async function uploadZipAndCreateSession(formData: FormData) {
  try {
    console.log("Server Action: uploadZipAndCreateSession");
    if (!prisma) {
      throw new Error("数据库连接客户端未初始化。");
    }
    const file = formData.get("file") as File;
    const creatorName = formData.get("creatorName") as string;
    if (!file || !creatorName) {
      return { success: false, error: "缺少文件或创建者名称。" };
    }
    const buffer = await file.arrayBuffer();
    const zip = await JSZip.loadAsync(buffer);
    const filesContent = await Promise.all(
      Object.keys(zip.files)
        .filter((filename) => {
          return !zip.files[filename].dir;
        })
        .map(async (filename) => {
          const content = await zip.files[filename].async("string");
          // 为了后续方便处理，我们可以把文件类型也加进去
          const extension = filename.split('.').pop().toLowerCase();
          return { path: filename, content, type: extension.replace('yml', 'yaml') }; // 统一 yml 为 yaml
        }),
    );

    if (filesContent.length === 0) {
      return { success: false, error: "ZIP 包中没有找到有效的 .json 文件。" };
    }
    // --- 开始重构的部分 ---
    // 步骤 1: 单独创建 Session
    console.log(`创建 Session: ${file.name}`);
    const session = await prisma.uploadSession.create({
      data: {
        name: file.name,
        creatorName: creatorName,
      },
    });
    console.log(`Session 创建成功, ID: ${session.id}`);
    // 步骤 2: 批量创建所有 File 记录
    // createMany 更高效，但不会返回创建的记录。
    // 所以我们先准备好文件数据，然后手动创建以获取它们的 ID。
    console.log(`准备创建 ${filesContent.length} 个文件...`);

    // 我们需要创建文件并立即获取它们的 ID，以便后续创建行。
    // 所以我们用 for...of 循环来逐一创建文件，而不是 createMany
    const createdFiles = [];
    for (const fileItem of filesContent) {
      const createdFile = await prisma.file.create({
        data: {
          path: fileItem.path,
          originalContent: fileItem.content,
          uploadSessionId: session.id, // 关联到刚刚创建的 session
        },
      });
      createdFiles.push({
        ...createdFile,
        linesData: fileItem.content.split("\n"),
      });
    }
    console.log(`${createdFiles.length} 个文件记录创建成功。`);
    // 步骤 3: 为每个文件批量创建所有 AttributedLine 记录
    // 这是最耗时的部分，我们使用 Prisma 事务来包裹所有行的创建
    console.log("开始批量创建所有文件的行记录...");
    await prisma.$transaction(async (tx) => {
      for (const fileWithLines of createdFiles) {
        const linesToCreate = fileWithLines.linesData.map(
          (lineContent, index) => ({
            lineNumber: index + 1,
            content: lineContent,
            isModified: false,
            fileId: fileWithLines.id, // 关联到刚刚创建的文件
          }),
        );

        // 使用 createMany 在事务中进行高效批量插入
        if (linesToCreate.length > 0) {
          await tx.attributedLine.createMany({
            data: linesToCreate,
          });
        }
      }
    });
    console.log("所有行记录创建成功。");
    return { success: true, sessionId: session.id };
  } catch (e: any) {
    console.error("在 uploadZipAndCreateSession 中发生严重错误:", e);
    // 检查是否有事务相关的回滚逻辑需要处理
    // 在我们的例子中，如果中途失败，数据会不完整，但这可能可以接受。
    return { success: false, error: `处理失败: ${e.message}` };
  }
}
// 更新 Action - 这是最复杂的部分
export async function updateFileLines(
  fileId: string,
  newFullContent: string,
  modifierName: string | null,
) {
  const modifier = modifierName || "未知用户";

  try {
    // 1. 获取数据库中旧的行数据和原始内容
    const [oldLines, fileInfo] = await Promise.all([
      prisma.attributedLine.findMany({
        where: { fileId },
        orderBy: { lineNumber: "asc" },
      }),
      prisma.file.findUnique({
        where: { id: fileId },
        select: { originalContent: true }
      })
    ]);

    const oldFullContent = oldLines.map((line) => line.content).join("\n");
    const originalLines = fileInfo?.originalContent.split('\n') || [];

    // 2. 计算新旧内容之间的差异
    const differences = diffLines(oldFullContent, newFullContent);

    // 3. 根据差异构建新的行数据列表
    const newAttributedLines: Omit<
      import("@prisma/client").AttributedLine,
      "id" | "fileId"
    >[] = [];
    let currentLineNumber = 1;
    let oldLineIndex = 0;

    differences.forEach((part: any) => {
      const lines = part.value.replace(/\n$/, "").split("\n");

      for (const lineContent of lines) {
        if (part.added) {
          // 检查新增的行是否与原始内容相同
          const originalLineIndex = currentLineNumber - 1;
          const isRestoredToOriginal = originalLines[originalLineIndex] === lineContent;

          if (isRestoredToOriginal) {
            // 如果恢复到原始状态，不标记为修改
            newAttributedLines.push({
              lineNumber: currentLineNumber++,
              content: lineContent,
              modifiedByName: null,
              modifiedAt: null,
              isModified: false,
            });
          } else {
            // 真正的新增行
            newAttributedLines.push({
              lineNumber: currentLineNumber++,
              content: lineContent,
              modifiedByName: modifier,
              modifiedAt: new Date(),
              isModified: true,
            });
          }
        } else if (part.removed) {
          oldLineIndex++;
        } else {
          // 未改变的行，保持原有信息
          const oldLine = oldLines[oldLineIndex++];
          newAttributedLines.push({
            lineNumber: currentLineNumber++,
            content: lineContent,
            modifiedByName: oldLine?.modifiedByName || null,
            modifiedAt: oldLine?.modifiedAt || null,
            isModified: oldLine?.isModified || false,
          });
        }
      }
    });

    // 4. 使用事务更新数据
    await prisma.$transaction([
      prisma.attributedLine.deleteMany({ where: { fileId } }),
      prisma.attributedLine.createMany({
        data: newAttributedLines.map((line) => ({ ...line, fileId })),
      }),
      prisma.file.update({
        where: { id: fileId },
        data: { updatedAt: new Date() },
      }),
    ]);

    return { success: true, message: "文件已更新" };
  } catch (error) {
    console.error("更新文件行失败:", error);
    return { success: false, error: "更新文件失败" };
  }
}

export async function getSessionById(sessionId: string, options?: {
  includeLines?: boolean;
  fileLimit?: number;
  lineLimit?: number;
}) {
  // 基本的输入验证
  if (!sessionId) {
    console.error("getSessionById: 收到一个空的 sessionId");
    return null;
  }

  const { includeLines = true, fileLimit, lineLimit } = options || {};

  try {
    console.log(`[Server Action] 正在获取 Session ID: ${sessionId}`);

    // 基础查询 - 只获取会话和文件信息
    const session = await prisma.uploadSession.findUnique({
      where: { id: sessionId },
      include: {
        files: {
          orderBy: { path: "asc" },
          ...(fileLimit && { take: fileLimit }),
          ...(includeLines && {
            include: {
              lines: {
                orderBy: { lineNumber: "asc" },
                ...(lineLimit && { take: lineLimit })
              }
            }
          })
        }
      }
    });

    if (!session) {
      console.warn(`[Server Action] 未在数据库中找到 Session ID: ${sessionId}`);
      return null;
    }

    console.log(`[Server Action] 成功获取 Session: ${session.name}`);
    return session;
  } catch (error) {
    console.error(`[Server Action] 获取会话 ${sessionId} 时发生错误:`, error);
    return null;
  }
}

// 新增：获取会话基本信息（不包含文件内容）
export async function getSessionBasicInfo(sessionId: string) {
  if (!sessionId) {
    console.error("getSessionBasicInfo: 收到一个空的 sessionId");
    return null;
  }

  try {
    console.log(`[Server Action] 正在获取 Session 基本信息: ${sessionId}`);

    const session = await prisma.uploadSession.findUnique({
      where: { id: sessionId },
      select: {
        id: true,
        name: true,
        creatorName: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            files: true
          }
        }
      }
    });

    if (!session) {
      console.warn(`[Server Action] 未在数据库中找到 Session ID: ${sessionId}`);
      return null;
    }

    console.log(`[Server Action] 成功获取 Session 基本信息: ${session.name}`);
    return session;
  } catch (error) {
    console.error(`[Server Action] 获取会话基本信息 ${sessionId} 时发生错误:`, error);
    return null;
  }
}

// 新增：分页获取文件列表
export async function getSessionFiles(sessionId: string, options?: {
  page?: number;
  pageSize?: number;
  includeLines?: boolean;
}) {
  if (!sessionId) {
    console.error("getSessionFiles: 收到一个空的 sessionId");
    return null;
  }

  const { page = 1, pageSize = 20, includeLines = true } = options || {};
  const skip = (page - 1) * pageSize;

  try {
    console.log(`[Server Action] 正在获取 Session 文件列表: ${sessionId}, 页码: ${page}`);

    const [files, totalCount] = await Promise.all([
      prisma.file.findMany({
        where: { uploadSessionId: sessionId },
        orderBy: { path: "asc" },
        skip,
        take: pageSize,
        include: includeLines ? {
          lines: {
            orderBy: { lineNumber: "asc" }
          }
        } : undefined
      }),
      prisma.file.count({
        where: { uploadSessionId: sessionId }
      })
    ]);

    console.log(`[Server Action] 成功获取 ${files.length} 个文件，总计: ${totalCount}`);
    return {
      files,
      totalCount,
      page,
      pageSize,
      totalPages: Math.ceil(totalCount / pageSize)
    };
  } catch (error) {
    console.error(`[Server Action] 获取会话文件列表 ${sessionId} 时发生错误:`, error);
    return null;
  }
}

/**
 * 获取所有会话的列表，用于在主页展示。
 * @returns 返回一个包含会话基本信息的数组。
 */
export async function getAllSessions() {
  try {
    const sessions = await prisma.uploadSession.findMany({
      orderBy: { order: 'asc' },
      select: {
        id: true,
        name: true,
        creatorName: true,
        createdAt: true,
      },
    });
    return sessions;
  } catch (e: any) {
    console.error('获取会话列表失败:', e);
    return [];
  }
}

/**
 * 删除指定的会话及其所有相关文件
 * @param sessionId 要删除的会话ID
 * @returns 删除结果
 */
export async function deleteSession(sessionId: string) {
  "use server";
  try {
    console.log("[Server Action] 删除会话:", sessionId);

    // 删除会话（会级联删除相关的文件和行记录）
    await prisma.uploadSession.delete({
      where: {
        id: sessionId,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("删除会话失败:", error);
    return { success: false, error: "删除会话失败" };
  }
}

/**
 * 重命名指定的会话
 * @param sessionId 要重命名的会话ID
 * @param newName 新的会话名称
 * @returns 重命名结果
 */
export async function renameSession(sessionId: string, newName: string) {
  "use server";
  try {
    console.log("[Server Action] 重命名会话:", sessionId, "新名称:", newName);

    if (!newName.trim()) {
      return { success: false, error: "会话名称不能为空" };
    }

    await prisma.uploadSession.update({
      where: { id: sessionId },
      data: { name: newName.trim() },
    });

    return { success: true };
  } catch (error) {
    console.error("重命名会话失败:", error);
    return { success: false, error: "重命名会话失败" };
  }
}


export async function exportSession(sessionId: string, options?: { exportType?: 'all' | 'modified' }) {
  try {
    const session = await prisma.uploadSession.findUnique({
      where: { id: sessionId },
      include: {
        files: {
          include: {
            lines: {
              orderBy: { lineNumber: "asc" }
            }
          }
        }
      }
    });

    if (!session) {
      return { success: false, error: '会话不存在' };
    }

    const zip = new JSZip();
    let modifiedFilesCount = 0;
    let totalFilesCount = session.files.length;

    const exportType = options?.exportType || 'modified';
    if (exportType === 'all') {
      for (const file of session.files) {
        const currentContent = file.lines.map(line => line.content).join('\n');
        zip.file(file.path, currentContent);
        // 统计有多少文件被修改
        const hasModifications = file.lines.some(line => line.isModified);
        if (hasModifications) modifiedFilesCount++;
      }
    } else {
      // 只导出被修改的文件
      for (const file of session.files) {
        const hasModifications = file.lines.some(line => line.isModified);
        if (hasModifications) {
          const currentContent = file.lines.map(line => line.content).join('\n');
          zip.file(file.path, currentContent);
          modifiedFilesCount++;
        }
      }
    }

    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    return {
      success: true,
      zipBuffer: Array.from(zipBuffer),
      fileName: `${session.name}_${exportType === 'all' ? 'all_files' : 'modified_files'}.zip`,
      stats: {
        modifiedFiles: modifiedFilesCount,
        totalFiles: totalFilesCount
      }
    };
  } catch (error) {
    console.error('导出会话失败:', error);
    return { success: false, error: '导出失败' };
  }
}

/**
 * 获取文件的修改历史记录
 * @param fileId 文件ID
 * @returns 返回文件的修改历史记录
 */
export async function getFileModificationHistory(fileId: string) {
  try {
    console.log(`[Server Action] 正在获取文件修改历史: ${fileId}`);

    // 获取文件基本信息
    const file = await prisma.file.findUnique({
      where: { id: fileId },
      select: {
        id: true,
        path: true,
        originalContent: true,
        createdAt: true,
        updatedAt: true,
        uploadSession: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!file) {
      console.warn(`[Server Action] 未在数据库中找到文件 ID: ${fileId}`);
      return null;
    }

    // 获取所有修改过的行，按修改时间分组
    const modifiedLines = await prisma.attributedLine.findMany({
      where: {
        fileId: fileId,
        isModified: true,
        modifiedByName: { not: null },
        modifiedAt: { not: null }
      },
      orderBy: { modifiedAt: "desc" },
      select: {
        lineNumber: true,
        content: true,
        modifiedByName: true,
        modifiedAt: true
      }
    });

    // 按修改者分组
    const modificationsByUser = modifiedLines.reduce((acc, line) => {
      const userName = line.modifiedByName || '未知用户';
      if (!acc[userName]) {
        acc[userName] = [];
      }
      acc[userName].push({
        lineNumber: line.lineNumber,
        content: line.content,
        modifiedAt: line.modifiedAt
      });
      return acc;
    }, {} as Record<string, Array<{
      lineNumber: number;
      content: string;
      modifiedAt: Date;
    }>>);

    // 转换为数组格式，按修改时间排序
    const history = Object.entries(modificationsByUser).map(([userName, modifications]) => ({
      userName,
      modifications: modifications.sort((a, b) => b.modifiedAt.getTime() - a.modifiedAt.getTime()),
      totalModifications: modifications.length,
      lastModified: modifications[0]?.modifiedAt
    })).sort((a, b) => {
      const aTime = a.lastModified?.getTime() || 0;
      const bTime = b.lastModified?.getTime() || 0;
      return bTime - aTime;
    });

    console.log(`[Server Action] 成功获取文件修改历史: ${file.path}, 共 ${history.length} 个用户进行了修改`);

    return {
      file,
      history,
      totalModifications: modifiedLines.length
    };
  } catch (error) {
    console.error(`[Server Action] 获取文件修改历史 ${fileId} 时发生错误:`, error);
    return null;
  }
}

/**
 * 批量更新会话顺序
 * @param orderList 会话ID和顺序的数组
 * @returns 更新结果
 */
export async function updateSessionOrder(orderList: {id: string, order: number}[]) {
  try {
    console.log('Server Action: updateSessionOrder', orderList);
    if (!prisma) {
      throw new Error("数据库连接客户端未初始化。");
    }
    
    // 使用事务批量更新
    await prisma.$transaction(
      orderList.map(({id, order}) =>
        prisma.uploadSession.update({
          where: { id },
          data: { order }
        })
      )
    );
    
    console.log('会话顺序更新成功');
    return { success: true };
  } catch (e: any) {
    console.error('更新会话顺序失败:', e);
    return { success: false, error: `更新失败: ${e.message}` };
  }
}