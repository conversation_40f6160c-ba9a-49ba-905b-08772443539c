"use server";

import prisma from "@/utils/prisma";

/**
 * 查找或创建用户
 * @param userData - 包含用户信息的对象，必须有 email
 * @returns 返回找到或创建的用户对象 (Prisma User Model)
 */
export async function findOrCreateUser(userData: User): Promise<User> {
  try {
    return await prisma.user.upsert({
      where: {
        username: userData.username,
      },
      update: {
        username: userData.username,
      },
      create: {
        // 如果未找到用户，则使用这些字段创建新用户
        username: userData.username,
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Database operation failed: ${error.message}`);
    }
    throw new Error("An unknown error occurred during user upsert.");
  }
}

export async function getUserById(id: number) {
  return prisma.user.findUnique({
    where: {
      id,
    },
  });
}

export async function updateUser(user: any) {
  const { id, ...updateData } = user;

  return prisma.user.update({
    where: {
      id,
    },
    data: updateData,
  });
}
