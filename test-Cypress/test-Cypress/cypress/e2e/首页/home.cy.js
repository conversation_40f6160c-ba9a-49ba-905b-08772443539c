describe('Login Test', () => {
	beforeEach(() => {
		cy.setCookie('versionCode', '90700600');
		const deviceInfo = {
			versionCode: 90700600,
			packageName: "com.hihonor.id",
			deviceModel: "MAA-AN00",
			appFingerprint: "EDE02A47F935EC8990EF9CD1DEE9D0E9353ED612DAD85900966F79A972EBE071",
			country: "CN",
			language: "zh-CN"
		};
		cy.setCookie('wallet_deviceInfo', JSON.stringify(deviceInfo));
		const userInfo = {
			userId: "6160086500000487098",
			accessToken: "CgB6e3x9vEoYkKe8xfOe7rwCw3eEVY0bxdICkRkJ8QTF0yDzexlfKP0WmmJt1Ge0cQDzZEjLhQUHHorFLqIkcdOtLYliMMeXpSAcMsrR9t0zRdVuTjF0ai9uGGk/kzdwMGE=",
			deviceId: "6600BFC8DE13F3E5F6754752ED778EF5E651193B4B1325624580CB80EB4233F6",
			cid: "0"
		};
		cy.setCookie('wallet_userInfo', JSON.stringify(userInfo));
		cy.setCookie('sdkVersionCode', '90005000');
		cy.visit('/wallet-loan-web/pages/index');
	});

	it('should submit form and show result', () => {
		cy.wait(1000); // 延迟1秒
		// cy.get('.model-contain-closed-icon', {timeout: 20000}).click();           // 点击按钮
		// cy.wait(1000); // 延迟1秒
		// cy.get('.scroll-container-blur').scrollTo('bottom', { duration: 2000 });
		// cy.wait(1000); // 延迟1秒
		// cy.get('.scroll-container-blur').scrollTo('top', { duration: 2000 });
	});
});