package com.hihonor.wallet;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hihonor.wallet.common.client.WiseOperClient;
import com.hihonor.wallet.common.util.WoUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.dto.DiversionRatioApprovalHistoryRecords;
import com.hihonor.wallet.dto.LoanSupplierConfigDto;
import com.hihonor.wallet.dto.SuggestListApprovalHistoryRecords;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.LoanSupplierEntity;
import com.hihonor.wallet.loan.mapper.LoanSupplierMapper;
import com.hihonor.wallet.service.LoanConfigService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
class WalletLoanmgrServiceApplicationTests {

    @Autowired
    LoanConfigService loanConfigService;

    @Mock
    private HttpServletRequest request; // 模拟 HttpServletRequest 对象

    @Mock
    private WiseOperClient wiseOperClient; // 模拟 WiseOperClient 对象

    @Mock
    private WoUtils woUtils;


    @Autowired
    private LoanSupplierMapper loanSupplierMapper;

    @Test
    void contextLoads() {
        String pollingOrder = "40";
        if (StringUtils.isNotBlank(pollingOrder)) {
            List<LoanSupplierEntity> data = this.getApiSupplierList();
            Map<String, String> supplierNameIdMap = data.stream().collect(Collectors.toMap(LoanSupplierEntity::getSupplierName,
                    loanSupplierDto -> String.valueOf(loanSupplierDto.getSupplierId())
            ));
            String[] supplierNameArray = pollingOrder.split(">");
            for (String supplierName : supplierNameArray) {
                String supplierId = supplierNameIdMap.get(supplierName);
                pollingOrder = pollingOrder.replaceAll(supplierName, StringUtils.isNotBlank(supplierId) ? supplierId : "");
            }
            pollingOrder = pollingOrder.replaceAll(">", ",");
        }
        LogUtil.runInfoLog(pollingOrder);
    }

    @Test
    public void getSuggestListApprovalHistoryRecords() {
        SuggestListApprovalHistoryRecords suggestListApprovalHistoryRecords = loanConfigService.getSuggestListApprovalHistoryRecords();
        LogUtil.runInfoLog(suggestListApprovalHistoryRecords.toString());
    }

    @Test
    public void getDiversionRatioApprovalHistoryRecords() {
        DiversionRatioApprovalHistoryRecords diversionRatioApprovalHistoryRecords = loanConfigService.getDiversionRatioApprovalHistoryRecords();
        LogUtil.runInfoLog(diversionRatioApprovalHistoryRecords.toString());
    }

    @Test
    public void getLoanSupplier() {
        List<LoanSupplierConfigDto> loanSupplierConfigDtoList = loanConfigService.getLoanSupplier();
        LogUtil.runInfoLog(loanSupplierConfigDtoList.toString());
    }


    private List<LoanSupplierEntity> getApiSupplierList() {
        return loanSupplierMapper.selectList(new LambdaQueryWrapper<LoanSupplierEntity>()
                .eq(LoanSupplierEntity::getSupportApi, LoanConstant.SupplierSupportApi.SUPPORT)
                .orderByAsc(LoanSupplierEntity::getSortIndex));
    }

}
