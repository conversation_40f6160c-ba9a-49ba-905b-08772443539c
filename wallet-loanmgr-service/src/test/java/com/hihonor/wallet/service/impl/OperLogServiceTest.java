package com.hihonor.wallet.service.impl;

import com.hihonor.wallet.WalletLoanMgrApplication;
import com.hihonor.wallet.common.kafka.KafkaMagicOperLogMessage;
import com.hihonor.wallet.common.kafka.client.KafkaWalletProducer;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperLogEntity;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.WoUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanMgrApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
public class OperLogServiceTest {
    @Mock
    private WoUtils woUtils;

    @Mock
    private EncryptUtil encryptUtil;

    @Mock
    private KafkaWalletProducer kafkaWalletProducer;

    @InjectMocks
    private OperLogService logService;

    @Test
    public void runSuccessLog_NormalCase() {
        // 准备测试数据
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        wiseoperLogEntity.setLoggerResponse("SUCCESS");

        // 模拟依赖
        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn("encryptedToken");

        // 执行方法
        logService.runSuccessLog(request, wiseoperLogEntity);

        // 验证
        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());

        // 验证消息内容
        KafkaMagicOperLogMessage message = messageCaptor.getValue();
        assertEquals("encryptedToken", message.getToken());
        assertEquals("SUCCESS", message.getLoggerResponse());
    }


    @Test()
    public void runSuccessLogRequestIsNull() {
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        logService.runSuccessLog(null, wiseoperLogEntity);
    }

    @Test()
    public void runSuccessLogEntityIsNull() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        logService.runSuccessLog(request, null);
    }

    @Test
    public void runSuccessLogEncryption() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        String expectedToken = "encryptedToken";

        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn(expectedToken);

        logService.runSuccessLog(request, wiseoperLogEntity);

        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());
        assertEquals(expectedToken, messageCaptor.getValue().getToken());
    }

    @Test
    public void runSuccessLogPropertyCopy() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        wiseoperLogEntity.setLoggerResponse("SUCCESS");

        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn("encryptedToken");

        logService.runSuccessLog(request, wiseoperLogEntity);

        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());
        assertEquals("testValue", messageCaptor.getValue().getOperatorBrowser());
    }


    @Test
    public void runFailLogNormalCase() {
        // 准备测试数据
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        wiseoperLogEntity.setLoggerResponse("FAIL");

        // 模拟依赖
        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn("encryptedToken");

        // 执行方法
        logService.runFailLog(request, wiseoperLogEntity);

        // 验证
        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());

        // 验证消息内容
        KafkaMagicOperLogMessage message = messageCaptor.getValue();
        assertEquals("encryptedToken", message.getToken());
        assertEquals("FAIL", message.getLoggerResponse());
    }

    @Test()
    public void runFailLog_RequestIsNull() {
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        logService.runFailLog(null, wiseoperLogEntity);
    }

    @Test()
    public void runFailLog_EntityIsNull() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        logService.runFailLog(request, null);
    }

    @Test
    public void runFailLog_Encryption() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        String expectedToken = "encryptedToken";

        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn(expectedToken);

        logService.runFailLog(request, wiseoperLogEntity);

        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());
        assertEquals(expectedToken, messageCaptor.getValue().getToken());
    }

    @Test
    public void runFailLogPropertyCopy() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        WiseoperLogEntity wiseoperLogEntity = new WiseoperLogEntity();
        wiseoperLogEntity.setLoggerResponse("FAIL");
        

        when(woUtils.getWoToken(request)).thenReturn("testToken");
        when(encryptUtil.encrypt("testToken")).thenReturn("encryptedToken");

        logService.runFailLog(request, wiseoperLogEntity);

        ArgumentCaptor<KafkaMagicOperLogMessage> messageCaptor = ArgumentCaptor.forClass(KafkaMagicOperLogMessage.class);
        verify(kafkaWalletProducer).sendMessage(any(), messageCaptor.capture());
        assertEquals("testValue", messageCaptor.getValue());
    }
    
    
    
}
