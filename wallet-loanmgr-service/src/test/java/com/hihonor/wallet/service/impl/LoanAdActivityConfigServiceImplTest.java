package com.hihonor.wallet.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.wallet.WalletLoanMgrApplication;
import com.hihonor.wallet.common.model.dto.loan.LoanAdActivityDto;
import com.hihonor.wallet.common.model.dto.transcard.PageBaseResponse;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperRequestContextUtils;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityListParam;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.LoanAdActivityEntity;
import com.hihonor.wallet.loan.entity.LoanApproveRecordEntity;
import com.hihonor.wallet.loan.mapper.LoanAdActivityMapper;
import com.hihonor.wallet.loan.mapper.LoanApproveRecordMapper;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanMgrApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
public class LoanAdActivityConfigServiceImplTest {

    @InjectMocks
    private LoanAdActivityConfigServiceImpl service;

    @Mock
    private LoanAdActivityMapper loanAdActivityMapper;

    @Mock
    private RedisUtil redisUtil;


    @Mock
    private LoanApproveRecordMapper loanApproveRecordMapper;


    private LoanAdActivityListParam loanAdActivityListParam;

    @BeforeEach
    public void setUp() {

        MockitoAnnotations.openMocks(this);
        WiseoperRequestContextUtils.setOperAccount("testOperId");

        loanAdActivityListParam = new LoanAdActivityListParam();
        loanAdActivityListParam.setPageIndex(1);
        loanAdActivityListParam.setPageSize(10);
        loanAdActivityListParam.setActivityDesc("test");
    }

    @Test
    public void getLoanAdActivityListwithValidParams() {
        // 准备测试数据
        List<LoanAdActivityEntity> loanAdActivityEntityList = prepareLoanAdActivityEntities();
        Page<LoanAdActivityEntity> page = new Page<>(1, 10);
        page.setRecords(loanAdActivityEntityList);
        page.setPages(1);
        page.setTotal(loanAdActivityEntityList.size());

        when(loanAdActivityMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        List<LoanApproveRecordEntity> loanApproveRecordEntities = prepareLoanApproveRecordEntities();
        when(loanApproveRecordMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(loanApproveRecordEntities);

        // 执行被测方法
        PageBaseResponse<LoanAdActivityDto> result = service.getLoanAdActivityList(loanAdActivityListParam);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getPages());
        assertEquals(loanAdActivityEntityList.size(), result.getTotal());
        assertEquals(loanAdActivityEntityList.size(), result.getRecords().size());
    }

    private List<LoanAdActivityEntity> prepareLoanAdActivityEntities() {
        List<LoanAdActivityEntity> entities = new ArrayList<>();
        LoanAdActivityEntity entity = new LoanAdActivityEntity();
        entity.setId(1);
        entity.setActivityDesc("test");
        entity.setPreDivisionSupplier("1");
        entities.add(entity);
        return entities;
    }

    private List<LoanApproveRecordEntity> prepareLoanApproveRecordEntities() {
        List<LoanApproveRecordEntity> records = new ArrayList<>();
        LoanApproveRecordEntity record = new LoanApproveRecordEntity();
        record.setType(2);
        record.setApproveContent("{\"id\":1,\"activityDesc\":\"test\"}");
        record.setApproveStatus(1);
        records.add(record);
        return records;
    }

    @Test
    public void saveLoanAdActivityNewActivity() {
        // Arrange
        LoanAdActivityParam param = new LoanAdActivityParam();
        param.setCid("testCid");
        param.setSubCid("testSubCid");
        param.setActivityDesc("testDescription");
        param.setPreDivisionSupplier(123);

        LoanAdActivityEntity expectedEntity = new LoanAdActivityEntity();
        BeanUtils.copyProperties(param, expectedEntity);
        expectedEntity.setOperId("testOperId");
        expectedEntity.setPreDivisionSupplier("123");
        expectedEntity.setActivityCode(generateExpectedActivityCode(null, "123"));
        expectedEntity.setPageLink(generateExpectedPageLink("testCid", "testSubCid", expectedEntity.getActivityCode()));

        when(loanAdActivityMapper.insert(any())).thenReturn(1); // Assuming insert returns the number of rows affected

        // Act
        service.saveLoanAdActivity(param);

        // Assert
        verify(loanAdActivityMapper, times(1)).insert(expectedEntity);
        verify(redisUtil, never()).remove(anyString()); // No removal from cache since it's a new activity
    }

    @Test
    public void saveLoanAdActivityUpdateExistingActivity() {
        // Arrange
        LoanAdActivityParam param = new LoanAdActivityParam();
        param.setId(1);
        param.setCid("testCid");
        param.setSubCid("testSubCid");
        param.setActivityDesc("testDescription");
        param.setPreDivisionSupplier(123);
        param.setActivityCode("originalActivityCode");

        LoanAdActivityEntity expectedEntity = new LoanAdActivityEntity();
        BeanUtils.copyProperties(param, expectedEntity);
        expectedEntity.setOperId("testOperId");
        expectedEntity.setPreDivisionSupplier("123");
        expectedEntity.setActivityCode(generateExpectedActivityCode("originalActivityCode", "123"));
        expectedEntity.setPageLink(generateExpectedPageLink("testCid", "testSubCid", expectedEntity.getActivityCode()));

        when(loanAdActivityMapper.update(any(), any())).thenReturn(1); // Assuming update returns the number of rows affected

        // Act
        service.saveLoanAdActivity(param);

        // Assert
        verify(loanAdActivityMapper, times(1)).update(eq(expectedEntity), any(LambdaUpdateWrapper.class));
        verify(redisUtil, times(1)).remove(eq(LoanConstant.LOAN_AD_ACTIVITY_CACHE_KEY_PRE + expectedEntity.getActivityCode()));
    }

    private String generateExpectedActivityCode(String originalActivityCode, String preDivisionSupplier) {
        String firstPart;
        if (ObjUtil.isNotEmpty(originalActivityCode)) {
            firstPart = originalActivityCode.substring(0, originalActivityCode.length() - 2);
        } else {
            firstPart = String.valueOf(System.currentTimeMillis());
        }
        String secondPart = LoanConstant.CPID_2_CPCODE_MAP.getOrDefault(preDivisionSupplier, "00");
        return firstPart + secondPart;
    }

    private String generateExpectedPageLink(String cid, String subCid, String activityCode) {
        String firstPart = String.format("asjkchk", cid, subCid);
        String secondPart = String.format("www.baidu.com", activityCode);
        return firstPart + secondPart;
    }


}
