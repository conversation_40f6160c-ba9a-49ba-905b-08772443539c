package com.hihonor.wallet.service.impl;

import com.hihonor.wallet.WalletLoanMgrApplication;
import com.hihonor.wallet.common.client.DmpClient;
import com.hihonor.wallet.common.config.nacos.ActivityConfig;
import com.hihonor.wallet.common.config.nacos.DmpConfig;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.model.dto.loan.GetActivityListDto;
import com.hihonor.wallet.common.model.param.loan.CreateActivityParam;
import com.hihonor.wallet.loan.entity.CrowdActivityEntity;
import com.hihonor.wallet.loan.mapper.CrowdActivityMapper;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


@SpringBootTest(classes = WalletLoanMgrApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
public class CrowdServiceImplTest {

    @Mock
    private DmpClient dmpClient;

    @Mock
    private DmpConfig dmpConfig;

    @Mock
    private CrowdActivityMapper crowdActivityMapper;

    @Mock
    private ActivityConfig activityConfig;

    @InjectMocks
    private CrowdServiceImpl crowdServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testReportCrowdUsageScene_Success_Insert() {
        // 准备测试数据
        CreateActivityParam param = new CreateActivityParam();
        param.setStartTime(new Date());
        param.setEndTime(new Date());
        param.setCrowdId("testCrowdId");
        param.setActivityName("testActivity");
        when(dmpConfig.getBusiness()).thenReturn("testBusiness");

        // 模拟 crowdActivityMapper.insert 调用
        when(crowdActivityMapper.insert(any())).thenReturn(1);

        // 执行方法
        crowdServiceImpl.reportCrowdUsageScene(param);

        // 验证方法调用
        verify(crowdActivityMapper).insert(any(CrowdActivityEntity.class));
        verify(dmpClient).reportCrowdUsageScene(any());
    }

    @Test
    public void testReportCrowdUsageScene_Success_Update() {
        // 准备测试数据
        CreateActivityParam param = new CreateActivityParam();
        param.setActivityId(1L);
        param.setStartTime(new Date());
        param.setEndTime(new Date());
        param.setCrowdId("testCrowdId");
        param.setActivityName("testActivity");
        when(dmpConfig.getBusiness()).thenReturn("testBusiness");

        // 模拟 crowdActivityMapper.update 调用
        when(crowdActivityMapper.update(any(), any())).thenReturn(1);

        // 执行方法
        crowdServiceImpl.reportCrowdUsageScene(param);

        // 验证方法调用
        verify(crowdActivityMapper).update(any(), any());
        verify(dmpClient).reportCrowdUsageScene(any());
    }

    @Test
    public void testReportCrowdUsageScene_InvalidTime() {
        // 准备测试数据
        CreateActivityParam param = new CreateActivityParam();
        param.setStartTime(new Date(System.currentTimeMillis() + 1000));
        param.setEndTime(new Date());

        // 执行方法并验证异常
        assertThrows(BusinessException.class, () -> {
            crowdServiceImpl.reportCrowdUsageScene(param);
        });
    }

    @Test
    public void testReportCrowdUsageScene_CrowdIdEmpty() {
        // 准备测试数据
        CreateActivityParam param = new CreateActivityParam();
        param.setStartTime(new Date());
        param.setEndTime(new Date());

        // 执行方法
        crowdServiceImpl.reportCrowdUsageScene(param);

        // 验证 dmpClient 方法未被调用
        verify(dmpClient, times(0)).reportCrowdUsageScene(any());
    }

    @Test
    public void testReportCrowdUsageScene_ParseException() {
        // 准备测试数据
        CreateActivityParam param = new CreateActivityParam();
        param.setStartTime(new Date());
        param.setEndTime(new Date());

        // 模拟日期解析失败
        when(crowdActivityMapper.insert(any())).thenThrow(new RuntimeException("Parse Exception"));

        // 执行方法并验证异常
        assertThrows(RuntimeException.class, () -> {
            crowdServiceImpl.reportCrowdUsageScene(param);
        });
    }

    @Test
    public void testGetCrowdActivityList_Normal() {
        // 准备测试数据
        List<CrowdActivityEntity> entities = new ArrayList<>();
        CrowdActivityEntity entity = new CrowdActivityEntity();
        entity.setActivityId(1L);
        entity.setStartTime(new Date());
        entity.setEndTime(new Date());
        entities.add(entity);

        when(crowdActivityMapper.selectList(any())).thenReturn(entities);
        when(activityConfig.getLoanActivitySceneMap()).thenReturn(Collections.singletonMap(1, "testScene"));
        when(activityConfig.getLoanActivityList()).thenReturn(new ArrayList<>());

        // 执行方法
        GetActivityListDto result = crowdServiceImpl.getCrowdActivityList();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getActivityList().size());
    }

    @Test
    public void testGetCrowdActivityList_StartTimeBefore() {
        // 准备测试数据
        CrowdActivityEntity entity = new CrowdActivityEntity();
        entity.setActivityId(1L);
        entity.setStartTime(new Date(System.currentTimeMillis() + 1000));
        entity.setEndTime(new Date());
        List<CrowdActivityEntity> entities = Collections.singletonList(entity);

        when(crowdActivityMapper.selectList(any())).thenReturn(entities);
        when(activityConfig.getLoanActivitySceneMap()).thenReturn(Collections.singletonMap(1, "testScene"));
        when(activityConfig.getLoanActivityList()).thenReturn(new ArrayList<>());

        // 执行方法
        GetActivityListDto result = crowdServiceImpl.getCrowdActivityList();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getActivityList().size());
        assertEquals(0, result.getActivityList().get(0).getStatus());
    }

    @Test
    public void testGetCrowdActivityList_EndTimeAfter() {
        // 准备测试数据
        CrowdActivityEntity entity = new CrowdActivityEntity();
        entity.setActivityId(1L);
        entity.setStartTime(new Date());
        entity.setEndTime(new Date(System.currentTimeMillis() - 1000));
        List<CrowdActivityEntity> entities = Collections.singletonList(entity);

        when(crowdActivityMapper.selectList(any())).thenReturn(entities);
        when(activityConfig.getLoanActivitySceneMap()).thenReturn(Collections.singletonMap(1, "testScene"));
        when(activityConfig.getLoanActivityList()).thenReturn(new ArrayList<>());

        // 执行方法
        GetActivityListDto result = crowdServiceImpl.getCrowdActivityList();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getActivityList().size());
        assertEquals(2, result.getActivityList().get(0).getStatus());
    }

    @Test
    public void testGetCrowdActivityList_EmptyList() {
        // 模拟空列表
        when(crowdActivityMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行方法
        GetActivityListDto result = crowdServiceImpl.getCrowdActivityList();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getActivityList().isEmpty());
    }

    @Test
    public void testGetCrowdActivityList_SelectListFail() {
        // 模拟查询失败
        when(crowdActivityMapper.selectList(any())).thenThrow(new RuntimeException("Query Failed"));

        // 执行方法并验证异常
        assertThrows(RuntimeException.class, () -> {
            crowdServiceImpl.getCrowdActivityList();
        });
    }
}
