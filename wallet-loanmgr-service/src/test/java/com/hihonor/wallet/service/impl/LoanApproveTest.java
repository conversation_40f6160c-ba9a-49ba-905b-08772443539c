package com.hihonor.wallet.service.impl;

import cn.hutool.core.date.DateTime;
import com.hihonor.wallet.WalletLoanMgrApplication;
import com.hihonor.wallet.common.client.OauthClient;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.model.dto.magicOper.*;
import com.hihonor.wallet.common.model.param.loan.ApplicationApprovalParam;
import com.hihonor.wallet.common.model.param.loan.LoanApproveParam;
import com.hihonor.wallet.common.model.param.loanmgr.SearchLoanInfoByAccountParam;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.WoUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.dto.*;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.LoanApproveRecordEntity;
import com.hihonor.wallet.loan.entity.LoanSupplierEntity;
import com.hihonor.wallet.loan.mapper.LoanApproveRecordMapper;
import com.hihonor.wallet.loan.mapper.LoanSupplierMapper;
import com.hihonor.wallet.obs.FileObsOperate;
import com.hihonor.wallet.service.LoanAdActivityConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = WalletLoanMgrApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class LoanApproveTest {

    @InjectMocks
    private LoanConfigServiceImpl loanConfigService;

    @Mock
    private LoanAdActivityConfigService loanAdActivityConfigService;

    @Mock
    private LoanSupplierMapper loanSupplierMapper;

    @Mock
    private LoanApproveRecordMapper loanApproveRecordMapper;

    @Mock
    private WoUtils woUtils;

    @Mock
    private FileObsOperate fileObsOperate;

    @Mock
    OauthClient oauthClient;

    @Mock
    RedisUtil redisUtil;

    @Mock
    RedisTemplate redisTemplate;

    @Value("${loan.query.limit}")
    private String QUERY_LIMIT;

    @Mock
    private MockHttpServletRequest mockHttpServletRequest;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(mockHttpServletRequest));
        ReflectionTestUtils.setField(loanConfigService, "realNameResourceId", "test_real_name_resource_id");
        ReflectionTestUtils.setField(loanConfigService, "loanDiversionResourceId", "test_loan_diversion_resource_id");
        ReflectionTestUtils.setField(loanConfigService, "suggestListResourceId", "test_suggest_list_resource_id");
    }

    @Test
    public void getApprovalInfoFlowNoNotExist() {
        ApprovalParam param = new ApprovalParam();
        param.setFlowNo("non_existent_flow_no");

        when(loanApproveRecordMapper.selectOne(any())).thenReturn(null);

        assertThrows(BusinessException.class, () -> loanConfigService.getApprovalInfo(param));
    }

    @Test
    public void getApprovalInfoWisoperTokenAuthFail() {
        ApprovalParam param = new ApprovalParam();
        param.setFlowNo("valid_flow_no");

        LoanApproveRecordEntity loanApproveRecordEntity = new LoanApproveRecordEntity();
        loanApproveRecordEntity.setFlowNo("valid_flow_no");

        when(loanApproveRecordMapper.selectOne(any())).thenReturn(loanApproveRecordEntity);
        when(woUtils.getCurrentUserInfoJsonObject(any(),any())).thenReturn("");

        assertThrows(BusinessException.class, () -> loanConfigService.getApprovalInfo(param));
    }

    @Test
    public void getApprovalInfoMagicOperResultException() {
        ApprovalParam param = new ApprovalParam();
        param.setFlowNo("valid_flow_no");

        LoanApproveRecordEntity loanApproveRecordEntity = new LoanApproveRecordEntity();
        loanApproveRecordEntity.setFlowNo("valid_flow_no");

        when(loanApproveRecordMapper.selectOne(any())).thenReturn(loanApproveRecordEntity);
        when(woUtils.getCurrentUserInfoJsonObject(any(), any())).thenReturn("{\"account\":\"test_user\"}");
        when(woUtils.getFlowDetailJsonObject(any(), eq("valid_flow_no"))).thenReturn("{}");

        assertThrows(BusinessException.class, () -> loanConfigService.getApprovalInfo(param));
    }

    @Test
    public void getApprovalInfoApprovalProcegssException() {
        ApprovalParam param = new ApprovalParam();
        param.setFlowNo("valid_flow_no");

        LoanApproveRecordEntity loanApproveRecordEntity = new LoanApproveRecordEntity();
        loanApproveRecordEntity.setFlowNo("valid_flow_no");

        FlowDetail flowDetail = new FlowDetail();
        flowDetail.setNodeList(Collections.emptyList());

        when(loanApproveRecordMapper.selectOne(any())).thenReturn(loanApproveRecordEntity);
        when(woUtils.getCurrentUserInfoJsonObject(any(), any())).thenReturn("{\"account\":\"test_user\"}");
        when(woUtils.getFlowDetailJsonObject(any(), eq("valid_flow_no"))).thenReturn(JsonUtils.toJson(flowDetail));

        assertThrows(BusinessException.class, () -> loanConfigService.getApprovalInfo(param));
    }

    @Test
    public void getApprovalInfoSuccess() throws Exception {
        ApprovalParam param = new ApprovalParam();
        param.setFlowNo("valid_flow_no");

        LoanApproveRecordEntity loanApproveRecordEntity = new LoanApproveRecordEntity();
        loanApproveRecordEntity.setFlowNo("valid_flow_no");
        loanApproveRecordEntity.setType(LoanConstant.ApproveType.adActivityRealName);
        loanApproveRecordEntity.setApprovalMaterialUrl("test_url");
        loanApproveRecordEntity.setApproveContent("{\"id\": \"test_id\"}");

        FlowDetail flowDetail = new FlowDetail();
        flowDetail.setNodeList(Collections.singletonList(new ActivitiImageInfo()));

        when(loanApproveRecordMapper.selectOne(any())).thenReturn(loanApproveRecordEntity);
        when(woUtils.getCurrentUserInfoJsonObject(any(), any())).thenReturn("{\"account\":\"test_user\"}");
        when(woUtils.getFlowDetailJsonObject(any(), eq("valid_flow_no"))).thenReturn(JsonUtils.toJson(flowDetail));
        when(fileObsOperate.generatePresignedUrl(eq("test_url"))).thenReturn("presigned_test_url");

        ApprovalResponse response = loanConfigService.getApprovalInfo(param);

        assertEquals("presigned_test_url", response.getApprovalMaterial());
        assertEquals(2, response.getType());
    }


    // 测试用例1：正常情况下获取历史记录
    @Test
    public void getDiversionRatioApprovalHistoryRecordsSuccess() throws Exception {
        // 准备数据
        LoanApproveRecordEntity mockRecord = new LoanApproveRecordEntity();
        mockRecord.setId(1);
        mockRecord.setType(LoanConstant.ApproveType.diversionRatio);
        mockRecord.setApproveStatus(LoanConstant.ApproveStatus.PASS);
        mockRecord.setStartTime(new Date());
        mockRecord.setEndTime(new Date());
        mockRecord.setApprovalMaterialUrl("testUrl");
        mockRecord.setApproveContent("[{\"supplierId\":1}]");

        List<LoanApproveRecordEntity> mockList = new ArrayList<>();
        mockList.add(mockRecord);

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);

        LoanSupplierEntity supplier = new LoanSupplierEntity();
        supplier.setSupplierId(1);
        supplier.setSupplierName("Supplier1");

        List<LoanSupplierEntity> suppliers = new ArrayList<>();
        suppliers.add(supplier);


        when(fileObsOperate.generatePresignedUrl("testUrl")).thenReturn("presignedUrl");

        // 执行方法
        DiversionRatioApprovalHistoryRecords result = loanConfigService.getDiversionRatioApprovalHistoryRecords();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());

    }

    // 测试用例2：approveContent解析失败
    @Test
    public void getDiversionRatioApprovalHistoryRecords_ParseException() throws Exception {
        // 准备数据
        LoanApproveRecordEntity mockRecord = new LoanApproveRecordEntity();
        mockRecord.setId(1);
        mockRecord.setType(LoanConstant.ApproveType.diversionRatio);
        mockRecord.setApproveStatus(LoanConstant.ApproveStatus.PASS);
        mockRecord.setStartTime(new Date());
        mockRecord.setEndTime(new Date());
        mockRecord.setApprovalMaterialUrl("testUrl");
        mockRecord.setApproveContent("invalidJson");

        List<LoanApproveRecordEntity> mockList = new ArrayList<>();
        mockList.add(mockRecord);

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);

        LoanSupplierEntity supplier = new LoanSupplierEntity();
        supplier.setSupplierId(1);
        supplier.setSupplierName("Supplier1");

        List<LoanSupplierEntity> suppliers = new ArrayList<>();
        suppliers.add(supplier);


        when(fileObsOperate.generatePresignedUrl("testUrl")).thenReturn("presignedUrl");

        // 执行方法并验证异常
        assertThrows(BusinessException.class, () -> {
            loanConfigService.getDiversionRatioApprovalHistoryRecords();
        });
    }

    // 测试用例3：没有历史记录
    @Test
    public void getDiversionRatioApprovalHistoryRecords_NoRecords() {
        // 准备数据
        List<LoanApproveRecordEntity> mockList = new ArrayList<>();

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);

        LoanSupplierEntity supplier = new LoanSupplierEntity();
        supplier.setSupplierId(1);
        supplier.setSupplierName("Supplier1");

        List<LoanSupplierEntity> suppliers = new ArrayList<>();
        suppliers.add(supplier);


        // 执行方法
        DiversionRatioApprovalHistoryRecords result = loanConfigService.getDiversionRatioApprovalHistoryRecords();

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getList().size());
    }

    // 测试用例4：approveContent为空
    @Test
    public void getDiversionRatioApprovalHistoryRecords_ApproveContentEmpty() throws Exception {
        // 准备数据
        LoanApproveRecordEntity mockRecord = new LoanApproveRecordEntity();
        mockRecord.setId(1);
        mockRecord.setType(LoanConstant.ApproveType.diversionRatio);
        mockRecord.setApproveStatus(LoanConstant.ApproveStatus.PASS);
        mockRecord.setStartTime(new Date());
        mockRecord.setEndTime(new Date());
        mockRecord.setApprovalMaterialUrl("testUrl");
        mockRecord.setApproveContent("");

        List<LoanApproveRecordEntity> mockList = new ArrayList<>();
        mockList.add(mockRecord);

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);

        LoanSupplierEntity supplier = new LoanSupplierEntity();
        supplier.setSupplierId(1);
        supplier.setSupplierName("Supplier1");

        List<LoanSupplierEntity> suppliers = new ArrayList<>();
        suppliers.add(supplier);

        when(fileObsOperate.generatePresignedUrl("testUrl")).thenReturn("presignedUrl");

        // 执行方法
        DiversionRatioApprovalHistoryRecords result = loanConfigService.getDiversionRatioApprovalHistoryRecords();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());

        DiversionRatioApprovelHistoryRecord record = result.getList().get(0);
        assertEquals(1, record.getRecordId());
        assertEquals("presignedUrl", record.getApprovalMaterialUrl());
        assertTrue(record.getDiversionRatioApprovalInfos().isEmpty());
    }


    @Test
    public void getSuggestListApprovalHistoryRecords_Success(){
        // 准备测试数据
        List<LoanApproveRecordEntity> mockList = new ArrayList<>();
        LoanApproveRecordEntity entity = new LoanApproveRecordEntity();
        entity.setId(1);
        entity.setType(1);
        entity.setApproveStatus(0);
        entity.setStartTime(DateTime.now());
        entity.setEndTime(DateTime.now());
        entity.setApprovalMaterialUrl("test_url");
        entity.setApproveContent("[{\"name\":\"test\",\"value\":1}]");

        mockList.add(entity);

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);
        when(fileObsOperate.generatePresignedUrl("test_url")).thenReturn("signed_url");

        // 执行方法
        SuggestListApprovalHistoryRecords result = loanConfigService.getSuggestListApprovalHistoryRecords();

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.getModifyFlag());
        assertEquals(1, result.getList().size());
        assertEquals("signed_url", result.getList().get(0).getApprovalMaterialUrl());
        assertEquals(1, result.getList().get(0).getSuggestInfos().size());
    }

    @Test
    public void getSuggestListApprovalHistoryRecords_NoData() {
        // 准备测试数据
        when(loanApproveRecordMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行方法
        SuggestListApprovalHistoryRecords result = loanConfigService.getSuggestListApprovalHistoryRecords();

        // 验证结果
        assertNotNull(result);
        assertEquals(true, result.getModifyFlag());
        assertTrue(result.getList().isEmpty());
    }

    @Test
    public void getSuggestListApprovalHistoryRecords_ParseException()  {
        // 准备测试数据
        List<LoanApproveRecordEntity> mockList = new ArrayList<>();
        LoanApproveRecordEntity entity = new LoanApproveRecordEntity();
        entity.setId(2);
        entity.setType(2);
        entity.setApproveStatus(2);
        entity.setStartTime(DateTime.now());
        entity.setEndTime(DateTime.now());
        entity.setApprovalMaterialUrl("test_url");
        entity.setApproveContent("invalid_json");

        mockList.add(entity);

        when(loanApproveRecordMapper.selectList(any())).thenReturn(mockList);

        // 执行方法并验证异常
        assertThrows(BusinessException.class, () -> {
            loanConfigService.getSuggestListApprovalHistoryRecords();
        });
    }


    @Test
    public void getSuggestInfoListSuccess() {
        // 准备测试数据
        List<LoanSupplierEntity> mockLoanSupplierList = new ArrayList<>();
        LoanSupplierEntity supplier1 = new LoanSupplierEntity();
        supplier1.setSupplierName("Supplier A");
        supplier1.setSortIndex(1);
        LoanSupplierEntity supplier2 = new LoanSupplierEntity();
        supplier2.setSupplierName("Supplier B");
        supplier2.setSortIndex(2);

        mockLoanSupplierList.add(supplier1);
        mockLoanSupplierList.add(supplier2);


        // 执行方法
        List<SuggestInfo> result = loanConfigService.getSuggestInfoList();

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("Supplier A", result.get(0).getSupplierName());
        assertEquals(1, result.get(0).getSortIndex());
        assertEquals("Supplier B", result.get(1).getSupplierName());
        assertEquals(2, result.get(1).getSortIndex());
    }

    @Test
    public void getSuggestInfoListEmptyList() {
        // 准备测试数据
        List<LoanSupplierEntity> mockLoanSupplierList = new ArrayList<>();


        // 执行方法
        List<SuggestInfo> result = loanConfigService.getSuggestInfoList();

        // 验证结果
        assertEquals(0, result.size());
    }


    @Test
    public void getLoanSupplierSuccess() {
        // 准备测试数据
        List<LoanSupplierEntity> mockData = new ArrayList<>();
        LoanSupplierEntity entity1 = new LoanSupplierEntity();
        entity1.setSupplierId(1);
        entity1.setSupplierName("Supplier A");
        entity1.setPollingOrder("1,2");
        entity1.setCreditVerifyList("A,B");

        LoanSupplierEntity entity2 = new LoanSupplierEntity();
        entity2.setSupplierId(2);
        entity2.setSupplierName("Supplier B");
        entity2.setPollingOrder("2,1");
        entity2.setCreditVerifyList("B,A");

        mockData.add(entity1);
        mockData.add(entity2);

        // 执行方法
        List<LoanSupplierConfigDto> result = loanConfigService.getLoanSupplier();

        // 验证结果
        assertEquals(2, result.size());

        LoanSupplierConfigDto configDto1 = result.get(0);
        assertEquals("Supplier A>Supplier B", configDto1.getPollingOrder());
        assertEquals("Credit Verify A/Credit Verify B", configDto1.getCreditVerifyList());

        LoanSupplierConfigDto configDto2 = result.get(1);
        assertEquals("Supplier B>Supplier A", configDto2.getPollingOrder());
        assertEquals("Credit Verify B/Credit Verify A", configDto2.getCreditVerifyList());
    }

    @Test
    public void getLoanSupplierEmptyList() {
        // 准备测试数据
        List<LoanSupplierEntity> mockData = new ArrayList<>();


        // 执行方法
        List<LoanSupplierConfigDto> result = loanConfigService.getLoanSupplier();

        // 验证结果
        assertEquals(0, result.size());
    }

    @Test
    public void getLoanSupplier_PollingOrderBlank() {
        // 准备测试数据
        List<LoanSupplierEntity> mockData = new ArrayList<>();
        LoanSupplierEntity entity1 = new LoanSupplierEntity();
        entity1.setSupplierId(1);
        entity1.setSupplierName("Supplier A");
        entity1.setPollingOrder(""); // 空字符串
        entity1.setCreditVerifyList("A,B");

        mockData.add(entity1);


        // 执行方法
        List<LoanSupplierConfigDto> result = loanConfigService.getLoanSupplier();

        // 验证结果
        assertEquals(1, result.size());

        LoanSupplierConfigDto configDto1 = result.get(0);
        assertEquals("", configDto1.getPollingOrder()); // 应该为空字符串
        assertEquals("Credit Verify A/Credit Verify B", configDto1.getCreditVerifyList());
    }

    @Test
    public void getLoanSupplier_CreditVerifyListBlank() {
        // 准备测试数据
        List<LoanSupplierEntity> mockData = new ArrayList<>();
        LoanSupplierEntity entity1 = new LoanSupplierEntity();
        entity1.setSupplierId(1);
        entity1.setSupplierName("Supplier A");
        entity1.setPollingOrder("1,2");
        entity1.setCreditVerifyList(""); // 空字符串

        mockData.add(entity1);

        // 执行方法
        List<LoanSupplierConfigDto> result = loanConfigService.getLoanSupplier();

        // 验证结果
        assertEquals(1, result.size());

        LoanSupplierConfigDto configDto1 = result.get(0);
        assertEquals("Supplier A>Supplier B", configDto1.getPollingOrder());
        assertEquals("", configDto1.getCreditVerifyList()); // 应该为空字符串
    }


    @Test
    public void applicationApprovalSuccess() throws IOException {
        // 准备测试数据
        String resourceId = "resourceId";
        String templateJson = "{\"nameCn\":\"模板中文名\",\"nameEn\":\"templateEn\",\"nodeList\":[]}";
        String putWorkFlowJson = "{\"flowId\":\"flowId\"}";

        ApplicationApprovalParam applicationApprovalParam = new ApplicationApprovalParam();
        applicationApprovalParam.setType(1);
        applicationApprovalParam.setObject(null);
        when(woUtils.getTemplateJsonObject(any(), resourceId)).thenReturn(templateJson);
        when(JsonUtils.parse(templateJson, TemplateDetail.class)).thenReturn(new TemplateDetail());
        when(woUtils.putWorkFlowJsonObject(any(), any(), any(), any(), resourceId))
                .thenReturn(putWorkFlowJson);
        when(JsonUtils.parse(putWorkFlowJson, FlowVo.class)).thenReturn(new FlowVo());

        // 执行方法
        loanConfigService.applicationApproval(applicationApprovalParam);

        // 验证日志记录
        ArgumentCaptor<String> logCaptor = ArgumentCaptor.forClass(String.class);
        assertTrue(logCaptor.getValue().contains("TemplateDetail"));

        // 验证方法调用
        verify(woUtils).getTemplateJsonObject(any(), resourceId);
        verify(woUtils).putWorkFlowJsonObject(any(), any(), any(), any(), resourceId);
    }




    @Test
    public void loanApprovePassWithInvalidApprovalDesc() {
        // 准备测试数据
        LoanApproveParam loanApproveParam = new LoanApproveParam();
        loanApproveParam.setType(LoanConstant.ApproveResultStatus.PASS);
        loanApproveParam.setApprovalDesc(""); // 空的审批意见

        // 模拟依赖
        when(woUtils.getFlowDetailJsonObject(any(), any())).thenReturn("{}");
        when(woUtils.processFlowJsonObject(any(), any(), any(), anyInt(), any())).thenReturn("{}");

        // 执行方法
        loanConfigService.loanApprove(loanApproveParam);
    }

    @Test
    public void loanApproveReject() {
        // 准备测试数据
        LoanApproveParam loanApproveParam = new LoanApproveParam();
        loanApproveParam.setType(LoanConstant.ApproveResultStatus.REJECT);
        loanApproveParam.setApprovalDesc("驳回");
        loanApproveParam.setFlowNo("123");

        // 模拟依赖
        when(woUtils.getFlowDetailJsonObject(any(), any())).thenReturn("{}");
        when(woUtils.processFlowJsonObject(any(), any(), any(), anyInt(), any())).thenReturn("{}");

        // 执行方法
        loanConfigService.loanApprove(loanApproveParam);

        // 验证数据库更新
        verify(loanApproveRecordMapper).update(any(), any());
    }

    @Test
    public void loanApprovePassWithValidApprovalDesc() {
        // 准备测试数据
        LoanApproveParam loanApproveParam = new LoanApproveParam();
        loanApproveParam.setType(LoanConstant.ApproveResultStatus.PASS);
        loanApproveParam.setApprovalDesc("有效审批意见"); // 有效审批意见

        // 模拟依赖
        when(woUtils.getFlowDetailJsonObject(any(), any())).thenReturn("{}");
        when(woUtils.processFlowJsonObject(any(), any(), any(), anyInt(), any())).thenReturn("{}");
        when(woUtils.getFlowDetailJsonObject(any(), any())).thenReturn("{}");

        // 检查 loanApproveParam 对象是否为 null
        assertNotNull(loanApproveParam);

        // 检查 loanConfigService 对象是否为 null
        assertNotNull(loanConfigService);

        // 执行方法
        try {
            loanConfigService.loanApprove(loanApproveParam);
        } catch (Exception e) {
            fail("执行 loanApprove 方法抛出异常");
        }

        // 验证数据库更新
        verify(loanApproveRecordMapper).update(any(), any());
    }


    @Test
    public void loanApproveRevoke() {
        // 准备测试数据
        LoanApproveParam loanApproveParam = new LoanApproveParam();
        loanApproveParam.setType(LoanConstant.ApproveResultStatus.REVOKE);
        loanApproveParam.setFlowNo("123");

        // 确保 woUtils 已初始化
        // 检查对象是否已初始化
        assertNotNull(loanConfigService);
        assertNotNull(woUtils);
        assertNotNull(loanApproveRecordMapper);


        // 模拟依赖
        when(woUtils.getFlowDetailJsonObject(any(), any())).thenReturn("{}");
        when(woUtils.processFlowJsonObject(any(), any(), any(), anyInt(), any())).thenReturn("{}");

        // 执行方法
        loanConfigService.loanApprove(loanApproveParam);

        // 验证数据库更新
        verify(loanApproveRecordMapper).update(any(), any());
    }

    @Test
    public void searchLoanInfoByAccount() {
        SearchLoanInfoByAccountParam param = new SearchLoanInfoByAccountParam();
        param.setAccount("***********");
        UserLoanInfo userLoanInfo = loanConfigService.searchLoanInfoByAccount(param);
        LogUtil.runInfoLog(userLoanInfo.toString());
    }

}
