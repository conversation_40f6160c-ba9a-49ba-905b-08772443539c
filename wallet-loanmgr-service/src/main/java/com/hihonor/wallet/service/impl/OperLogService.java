/*
 * Copyright (c) Honor Terminal Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.service.impl;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hihonor.wallet.common.kafka.KafkaMagicOperLogMessage;
import com.hihonor.wallet.common.kafka.client.KafkaWalletProducer;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperLogEntity;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.WoUtils;

/**
 * 功能描述
 *
 * @since 2023-03-03
 */
@Service
public class OperLogService {

    @Resource
    private WoUtils woUtils;

    @Autowired
    private KafkaWalletProducer kafkaWalletProducer;

    @Value("${spring.kafka.wallet.topic.magicoperLog}")
    private String magicoperLogTopic;

    /**
     * 成功操作记录
     * 
     * @param wiseoperLogEntity 接入wiseoper日志
     */
    public void runSuccessLog(HttpServletRequest request, WiseoperLogEntity wiseoperLogEntity) {
        // 日志插入magicOper系统
        wiseoperLogEntity.setLoggerResponse("SUCCESS");
        KafkaMagicOperLogMessage message = new KafkaMagicOperLogMessage();
        message.setToken(EncryptUtil.encrypt(woUtils.getWoToken(request)));
        BeanUtils.copyProperties(wiseoperLogEntity,message);
        kafkaWalletProducer.sendMessage(magicoperLogTopic, message);
    }

    /**
     * 成功操作记录
     *
     * @param wiseoperLogEntity 接入wiseoper日志
     */
    public void runFailLog(HttpServletRequest request, WiseoperLogEntity wiseoperLogEntity) {
        wiseoperLogEntity.setLoggerResponse("FAIL");
        KafkaMagicOperLogMessage message = new KafkaMagicOperLogMessage();
        message.setToken(EncryptUtil.encrypt(woUtils.getWoToken(request)));
        BeanUtils.copyProperties(wiseoperLogEntity,message);
        kafkaWalletProducer.sendMessage(magicoperLogTopic, message);
    }
}
