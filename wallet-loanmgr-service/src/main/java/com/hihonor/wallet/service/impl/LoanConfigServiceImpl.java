package com.hihonor.wallet.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.amazonaws.services.s3.model.PutObjectResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.hihonor.wallet.common.client.OauthClient;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.feign.loan.LoanFeign;
import com.hihonor.wallet.common.enums.AccountTypeEnum;
import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.model.dto.config.DictItemDto;
import com.hihonor.wallet.common.model.dto.loan.CouponDto;
import com.hihonor.wallet.common.model.dto.loan.LoanRecordDetailDto;
import com.hihonor.wallet.common.model.dto.loan.QueryUserCreditInfoDto;
import com.hihonor.wallet.common.model.dto.loan.RepayPlanTermDto;
import com.hihonor.wallet.common.model.dto.magicOper.ActivitiImageInfo;
import com.hihonor.wallet.common.model.dto.magicOper.FlowDetail;
import com.hihonor.wallet.common.model.dto.magicOper.FlowVo;
import com.hihonor.wallet.common.model.dto.magicOper.TemplateDetail;
import com.hihonor.wallet.common.model.dto.oauth.detailInfo.DetailInfoResp;
import com.hihonor.wallet.common.model.param.loan.*;
import com.hihonor.wallet.common.model.param.loanmgr.SearchLoanInfoByAccountParam;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.*;
import com.hihonor.wallet.dto.*;
import com.hihonor.wallet.dto.DiversionRatioApprovalInfo;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.*;
import com.hihonor.wallet.loan.mapper.*;
import com.hihonor.wallet.obs.FileObsOperate;
import com.hihonor.wallet.param.DictQueryParam;
import com.hihonor.wallet.service.LoanAdActivityConfigService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hihonor.wallet.common.model.dto.magicOper.*;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperRequestContextUtils;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.service.LoanConfigService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
public class LoanConfigServiceImpl implements LoanConfigService {

    private final static String FILESPLIT = ".";

    private final static String REGEX = "[\\s~·`!！@#￥$%^……&*（()）\\-——\\-_=+【\\[\\]】｛{}｝\\|、\\\\；;：:‘'“”\"，,《<。.》>、/？?]";

    private static final Pattern NUMBER_PATTERN = Pattern.compile("^\\d+(\\.\\d{0,2})?$");


    // 定义允许的文件后缀名
    @Value("${allowed_extensions}")
    private String ALLOWED_EXTENSIONS;

    @Value("${loan.query.limit:30}")
    private Integer QUERY_LIMIT;


    @Autowired
    private LoanAdActivityConfigService loanAdActivityConfigService;

    @Autowired
    private LoanSupplierMapper loanSupplierMapper;


    @Autowired
    private LoanApproveRecordMapper loanApproveRecordMapper;


    @Autowired
    private WoUtils woUtils;

    @Autowired
    private HttpServletRequest request;


    @Value("${wo.realname.resourceId}")
    private String realNameResourceId;

    @Value("${wo.diversion.resourceId}")
    private String loanDiversionResourceId;

    @Value("${wo.suggestList.resourceId}")
    private String suggestListResourceId;

    @Autowired
    private FileObsOperate fileObsOperate;

    @Autowired
    LoanFeign loanFeign;

    @Autowired
    RedisUtil redisUtil;

    @Autowired
    private LoanUserMapper loanUserMapper;

    @Autowired
    private CreditApplyMapper creditApplyMapper;

    @Autowired
    private LoanApplyMapper loanApplyMapper;

    @Autowired
    private LoanRepayMapper loanRepayMapper;

    @Autowired
    private CouponNotifyMapper couponNotifyMapper;

    @Resource
    private OauthClient oauthClient;

    @Resource
    private DictItemMapper dictItemMapper;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    private void convertPollingOrder(List<DiversionRatioApprovalInfo> diversionRatioApprovalInfoList) {
        for (DiversionRatioApprovalInfo diversionRatioApprovalInfo : diversionRatioApprovalInfoList) {
            if (StringUtils.isNotBlank(diversionRatioApprovalInfo.getCurrentPollingOrder()) && StringUtils.isBlank(diversionRatioApprovalInfo.getPollingOrder())) {
                diversionRatioApprovalInfo.setPollingOrder(diversionRatioApprovalInfo.getCurrentPollingOrder());
            }
        }
    }

    @Override
    public ApprovalResponse getApprovalInfo(ApprovalParam param) {
        ApprovalResponse approvalResponse = new ApprovalResponse();
        String flowNo = param.getFlowNo();
        // 审批信息
        LambdaQueryWrapper<LoanApproveRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoanApproveRecordEntity::getFlowNo, flowNo);
        LoanApproveRecordEntity loanApproveRecordEntity = loanApproveRecordMapper.selectOne(wrapper);

        if (Objects.isNull(loanApproveRecordEntity)) {
            throw new BusinessException(WalletResultCode.FLOWNO_NOTEXIST);
        }
        // magicoper:获取当前登录用户信息
        String operAccount = WiseoperRequestContextUtils.getOperAccount();
        if (StringUtils.isBlank(operAccount)) {
            throw new BusinessException(WalletResultCode.WISOPER_TOKEN_AUTH_FAIL);
        }
        UserInfoDto userInfoDto = new UserInfoDto();
        userInfoDto.setAccount(operAccount);
        String userInfoJson = woUtils.getCurrentUserInfoJsonObject(request, userInfoDto);
        LogUtil.runSensitiveInfoLog("magicoper获取当前登录用户信息:{}", userInfoJson);
        UserInfoVo userInfoVo = JsonUtils.parse(userInfoJson, UserInfoVo.class);

        // magicoper: 根据流程编号查找电子流详情
        String flowInfo = woUtils.getFlowDetailJsonObject(request, flowNo);
        LogUtil.runSensitiveInfoLog("magicoper电子流详情:{}", flowInfo);
        FlowDetail flowDetail = JsonUtils.parse(flowInfo, FlowDetail.class);
        if (Objects.isNull(flowDetail)) {
            LogUtil.runErrorLog("magicoper电子流异常，请联系运营人员处理!");
            throw new BusinessException(WalletResultCode.MAGICOPER_RESULT_EXCEPTION);
        }
        List<ActivitiImageInfo> nodeList = flowDetail.getNodeList();
        if (CollectionUtils.isEmpty(nodeList)) {
            LogUtil.runErrorLog("magicoper电子流nodeList为空，请联系运营人员处理!");
            throw new BusinessException(WalletResultCode.MAGICOPER_RESULT_EXCEPTION);
        }
        List<ActivitiImageInfo> tempList = new ArrayList<>(nodeList);
        tempList.remove(0);
        tempList.remove(tempList.size() - 1);
        if (CollectionUtils.isEmpty(tempList)) {
            LogUtil.runErrorLog("magicoper电子流-审批节点为空，请联系运营人员处理!");
            throw new BusinessException(WalletResultCode.MAGICOPER_RESULT_EXCEPTION);
        }
        List<String> auditors = tempList.stream()
                .flatMap(info -> info.getSelectUsers().stream())
                .map(AuditUser::getAccount)
                .collect(Collectors.toList());
        if (!auditors.contains(userInfoVo.getAccount()) && !flowDetail.getSubmitter().equals(userInfoVo.getUserId())) {
            LogUtil.runErrorLog("流程异常，请联系运营人员处理!");
            throw new BusinessException(WalletResultCode.APPROVAL_PROCESS_EXCEPTION);
        }

        approvalResponse.setFlowDetail(flowDetail);

        approvalResponse.setApprovalMaterial(fileObsOperate.generatePresignedUrl(loanApproveRecordEntity.getApprovalMaterialUrl()));
        // 审批内容
        String approveContent = loanApproveRecordEntity.getApproveContent();
        // 审批类型判断 (1-分流比例，2-实名运营页，3-H5产品顺序)
        if (loanApproveRecordEntity.getType() == LoanConstant.ApproveType.adActivityRealName) {
            LoanAdActivityApproveContent loanAdActivityApproveContent = JsonUtils.parse(approveContent, LoanAdActivityApproveContent.class);
            approvalResponse.setApprovalInfo(loanAdActivityApproveContent);
            if (Objects.isNull(loanAdActivityApproveContent.getId())) {
                approvalResponse.setType(3);
            } else {
                approvalResponse.setType(2);
            }
        } else if (loanApproveRecordEntity.getType() == LoanConstant.ApproveType.diversionRatio) {
            approvalResponse.setType(1);
            ObjectMapper objectMapper = new ObjectMapper();
            List<DiversionRatioApprovalInfo> diversionRatioApprovalRaws = new ArrayList<>();
            try {
                diversionRatioApprovalRaws = objectMapper.readValue(approveContent, objectMapper.getTypeFactory().constructCollectionType(List.class, DiversionRatioApprovalInfo.class));
                convertPollingOrder(diversionRatioApprovalRaws);
            } catch (JsonProcessingException e) {
                LogUtil.runErrorLog("转换异常：", e.getMessage());
            }
            approvalResponse.setApprovalInfo(diversionRatioApprovalRaws);
            approvalResponse.setManualDiversionRatio(loanApproveRecordEntity.getManualDiversionRatio());
            approvalResponse.setModelDiversionRatio(loanApproveRecordEntity.getModelDiversionRatio());
            approvalResponse.setBeforeModelDiversionRatio(loanApproveRecordEntity.getBeforeModelDiversionRatio());
            approvalResponse.setBeforeManualDiversionRatio(loanApproveRecordEntity.getBeforeManualDiversionRatio());

        } else if (loanApproveRecordEntity.getType() == LoanConstant.ApproveType.suggestList) {
            approvalResponse.setType(4);
            ObjectMapper objectMapper = new ObjectMapper();
            List<SuggestInfo> suggestInfos = new ArrayList<>();
            try {
                suggestInfos = objectMapper.readValue(approveContent, objectMapper.getTypeFactory().constructCollectionType(List.class, SuggestInfo.class));
            } catch (JsonProcessingException e) {
                LogUtil.runErrorLog("转换异常：", e.getMessage());
            }
            approvalResponse.setApprovalInfo(suggestInfos);
        }
        return approvalResponse;
    }

    @Override
    public UserInfoVo getWoCurrentUser() {
        try {
            String userInfoJsonObject = woUtils.getUserInfoJsonObject(request);
            return JsonUtils.parse(userInfoJsonObject, UserInfoVo.class);
        } catch (Exception e) {
            LogUtil.runErrorLog("获取magicoper当前用户信息异常，请联系运营人员!");
            throw new BusinessException(WalletResultCode.MAGICOPER_RESULT_EXCEPTION, e);
        }
    }

    private String cleanString(String inputString) {

        String cleanedString = inputString.replaceAll("(?<=^|>)>(?=|$)", "").replaceAll(">{2,}", ">");

        return cleanedString;
    }

    /**
     * cp分流配置按钮是否可用
     *
     * @return
     */
    private Boolean isDiversionConfigButtonAbled() {
        LambdaQueryWrapper<LoanApproveRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoanApproveRecordEntity::getType, 1)
                // 审批状态 (0-提交，1-驳回，2-审批中，3-审批通过，4-撤回)
                .eq(LoanApproveRecordEntity::getApproveStatus, 2);
        Long count = loanApproveRecordMapper.selectCount(wrapper);
        if (count == 0) {
            return true;
        } else {
            return false;
        }

    }

    private Boolean isSuggestListConfigButtonAbled() {
        LambdaQueryWrapper<LoanApproveRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoanApproveRecordEntity::getType, LoanConstant.ApproveType.suggestList)
                // 审批状态 (0-提交，1-驳回，2-审批中，3-审批通过，4-撤回)
                .eq(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.APPROVING);
        Long count = loanApproveRecordMapper.selectCount(wrapper);
        if (count == 0) {
            return true;
        } else {
            return false;
        }
    }


    @Override
    public DiversionRatioApprovalHistoryRecords getDiversionRatioApprovalHistoryRecords() {
        // 修改按钮置灰
        Boolean diversionConfigButtonAbled = isDiversionConfigButtonAbled();
        DiversionRatioApprovalHistoryRecords res = new DiversionRatioApprovalHistoryRecords();
        res.setModifyFlag(diversionConfigButtonAbled);
        // 查询cp分流历史记录
        LambdaQueryWrapper<LoanApproveRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoanApproveRecordEntity::getType, LoanConstant.ApproveType.diversionRatio)
                .eq(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.PASS)
                .isNotNull(LoanApproveRecordEntity::getStartTime);
        List<LoanApproveRecordEntity> list = loanApproveRecordMapper.selectList(wrapper);

        // 获取所有供应商id和name的map
        List<LoanSupplierEntity> allSupplierList = getAllSupplierList();
        Map<Integer, String> allSupplierIdNameMap = allSupplierList.stream().collect(Collectors.toMap(LoanSupplierEntity::getSupplierId, LoanSupplierEntity::getSupplierName));

        List<DiversionRatioApprovelHistoryRecord> diversionRatioApprovelHistoryRecords = new ArrayList<>();

        for (LoanApproveRecordEntity loanApproveRecordEntity : list) {
            // 获取该条记录修改依据 配置人 生效时间 失效时间
            DiversionRatioApprovelHistoryRecord record = new DiversionRatioApprovelHistoryRecord();
            BeanUtils.copyProperties(loanApproveRecordEntity, record);
            record.setRecordId(loanApproveRecordEntity.getId());
            record.setEffectiveDate(loanApproveRecordEntity.getStartTime());
            record.setExpirationDate(loanApproveRecordEntity.getEndTime());
            record.setApprovalMaterialUrl(fileObsOperate.generatePresignedUrl(loanApproveRecordEntity.getApprovalMaterialUrl()));
            // 获取分流比例和轮询配置
            String approveContent = loanApproveRecordEntity.getApproveContent();

            ObjectMapper objectMapper = new ObjectMapper();
            List<DiversionRatioApprovalInfo> diversionRatioApprovalInfoList = new ArrayList<>();
            try {
                diversionRatioApprovalInfoList = objectMapper.readValue(approveContent, objectMapper.getTypeFactory().constructCollectionType(List.class, DiversionRatioApprovalInfo.class));
                convertPollingOrder(diversionRatioApprovalInfoList);
            } catch (JsonProcessingException e) {
                LogUtil.runErrorLog("approveContent JsonProcessingException");
                throw new BusinessException(WalletResultCode.PARSE_EXCEPTION, e);
            }

            if (!CollectionUtils.isEmpty(diversionRatioApprovalInfoList)) {
                for (DiversionRatioApprovalInfo info : diversionRatioApprovalInfoList) {
                    info.setSupplierName(allSupplierIdNameMap.get(info.getSupplierId()));
                }
            }

            record.setDiversionRatioApprovalInfos(diversionRatioApprovalInfoList);
            record.setManualDiversionRatio(loanApproveRecordEntity.getManualDiversionRatio());
            record.setModelDiversionRatio(loanApproveRecordEntity.getModelDiversionRatio());
            diversionRatioApprovelHistoryRecords.add(record);
        }

        diversionRatioApprovelHistoryRecords = diversionRatioApprovelHistoryRecords.stream()
                .sorted(Comparator.comparing(DiversionRatioApprovelHistoryRecord::getEffectiveDate).reversed())
                .collect(Collectors.toList());

        res.setList(diversionRatioApprovelHistoryRecords);

        return res;
    }

    @Override
    public SuggestListApprovalHistoryRecords getSuggestListApprovalHistoryRecords() {
        // 修改按钮置灰
        Boolean suggestListConfigButtonAbled = isSuggestListConfigButtonAbled();
        SuggestListApprovalHistoryRecords res = new SuggestListApprovalHistoryRecords();
        res.setModifyFlag(suggestListConfigButtonAbled);
        // 查询H5产品排序历史记录
        LambdaQueryWrapper<LoanApproveRecordEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoanApproveRecordEntity::getType, LoanConstant.ApproveType.suggestList)
                .eq(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.PASS)
                .isNotNull(LoanApproveRecordEntity::getStartTime);
        List<LoanApproveRecordEntity> list = loanApproveRecordMapper.selectList(wrapper);

        List<SuggestListApprovalHistoryRecord> suggestListApprovalHistoryRecords = new ArrayList<>();

        for (LoanApproveRecordEntity loanApproveRecordEntity : list) {
            // 获取该条记录修改依据 配置人 生效时间 失效时间
            SuggestListApprovalHistoryRecord record = new SuggestListApprovalHistoryRecord();
            BeanUtils.copyProperties(loanApproveRecordEntity, record);
            record.setRecordId(loanApproveRecordEntity.getId());
            record.setEffectiveDate(loanApproveRecordEntity.getStartTime());
            record.setExpirationDate(loanApproveRecordEntity.getEndTime());
            record.setApprovalMaterialUrl(fileObsOperate.generatePresignedUrl(loanApproveRecordEntity.getApprovalMaterialUrl()));

            // 获取H5产品排序配置
            String approveContent = loanApproveRecordEntity.getApproveContent();

            ObjectMapper objectMapper = new ObjectMapper();
            List<SuggestInfo> suggestInfos = new ArrayList<>();
            try {
                suggestInfos = objectMapper.readValue(approveContent, objectMapper.getTypeFactory().constructCollectionType(List.class, SuggestInfo.class));
            } catch (JsonProcessingException e) {
                LogUtil.runErrorLog("approveContent JsonProcessingException");
                throw new BusinessException(WalletResultCode.PARSE_EXCEPTION, e);
            }

            record.setSuggestInfos(suggestInfos);

            suggestListApprovalHistoryRecords.add(record);
        }

        suggestListApprovalHistoryRecords = suggestListApprovalHistoryRecords.stream()
                .sorted(Comparator.comparing(SuggestListApprovalHistoryRecord::getEffectiveDate).reversed())
                .collect(Collectors.toList());

        res.setList(suggestListApprovalHistoryRecords);

        return res;
    }

    @Override
    public List<SuggestInfo> getSuggestInfoList() {
        List<LoanSupplierEntity> loanSupplierEntityList = this.getSuggestSupplierList();
        List<SuggestInfo> suggestInfoList = new ArrayList<>();
        for (LoanSupplierEntity loanSupplier : loanSupplierEntityList) {
            SuggestInfo suggestInfo = new SuggestInfo();
            suggestInfo.setSupplierName(loanSupplier.getSupplierName());
            suggestInfo.setSortIndex(loanSupplier.getSortIndex());
            suggestInfoList.add(suggestInfo);
        }
        return suggestInfoList;
    }

    @Override
    public UserLoanInfo searchLoanInfoByAccount(SearchLoanInfoByAccountParam param) {
        String operAccount = WiseoperRequestContextUtils.getOperAccount();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String currentDate = LocalDate.now().format(formatter);
        String redisKey = LoanConstant.LOAN_QUERY_LIMIT_REDIS_KEY + DigestUtils.sha256Hex(operAccount) + ":" + currentDate;
        Long count = getDailyOperatorAccessCount(redisKey);
        if (count != null && count > QUERY_LIMIT) {
            throw new BusinessException(WalletResultCode.LOAN_QUERY_LIMIT_EXCEEDED);
        }

        String userId;
        String account;
        if (param.getAccount().length() == 11) {
            try {
                userId = oauthClient.getUserIdByAccount(AccountTypeEnum.Phone.getValue(), param.getAccount());
                account = param.getAccount();
            } catch (Exception e) {
                throw new BusinessException(WalletResultCode.USER_ACCOUNT_ERROR);
            }
        } else {
            userId = param.getAccount();
            try {
                DetailInfoResp detailInfoResp =
                        oauthClient.getUserDetailInfoByUserId(userId);
                LogUtil.runInfoLog("获取到的账号数据为:{}", detailInfoResp.toString());

                if (Objects.isNull(detailInfoResp.getUserAccount())) {
                    throw new BusinessException(WalletResultCode.USER_ACCOUNT_ERROR);
                }
                account = detailInfoResp.getUserAccount();
            } catch (Exception e) {
                throw new BusinessException(WalletResultCode.USER_ACCOUNT_ERROR);
            }

        }
        UserLoanInfo userLoanInfo = new UserLoanInfo();
        userLoanInfo.setUserId(userId);
        // 获取最近的准入记录
        LoanUserEntity loanUserEntity = getRecentLoanUserEntity(userId);
        userLoanInfo.setCreditLimitStatus(5);
        if (loanUserEntity == null) {
            return userLoanInfo;
        }
        userLoanInfo.setIsMobileNoMatched(account.equals(EncryptUtil.decrypt(loanUserEntity.getMobileNo())));
        userLoanInfo.setAccessTime(loanUserEntity.getCreateTime());
        userLoanInfo.setAccessResult(loanUserEntity.getAccessResult());
        //获取所有的渠道配置
        List<LoanSupplierEntity> loanSupplierList = loanSupplierMapper.selectList(null);
        //将渠道配置转为map，以渠道id为key
        Map<Integer, LoanSupplierEntity> loanSupplierMap = loanSupplierList.stream().collect(Collectors.toMap(LoanSupplierEntity::getSupplierId, item -> item));
        userLoanInfo.setAccessSupplierName(loanSupplierMap.get(loanUserEntity.getSupplier()).getSupplierName());
        try {
            ResponseResult<QueryUserCreditInfoDto> result = loanFeign.getCreditInfo(Long.parseLong(userId));
            QueryUserCreditInfoDto creditInfo = result.getData();
            LogUtil.runInfoLog("获取到的用户额度信息:{}", creditInfo);
            if (Objects.nonNull(creditInfo) && Objects.nonNull(creditInfo.getStatus())) {
                userLoanInfo.setCreditLimitStatus(creditInfo.getStatus());
                if (Objects.nonNull(creditInfo.getStatus())) {
                    userLoanInfo.setCurrentSupplierName(loanSupplierMap.get(loanUserEntity.getSupplier()).getSupplierName());
                }
            } else {
                userLoanInfo.setCreditLimitStatus(5);
            }
        } catch (Exception e) {
            userLoanInfo.setCreditLimitStatus(5);
        }
        // 查询近半年授信记录
        List<CreditApplyEntity> creditApplyEntities = getRecentCreditApplications(userId);
        List<creditRecordDto> creditRecords = creditApplyEntities.stream()
                .map(entity -> {
                    creditRecordDto record = new creditRecordDto();
                    record.setOrderNo(entity.getOutOrderNo());
                    record.setCreditTime(entity.getCreateTime());
                    record.setCreditStatus(entity.getApplyStatus());
                    record.setRefuseMsg(entity.getRefuseMsg());
                    record.setCreditSupplierName(loanSupplierMap.get(entity.getSupplier()).getSupplierName());
                    return record;
                })
                .collect(Collectors.toList());
        userLoanInfo.setCreditRecords(creditRecords);
        // 查询近半年借款记录
        List<LoanApplyEntity> loanApplyEntities = getRecentLoanRecords(userId);
        List<LoanRecordDto> loanRecords = new ArrayList<>();
        for (LoanApplyEntity loanApplyEntity : loanApplyEntities) {
            LoanRecordDto loanRecordDto = new LoanRecordDto();
            loanRecordDto.setOrderNo(loanApplyEntity.getOutOrderNo());
            loanRecordDto.setApplyTime(loanApplyEntity.getCreateTime());
            loanRecordDto.setApplyStatus(loanApplyEntity.getApplyStatus());
            loanRecordDto.setRefuseMsg(loanApplyEntity.getRefuseMsg());
            LoanApplyEncryptInfo loanApplyEncryptInfo = JsonUtils.parse(EncryptUtil.decrypt(loanApplyEntity.getLoanInfo()), LoanApplyEncryptInfo.class);
            Map<String, String> repayMethod = this.getLoanHomeRepayMethod(loanUserEntity.getSupplier());
            if (Objects.nonNull(loanApplyEncryptInfo.getRepayMethod())) {
                loanRecordDto.setRepayMethod(repayMethod.get(Integer.toString(loanApplyEncryptInfo.getRepayMethod())));

                loanRecordDto.setLoanMethod(loanApplyEncryptInfo.getRepayMethod() == 4 ? "按期还" : "灵活还");
            }
            if (loanApplyEntity.getApplyStatus() == 2) {
                try {
                    ResponseResult<LoanRecordDetailDto> result = loanFeign.getLoanRecordDetail(Long.parseLong(userId), loanApplyEntity.getApplyNo(), loanApplyEntity.getOutOrderNo());
                    LoanRecordDetailDto dto = result.getData();
                    LogUtil.runInfoLog("获取到的借款详情记录为:{}", dto);
                    if (Objects.nonNull(dto)) {
                        if (Objects.isNull(loanApplyEncryptInfo.getRepayMethod()) && Objects.nonNull(dto.getRepayMethod())) {
                            loanRecordDto.setRepayMethod(repayMethod.get(Integer.toString(dto.getRepayMethod())));

                            loanRecordDto.setLoanMethod(dto.getRepayMethod() == 4 ? "按期还" : "灵活还");
                        }
                        loanRecordDto.setLatestShouldRepayDate(findLatestShouldRepayDate(dto.getRepayPlanTerms()));
                        loanRecordDto.setLatestRepayedTermDate(findLatestRepayedTermDate(dto.getRepayPlanTerms()));
                    }
                } catch (Exception e) {
                    LogUtil.runInfoLog("获取借款记录详情失败");
                }
            }
            loanRecords.add(loanRecordDto);
        }
        userLoanInfo.setLoanRecords(loanRecords);
        // 近半年的还款失败记录
        List<LoanRepayEntity> loanRepayEntities = getRecentRepayFailRecords(userId);
        List<repayFailDto> repayFailRecords = loanRepayEntities.stream()
                .map(entity -> {
                    repayFailDto dto = new repayFailDto();
                    dto.setOutLoanNo(entity.getOutLoanNo());
                    dto.setRepayTime(entity.getCreateTime());
                    LoanRepayEncryptInfo loanRepayEncryptInfo = JsonUtils.parse(EncryptUtil.decrypt(entity.getAmountInfo()), LoanRepayEncryptInfo.class);
                    dto.setRepayAmount(loanRepayEncryptInfo.getRepayAmount());
                    if (Objects.isNull(entity.getRepayOriginType())) {
                        dto.setRepaySource(entity.getRepaySource().equals("OTHER") ? 1 : 0);
                    } else {
                        dto.setRepaySource(entity.getRepayOriginType() == 1 ? 1 : 0);
                    }
                    dto.setRefuseReason(entity.getRepayResult());
                    return dto;
                })
                .collect(Collectors.toList());
        userLoanInfo.setRepayFailRecords(repayFailRecords);
        // 近半年持有的利息券
        List<CouponNotifyEntity> couponNotifyEntities = getRecentCouponRecords(userId);
        List<CouponDto> couponDtoList;
        try {
            ResponseResult<List<CouponDto>> result = loanFeign.couponsByUserId(Long.parseLong(userId));
            couponDtoList = result.getData();
            LogUtil.runInfoLog("获取到的优惠券列表为:{}", couponDtoList);
        } catch (Exception e) {
            LogUtil.runInfoLog("获取优惠券列表失败");
            couponDtoList = new ArrayList<>();
        }
        List<CouponDto> finalCouponDtoList = couponDtoList;
        List<couponDto> couponNotifyRecords = couponNotifyEntities.stream()
                .map(entity -> {
                            couponDto dto = new couponDto();
                            dto.setCouponNo(entity.getCouponNo());
                            dto.setCouponName(entity.getCouponName());
                            dto.setCouponStatus(entity.getCouponStatus());
                            dto.setCreateTime(entity.getCreateTime());
                            dto.setUseTime(Objects.nonNull(entity.getUseTime()) ? new Date(entity.getUseTime()) : null);
                            dto.setEndTime(Objects.nonNull(entity.getEndTime()) ? new Date(entity.getEndTime()) : null);
                            CouponDto couponDto = finalCouponDtoList.stream()
                                    .filter(coupon -> coupon.getCouponNo().equals(entity.getCouponNo())) // 过滤条件
                                    .findFirst() // 找到符合条件的第一个元素
                                    .orElse(null); // 如果没有找到，返回 null
                            if (couponDto != null) {
                                dto.setUseRules(couponDto.getUseRules());
                                if (StringUtils.isBlank(dto.getCouponName())) {
                                    dto.setCouponName(couponDto.getCouponName());
                                }
                                dto.setCouponStatus(couponDto.getStatus());
                                if (Objects.isNull(dto.getUseTime())) {
                                    dto.setUseTime(Objects.nonNull(couponDto.getUsedTime()) ? new Date(couponDto.getUsedTime()) : null);
                                }
                                if (Objects.isNull(dto.getEndTime())) {
                                    dto.setEndTime(Objects.nonNull(couponDto.getEndTime()) ? new Date(couponDto.getEndTime()) : null);
                                }
                            }
                            return dto;
                        }
                )
                .collect(Collectors.toList());
        userLoanInfo.setCouponNotifyRecords(couponNotifyRecords);

        return userLoanInfo;
    }


    @Override
    public List<LoanSupplierConfigDto> getLoanSupplier() {
        List<LoanSupplierEntity> data = this.getApiSupplierList();
        Map<String, String> supplierIdNameMap = data.stream().collect(Collectors.toMap(loanSupplierDto -> String.valueOf(loanSupplierDto.getSupplierId()), LoanSupplierEntity::getSupplierName));
        List<LoanSupplierConfigDto> loanSupplierConfigDtos = BeanUtilCopy.copyListProperties(data, LoanSupplierConfigDto::new, (loanSupplierDto, loanSupplierConfigDto) -> {
            String pollingOrder = loanSupplierDto.getPollingOrder();
            StringBuffer str = new StringBuffer();
            if (StringUtils.isNotBlank(pollingOrder)) {
                String[] supplierIdArray = pollingOrder.split(",");
                for (String supplierId : supplierIdArray) {
                    String supplierName = supplierIdNameMap.get(supplierId);
                    str.append(supplierName == null ? "" : supplierName).append(">");
                }
            }
            pollingOrder = str.toString();
            if (pollingOrder.endsWith(">")) {
                pollingOrder = pollingOrder.substring(0, pollingOrder.length() - 1);
            }
            loanSupplierConfigDto.setPollingOrder(pollingOrder);
            String creditVerifyList = loanSupplierConfigDto.getCreditVerifyList();
            if (StringUtils.isNotBlank(creditVerifyList)) {
                String[] creditVerifyArray = creditVerifyList.split(",");
                for (String creditVerify : creditVerifyArray) {
                    creditVerifyList = creditVerifyList.replaceAll(creditVerify, LoanConstant.CREDIT_VERIFY_MAP.get(creditVerify));
                }
                creditVerifyList = creditVerifyList.replaceAll(",", "/");
            }
            loanSupplierConfigDto.setCreditVerifyList(creditVerifyList);
        });
        List<DictItemEntity> dictItemEntityList = dictItemMapper.selectList(new LambdaQueryWrapper<DictItemEntity>()
                .eq(DictItemEntity::getParent, LoanConstant.DiversionType.PARENT_DIVERSION_TYPE));
        if (!CollectionUtils.isEmpty(loanSupplierConfigDtos)){
            for (DictItemEntity entity : dictItemEntityList){
                if (LoanConstant.DiversionType.MODEL_DIVERSION_RATIO.equals(entity.getName())){
                    loanSupplierConfigDtos.get(0).setModelDiversionRatio(Double.valueOf(entity.getValue()));
                }
                if (LoanConstant.DiversionType.MANUAL_DIVERSION_RATIO.equals(entity.getName())){
                    loanSupplierConfigDtos.get(0).setManualDiversionRatio(Double.valueOf(entity.getValue()));
                }
            }
        }
        return loanSupplierConfigDtos;
    }

    private List<LoanSupplierEntity> getApiSupplierList() {
        return loanSupplierMapper.selectList(new LambdaQueryWrapper<LoanSupplierEntity>()
                .eq(LoanSupplierEntity::getSupportApi, LoanConstant.SupplierSupportApi.SUPPORT)
                .orderByAsc(LoanSupplierEntity::getSortIndex));
    }

    private List<LoanSupplierEntity> getSuggestSupplierList() {
        return loanSupplierMapper.selectList(new LambdaQueryWrapper<LoanSupplierEntity>()
                .eq(LoanSupplierEntity::getSuggest, LoanConstant.SupplierIsSuggest.Suggest)
                .orderByAsc(LoanSupplierEntity::getSupplierId));
    }

    private List<LoanSupplierEntity> getAllSupplierList() {
        return loanSupplierMapper.selectList(new LambdaQueryWrapper<LoanSupplierEntity>()
                .orderByAsc(LoanSupplierEntity::getSortIndex));
    }


    @Override
    public void applicationApproval(ApplicationApprovalParam applicationApprovalParam) {
        try {
            checkApprovalStatus(applicationApprovalParam);
            checkApprovalContent(applicationApprovalParam);
            String resourceId = getResourceId(applicationApprovalParam);
            String template = woUtils.getTemplateJsonObject(request, resourceId);
            TemplateDetail templateDetail = JsonUtils.parse(template, TemplateDetail.class);
            LogUtil.runInfoLog("模版信息:{}", templateDetail.toString());
            String putWorkFlow = woUtils.putWorkFlowJsonObject(request, templateDetail.getNameCn(),
                    templateDetail.getNameEn(), templateDetail.getNodeList(), resourceId);
            FlowVo flowVo = JsonUtils.parse(putWorkFlow, FlowVo.class);
            LoanApproveRecordParam param = new LoanApproveRecordParam();
            saveLoanApproveRecord(param, flowVo, templateDetail, applicationApprovalParam);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static boolean isValidNumber(String input) {
        return NUMBER_PATTERN.matcher(input).matches();
    }

    // 检查文件扩展名是否允许
    private boolean isAllowedExtension(String fileName) {
        String[] extensions = ALLOWED_EXTENSIONS.split(",");
        for (String ext : extensions) {
            if (fileName.toLowerCase().endsWith(ext)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验传参规范
     *
     * @param applicationApprovalParam
     */
    private void checkApprovalContent(ApplicationApprovalParam applicationApprovalParam) {
        if (Objects.nonNull(applicationApprovalParam.getApprovalMaterial())) {
            String originalFilename = applicationApprovalParam.getApprovalMaterial().getOriginalFilename();
            // 校验文件后缀名
            if (StringUtils.isBlank(originalFilename) || !isAllowedExtension(originalFilename)) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "只允许上传" + ALLOWED_EXTENSIONS + "格式的文件");
            }
        }

        if (applicationApprovalParam.getType() == LoanConstant.ApproveType.diversionRatio) {
            List<DiversionRatioApprovalInfo> diversionRatioApprovalInfos = JsonUtils.parseArray(applicationApprovalParam.getObject().toString(), DiversionRatioApprovalInfo.class);
            for (DiversionRatioApprovalInfo info : diversionRatioApprovalInfos) {
                if (Objects.isNull(info.getSupplierId()) || StringUtils.isBlank(info.getDiversionRatio()) || StringUtils.isBlank(info.getPollingOrder()) || applicationApprovalParam.getManualDiversionRatio() == null || applicationApprovalParam.getModelDiversionRatio() == null) {
                    throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "必传参数为空");
                }
                // 校验分流比率
                boolean isNumber = isValidNumber(info.getDiversionRatio());
                if (!isNumber) {
                    throw new BusinessException(WalletResultCode.INVALID_NUMBER_FORMAT);
                }
            }
            double sum = diversionRatioApprovalInfos.stream().mapToDouble(info -> Double.parseDouble(info.getDiversionRatio())).sum();
            if (sum > 100) {
                throw new BusinessException("分流比例总和超过 100%: " + sum + "%");
            }
            double diversionRatioSum = applicationApprovalParam.getModelDiversionRatio().doubleValue() + applicationApprovalParam.getManualDiversionRatio().doubleValue();
            if (diversionRatioSum > 100){
                throw new BusinessException("模型分流比例 + 手工分流比例 总和超过 100%: " + sum + "%");
            }
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.adActivityRealName) {
            LoanAdActivityApproveContent loanAdActivityApproveContent = JsonUtils.parse(applicationApprovalParam.getObject().toString(), LoanAdActivityApproveContent.class);
            if (Objects.isNull(loanAdActivityApproveContent.getEndTime()) || StringUtils.isBlank(loanAdActivityApproveContent.getBackPicUrl())
                    || StringUtils.isBlank(loanAdActivityApproveContent.getButtonImageUrl()) || StringUtils.isBlank(loanAdActivityApproveContent.getLoginPromptText())
                    || StringUtils.isBlank(loanAdActivityApproveContent.getLoginButtonImageUrl()) || StringUtils.isBlank(loanAdActivityApproveContent.getLoginBackPicUrl())
                    || StringUtils.isBlank(loanAdActivityApproveContent.getLoginPromptText())) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "必传参数为空");
            }

            if (Objects.nonNull(loanAdActivityApproveContent.getCid()) && loanAdActivityApproveContent.getCid().length() > 20) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "cid长度超限");
            }

            if (Objects.nonNull(loanAdActivityApproveContent.getSubCid()) && loanAdActivityApproveContent.getSubCid().length() > 20) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "subCid长度超限");
            }

            if (loanAdActivityApproveContent.getLoginPromptText().length() > 50) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "未登录状态文案提示长度超限");
            }
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.suggestList) {
            List<SuggestInfo> suggestInfos = JsonUtils.parseArray(applicationApprovalParam.getObject().toString(), SuggestInfo.class);
            this.insertBeforeSortIndex(suggestInfos);
            Set<Integer> sortIndexSet = suggestInfos.stream().map(SuggestInfo::getSortIndex).collect(Collectors.toSet());
            if (sortIndexSet.size() != suggestInfos.size()) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "严选推荐产品的排序值不可重复");
            }
            if (Collections.min(sortIndexSet) != 1 || Collections.max(sortIndexSet) != sortIndexSet.size()) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, String.format("严选推荐产品的排序值应该为1到%d的连续值", suggestInfos.size()));
            }
            boolean sortIndexChangeStatus = suggestInfos.stream()
                    .anyMatch(suggestInfo -> !suggestInfo.getSortIndex().equals(suggestInfo.getBeforeSortIndex()));
            if (!sortIndexChangeStatus) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "严选推荐产品的新排序不能与原排序相同");
            }
        }
    }

    /**
     * 校验审批状态
     *
     * @param applicationApprovalParam
     */
    private void checkApprovalStatus(ApplicationApprovalParam applicationApprovalParam) {
        if (applicationApprovalParam.getType() == LoanConstant.ApproveType.diversionRatio || applicationApprovalParam.getType() == LoanConstant.ApproveType.suggestList) {
            LambdaQueryWrapper<LoanApproveRecordEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(LoanApproveRecordEntity::getType, applicationApprovalParam.getType()).
                    in(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.SUBMIT, LoanConstant.ApproveStatus.APPROVING);
            Boolean isExist = loanApproveRecordMapper.exists(lambdaQueryWrapper);
            if (isExist) {
                throw new BusinessException(WalletResultCode.APPROVAL_SUBMIT_EXCEPTION);
            }
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.adActivityRealName) {
            LoanAdActivityParam curApproveContent = JsonUtils.parse(applicationApprovalParam.getObject().toString(), LoanAdActivityParam.class);
            if (Objects.nonNull(curApproveContent.getId())) {
                LambdaQueryWrapper<LoanApproveRecordEntity> queryWrapperLoanApproveRecord = new LambdaQueryWrapper<>();
                queryWrapperLoanApproveRecord.eq(LoanApproveRecordEntity::getType, LoanConstant.ApproveType.adActivityRealName).
                        in(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.SUBMIT, LoanConstant.ApproveStatus.APPROVING);
                List<LoanApproveRecordEntity> loanApproveEntities = loanApproveRecordMapper.selectList(queryWrapperLoanApproveRecord);

                for (LoanApproveRecordEntity loanApproveRecord : loanApproveEntities) {
                    LoanAdActivityParam approveContent = JsonUtils.parse(loanApproveRecord.getApproveContent(), LoanAdActivityParam.class);
                    if (curApproveContent.getId().equals(approveContent.getId())) {
                        throw new BusinessException(WalletResultCode.APPROVAL_SUBMIT_EXCEPTION);
                    }
                }
            }
        }

        if ((applicationApprovalParam.getType() == LoanConstant.ApproveType.diversionRatio || applicationApprovalParam.getType() == LoanConstant.ApproveType.suggestList) && Objects.isNull(applicationApprovalParam.getApprovalMaterial())) {
            throw new BusinessException(WalletResultCode.MATERIAL_EXCEPTION);
        }
    }

    private String getResourceId(ApplicationApprovalParam applicationApprovalParam) {
        if (applicationApprovalParam.getType() == LoanConstant.ApproveType.diversionRatio) {
            return loanDiversionResourceId;
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.adActivityRealName) {
            return realNameResourceId;
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.suggestList) {
            return suggestListResourceId;
        }
        return null;
    }

    @Override
    public void loanApprove(LoanApproveParam loanApproveParam) {
        if (
                loanApproveParam.getType() == LoanConstant.ApproveResultStatus.PASS &&
                        (
                                StringUtils.isBlank(loanApproveParam.getApprovalDesc()) ||
                                        loanApproveParam.getApprovalDesc().length() > 50
                        )
        ) {
            LogUtil.runInfoLog("审批意见不能为空且不能超过50字:{}", loanApproveParam.getApprovalDesc());
        }
        String operAccount = WiseoperRequestContextUtils.getOperAccount();
        long currentTimeMillis = System.currentTimeMillis();
        Date currentDate = new Date(currentTimeMillis);
        String flow = woUtils.getFlowDetailJsonObject(request, loanApproveParam.getFlowNo());
        FlowDetail flowDetail = JsonUtils.parse(flow, FlowDetail.class);
        LogUtil.runInfoLog("审批前电子流具体信息:{}", flowDetail.toString());
        String processFlow = woUtils.processFlowJsonObject(request, loanApproveParam.getFlowNo(),
                flowDetail.getStatus(), loanApproveParam.getType(), loanApproveParam.getApprovalDesc());
        FlowVo flowVo = JsonUtils.parse(processFlow, FlowVo.class);
        LogUtil.runInfoLog("审批信息:{}", flowVo.toString());

        String flowAfterProcess = woUtils.getFlowDetailJsonObject(request, flowVo.getFlowNo());
        FlowDetail flowDetailAfterProcess = JsonUtils.parse(flowAfterProcess, FlowDetail.class);
        LogUtil.runInfoLog("审批后电子流具体信息:{}", flowAfterProcess.toString());

        if (loanApproveParam.getType() == LoanConstant.ApproveResultStatus.REJECT) {
            loanApproveRecordMapper.update(null,
                    new LambdaUpdateWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getFlowNo, loanApproveParam.getFlowNo())
                            .set(LoanApproveRecordEntity::getOperId, operAccount)
                            .set(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.REJECT));
        } else if (loanApproveParam.getType() == LoanConstant.ApproveResultStatus.PASS) {
            // 更新t_loan_approve_record
            List<ActivitiImageInfo> nodeList = flowDetailAfterProcess.getNodeList();
            if (approvePass(nodeList)) {
                loanApproveRecordMapper.update(null,
                        new LambdaUpdateWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getFlowNo, loanApproveParam.getFlowNo())
                                .set(LoanApproveRecordEntity::getOperId, operAccount)
                                .set(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.PASS)
                                .set(LoanApproveRecordEntity::getStartTime, currentDate));
                LoanApproveRecordEntity entity = loanApproveRecordMapper.selectOne(new LambdaQueryWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getFlowNo, loanApproveParam.getFlowNo()));
                if (entity.getType() == LoanConstant.ApproveType.diversionRatio || entity.getType() == LoanConstant.ApproveType.suggestList) {
                    loanApproveRecordMapper.update(null,
                            new LambdaUpdateWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.PASS)
                                    .eq(LoanApproveRecordEntity::getType, entity.getType())
                                    .ne(LoanApproveRecordEntity::getFlowNo, entity.getFlowNo())
                                    .isNotNull(LoanApproveRecordEntity::getStartTime)
                                    .isNull(LoanApproveRecordEntity::getEndTime)
                                    .set(LoanApproveRecordEntity::getEndTime, currentDate)
                    );
                }
                if (entity.getType() == LoanConstant.ApproveType.diversionRatio) {
                    // 更新 t_dict_item表：模型分流比例 & 手工分流比例
                    dictItemMapper.update(null, new LambdaUpdateWrapper<DictItemEntity>().eq(DictItemEntity::getName, LoanConstant.DiversionType.MANUAL_DIVERSION_RATIO)
                            .eq(DictItemEntity::getParent, LoanConstant.DiversionType.PARENT_DIVERSION_TYPE)
                            .set(DictItemEntity::getValue, entity.getManualDiversionRatio()));

                    dictItemMapper.update(null, new LambdaUpdateWrapper<DictItemEntity>().eq(DictItemEntity::getName, LoanConstant.DiversionType.MODEL_DIVERSION_RATIO)
                            .eq(DictItemEntity::getParent, LoanConstant.DiversionType.PARENT_DIVERSION_TYPE)
                            .set(DictItemEntity::getValue, entity.getModelDiversionRatio()));
                }
                updateResult(entity);
            } else {
                loanApproveRecordMapper.update(null,
                        new LambdaUpdateWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getFlowNo, loanApproveParam.getFlowNo())
                                .set(LoanApproveRecordEntity::getOperId, operAccount)
                                .set(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.APPROVING));
            }
        } else if (loanApproveParam.getType() == LoanConstant.ApproveResultStatus.REVOKE) {
            loanApproveRecordMapper.update(null,
                    new LambdaUpdateWrapper<LoanApproveRecordEntity>().eq(LoanApproveRecordEntity::getFlowNo, loanApproveParam.getFlowNo())
                            .set(LoanApproveRecordEntity::getOperId, operAccount)
                            .set(LoanApproveRecordEntity::getApproveStatus, LoanConstant.ApproveStatus.REVOKE));
        }
    }

    private void updateResult(LoanApproveRecordEntity entity) {
        if (entity.getType() == LoanConstant.ApproveType.diversionRatio) {
            List<DiversionRatioApprovalInfo> diversionRatioApprovalInfos = JsonUtils.parseArray(entity.getApproveContent(), DiversionRatioApprovalInfo.class);
            convertPollingOrder(diversionRatioApprovalInfos);
            for (DiversionRatioApprovalInfo dto : diversionRatioApprovalInfos) {
                List<LoanSupplierEntity> data = this.getApiSupplierList();
                Map<String, String> supplierNameIdMap = data.stream().collect(Collectors.toMap(LoanSupplierEntity::getSupplierName,
                        loanSupplierDto -> String.valueOf(loanSupplierDto.getSupplierId())
                ));
                String pollingOrder = dto.getPollingOrder();
                if (StringUtils.isNotBlank(pollingOrder)) {
                    String[] supplierNameArray = pollingOrder.split(">");
                    for (String supplierName : supplierNameArray) {
                        String supplierId = supplierNameIdMap.get(supplierName);
                        pollingOrder = pollingOrder.replaceAll(supplierName, StringUtils.isNotBlank(supplierId) ? supplierId : "");
                    }
                    pollingOrder = pollingOrder.replaceAll(">", ",");
                }
                loanSupplierMapper.update(null,
                        new LambdaUpdateWrapper<LoanSupplierEntity>().eq(LoanSupplierEntity::getSupplierId, dto.getSupplierId())
                                .set(LoanSupplierEntity::getDiversionRatio, dto.getCurrentDiversionRatio())
                                .set(LoanSupplierEntity::getPollingOrder, pollingOrder));
            }
            loanFeign.deleteApiSupplierList();
        } else if (entity.getType() == LoanConstant.ApproveType.adActivityRealName) {
            LoanAdActivityParam approveContent = JsonUtils.parse(entity.getApproveContent(), LoanAdActivityParam.class);
            loanAdActivityConfigService.saveLoanAdActivity(approveContent);
        } else if (entity.getType() == LoanConstant.ApproveType.suggestList) {
            // 更新审批结果落库，删除redis缓存
            List<SuggestInfo> suggestInfos = JsonUtils.parseArray(entity.getApproveContent(), SuggestInfo.class);
            for (SuggestInfo suggestInfo : suggestInfos) {
                loanSupplierMapper.update(null,
                        new LambdaUpdateWrapper<LoanSupplierEntity>().eq(LoanSupplierEntity::getSupplierName, suggestInfo.getSupplierName())
                                .set(LoanSupplierEntity::getSortIndex, suggestInfo.getSortIndex()));
            }
            redisUtil.remove(LoanConstant.SUGGEST_LIST_REDIS_KEY);
        }
    }

    private void saveLoanApproveRecord(LoanApproveRecordParam param, FlowVo flowVo, TemplateDetail templateDetail, ApplicationApprovalParam applicationApprovalParam) {
        LogUtil.runInfoLog("审批信息为: {}", applicationApprovalParam.getObject().toString());
        String operAccount = WiseoperRequestContextUtils.getOperAccount();
        if (applicationApprovalParam.getType() == LoanConstant.ApproveType.diversionRatio) {
            List<DiversionRatioApprovalInfo> diversionRatioApprovalInfo = JsonUtils.parseArray(applicationApprovalParam.getObject().toString(), DiversionRatioApprovalInfo.class);
            List<DiversionRatioApprovalInfo> loanAdActivityDtoList = new ArrayList<>();
            List<LoanSupplierEntity> data = this.getApiSupplierList();
            Map<String, String> supplierIdNameMap = data.stream().collect(Collectors.toMap(loanSupplierDto -> String.valueOf(loanSupplierDto.getSupplierId()), LoanSupplierEntity::getSupplierName));

            for (DiversionRatioApprovalInfo info : diversionRatioApprovalInfo) {
                DiversionRatioApprovalInfo dto = new DiversionRatioApprovalInfo();
                LoanSupplierEntity loanSupplierDto = data.stream().filter(supplierDto -> supplierDto.getSupplierId() == info.getSupplierId()).findFirst().orElse(null);
                dto.setSupplierId(info.getSupplierId());
                dto.setCurrentDiversionRatio(new BigDecimal(info.getDiversionRatio()));
                dto.setPollingOrder(info.getPollingOrder());
                dto.setBeforeDiversionRatio(loanSupplierDto.getDiversionRatio());
                String pollingOrder = loanSupplierDto.getPollingOrder();
                if (StringUtils.isNotBlank(pollingOrder)) {
                    StringBuffer str = new StringBuffer();
                    if (StringUtils.isNotBlank(pollingOrder)) {
                        String[] supplierIdArray = pollingOrder.split(",");
                        for (String supplierId : supplierIdArray) {
                            String supplierName = supplierIdNameMap.get(supplierId);
                            str.append(supplierName == null ? "" : supplierName).append(">");
                        }
                    }
                    pollingOrder = str.toString();
                    if (pollingOrder.endsWith(">")) {
                        pollingOrder = pollingOrder.substring(0, pollingOrder.length() - 1);
                    }
                }
                dto.setBeforePollingOrder(pollingOrder);
                loanAdActivityDtoList.add(dto);
            }
            param.setApproveContent(JsonUtils.toJson(loanAdActivityDtoList));
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.adActivityRealName) {
            LoanAdActivityParam loanAdActivityApprovalInfo = JsonUtils.parse(applicationApprovalParam.getObject().toString(), LoanAdActivityParam.class);
            loanAdActivityApprovalInfo.setOperId(operAccount);
            param.setApproveContent(JsonUtils.toJson(loanAdActivityApprovalInfo));
        } else if (applicationApprovalParam.getType() == LoanConstant.ApproveType.suggestList) {
            List<SuggestInfo> suggestInfos = JsonUtils.parseArray(applicationApprovalParam.getObject().toString(), SuggestInfo.class);
            this.insertBeforeSortIndex(suggestInfos);
            param.setApproveContent(JsonUtils.toJson(suggestInfos));
        }
        param.setOperId(operAccount);
        param.setApproveStatus(LoanConstant.ApproveStatus.APPROVING);
        param.setFlowNo(flowVo.getFlowNo());
        param.setApprover(operAccount);
        param.setType(applicationApprovalParam.getType());
        if (applicationApprovalParam.getApprovalMaterial() != null) {
            String url = obsFileUpload(applicationApprovalParam.getApprovalMaterial());
            param.setApprovalMaterialUrl(url);
        }
        LoanApproveRecordEntity entity = new LoanApproveRecordEntity();
        BeanUtils.copyProperties(param, entity);
        entity.setManualDiversionRatio(applicationApprovalParam.getManualDiversionRatio());
        entity.setModelDiversionRatio(applicationApprovalParam.getModelDiversionRatio());
        entity.setBeforeManualDiversionRatio(applicationApprovalParam.getBeforeManualDiversionRatio());
        entity.setBeforeModelDiversionRatio(applicationApprovalParam.getBeforeModelDiversionRatio());
        loanApproveRecordMapper.insert(entity);
    }

    private void insertBeforeSortIndex(List<SuggestInfo> suggestInfos) {
        List<LoanSupplierEntity> suggestSupplierList = this.getSuggestSupplierList();
        Map<String, Integer> supplierSortIndexMap = suggestSupplierList.stream()
                .collect(Collectors.toMap(LoanSupplierEntity::getSupplierName, LoanSupplierEntity::getSortIndex));
        for (SuggestInfo suggestInfo : suggestInfos) {
            if (supplierSortIndexMap.containsKey(suggestInfo.getSupplierName())) {
                suggestInfo.setBeforeSortIndex(supplierSortIndexMap.get(suggestInfo.getSupplierName()));
            }
        }
    }

    private Boolean approvePass(List<ActivitiImageInfo> nodeList) {
        ActivitiImageInfo info = nodeList.stream().filter(node -> node.getNodeName().equals("END")).findFirst().orElse(null);
        if (info != null && info.getState() == LoanConstant.nodeState.PASSED || info.getState() == LoanConstant.nodeState.CURRENT) {
            return true;
        }
        return false;
    }

    private String obsFileUpload(MultipartFile file) {
        Long timeStamp = System.currentTimeMillis();
        //此处对文件名称
        String originalFilename = file.getOriginalFilename();
        String fileName = originalFilename.substring(0, originalFilename.lastIndexOf(FILESPLIT));
        String suffix = originalFilename.substring(originalFilename.lastIndexOf(FILESPLIT));
        Pattern p = Pattern.compile(REGEX);

        Matcher m = p.matcher(fileName);
        if (m.find()) {
            originalFilename = m.replaceAll("") + suffix;
        }

        String path = timeStamp + "/" + originalFilename;
        PutObjectResult putObjectResult = fileObsOperate.upload(file, path);
        if (putObjectResult == null) {
            throw new BusinessException(WalletResultCode.FILE_UPLOADFAIL_ERROR);
        }
        String obsFileUrl = fileObsOperate.getFilePath() + "/" + path;
        return obsFileUrl;
    }

    private LoanUserEntity getRecentLoanUserEntity(String userId) {
        LambdaQueryWrapper<LoanUserEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanUserEntity::getUserId, Long.parseLong(userId))
                .orderByDesc(LoanUserEntity::getCreateTime)
                .orderByDesc(LoanUserEntity::getRecordId)
                .last("LIMIT 1");
        return loanUserMapper.selectOne(lambdaQueryWrapper);
    }

    public List<CreditApplyEntity> getRecentCreditApplications(String userId) {
        // 获取当前时间的六个月前
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);

        // 构建查询条件
        LambdaQueryWrapper<CreditApplyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CreditApplyEntity::getUserId, Long.parseLong(userId))
                .ge(CreditApplyEntity::getCreateTime, sixMonthsAgo) // 大于等于六个月前
                .orderByDesc(CreditApplyEntity::getCreateTime); // 按照创建时间降序排序

        // 执行查询
        return creditApplyMapper.selectList(queryWrapper);
    }

    public List<LoanApplyEntity> getRecentLoanRecords(String userId) {
        // 获取当前时间的六个月前
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);

        // 构建查询条件
        LambdaQueryWrapper<LoanApplyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanApplyEntity::getUserId, Long.parseLong(userId))
                .gt(LoanApplyEntity::getApplyStatus, 1) // applyStatus 大于 0
                .ge(LoanApplyEntity::getCreateTime, sixMonthsAgo) // 大于等于六个月前
                .orderByDesc(LoanApplyEntity::getCreateTime); // 按照创建时间降序排序

        // 执行查询
        return loanApplyMapper.selectList(queryWrapper);
    }

    public List<LoanRepayEntity> getRecentRepayFailRecords(String userId) {
        // 获取当前时间的六个月前
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);

        LambdaQueryWrapper<LoanRepayEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanRepayEntity::getUserId, Long.parseLong(userId))
                .eq(LoanRepayEntity::getRepayStatus, LoanConstant.RepayStatus.REPAY_FAIL)
                .ge(LoanRepayEntity::getCreateTime, sixMonthsAgo) // 大于等于六个月前
                .orderByDesc(LoanRepayEntity::getCreateTime); // 按照创建时间降序排序

        return loanRepayMapper.selectList(queryWrapper);
    }

    public List<CouponNotifyEntity> getRecentCouponRecords(String userId) {
        // 获取当前时间的六个月前
        LocalDateTime sixMonthsAgo = LocalDateTime.now().minusMonths(6);

        LambdaQueryWrapper<CouponNotifyEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CouponNotifyEntity::getUserId, userId)
                .ge(CouponNotifyEntity::getCreateTime, sixMonthsAgo)
                .orderByDesc(CouponNotifyEntity::getCreateTime);

        return couponNotifyMapper.selectList(queryWrapper);
    }

    public Map<String, String> getLoanHomeRepayMethod(Integer supplier) {
        DictQueryParam queryParam = new DictQueryParam();
        List<String> list = new ArrayList<>();
        list.add(LoanConstant.SUPPLIER_REPAY_METHOD);
        queryParam.setItemNames(list);
        LambdaQueryWrapper<DictItemEntity> dictWrapper = new LambdaQueryWrapper<>();
        dictWrapper.orderByAsc(DictItemEntity::getSortIndex);
        List<DictItemEntity> dicts = dictItemMapper.selectList(dictWrapper);
        Map<String, List<DictItemDto>> map = dicts.stream()
                .map(entity -> {
                    DictItemDto dto = new DictItemDto();
                    BeanUtils.copyProperties(entity, dto);
                    return dto;
                })
                .collect(Collectors.groupingBy(DictItemDto::getParent));
        Map<String, String> repayMethod = new HashMap<>();
        List<DictItemDto> dictItemEntityList = map.get(LoanConstant.SUPPLIER_LOAN_DETAIL_REPAY_METHOD);
        if (org.springframework.util.CollectionUtils.isEmpty(dictItemEntityList)) {
            return repayMethod;
        }
        repayMethod = dictItemEntityList.stream()
                .filter(item -> item.getSupplierId().equals(supplier))
                .collect(Collectors.toMap(
                        DictItemDto::getName,
                        DictItemDto::getValue
                ));
        return repayMethod;
    }

    public String findLatestShouldRepayDate(List<RepayPlanTermDto> repayPlanTerms) {
        return repayPlanTerms.stream()
                .filter(term -> term.getTermStatus() != 1)
                .map(term -> term.getShouldRepayDate())
                .findFirst()
                .orElse(null);
    }

    public String findLatestRepayedTermDate(List<RepayPlanTermDto> repayPlanTerms) {
        return repayPlanTerms.stream()
                .filter(term -> term.getTermStatus() == 1)
                .map(term -> term.getShouldRepayDate())
                .reduce((first, second) -> second)
                .orElse(null);
    }


    public Long getDailyOperatorAccessCount(String key) {
        // 增加计数
        Long count = redisTemplate.opsForValue().increment(key);

        // 设置过期时间
        if (count == 1) { // 如果是第一次访问，设置过期时间
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
        }

        return count;
    }

}
