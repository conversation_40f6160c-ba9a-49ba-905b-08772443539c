package com.hihonor.wallet.service;

import java.io.IOException;
import java.util.List;

import com.hihonor.wallet.common.model.dto.magicOper.ApprovalParam;
import com.hihonor.wallet.common.model.dto.magicOper.UserInfoVo;
import com.hihonor.wallet.common.model.param.loan.ApplicationApprovalParam;
import com.hihonor.wallet.common.model.param.loan.LoanApproveParam;
import com.hihonor.wallet.common.model.param.loanmgr.SearchLoanInfoByAccountParam;
import com.hihonor.wallet.dto.*;

/**
 * <AUTHOR>
 */
public interface LoanConfigService {

    /**
     * 获取cp配置
     *
     * @return
     */
    List<LoanSupplierConfigDto> getLoanSupplier();

    //    ApprovalResponse getApprovalInfo(HttpServletRequest request, UserInfoDto userInfoDto);

    DiversionRatioApprovalHistoryRecords getDiversionRatioApprovalHistoryRecords();

    /**
     * 1.2 申请审批
     * @param applicationApprovalParam
     * @return
     */
    void applicationApproval(ApplicationApprovalParam applicationApprovalParam) throws IOException;

    /**
     * 1.3 审批接口
     * @param loanApproveParam
     * @return
     */
    void loanApprove(LoanApproveParam loanApproveParam);

    ApprovalResponse getApprovalInfo(ApprovalParam param);

    UserInfoVo getWoCurrentUser();

    SuggestListApprovalHistoryRecords getSuggestListApprovalHistoryRecords();

    List<SuggestInfo> getSuggestInfoList();

    UserLoanInfo searchLoanInfoByAccount(SearchLoanInfoByAccountParam param);
}
