package com.hihonor.wallet.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.loan.entity.LoanApproveRecordEntity;
import com.hihonor.wallet.loan.mapper.LoanApproveRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hihonor.wallet.common.model.dto.loan.LoanAdActivityDto;
import com.hihonor.wallet.common.model.dto.transcard.PageBaseResponse;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperRequestContextUtils;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityListParam;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.BeanUtilCopy;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.LoanAdActivityEntity;
import com.hihonor.wallet.loan.mapper.LoanAdActivityMapper;
import com.hihonor.wallet.service.LoanAdActivityConfigService;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;

/**
 * 实名认证运营配置服务
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Service
@RefreshScope
public class LoanAdActivityConfigServiceImpl implements LoanAdActivityConfigService {
    @Autowired
    private LoanAdActivityMapper loanAdActivityMapper;


    @Autowired
    private LoanApproveRecordMapper loanApproveRecordMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${ad.activity.pre}")
    private String adActivityPre;

    @Value("${ad.activity.url}")
    private String adActivityUrl;

    @Override
    public void saveLoanAdActivity(LoanAdActivityParam loanAdActivityParam) {
        LoanAdActivityEntity activity = new LoanAdActivityEntity();
        BeanUtils.copyProperties(loanAdActivityParam, activity);
        if (ObjUtil.isNull(activity.getOperId())) {
            activity.setOperId(WiseoperRequestContextUtils.getOperAccount());
        }

        if (ObjUtil.isNotNull(loanAdActivityParam.getPreDivisionSupplier())) {
            activity.setPreDivisionSupplier(String.valueOf(loanAdActivityParam.getPreDivisionSupplier()));
        }

        String activityCode = generateActivityCode(activity.getActivityCode(), activity.getPreDivisionSupplier());
        activity.setActivityCode(activityCode);

        String pageLink = generatePageLink(activity.getCid(), activity.getSubCid(), activity.getActivityCode());
        activity.setPageLink(pageLink);

        if (Objects.isNull(loanAdActivityParam.getId())) {
            activity.setCreateTime(new Date());
            activity.setUpdateTime(new Date());
            loanAdActivityMapper.insert(activity);
        } else {
            activity.setUpdateTime(new Date());
            loanAdActivityMapper.update(activity,
                new LambdaUpdateWrapper<LoanAdActivityEntity>().eq(LoanAdActivityEntity::getId,
                    loanAdActivityParam.getId()));
            String cacheKey = LoanConstant.LOAN_AD_ACTIVITY_CACHE_KEY_PRE + loanAdActivityParam.getActivityCode();
            redisUtil.remove(cacheKey);
        }
    }

    /**
     * 生成活动编码
     *
     * @param preDivisionSupplier 前置分流CP
     * @return 活动编码
     */
    private String generateActivityCode(String activityCode, String preDivisionSupplier) {
        String firstPart;
        String secondPart;

        if (ObjUtil.isNotEmpty(activityCode)) {
            firstPart = StrUtil.sub(activityCode, 0, activityCode.length() - 2);
        } else {
            firstPart = String.valueOf(System.currentTimeMillis());
        }

        secondPart = LoanConstant.CPID_2_CPCODE_MAP.getOrDefault(preDivisionSupplier, "00");

        return firstPart + secondPart;
    }

    /**
     * 生成活动编码
     *
     * @param cid          一级渠道
     * @param subCid       二级渠道
     * @param activityCode 活动编码
     * @return 活动编码
     */
    private String generatePageLink(String cid, String subCid, String activityCode) {
        String firstPart = StrUtil.format(adActivityPre, URLUtil.encodeAll(cid), URLUtil.encodeAll(subCid));
        String secondPart = URLUtil.encodeAll(StrUtil.format(adActivityUrl, activityCode));
        return firstPart + secondPart;
    }

    @Override
    public PageBaseResponse<LoanAdActivityDto> getLoanAdActivityList(LoanAdActivityListParam loanAdActivityListParam) {
        Integer pageSize = loanAdActivityListParam.getPageSize();
        if (pageSize != null && pageSize > 100) {
            loanAdActivityListParam.setPageSize(20);
        }
        Integer pageIndex = loanAdActivityListParam.getPageIndex();
        String activityDesc = loanAdActivityListParam.getActivityDesc();
        LambdaQueryWrapper<LoanAdActivityEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotEmpty(activityDesc), LoanAdActivityEntity::getActivityDesc,
            activityDesc).orderByDesc(LoanAdActivityEntity::getUpdateTime);
        List<LoanAdActivityEntity> loanAdActivityEntityList = new ArrayList<>();
        PageBaseResponse<LoanAdActivityDto> response = new PageBaseResponse();
        if (pageIndex == null || pageSize == null) {
            loanAdActivityEntityList = loanAdActivityMapper.selectList(lambdaQueryWrapper);
        } else {
            Page<LoanAdActivityEntity> page = Page.of(pageIndex, pageSize);
            Page<LoanAdActivityEntity> loanAdActivityEntityPage =
                loanAdActivityMapper.selectPage(page, lambdaQueryWrapper);
            loanAdActivityEntityList = loanAdActivityEntityPage.getRecords();
            response.setPages(loanAdActivityEntityPage.getPages());
            response.setTotal(loanAdActivityEntityPage.getTotal());
        }
        if (loanAdActivityEntityList.isEmpty()) {
            return response;
        }
        List<LoanAdActivityDto> loanAdActivityDtoList =
            BeanUtilCopy.copyListProperties(loanAdActivityEntityList, LoanAdActivityDto::new,
                (loanAdActivityEntity, loanAdActivityDto) -> {
                    if (Objects.nonNull(loanAdActivityEntity.getPreDivisionSupplier())) {
                        loanAdActivityDto.setPreDivisionSupplier(
                            Integer.valueOf(loanAdActivityEntity.getPreDivisionSupplier()));
                    }
                });
        Map<Integer, LoanAdActivityDto> objMap = loanAdActivityDtoList.stream()
                .collect(Collectors.toMap(
                        LoanAdActivityDto::getId,
                        dto -> dto,
                        (existingValue, newValue) -> existingValue
                ));

        LambdaQueryWrapper<LoanApproveRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoanApproveRecordEntity::getType,2)
                .orderByDesc(LoanApproveRecordEntity::getStartTime);
        List<LoanApproveRecordEntity> loanApproveRecordEntities = loanApproveRecordMapper.selectList(queryWrapper);
        // 审批记录表
        for (LoanApproveRecordEntity loanApproveRecordEntity : loanApproveRecordEntities) {
            String approveContent = loanApproveRecordEntity.getApproveContent();
            LoanAdActivityDto dto = JsonUtils.parse(approveContent, LoanAdActivityDto.class);
            if (Objects.isNull(dto.getId()) || Objects.isNull(loanApproveRecordEntity.getApproveStatus()) || Objects.isNull(objMap.get(dto.getId()))) {
                continue;
            }
            LoanAdActivityDto loanAdActivityDto = objMap.get(dto.getId());
            Integer approveStatus = loanApproveRecordEntity.getApproveStatus();
            loanAdActivityDto.setModifyFlag((approveStatus == 0 || approveStatus == 2) ? 0 : 1);
        }

        loanAdActivityDtoList.forEach(dto -> dto.setModifyFlag(Optional.ofNullable(dto.getModifyFlag()).orElse(1)));

        response.setRecords(loanAdActivityDtoList);

        return response;
    }
}
