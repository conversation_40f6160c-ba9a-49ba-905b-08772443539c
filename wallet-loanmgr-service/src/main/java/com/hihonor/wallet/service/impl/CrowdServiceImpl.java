package com.hihonor.wallet.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hihonor.wallet.common.client.DmpClient;
import com.hihonor.wallet.common.config.nacos.ActivityConfig;
import com.hihonor.wallet.common.config.nacos.ActivityScene;
import com.hihonor.wallet.common.config.nacos.DmpConfig;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.model.dto.loan.ActivityDto;
import com.hihonor.wallet.common.model.dto.loan.GetActivityListDto;
import com.hihonor.wallet.common.model.param.dmp.DmpReportCrowdParam;
import com.hihonor.wallet.common.model.param.dmp.DmpReportCrowdScene;
import com.hihonor.wallet.common.model.param.loan.CreateActivityParam;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.UUIDGenerator;
import com.hihonor.wallet.loan.entity.CrowdActivityEntity;
import com.hihonor.wallet.loan.mapper.CrowdActivityMapper;
import com.hihonor.wallet.service.CrowdService;


/**
 * 功能描述
 *
 * @since 2024-10-08
 */
@Service
public class CrowdServiceImpl implements CrowdService {

    private static final String SCENE_TYPE = "红点展示";


    @Autowired
    private DmpClient dmpClient;

    @Autowired
    private DmpConfig dmpConfig;

    @Autowired
    private CrowdActivityMapper crowdActivityMapper;

    @Autowired
    private ActivityConfig activityConfig;

    @Override
    public void reportCrowdUsageScene(CreateActivityParam param) {
        // 活动信息入库
        CrowdActivityEntity entity = new CrowdActivityEntity();
        BeanUtils.copyProperties(param, entity);
        // 校验活动时间
        validate(param);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            entity.setStartTime(sdf.parse(sdf.format(param.getStartTime())));
            entity.setEndTime(sdf.parse(sdf.format(param.getEndTime())));
        } catch (ParseException e) {
            throw new RuntimeException(e.getMessage());
        }
        entity.setCrowdId(param.getCrowdId());
        if (param.getActivityId() != null ) {
            CrowdActivityEntity crowdActivityEntity= crowdActivityMapper.selectOne(new LambdaQueryWrapper<CrowdActivityEntity>().eq(CrowdActivityEntity::getActivityId, param.getActivityId()));
            // 获取当前时间
            Date currentDate = new Date();
            if (crowdActivityEntity != null && crowdActivityEntity.getEndTime().before(currentDate)) {
                throw new BusinessException(WalletResultCode.CROWD_CREATE_UPDATE_ERROR);
            }
            crowdActivityMapper.update(entity, new LambdaUpdateWrapper<CrowdActivityEntity>().eq(CrowdActivityEntity::getActivityId, param.getActivityId()));
        } else {
            crowdActivityMapper.insert(entity);
        }
        if (StringUtils.isNotEmpty(param.getCrowdId())) {
            DmpReportCrowdParam dmpReportCrowdParam = new DmpReportCrowdParam();
            dmpReportCrowdParam.setBusinessSystemMark(dmpConfig.getBusiness());
            dmpReportCrowdParam.setRequestNo(UUIDGenerator.generate());

            DmpReportCrowdScene dmpReportCrowdScene = new DmpReportCrowdScene();
            dmpReportCrowdScene.setSceneId(String.valueOf(entity.getActivityId()));
            dmpReportCrowdScene.setSceneName(param.getActivityName());
            dmpReportCrowdScene.setSceneType(SCENE_TYPE);
            dmpReportCrowdScene.setReferCrowds(param.getCrowdId());
            dmpReportCrowdScene.setStartTime(sdf.format(param.getStartTime()));
            dmpReportCrowdScene.setEndTime(sdf.format(param.getEndTime()));
            dmpReportCrowdScene.setDeletedFlag("0");
            dmpReportCrowdParam.setScenes(Collections.singletonList(dmpReportCrowdScene));
            dmpClient.reportCrowdUsageScene(dmpReportCrowdParam);
        }
    }

    private void validate(CreateActivityParam param){
        if (param.getStartTime().after(param.getEndTime()) ){
            throw new BusinessException(WalletResultCode.ILLEGAL_PARAM, "生效时间不得晚于失效时间");
        }
    }

    @Override
    public GetActivityListDto getCrowdActivityList() {
        List<CrowdActivityEntity> list = crowdActivityMapper.selectList(null);
        List<ActivityDto> activityResult = new ArrayList<>();
        List<ActivityScene> sceneResult = activityConfig.getLoanActivityList();
        Date now = new Date();
        for (CrowdActivityEntity entity : list) {
            ActivityDto dto = new ActivityDto();
            BeanUtils.copyProperties(entity, dto);
            if (now.before(entity.getEndTime()) && now.after(entity.getStartTime())) {
                dto.setStatus(1);
            } else if (now.before(entity.getStartTime())){
                dto.setStatus(0);
            } else {
                dto.setStatus(2);
            }
            dto.setSceneName(activityConfig.getLoanActivitySceneMap().get(entity.getScene()));
            activityResult.add(dto);
        }
        GetActivityListDto result = new GetActivityListDto();
        result.setActivityList(activityResult);
        result.setActivitySceneList(sceneResult);
        return result;
    }
}
