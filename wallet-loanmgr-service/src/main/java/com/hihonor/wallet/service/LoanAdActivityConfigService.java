package com.hihonor.wallet.service;

import com.hihonor.wallet.common.model.dto.loan.LoanAdActivityDto;
import com.hihonor.wallet.common.model.dto.transcard.PageBaseResponse;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityListParam;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;

/**
 * 实名认证运营页配置服务
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
public interface LoanAdActivityConfigService {
    /**
     * 新增实名认证页配置
     *
     * @param loanAdActivityParam
     */
    void saveLoanAdActivity(LoanAdActivityParam loanAdActivityParam);

    /**
     * 获取实名认证页配置
     *
     * @param loanAdActivityListParam
     * @return
     */
    PageBaseResponse<LoanAdActivityDto> getLoanAdActivityList(LoanAdActivityListParam loanAdActivityListParam);

}
