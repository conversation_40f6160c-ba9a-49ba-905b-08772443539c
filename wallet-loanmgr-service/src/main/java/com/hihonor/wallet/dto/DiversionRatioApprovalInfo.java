package com.hihonor.wallet.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DiversionRatioApprovalInfo {

    /**
     * cp渠道iD
     */
    @ApiModelProperty(value = "主键", required = true)
    private Integer supplierId;

    @ApiModelProperty(value = "cp", required = true)
    private String supplierName;

    /**
     * 分流比率
     */
    @ApiModelProperty(value = "分流比率", required = true)
    private String diversionRatio;

    /**
     * 轮询顺序
     */
    @ApiModelProperty(value = "轮询顺序", required = true)
    private String pollingOrder;

    /**
     * 新的轮询顺序(兼容老版本场景，与pollingOrder一致)
     */
    private String currentPollingOrder;

    /**
     * 老的轮询顺序
     */
    private String beforePollingOrder;



    /**
     * 老的分流比例
     */
    private BigDecimal beforeDiversionRatio;

    /**
     * 新的分流比例
     */
    private BigDecimal currentDiversionRatio;

}
