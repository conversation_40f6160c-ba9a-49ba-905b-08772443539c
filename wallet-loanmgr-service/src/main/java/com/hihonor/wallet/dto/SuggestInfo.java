package com.hihonor.wallet.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 严选推荐供应商排序信息
 */
@Data
public class SuggestInfo {
    /**
     * 供应商名称
     */
    @NotNull
    @ApiModelProperty(value = "供应商名称", required = true)
    private String supplierName;

    /**
     * 供应商排序
     */
    @NotNull
    @ApiModelProperty(value = "供应商当前排序", required = true)
    private Integer sortIndex;

    /**
     * 供应商上一次排序
     */
    private Integer beforeSortIndex;
}
