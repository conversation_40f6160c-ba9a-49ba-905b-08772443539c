package com.hihonor.wallet.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 严选推荐供应商排序审批记录
 */
@Data
public class SuggestListApprovalHistoryRecord {
    /**
     * 记录Id
     */
    private Integer recordId;

    /**
     * 配置人
     */
    private String approver;

    /**
     * 审批材料链接
     */
    private String approvalMaterialUrl;

    /**
     * 严选推荐列表
     */
    private List<SuggestInfo> suggestInfos;

    /**
     * 生效时间
     */
    private Date effectiveDate;

    /**
     * 失效时间
     */
    private Date expirationDate;
}
