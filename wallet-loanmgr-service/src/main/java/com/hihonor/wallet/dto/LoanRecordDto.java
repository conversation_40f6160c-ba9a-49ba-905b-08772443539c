package com.hihonor.wallet.dto;

import lombok.Data;

import java.util.Date;

@Data
public class LoanRecordDto {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 借款时间
     */
    private Date applyTime;

    /**
     * 借款方式
     */
    private String loanMethod;

    /**
     * 还款方式
     */
    private String repayMethod;

    /**
     * 借款状态（1-审核中，2-成功，3-失败，4-拒绝，5-取消，6-需鉴权）
     */
    private Integer applyStatus;

    /**
     * 拒绝原因
     */
    private String refuseMsg;

    /**
     * 最近一期待还款到期日(yyyyMMDD)
     */
    private String latestShouldRepayDate;

    /**
     * 最近一期已还款的期次还款日(yyyyMMDD)
     */
    private String latestRepayedTermDate;
}
