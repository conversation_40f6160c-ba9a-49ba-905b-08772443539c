package com.hihonor.wallet.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 审批记录实体类
 */
@Data
public class DiversionRatioApprovelHistoryRecord {

    /**
     * 记录Id
     */
    private Integer recordId;

    /**
     * cpId
     */
    private String supplierId;

    /**
     * cp名称
     */
    private String supplierName;

    /**
     * 分流比率
     */
    private String diversionRatio;


    /**
     * 审批材料链接
     */
    private String approvalMaterialUrl;

    /**
     * 轮询顺序
     */
    private String pollingOrder;

    /**
     * 生效时间
     */
    private Date effectiveDate;

    /**
     * 失效时间
     */
    private Date expirationDate;

    /**
     * 配置人
     */
    private String approver;


    private List<DiversionRatioApprovalInfo> diversionRatioApprovalInfos;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;


    /**
     *  模型分流比例
     */
    private BigDecimal modelDiversionRatio;

    /**
     * 手工分流比例
     */
    private BigDecimal manualDiversionRatio;
}