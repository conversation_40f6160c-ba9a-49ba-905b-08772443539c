/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.wallet.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@ApiModel(description="小贷渠道配置")
public class LoanSupplierConfigDto implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
    @ApiModelProperty(value = "主键", required = true)
	private Integer supplierId;

	/**
	 * 渠道名称
	 */
    @ApiModelProperty(value = "渠道名称", required = true)
	private String supplierName;

	/**
	 * logo图片地址
	 */
    @ApiModelProperty(value = "logo图片地址")
	private String logoUrl;

	/**
	 * 副标题
	 */
    @ApiModelProperty(value = "副标题")
	private String subTitle;

	/**
	 * 最高可借金额，单位：元
	 */
    @ApiModelProperty(value = "最高可借金额，单位：元")
	private Integer maxLoan;

	/**
	 * 年华利率
	 */
	private String apr;

	/**
	 * 跳转地址
	 */
	private String targetUrl;

	/**
	 * 排序，升序排列
	 */
	private Integer sortIndex;

	/**
	 * 严选推荐，1-是
	 */
	private Integer suggest;

	/**
	 * API接入 1-是
	 */
	private Integer supportApi;

	/**
	 * 支持的账号apk版本号
	 */
	private Long supportHnidVer;

	/**
	 * 授信验证步骤，逗号隔开
	 */
	private String creditVerifyList;

	/**
	 * 渠道英文名称
	 */
	private String supplierEnName;

	/**
	 * 渠道客服电话
	 */
	private String supplierContact;

	/**
	 * 强制阅读的协议的强制阅读时间，单位秒
	 */
	private Integer agreementReadingTime;

	/**
	 * 强制阅读的协议是否需要上滑阅读全部，1-需要
	 */
	private Integer agreementReadingAll;

	/**
	 * 协议名称是否默认展开
	 */
	private Integer agreementExpand;

	/**
	 * 授信失败后锁定期
	 */
	private Integer creditFailLockTime;

	/**
	 * 所有Cp准入失败锁定期
	 */
	private Integer bindFailLockTime;

	/**
	 * 提前还款规则或说明
	 */
	private String fixedTermPenaltyExplanation;

	/**
	 * 借款说明页运营位配置-固定期限
	 */
	private String loanFixedTermPositionUrl;

	/**
	 * 借款说明页运营位配置-随借随还
	 */
	private String loanBrPositionUrl;

	/**
	 * 是否支持开具结清证明
	 */
	private Integer supportSettlementCertificate;

	/**
	 * 还款成功是否支持reoffer
	 */
	private Integer supportReoffer;

	/**
	 * 是否提供身份证校验方法
	 */
	private Integer provideIdVerficationMethod;

	/**
	 * 分流比例
	 */
	private BigDecimal diversionRatio;

	/**
	 * 准入或者授信失败轮询顺序
	 */
	private String pollingOrder;

	/**
	 * 更新时间
	 */
	private Date updateTime;

	/**
	 * 随借随换文案说明
	 */
	private String loanBrExplanation;

	/**
	 * 还款违约金说明
	 */
	private String repayViolateFeeExplanation;

	/**
	 * 还款违约利息说明
	 */
	private String repayViolateInterestExplanation;

	/**
	 * 是否凌晨自动扣款
	 */
	private Integer deductionEarlyMorning;

	/**
	 *  描述那列就写支持多笔借款申请中
	 */
	private Integer supportMultipleLoans;

	/**
	 * api接入图片
	 */
	private String apiLogoUrl;

	/**
	 * 隐私提示语
	 */
	private String privacyNotice;

	/**
	 * 结清证明开局方式
	 */
	private String issueSettlementMethod;

	/**
	 * 结清证明全部文案
	 */
	private String settlementAllContext;

	/**
	 * 结清证明部分文案
	 */
	private String settlementPartContext;

	/**
	 * 结清时间说明
	 */
	private String settlementTimeExplanation;

	/**
	 * 当前期次显示
	 */
	private Integer currentTermDisplay;

	/**
	 * 展示逾期提示
	 */
	private Integer showDueTip;


	/**
	 * 0-不支持重新签约检查，1-支持重新签约检查
	 */
	private Integer supportResignCheck;

	/**
	 * 0-不支持注销，1-支持注销
	 */
	private Integer supportLogoff;

	/**
	 * 息费说明文案
	 */
	private String interestFeeExplanation;

	/**
	 *  模型分流比例
	 */
	private Double modelDiversionRatio;

	/**
	 * 手工分流比例
	 */
	private Double manualDiversionRatio;
}