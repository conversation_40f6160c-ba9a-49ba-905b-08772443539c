package com.hihonor.wallet.dto;

import lombok.Data;

import java.util.Date;

@Data
public class LoanAdActivityApproveContent {
    /**
     * 主键(更新操作必传)
     */
    private Integer id;

    /**
     * 活动编码（更新操作必传）
     */
    private String activityCode;

    /**
     * 投放描述
     */
    private String activityDesc;

    /**
     * 背景图片地址
     */
    private String backPicUrl;

    /**
     * 按钮图片地址
     */
    private String buttonImageUrl;

    /**
     * 按钮上方标题图地址
     */
    private String labelImageUrl;

    /**
     * 按钮手型图片地址
     */
    private String buttonHandIconUrl;

    /**
     * 前置分流cp
     */
    private Integer preDivisionSupplier;

    /**
     * 未登录状态文案提示
     */
    private String loginPromptText;

    /**
     * 未登录状态按钮图片链接
     */
    private String loginButtonImageUrl;

    /**
     * 未登录状态下的背景图片链接
     */
    private String loginBackPicUrl;

    /**
     * 页面链接
     */
    private String pageLink;

    /**
     * 操作员账号
     */
    private String operId;

    /**
     * 失效时间
     */
    private Date endTime;

    /**
     * 一级渠道
     */
    private String cid;

    /**
     * 二级渠道
     */
    private String subCid;
}
