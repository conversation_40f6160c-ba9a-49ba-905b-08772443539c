package com.hihonor.wallet.dto;


import com.hihonor.wallet.common.model.dto.magicOper.ActivitiImageInfo;
import com.hihonor.wallet.common.model.dto.magicOper.FlowDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ApprovalResponse {
    // 审批信息--分流
    private Object approvalInfo;

    private FlowDetail flowDetail;


    /**
     * 审批依据url
     * 分流审批必返回
     */
    private String approvalMaterial;

    /**
     *  1-分流，2-授信修改，3-授信新增，4-H5产品排序
     */
    private Integer type;

    /**
     *  模型分流比例
     */
    private BigDecimal modelDiversionRatio;

    /**
     * 手工分流比例
     */
    private BigDecimal manualDiversionRatio;

    /**
     * 修改之前手工分流比例修改前手工
     */
    private BigDecimal beforeManualDiversionRatio;

    /**
     * 修改之前模型分流比例
     */
    private BigDecimal beforeModelDiversionRatio;

}
