package com.hihonor.wallet.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserLoanInfo {
    /**
     *  客户id
     */
    private String userId;

    /**
     *  准入手机号是否与注册手机号一致
     */
    private Boolean isMobileNoMatched;

    /**
     *  最近一次准入时间
     */
    private Date accessTime;

    /**
     *  最近一次准入状态 1-准入成功，2-拒绝，3-注销
     */
    private Integer accessResult;

    /**
     *  最近一次准入CP
     */
    private String accessSupplierName;

    /**
     *  当前额度状态   0-没有额度,1-正常，2-审核中，3-失效，4-拒绝,5-未授信
     */
    private Integer creditLimitStatus;

    /**
     *  当前cp(若当前已授信，展示对应CP)
     */
    private String currentSupplierName;

    /**
     *  授信信息（近半年授信记录）
     */
    private List<creditRecordDto> creditRecords;

    /**
     *  借款信息（近一年借款记录）
     */
    private List<LoanRecordDto> loanRecords;

    /**
     *  还款失败信息（近半年还款失败记录）
     */
    private List<repayFailDto> repayFailRecords;

    /**
     *  利息券信息（近半年持有的利息券）
     */
    private List<couponDto> couponNotifyRecords;
}
