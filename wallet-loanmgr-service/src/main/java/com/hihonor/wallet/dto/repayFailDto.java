package com.hihonor.wallet.dto;

import lombok.Data;

import java.util.Date;

@Data
public class repayFailDto {
    /**
     * 借款订单号
     */
    private String outLoanNo;

    /**
     * 还款时间
     */
    private Date repayTime;

    /**
     * 还款类型 0-主动还款 1-自动代扣
     */
    private Integer repaySource;

    /**
     * 还款类型 0-主动还款 1-自动代扣
     */
    private Long repayAmount;

    /**
     * 还款失败原因
     */
    private String refuseReason;
}
