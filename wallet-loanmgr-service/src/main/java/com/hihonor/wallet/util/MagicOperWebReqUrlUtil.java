package com.hihonor.wallet.util;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.stereotype.Component;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.hihonor.wallet.common.config.nacos.MagicOperWebReqUrlJson;
import com.hihonor.wallet.common.config.nacos.MagicOperWebReqUrlJsonListener;
import com.hihonor.wallet.common.constant.HeaderConstant;
import com.hihonor.wallet.util.vo.MagicOperTyper;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@Component
public class MagicOperWebReqUrlUtil {
    private final String DEFAULTOPERTYPE = "15";
    @Resource
    private MagicOperWebReqUrlJsonListener listener;

    //referer -> page=transcardmgr
    public MagicOperTyper getConfigByReqUrl(HttpServletRequest request, String reqUrl) {
        //单独处理csrf接口
        if(reqUrl.indexOf("csrf/getCsrfToken")!=-1){
            String referer = request.getHeader(HeaderConstant.REFERER_HEADER);
            Pattern compile = Pattern.compile("\\/.{6,10}\\/index\\.html\\?page=.+?\\&");
            Matcher matcher = compile.matcher(referer);
            if (!referer.contains("page")) {
                referer = referer.contains("uat") ? "/static_uat/index.html" : "/static/index.html";
            } else if(matcher.find()){
                referer = matcher.group();
                referer = referer.substring(0, referer.length()-1);
            }
            referer = "/loanmgr" + referer;
            MagicOperTyper magicOperTyper = new MagicOperTyper();
            magicOperTyper.setOperType(DEFAULTOPERTYPE);
            magicOperTyper.setMagicOperUrl(referer);
            return magicOperTyper;
        }
        MagicOperWebReqUrlJson data = listener.getData();
        List<MagicOperWebReqUrlJson.MaigcWebUrlCofig> maigcWebUrlCofigList = data.getSingleMaigcWebUrlCofigList();

        if (CollectionUtils.isNotEmpty(maigcWebUrlCofigList)) {
            for (MagicOperWebReqUrlJson.MaigcWebUrlCofig maigcWebUrlCofig : maigcWebUrlCofigList) {
                String maigcWebUrl = maigcWebUrlCofig.getMaigcWebUrl();
                List<MagicOperWebReqUrlJson.SingleReqUrlConfig> reqUrlConfigList = maigcWebUrlCofig.getReqUrlConfigList();
                Map<String, MagicOperWebReqUrlJson.SingleReqUrlConfig> map = reqUrlConfigList.stream()
                        .collect(Collectors.toMap(MagicOperWebReqUrlJson.SingleReqUrlConfig::getReqUrl, a -> a, (k1, k2) -> k1));
                MagicOperWebReqUrlJson.SingleReqUrlConfig reqUrlConfig = map.get(reqUrl);
                if (!Objects.isNull(reqUrlConfig)) {
                    MagicOperTyper magicOperTyper = new MagicOperTyper();
                    magicOperTyper.setMagicOperUrl(maigcWebUrl);
                    String operType = Objects.isNull(reqUrlConfig.getOperType()) ? DEFAULTOPERTYPE : reqUrlConfig.getOperType();
                    magicOperTyper.setOperType(operType);
                    magicOperTyper.setParamId(reqUrlConfig.getParamId());
                    magicOperTyper.setInterType(reqUrlConfig.getInterType());
                    return magicOperTyper;
                }
            }
        }
        //https://wo-drcn-security.cloud.hihonor.com/wallet/static/index.html?page=transcardmgr&__lang=zh_CN&t=0.31121035469927194
        String referer = request.getHeader(HeaderConstant.REFERER_HEADER);
        if (referer.contains("?")) {
            referer = referer.split("\\?")[1];
        }
        if (referer.contains("&")) {
            referer = referer.split("&")[0];
        }
        //单独的找不到，继续查找公共的接口
        List<MagicOperWebReqUrlJson.CommonMaigcWebUrlCofig> commonMaigcWebUrlCofigList = data.getCommonMaigcWebUrlCofigList();
        if (CollectionUtils.isNotEmpty(commonMaigcWebUrlCofigList)) {
            for (MagicOperWebReqUrlJson.CommonMaigcWebUrlCofig commonMaigcWebUrlCofig : commonMaigcWebUrlCofigList) {
                if (commonMaigcWebUrlCofig.getReqUrl().equals(reqUrl)) {
                    List<String> maigcWebUrlList = commonMaigcWebUrlCofig.getMaigcWebUrlList();
                    //todo 获取 page=transcardmgr
                    String finalReferer = referer;
                    String maigcWebUrl = maigcWebUrlList.stream().filter(str -> str.endsWith(finalReferer)).findFirst().get();
                    if (StringUtils.isBlank(maigcWebUrl)) {
                        return null;
                    }
                    MagicOperTyper magicOperTyper = new MagicOperTyper();
                    magicOperTyper.setMagicOperUrl(maigcWebUrl);
                    String operType = Objects.isNull(commonMaigcWebUrlCofig.getOperType()) ? DEFAULTOPERTYPE : commonMaigcWebUrlCofig.getOperType();
                    magicOperTyper.setOperType(operType);
                    return magicOperTyper;

                }
            }
        }

        return null;
    }
}
