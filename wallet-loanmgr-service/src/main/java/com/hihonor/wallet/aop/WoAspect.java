/*
 * Copyright (c) Honor Terminal Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.wallet.aop;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.hihonor.wallet.common.constant.CommonConstant;
import com.hihonor.wallet.common.constant.HeaderConstant;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperLogEntity;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperRequestContextUtils;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.IpUtils;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.UrlUtil;
import com.hihonor.wallet.common.util.WoUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.SensitiveLog;
import com.hihonor.wallet.common.web.wrapper.RequestWrapper;
import com.hihonor.wallet.controller.CsrfTokenController;
import com.hihonor.wallet.service.impl.OperLogService;
import com.hihonor.wallet.util.MagicOperWebReqUrlUtil;
import com.hihonor.wallet.util.vo.MagicOperTyper;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;

/**
 * 功能描述:wo退款操作鉴权及日志优化切面
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Component
@Aspect
@Order(3)
public class WoAspect {
    /**
     * wiseoper需要的appId
     */
    @Value("${wo.constant.appID}")
    private String woConstantAppId;

    /**
     * 站点id
     */
    @Value("${loan.siteId}")
    private String siteId;


    @Autowired
    private WoUtils woUtils;

    @Autowired
    private HttpServletRequest request;

    @Autowired
    private HttpServletResponse response;

    @Resource
    private OperLogService operLogService;

    @Resource
    private MagicOperWebReqUrlUtil magicOperWebReqUrlUtil;

    /**
     * wo鉴权及日志记录
     *
     * @param pjp 切点
     * @return 执行结果
     * @throws Throwable
     */
    @Around("execution(* com.hihonor.wallet.controller.*.*(..))")
    public Object validateWoAuthAndInsertOperLog(ProceedingJoinPoint pjp) throws Throwable {
        // 代理方法的执行
        Object proceed = null;
        woUtils.validateCsrfForWalletCard(request);

        //获取到对应的接口url
        String requestUrl = request.getServletPath();

        MagicOperTyper config = magicOperWebReqUrlUtil.getConfigByReqUrl(request, requestUrl);

        if (Objects.isNull(config)) {
            LogUtil.runInfoLog("找不到对应接口：{},配置对应的magicOper资源", requestUrl);
            throw new BusinessException(WalletResultCode.WISOPER_AUTH_FAIL);
        }

        // 校验wo登陆态
        String userInfo = woUtils.getUserInfoJsonObject(request);
        // 操作页面
        String pathType = config.getMagicOperUrl();
        // 操作类型
        String operType = config.getOperType();

        boolean isAuth = woUtils.checkUserAuthByToken(request, pathType);
        validateWoAuth(userInfo, isAuth);

        //校验参数签名
        String vaildToken = request.getHeader(HeaderConstant.CSRF_TOKEN_HEADER3);
        if(getSignatureValue(pjp,vaildToken,config)){
            throw new BusinessException(WalletResultCode.SIGN_INVALID);
        }
        //校验csrfToken与当前会话
        if(checkCsrfTokenAuth(request)){
            throw new BusinessException(WalletResultCode.WISEOPER_NO_CSRF, "csrfToken会话校验未通过");
        }
        // 记录日志：
        String account = (String) JSONUtil.parseObj(userInfo).get("account");
        String realIp = IpUtils.getRealIP(request);
        // 入参
        String requestArgs = Arrays.toString(pjp.getArgs());
        // 日志参数处理：
        String requestArgStr = getLogRequest(operType, requestArgs);
        WiseoperLogEntity wiseoperLogEntity = getWiseoperLogEntity(account, realIp, requestArgStr, operType);
        //记录操作人
        try {
            WiseoperRequestContextUtils.setOperAccount(account);
            proceed = pjp.proceed();
            LogUtil.runSensitiveInfoLog(getLogFormat(), account, operType, SensitiveLog.hideMarkLog(requestArgStr));
            operLogService.runSuccessLog(request, wiseoperLogEntity);
        } catch (Throwable ex) {
            operLogService.runFailLog(request, wiseoperLogEntity);
            throw ex;
        } finally {
            WiseoperRequestContextUtils.destoryLocalRequestContext();
        }
        woUtils.setHeaders(response);
        return proceed;
    }

    private boolean checkCsrfTokenAuth(HttpServletRequest request) {
        String requestUrl = request.getServletPath();
        if(requestUrl.indexOf("csrf/getCsrfToken") != -1){
            return false;
        }
        try {
            String code = request.getHeader(HeaderConstant.CSRF_TOKEN_HEADER2);
            code = code.substring(0, code.length()-64);
            String auth = EncryptUtil.decrypt(UrlUtil.getUrlDecoderString(code));
            String token = woUtils.getWoToken(request);
            if (token.equals(auth)) {
                return false;
            }
        }catch (Exception e){
            LogUtil.runErrorLog("checkCsrfTokenAuth is fail", e);
        }
        return true;
    }

    // 权限校验
    private void validateWoAuth(String userInfo, boolean isAuth) {
        if (NumberUtil.isInteger(userInfo)) {
            throw new BusinessException(WalletResultCode.WISOPER_TOKEN_AUTH_FAIL);
        }
        if (!isAuth) {
            throw new BusinessException(WalletResultCode.WISOPER_AUTH_FAIL);
        }
    }

    private boolean getSignatureValue(ProceedingJoinPoint pjp,String vaildtoken, MagicOperTyper config){
        if(pjp.getArgs().length == 0){
            return false;
        }
        if(pjp.getTarget() instanceof CsrfTokenController){
            return false;
        }
        String method = request.getMethod();
        String header = request.getHeader(HeaderConstant.ContentType);
        String body = "";
        TreeMap treeMap = null;
        if(CommonConstant.GET.equalsIgnoreCase(method)){
            String queryString = request.getQueryString();
            treeMap = analysisQuery(queryString);
        }else if(header != null && header.contains(MediaType.MULTIPART_FORM_DATA_VALUE)){
            Map<String, String[]> parameterMap = request.getParameterMap();
            if(parameterMap !=  null){
                treeMap = new TreeMap();
                TreeMap finalTreeMap = treeMap;
                parameterMap.forEach((key, value)->{
                    finalTreeMap.put(key,value[0]);
                });
            }
        }else{
            RequestWrapper requestWrapper = null;
            try {
                requestWrapper = new RequestWrapper(request);
                String bodyStr = requestWrapper.getBodyString();
                if (StringUtils.isNotBlank(bodyStr)) {
                    treeMap = JsonUtils.parse(requestWrapper.getBodyString(),TreeMap.class);
                }
            } catch (IOException e) {
                LogUtil.runErrorLog("转换requestWrapper出错!");
            }
        }
        if(treeMap != null){
            //处理日志操作状态
            handleOperType(config,treeMap);
            body = JsonUtils.toJson(treeMap);
        }
        //为空则不做校验
        if(StringUtils.isBlank(body)){
            return false;
        }
        LogUtil.runSensitiveInfoLog("需校验的数据，{}", body);
        String sha256HexStr = woUtils.getSha256HexStr(body);
        return !sha256HexStr.equals(vaildtoken);
    }

    /**
     * 针对新增和编辑公用接口状态判断
     * @param config
     * @param treeMap
     */
    private void handleOperType(MagicOperTyper config,TreeMap treeMap){
        if(config == null){
            return;
        }
        String paramId = config.getParamId();
        if(StringUtils.isNotEmpty(paramId)){
            Object obj = treeMap.get(paramId);
            config.setOperType(Objects.isNull(obj)?"11":"14");
        }
    }

    public TreeMap analysisQuery(String query){
        TreeMap treeMap = new TreeMap<>();
        if(StringUtils.isNotEmpty(query)){
            try{
                query = URLDecoder.decode(query, "utf-8");
            }catch (UnsupportedEncodingException e){
                LogUtil.runErrorLog("URLDecoder出错!");
            }
            String[] split = query.split("&");
            for(int i = 0; i < split.length; i++){
                String[] split1 = split[i].split("=");
                String key = split1[0];
                String value = split1.length==2?split1[1]:"";
                if(treeMap.containsKey(key)){
                    if(!(treeMap.get(key) instanceof List)){
                        List list = new ArrayList();
                        list.add(treeMap.get(key));
                        treeMap.put(key,list);
                    }
                    ((List)treeMap.get(key)).add(value);
                    continue;
                }
                treeMap.put(key,value);
            }
        }
        return treeMap;
    }

    // 组装日志记录参数
    private String getLogRequest(String operType, String requestArgs) {
        String result = operType + ":" + requestArgs;
        return result;
    }

    private WiseoperLogEntity getWiseoperLogEntity(String account, String realIp, String requestArgStr,
                                                   String operType) {
        WiseoperLogEntity entity = new WiseoperLogEntity();
        entity.setOperatorId(account);
        entity.setOperatorResouname(UrlUtil.getNormalizeRequestServletURI(request));
        entity.setLoggerRequest(requestArgStr);
        entity.setOperatorType(operType);
        entity.setOperatorIp(realIp);
        entity.setOperatorBrowser(request.getHeader("user-agent"));
        entity.setOperatorModuleId(woConstantAppId);
        entity.setCurrentSite(siteId);
        return entity;
    }

    private String getLogFormat() {
        return "operator:{}|operType:{}|additionInfo:{}";
    }
}
