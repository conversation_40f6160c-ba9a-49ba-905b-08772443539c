package com.hihonor.wallet.obs;

import java.io.IOException;

import java.net.URL;
import java.util.Date;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.SdkClientException;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.hihonor.wallet.common.util.log.LogUtil;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "tencent.cos")
@Data
public class TencentObsOperate implements FileObsOperate, InitializingBean {
    /**
     * secretId
     */
    private String secretId;
    /**
     * secretKey
     */
    private String secretKey;
    /**
     * 存储桶地域
     */
    private String region;
    /**
     * 服务端点
     */
    private String endpoint;
    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 存储对象目录路径
     */
    private String fileDirPathKey;
    /**
     * AwsClient 对象
     */
    private AmazonS3 awsClient = null;

    private static final int URL_EXPIRE_HOURS = 2;

    @Override
    public PutObjectResult upload(MultipartFile file, String path) {
        if (Objects.isNull(file)) {
            LogUtil.runInfoLog("文件不存在");
            return null;
        }
        String pathKey = fileDirPathKey + "/" + path;
        try {
            PutObjectRequest request = new PutObjectRequest(bucketName, pathKey, file.getInputStream(), null);
            PutObjectResult putObjectResult = awsClient.putObject(request);
            return putObjectResult;
        } catch (SdkClientException | IOException e) {
            LogUtil.runInfoLog("upload file error,fileUrl is :{};error is {}", path, e);
            return null;
        }
    }

    @Override
    public boolean delete(String fileUrl) {
        // Bucket 的命名格式为 BucketName-APPID ，此处填写的存储桶名称必须为此格式
        // 指定被删除的文件在 COS 上的路径，即对象键。例如对象键为 folder/picture.jpg，则表示删除位于 folder 路径下的文件 picture.jpg
        try {
            String key = getDelKey(fileUrl);
            if (StringUtils.isBlank(key)) {
                return false;
            }
            awsClient.deleteObject(bucketName, key);
        } catch (SdkClientException e) {
            LogUtil.runInfoLog("delete file error,fileUrl is :{};error is {}", fileUrl, e);
            return false;
        }
        return true;
    }

    /**
     * 根据传入的地址，获取删除的key
     *
     * @return https://wallet-1311274267.cos.ap-chengdu.myqcloud.com/demo/tesla_ic_bonnet.png
     */
    private String getDelKey(String fileUrl) {
        if (StringUtils.isBlank(fileUrl) ||
                fileUrl.indexOf(fileDirPathKey) == -1) {
            return "";
        }
        int index = fileUrl.indexOf(fileDirPathKey);
        String key = fileUrl.substring(index);
        return key;
    }

    @Override
    public String getFilePath() {
        return this.getFileHost() + fileDirPathKey;
    }

    @Override
    public String generatePresignedUrl(String fileUrl) {
        if (StringUtils.isBlank(fileUrl)) {
            return "";
        }

        String fileKey = fileUrl;
        String host = this.getFileHost();
        if (fileUrl.startsWith(host)) {
            fileKey = fileUrl.replaceFirst(host, "");
        }

        Date expireTime = DateUtils.addHours(new Date(), URL_EXPIRE_HOURS);
        URL url = awsClient.generatePresignedUrl(bucketName, fileKey, expireTime);
        return url.toString();
    }

    private String getFileHost() {
        return new StringBuffer().append("https://").append(bucketName).append(".").
                append(endpoint).append("/").toString();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (awsClient == null) {
            awsClient = AmazonS3ClientBuilder
                    .standard()
                    .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, region))
                    .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(secretId, secretKey)))
                    .build();
        }
    }

}