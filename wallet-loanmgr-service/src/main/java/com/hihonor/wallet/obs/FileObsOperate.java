package com.hihonor.wallet.obs;

import org.springframework.web.multipart.MultipartFile;

import com.amazonaws.services.s3.model.PutObjectResult;

/**
 * 功能描述 操作文件
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface FileObsOperate {

    PutObjectResult upload(MultipartFile file, String path);

    boolean delete(String fileUrl);

    String getFilePath();

    /**
     * 获取签名的下载地址
     */
    String generatePresignedUrl(String fileUrl);
}
