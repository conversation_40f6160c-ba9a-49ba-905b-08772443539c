package com.hihonor.wallet;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling
@RefreshScope
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.hihonor.wallet")
@ComponentScan("com.hihonor.wallet")
@SpringBootApplication
public class WalletLoanMgrApplication {

    public static void main(String[] args) {
        SpringApplication.run(WalletLoanMgrApplication.class, args);
    }

}
