package com.hihonor.wallet.adapter;

import java.nio.charset.Charset;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

/**
 * 功能描述
 *
 * @since 2024-02-22
 */
@Configuration
public class WebMvcAdapter {
    @Autowired
    private RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    @PostConstruct
    public void init(){
        requestMappingHandlerAdapter.getMessageConverters().forEach(c ->{
            if(c instanceof MappingJackson2HttpMessageConverter){
                ((MappingJackson2HttpMessageConverter)c).setDefaultCharset(Charset.forName("UTF-8"));
            }
        });
    }
}
