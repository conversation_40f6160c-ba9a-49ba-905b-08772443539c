/*
 * Copyright (c) Honor Device Co., Ltd. 2023-2023. All rights reserved.
 */

package com.hihonor.wallet.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import com.hihonor.wallet.common.security.SecurityVerifyIgnore;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.hihonor.wallet.common.client.ContentClient;
import com.hihonor.wallet.common.constant.CommonConstant;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.model.param.configmgr.ContentReq;
import com.hihonor.wallet.common.model.param.configmgr.WiseoperRequestContextUtils;
import com.hihonor.wallet.common.redis.RedisUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 车钥匙管理相关接口
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Api(value = "configmgr.CardKeyController", tags = {"车钥匙管理相关接口"})
@RestController
public class CommonController {

    @Resource
    private ContentClient contentClient;

    @Resource
    private RedisUtil redisUtil;

    /**
     * 管理台上传图片
     *
     * @param file 文件
     * @return ResponseResult
     */
    @ApiOperation(value = "上传图片")
    @PostMapping("/wo/carkey/fileUpload")
    public ResponseResult fileUpload(@Validated MultipartFile file) {
        String account = WiseoperRequestContextUtils.getOperAccount();
        if (!redisUtil.tryLock("file_upload_lock_" + account, 100L, TimeUnit.MILLISECONDS)) {
            return ResponseResult.fail(WalletResultCode.REPLAY_REQUEST_REPEAT);
        }
        List<String> picType = new ArrayList<>();
        picType.add(CommonConstant.FILE_SUFFIX_JPG);
        picType.add(CommonConstant.FILE_SUFFIX_PNG);
        picType.add(CommonConstant.FILE_SUFFIX_GIF);
        picType.add(CommonConstant.FILE_SUFFIX_JPEG);
        picType.add(CommonConstant.FILE_SUFFIX_WEBP);
        List<String> contentTypeList = new ArrayList<>();
        contentTypeList.add(CommonConstant.CONTENT_TYPE_JPG);
        contentTypeList.add(CommonConstant.CONTENT_TYPE_PNG);
        contentTypeList.add(CommonConstant.CONTENT_TYPE_JPEG);
        contentTypeList.add(CommonConstant.CONTENT_TYPE_GIF);
        contentTypeList.add(CommonConstant.CONTENT_TYPE_WEBP);
        if (contentClient.fileTypeValid(file, picType) || contentClient.contentTypeValid(file, contentTypeList)) {
            return ResponseResult.fail(WalletResultCode.FILE_SUFFIX_VALID_ERROR);
        }
        if (file.getSize() > 1048576L) {
            return ResponseResult.fail(WalletResultCode.FILE_SIZE_VALID_ERROR);
        }
        ContentReq req = new ContentReq();
        req.setFiles(new ArrayList<>());
        req.getFiles().add(ContentReq.FileObj.builder().file(file).build());
        return ResponseResult.success(contentClient.uploadFile(req));
    }
}
