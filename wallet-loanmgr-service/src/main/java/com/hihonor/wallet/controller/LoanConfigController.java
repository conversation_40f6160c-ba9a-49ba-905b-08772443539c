/*
 * Copyright (c) Honor Device Co., Ltd. 2023-2024. All rights reserved.
 */

package com.hihonor.wallet.controller;

import java.io.IOException;
import java.util.List;

import com.hihonor.wallet.common.model.dto.magicOper.ApprovalParam;
import com.hihonor.wallet.common.model.param.loan.*;
import com.hihonor.wallet.common.model.param.loanmgr.SearchLoanInfoByAccountParam;
import com.hihonor.wallet.common.security.SecurityVerifyIgnore;
import com.hihonor.wallet.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.service.LoanConfigService;


import io.swagger.annotations.Api;


/**
 * <AUTHOR>
 */
@Api(value = "configmgr.LoanConfigController", tags = {"借贷配置"})
@RestController
@RequestMapping("/wo/loan")
public class LoanConfigController {
    @Autowired
   private LoanConfigService loanConfigService;

    /**
     * 获取api配置的列表
     *
     * @return ResponseResult
     */
    @GetMapping("/getLoanSupplier")
    public ResponseResult<List<LoanSupplierConfigDto>> getLoanSupplier() {
        return ResponseResult.success(loanConfigService.getLoanSupplier());
    }


    /**
     * 获取审批信息
     * @param
     * @return
     */
    @PostMapping("/getApprovalInfo")
    public ResponseResult<ApprovalResponse> getApprovalInfo(@RequestBody @Validated ApprovalParam param) {
        ApprovalResponse approvalResponse = loanConfigService.getApprovalInfo(param);
        return ResponseResult.success(approvalResponse);
    }


    /**
     *  1.1 查询分流比例轮询顺序审批历史
     * @return
     */
    @PostMapping("/getDiversionRatioApprovalHistoryRecords")
    public ResponseResult<DiversionRatioApprovalHistoryRecords> getDiversionRatioApprovalHistoryRecords() {
        return ResponseResult.success(loanConfigService.getDiversionRatioApprovalHistoryRecords());
    }

    /**
     *  发起审批
     * @param applicationApprovalParam
     * @return
     * @throws IOException
     */
    @PostMapping("/applicationApproval")
    public ResponseResult applicationApproval(@Validated ApplicationApprovalParam applicationApprovalParam) throws IOException {
        loanConfigService.applicationApproval(applicationApprovalParam);
        return ResponseResult.success();
    }

    /**
     * 审批
     * @param loanApproveParam
     * @return
     */
    @PostMapping("/approve")
    public ResponseResult loanApprove(@Validated @RequestBody LoanApproveParam loanApproveParam){
        loanConfigService.loanApprove(loanApproveParam);
        return ResponseResult.success();
    }

    @GetMapping("/getWoCurrentUser")
    public ResponseResult getWoCurrentUser() {
        return ResponseResult.success(loanConfigService.getWoCurrentUser());
    }

    @PostMapping("/getSuggestInfoList")
    public ResponseResult<List<SuggestInfo>> getSuggestInfoList() {
        return ResponseResult.success(loanConfigService.getSuggestInfoList());
    }

    /**
     *  1.9 查询H5产品审批历史
     * @return
     */
    @PostMapping("/getSuggestApprovalHistoryRecords")
    public ResponseResult<SuggestListApprovalHistoryRecords> getSuggestListApprovalHistoryRecords() {
        return ResponseResult.success(loanConfigService.getSuggestListApprovalHistoryRecords());
    }

    /**
     *  1.10 查询客户的借贷状态
     * @return
     */

    @PostMapping("/searchLoanInfoByAccount")
    public ResponseResult<UserLoanInfo> searchLoanInfoByAccount(@Validated @RequestBody SearchLoanInfoByAccountParam param)  {
        return ResponseResult.success(loanConfigService.searchLoanInfoByAccount(param));
    }
}
