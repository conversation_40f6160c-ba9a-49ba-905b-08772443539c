package com.hihonor.wallet.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.UrlUtil;
import com.hihonor.wallet.common.util.WoUtils;


/**
 * 功能描述
 *
 * @since 2024-03-13
 */
@RestController
@RequestMapping("/wo/csrf")
public class CsrfTokenController {

    @Resource
    private WoUtils woUtils;

    /**
     * 获取csrfToken
     *
     * @return ResponseResult
     */
    @GetMapping("/getCsrfToken")
    public ResponseResult<String> getCsrfToken(HttpServletRequest request) {
        String token = woUtils.getWoToken(request);
        String encrypt = EncryptUtil.encrypt(token);
        return ResponseResult.success(UrlUtil.getUrlEncoderString(encrypt));
    }
}
