package com.hihonor.wallet.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.model.dto.loan.GetActivityListDto;
import com.hihonor.wallet.common.model.param.loan.CreateActivityParam;
import com.hihonor.wallet.service.CrowdService;

/**
 * 功能描述
 *
 * @since 2024-09-24
 */
@RestController
@RequestMapping("/wo/crowd")
public class CrowdController {

    @Autowired
    private CrowdService crowdService;

    /**
     * 创建活动
     *
     * @return ResponseResult
     */
    @PostMapping("/createActivity")
    public ResponseResult createActivity(@Validated @RequestBody CreateActivityParam param) {
        crowdService.reportCrowdUsageScene(param);
        return ResponseResult.success();
    }

    /**
     * 活动列表
     *
     * @return ResponseResult
     */
    @PostMapping("/getActivityList")
    public ResponseResult<GetActivityListDto> getActivityList() {
        return ResponseResult.success(crowdService.getCrowdActivityList());
    }
}
