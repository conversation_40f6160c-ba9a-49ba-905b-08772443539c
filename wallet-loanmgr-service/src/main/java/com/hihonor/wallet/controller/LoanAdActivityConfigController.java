package com.hihonor.wallet.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.model.dto.loan.LoanAdActivityDto;
import com.hihonor.wallet.common.model.dto.transcard.PageBaseResponse;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityListParam;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;
import com.hihonor.wallet.service.LoanAdActivityConfigService;

import io.swagger.annotations.Api;

/**
 * 实名认证运营配置管理台接口
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Api(value = "configmgr.LoanAdActivityConfigController", tags = {"实名认证运营配置"})
@RestController
@RequestMapping("/wo/loan/loanAdActivity")
public class LoanAdActivityConfigController {

    @Autowired
    private LoanAdActivityConfigService loanAdActivityConfigService;

    /**
     * 新增实名认证页配置
     *
     * @param loanAdActivityParam 认证页配置数据
     * @return
     */
    @PostMapping("/save")
    public ResponseResult saveLoanAdActivity(@Validated @RequestBody LoanAdActivityParam loanAdActivityParam) {
        loanAdActivityConfigService.saveLoanAdActivity(loanAdActivityParam);
        return ResponseResult.success();
    }

    /**
     * 获取实名认证页配置
     *
     * @param loanAdActivityListParam 认证页配置数据
     * @return
     */
    @PostMapping("/list")
    public ResponseResult<PageBaseResponse<LoanAdActivityDto>> getLoanAdActivityList(
        @Validated @RequestBody LoanAdActivityListParam loanAdActivityListParam) {
        PageBaseResponse<LoanAdActivityDto> pageBaseResponse =
            loanAdActivityConfigService.getLoanAdActivityList(loanAdActivityListParam);
        return ResponseResult.success(pageBaseResponse);
    }

}
