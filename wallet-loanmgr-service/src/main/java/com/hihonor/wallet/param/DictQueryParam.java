package com.hihonor.wallet.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 字典配置
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@ApiModel(description = "字典配置查询参数")
public class DictQueryParam{

    /**
     * 字典
     */
    @ApiModelProperty(value = "字典，对应字典表的parent", required = true)
    @NotEmpty
    @Size(max = 100, message = "itemNames参数超长")
    private List<String> itemNames;

}