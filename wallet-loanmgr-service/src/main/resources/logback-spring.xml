<?xml version="1.0" encoding="UTF-8"?>
<!--
    scan：当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
    scanPeriod：设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒；当scan为true时，此属性生效。默认的时间间隔为1分钟。
    debug：当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
-->
<configuration scan="true" scanPeriod="10 seconds" debug="false">
    <!--<contextName>logback</contextName>-->
    <define name="hostName" class="com.hihonor.wallet.common.util.GetHostNameUtil"/>
    <property resource="application.yml"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>

    <!-- 定义日志的脱敏 -->
    <conversionRule conversionWord="msg" converterClass="com.hihonor.wallet.common.util.log.SensitiveDataConverter"/>

    <!-- 定义日志的根目录 -->
    <property name="path" value="/opt/hihonor/logs/wallet-loanmgr-service"/>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 定义日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%green(%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}) %highlight(%-5level) [%blue(%t)] %yellow(%logger{36} %X{threadHexId}-%X{startTimeHex}): %msg%n%throwable"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <!--1. 输出到控制台-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!-- 设置字符集 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--2. 输出到文档-->
    <!-- 2.1 level为 DEBUG 日志，时间滚动输出  -->
    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/debug.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
<!--            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX} [%thread] %-5level %logger{50}:%line - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%method,%line] - %X{traceId} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志归档 -->
            <fileNamePattern>${path}/debug-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 此日志文档只记录debug级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>debug</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 2.2 level为 INFO 日志，时间滚动输出  -->
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/info.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
<!--            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX} [%thread] %-5level %logger{50}:%line - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%method,%line] - %X{traceId} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${path}/info-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 此日志文档只记录info级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 2.3 level为 WARN 日志，时间滚动输出  -->
    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/warn.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
<!--            <pattern>%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%method,%line] - %X{traceId} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${path}/warn-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 此日志文档只记录warn级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 2.4 level为 ERROR 日志，时间滚动输出  -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/error.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - [%method,%line] - %X{traceId} - %msg%n</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${path}/error-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 此日志文档只记录ERROR级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 2.5 接口日志，时间滚动输出  -->
    <appender name="INTERFACE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/interface.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
            <pattern>
                %X{version}|%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}|%X{type}|%X{interfaceName}|%X{sourceId}|%X{destinationId}|%X{responseTime}|%X{flag}|%X{responseCode}|%X{traceId}|%X{deviceId}|%X{userId}|%X{deviceModel}|%X{versionCode}|%X{responseInfo}|%X{requestArgs}|%msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${path}/interface-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 2.6 运行日志，时间滚动输出  -->
    <appender name="RUN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/run.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
            <pattern>
<!--                %d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}|%-5level|%X{serviceName}|%X{traceId}|%X{deviceId}|%X{userId}|[%thread]|%X{interfaceName}|%msg%n-->
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%X{serviceName}] [%thread] [%X{interfaceName}] - %X{deviceId} - %X{userId} - %-5level - [%method,%line] - %X{traceId} - %msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${path}/run-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 2.7 通用日志，时间滚动输出  -->
    <appender name="GENERAL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文档的路径及文档名 -->
        <file>${path}/general.${hostName}.log</file>
        <!--日志文档输出格式-->
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天日志归档路径以及格式 -->
            <fileNamePattern>${path}/general-%d{yyyy-MM-dd-HH}.${hostName}.log</fileNamePattern>
            <!--日志文档保留天数-->
            <maxHistory>180</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 4. 最终的策略 -->
    <!-- 4.1 开发环境:打印控制台-->
    <springProfile name="dev">
        <logger name="com.sdcm.pmp" level="debug"/>
    </springProfile>

    <logger name="INTERFACE_LOG" additivity="false">
        <appender-ref ref="INTERFACE_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="RUN_LOG" additivity="false">
        <appender-ref ref="RUN_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="GENERAL_LOG" additivity="false">
        <appender-ref ref="GENERAL_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="com.alibaba.nacos" additivity="false">
        <level ref="ERROR"/>
    </logger>

    <logger name="com.hihonor.truss" additivity="false">
        <level ref="ERROR" />
    </logger>

    <logger name="com.hihonor.it.apimall" additivity="false">
        <level ref="ERROR"/>
    </logger>

    <logger name="ShardingSphere-SQL" level="WARN"/>

    <root level="info">
        <appender-ref ref="CONSOLE"/>
        <!--<appender-ref ref="DEBUG_FILE"/>-->
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="WARN_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
    </root>

</configuration>
