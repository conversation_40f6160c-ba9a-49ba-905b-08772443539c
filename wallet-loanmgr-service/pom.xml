<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>loan-server</artifactId>
        <groupId>com.hihonor.cloud.wallet</groupId>
        <version>1.0.0</version>
    </parent>

    <artifactId>wallet-loanmgr-service</artifactId>


    <properties>
        <surefireArgLine>
            -Dspring.application.name=wallet-loanmgr-service
        </surefireArgLine>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hihonor.cloud.wallet</groupId>
            <artifactId>wallet-loan-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.hihonor.generator</groupId>
                <artifactId>generator-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>