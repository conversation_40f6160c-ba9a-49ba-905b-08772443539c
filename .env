# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="mysql://root:Wal%23let%40Sql753%21@***********:3306/mock?timezone=Asia%2FShanghai"
CALLBACK_URL="http://card-dev-drcn.inner.wallet.hihonorcloud.com"
NEXT_PUBLIC_BIND_MOCK_QR="https://content-test-drcn.hihonorcdn.com/honorWallet/c607a59dedcb38e10e15332c6650279d_20250724150214.png"
DOMAIN="localhost:3000"
LOAN_DOMAIN="localhost:8080"