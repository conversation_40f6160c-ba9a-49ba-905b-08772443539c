const { io } = require('socket.io-client');

// 测试光标位置功能
async function testCursorPosition() {
  console.log('开始测试光标位置功能...');
  
  const socket = io('http://localhost:3001', {
    transports: ['websocket']
  });

  socket.on('connect', () => {
    console.log('✅ 连接成功');
    
    // 加入会话
    socket.emit('join-session', { 
      sessionId: 'test-session', 
      username: 'test-user' 
    });
  });

  socket.on('disconnect', () => {
    console.log('❌ 连接断开');
  });

  socket.on('users-updated', (users) => {
    console.log('👥 用户列表更新:', users);
  });

  socket.on('cursor-changed', (data) => {
    console.log('📍 收到光标变更:', {
      fileId: data.fileId,
      username: data.username,
      line: data.cursor.line,
      column: data.cursor.column,
      lineType: typeof data.cursor.line,
      columnType: typeof data.cursor.column
    });
  });

  // 等待连接建立
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 测试发送有效的光标数据
  console.log('📤 发送有效光标数据...');
  socket.emit('cursor-change', {
    fileId: 'test-file',
    cursor: {
      line: 5,
      column: 10
    }
  });

  // 测试发送无效的光标数据
  console.log('📤 发送无效光标数据...');
  socket.emit('cursor-change', {
    fileId: 'test-file',
    cursor: {
      line: undefined,
      column: null
    }
  });

  // 测试发送缺失的光标数据
  console.log('📤 发送缺失光标数据...');
  socket.emit('cursor-change', {
    fileId: 'test-file',
    cursor: null
  });

  // 等待一段时间后断开连接
  setTimeout(() => {
    console.log('🔚 测试完成，断开连接');
    socket.disconnect();
    process.exit(0);
  }, 3000);
}

// 运行测试
testCursorPosition().catch(console.error); 