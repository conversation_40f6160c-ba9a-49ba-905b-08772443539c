#!/usr/bin/env node

const { io } = require('socket.io-client');

console.log('🧪 开始测试 WebSocket 连接...');

// 创建 Socket.IO 客户端
const socket = io('http://localhost:3001', {
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000
});

let connectionCount = 0;
let disconnectCount = 0;

// 连接事件
socket.on('connect', () => {
  connectionCount++;
  console.log(`✅ 连接成功 (第 ${connectionCount} 次): ${socket.id}`);
  
  // 加入测试会话
  socket.emit('join-session', {
    sessionId: 'test-session',
    username: 'test-user'
  });
});

// 断开连接事件
socket.on('disconnect', (reason) => {
  disconnectCount++;
  console.log(`❌ 连接断开 (第 ${disconnectCount} 次): ${reason}`);
});

// 连接错误
socket.on('connect_error', (error) => {
  console.error(`🚨 连接错误:`, error.message);
});

// 重连尝试
socket.on('reconnect_attempt', (attemptNumber) => {
  console.log(`🔄 重连尝试 (第 ${attemptNumber} 次)`);
});

// 重连成功
socket.on('reconnect', (attemptNumber) => {
  console.log(`✅ 重连成功 (第 ${attemptNumber} 次尝试)`);
});

// 重连失败
socket.on('reconnect_failed', () => {
  console.log(`❌ 重连失败，已达到最大尝试次数`);
});

// 用户更新事件
socket.on('users-updated', (users) => {
  console.log(`👥 用户列表更新:`, users);
});

// 用户加入事件
socket.on('user-joined', (user) => {
  console.log(`👋 用户加入:`, user);
});

// 用户离开事件
socket.on('user-left', (user) => {
  console.log(`👋 用户离开:`, user);
});

// 文件变更事件
socket.on('file-changed', (change) => {
  console.log(`📝 文件变更:`, change);
});

// 光标变更事件
socket.on('cursor-changed', (cursor) => {
  console.log(`👆 光标变更:`, cursor);
});

// 定期发送测试数据
let testInterval;

socket.on('connect', () => {
  // 每5秒发送一次测试数据
  testInterval = setInterval(() => {
    if (socket.connected) {
      console.log('📤 发送测试数据...');
      socket.emit('file-change', {
        fileId: 'test-file',
        content: `测试内容 ${new Date().toISOString()}`
      });
    }
  }, 5000);
});

// 清理函数
const cleanup = () => {
  if (testInterval) {
    clearInterval(testInterval);
  }
  if (socket.connected) {
    socket.disconnect();
  }
  console.log('\n📊 测试统计:');
  console.log(`连接次数: ${connectionCount}`);
  console.log(`断开次数: ${disconnectCount}`);
  process.exit(0);
};

// 监听进程退出信号
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// 30秒后自动结束测试
setTimeout(() => {
  console.log('\n⏰ 测试时间结束');
  cleanup();
}, 30000);

console.log('⏳ 测试将在30秒后自动结束，或按 Ctrl+C 手动停止'); 