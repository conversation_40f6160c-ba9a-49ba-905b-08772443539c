const { io } = require('socket.io-client');

// 测试光标位置修复
async function testCursorFix() {
  console.log('🔧 开始测试光标位置修复...');

  const socket = io('http://localhost:3001', {
    transports: ['websocket']
  });

  socket.on('connect', () => {
    console.log('✅ 连接成功');

    // 加入会话
    socket.emit('join-session', {
      sessionId: 'test-session',
      username: 'test-user'
    });
  });

  socket.on('disconnect', () => {
    console.log('❌ 连接断开');
  });

  socket.on('cursor-changed', (data) => {
    console.log('📍 收到光标变更:', {
      fileId: data.fileId,
      username: data.username,
      userId: data.userId,
      cursor: {
        line: data.cursor.line,
        column: data.cursor.column,
        lineType: typeof data.cursor.line,
        columnType: typeof data.cursor.column
      }
    });

    // 验证数据格式
    if (typeof data.cursor.line === 'number' && typeof data.cursor.column === 'number') {
      console.log('✅ 光标数据格式正确');
    } else {
      console.log('❌ 光标数据格式错误');
    }
  });

  // 等待连接建立
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 测试发送有效的光标数据
  console.log('📤 发送有效光标数据...');
  socket.emit('cursor-change', {
    fileId: 'test-file',
    cursor: {
      line: 10,
      column: 15
    }
  });

  // 等待一段时间后断开连接
  setTimeout(() => {
    console.log('🔚 测试完成，断开连接');
    socket.disconnect();
    process.exit(0);
  }, 2000);
}

// 运行测试
testCursorFix().catch(console.error); 