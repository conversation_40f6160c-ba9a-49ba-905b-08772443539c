#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 启动多人在线编辑功能...');

// 启动WebSocket服务器
const wsServer = spawn('node', ['server.js'], {
  stdio: 'inherit',
  cwd: path.join(__dirname, '..')
});

wsServer.on('error', (error) => {
  console.error('❌ WebSocket服务器启动失败:', error);
});

wsServer.on('close', (code) => {
  console.log(`WebSocket服务器退出，退出码: ${code}`);
});

// 启动Next.js开发服务器
const nextServer = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  cwd: path.join(__dirname, '..')
});

nextServer.on('error', (error) => {
  console.error('❌ Next.js服务器启动失败:', error);
});

nextServer.on('close', (code) => {
  console.log(`Next.js服务器退出，退出码: ${code}`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  wsServer.kill('SIGINT');
  nextServer.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  wsServer.kill('SIGTERM');
  nextServer.kill('SIGTERM');
  process.exit(0);
});

console.log('✅ 服务器启动完成！');
console.log('📱 访问 http://localhost:3000 开始使用');
console.log('🔌 WebSocket服务器运行在 ws://localhost:3001'); 