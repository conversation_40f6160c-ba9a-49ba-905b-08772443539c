# WebSocket 连接问题诊断指南

## 问题描述
WebSocket服务器启动后一直打印"用户连接"和"用户断开连接"的日志。

## 可能原因

### 1. Next.js 热重载问题
**症状**: 在开发模式下，每次代码保存都会触发组件重新挂载
**解决方案**: 
- 使用 `useRef` 稳定回调函数引用
- 减少 `useEffect` 依赖项
- 添加连接配置参数

### 2. 依赖项过多导致频繁重连
**症状**: `useEffect` 依赖项包含频繁变化的函数
**解决方案**:
- 使用 `useRef` 存储回调函数
- 只依赖稳定的值（如 `sessionId`, `username`）

### 3. 编辑器内容变化触发重连
**症状**: 每次编辑器内容变化都触发连接状态变化
**解决方案**:
- 从 `useEffect` 依赖项中移除 `sendFileChange`
- 使用防抖处理文件变更

## 修复措施

### 1. 已修复的问题
- ✅ 使用 `useRef` 稳定回调函数引用
- ✅ 减少 `connect` 函数的依赖项
- ✅ 添加 Socket.IO 重连配置
- ✅ 移除 `sendFileChange` 从依赖项
- ✅ 增强服务器端日志记录

### 2. 新增功能
- ✅ 连接统计信息
- ✅ 详细的连接日志
- ✅ 错误处理和诊断
- ✅ 测试脚本

## 测试步骤

### 1. 启动服务器
```bash
npm run start:collaboration
```

### 2. 运行测试脚本
```bash
npm run test:websocket
```

### 3. 观察日志
正常情况下的日志应该显示：
```
用户连接: [socket-id] (总连接数: 1)
用户 [socket-id] 加入会话: [session-id], 用户名: [username]
会话 [session-id] 当前用户数: 1
```

### 4. 检查连接稳定性
- 连接次数应该接近断开次数
- 不应该有频繁的连接/断开循环
- 重连次数应该很少

## 常见问题解决

### 问题1: 频繁的连接/断开
**原因**: Next.js 热重载或依赖项问题
**解决**: 检查 `useEffect` 依赖项，确保只依赖稳定值

### 问题2: 连接超时
**原因**: 网络问题或服务器配置
**解决**: 检查 `pingTimeout` 和 `pingInterval` 配置

### 问题3: CORS 错误
**原因**: 跨域配置问题
**解决**: 确保 CORS 配置正确，允许 `http://localhost:3000`

### 问题4: 端口冲突
**原因**: 端口被占用
**解决**: 检查端口 3001 是否可用，或修改端口配置

## 调试技巧

### 1. 使用测试脚本
```bash
npm run test:websocket
```

### 2. 检查浏览器控制台
- 查看 WebSocket 连接状态
- 检查是否有错误信息
- 观察连接事件

### 3. 检查服务器日志
- 观察连接和断开日志
- 检查错误信息
- 监控连接统计

### 4. 网络调试
- 使用浏览器开发者工具的 Network 标签
- 查看 WebSocket 连接状态
- 检查请求和响应

## 性能优化建议

### 1. 减少不必要的重连
- 使用 `useRef` 稳定引用
- 减少依赖项
- 添加防抖处理

### 2. 优化连接配置
```javascript
{
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
  reconnectionDelayMax: 5000,
  timeout: 20000
}
```

### 3. 监控连接状态
- 添加连接统计
- 记录连接历史
- 设置告警阈值

## 预期行为

修复后的正常行为：
1. 页面加载时建立一次连接
2. 连接保持稳定，不会频繁断开
3. 只在组件真正卸载时才断开连接
4. 热重载时连接保持稳定
5. 网络问题时自动重连

如果仍然有问题，请运行测试脚本并提供日志信息。 