/** @type {import('next').NextConfig} */
const nextConfig = {
	output: 'standalone',
	// ... 其他配置
	typescript: {
		// !! WARN !!
		// Dangerously allow production builds to successfully complete even if
		// your project has type errors.
		// !! WARN !!
		ignoreBuildErrors: true,
	},
	// ... 其他配置
	eslint: {
		// Warning: This allows production builds to successfully complete even if
		// your project has ESLint errors.
		ignoreDuringBuilds: true,
	},
	swcMinify: false, // 设置为 false 来禁用 SWC 压缩，会回退到 Terser
	basePath: '/mock'
}

module.exports = nextConfig
