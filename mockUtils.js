import fs from "fs";
import path from "path";

export default async function getMockData(url) {
  const filename = url.substring(1).replace(/\//g, "_");
  console.log(filename);
  const mockPath = `./mock/${filename}.json`;
  if (!fs.existsSync(path.dirname(mockPath))) {
    fs.mkdirSync(path.dirname(mockPath), { recursive: true });
  }
  if (!fs.existsSync(mockPath)) {
    fs.writeFileSync(mockPath, JSON.stringify({}), "utf8");
  }
  const data = fs.readFileSync(mockPath, "utf8");
  return JSON.parse(data);
}
