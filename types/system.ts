export interface System {
  key: number;
  label: string;
  category: 'mock' | 'inspection' | 'fileChange';
  tutorialUrl?: string;
}

export const systems: System[] = [
  {
    key: 0,
    label: "云测环境",
    category: "mock",
    tutorialUrl: "https://elink.e.hihonor.com/wiki/Au9Uw33bTiK2Kakg0azlpeuArwe"
  },
  {
    key: 1,
    label: "虚拟CP",
    category: "mock",
    tutorialUrl: "https://elink.e.hihonor.com/wiki/S0o0wKcP4iyyJCk8R2llIGkkrxc"
  },
  {
    key: 2,
    label: "日常巡检数据分析",
    category: "inspection"
  },
  {
    key: 3,
    label: "配置变更",
    category: "fileChange"
  }
]; 