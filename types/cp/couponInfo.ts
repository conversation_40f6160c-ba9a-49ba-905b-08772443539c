export interface CouponInfo {
  status: CouponStatusInfo;
}

export interface CouponStatusInfo {
  couponList: Coupon[];
}

export interface CouponRule {
  field: 'loanAmount' | 'termNums' | 'repayMethod';  // 规则字段
  operator: 'gt' | 'gte' | 'lt' | 'lte' | 'eq';     // 操作符
  value: string;                                     // 规则值
}

export interface Coupon {
  couponNo?: string;           // 优惠券编号
  couponBatchNo?: string;     // 优惠券批次号
  presetCouponType?: PresetCouponTypeEnum;   // 预设券类型
  couponName?: string;         // 优惠券名称
  couponType?: number;         // 优惠券类型
  status?: number;             // 状态
  startTime?: number;          // 开始时间
  endTime?: number;            // 结束时间
  discountAmount: number;
  unusableReason: string;
  useRules?: CouponRule[];     // 使用规则列表
  receiveTime: number;
  usedTime: number;
}

export enum CouponTypeEnum {
  // 放款券
  LOAN = 1,

  // 还款券
  REPAY = 2,
}

export const couponTypeMap = new Map<number, string>([
  [CouponTypeEnum.LOAN, "放款券"],
  [CouponTypeEnum.REPAY, "还款券"],
]);

export enum CouponStatusEnum {
  // 未使用
  UNUSED = 1,

  // 已使用
  USED = 2,

  // 已过期
  EXPIRED = 3,
}

export const couponStatusMap = new Map<number, string>([
  [CouponStatusEnum.UNUSED, "未使用"],
  [CouponStatusEnum.USED, "已使用"],
  [CouponStatusEnum.EXPIRED, "已过期"],
]);

export enum PresetCouponTypeEnum {
  // 7天免息券
  SEVEN_DAY_INTEREST_FREE = 1,
}

export const presetCoupons: Coupon[] = [
  {
    couponNo: crypto.randomUUID(),
    presetCouponType: PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE,
    couponName: "7天免息券",
    couponType: CouponTypeEnum.LOAN,
    discountAmount: 0,
    unusableReason: "",
    receiveTime: Date.now(),
    usedTime: 0
  }
];