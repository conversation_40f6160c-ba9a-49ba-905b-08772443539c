export interface BankCardInfo {
  status: BankCardStatus;
  action: BankCardAction;
}

export interface BankCardStatus {
  bankCardList: BankCard[];
}

export interface BankCard {
  /** 绑卡Id */
  bankCardId: string;
   /** 银行卡号（卡号后四位） */
  bankCardNo: string;
  /** 银行名称 */
  bankName: string;
  /** 银行代码 */
  bankCode?: string;
  /** 卡类型，1-借记卡，2-信用卡 */
  cardType?: number;
  /** 单日限额,单位：分 */
  dayLimit?: number;
  /** 单笔限额，单位：分 */
  singleLimit?: number;
  /** 状态 */
  status: BankCardBindEnum;
}

export interface BankCardAction {
  bandStatus: CardBandStatusEnum;
}


export enum CardBandStatusEnum {
  // 通过
  PASSED = 1,
  // 拒绝
  REJECTED = 2,
}

export enum BankCardBindEnum {
  // 未绑定
  NOT_BOUND = 1,
  // 已绑定
  BOUND = 2,
}

export const cardBandStatusNameMap = new Map<number, string>([
  [CardBandStatusEnum.PASSED, "通过"],
  [CardBandStatusEnum.REJECTED, "拒绝"],
]);