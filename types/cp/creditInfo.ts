export interface CreditStatus {
  applyNo: string;
  outOrderNo: string;
  applyStatus: number;
  refuseCode?: string;
  refuseMsg?: string;
  refuseMsgData?: string;
  creditLimit: number;
  remainLimit: number;
  applyTime: number;
  productInfos: ProductInfo[];
  tempLimitInfos?: TempLimitInfo[];
  tempRateInfos?: TempRateInfo[];
  creditStatus: CreditStatusEnum;
  limitUseErrStatus?: number;
  greyExpireTime?: number;
  userCancelStatus: UseCancelStatusEnum;
}

export interface ProductInfo {
  dayRate?: string;
  monthRate?: string;
  apr?: string;
  repayMethod: RepayMethodEnum;
  earlyRepay: boolean;
  termNums: number[];
  termPriceInfos?: TermPriceInfoDto[];
  tempDayRate?: string;
  tempApr?: string;
  tempPriceDueTime?: string;
  isDynamicRate?:boolean;
}

export interface TempLimitInfo {
  id: string;
  tempCreditLimit: number;
  tempAvailableLimit: number;
  tempLimitValidTime: string;
  adjustTime?: Date;
}

export interface TempRateInfo {
  id: string;
  tempAdjustRate: string;
  repayMethod: RepayMethodEnum;
  tempLimitValidTime: string;
  adjustTime?: Date;
}

/** 还款方式 */
export enum RepayMethodEnum {
  /** 1-等额本息(灵活还) */
  EqualPrincipalAndInterest = 1,
  /** 2-等额本金(灵活还) */
  EqualPrincipal = 2,
  /** 3-先息后本(灵活还) */
  InterestFirst = 3,
  /** 4-等额本息(固定期限) */
  FixedTerm = 4,
}

export interface TermPriceInfoDto {
  termNum: number;
  dayRate: string;
  monthRate: string;
  apr: string;
  tempDayRate: string;
  tempApr: string;
  tempPriceDueTime: string;
}

export enum CreditApplyStatusEnum {
  // 审核中
  PENDING = 1,
  // 通过
  PASSED = 2,
  // 拒绝
  REJECTED = 3,
}

export const creditApplyStatusNameMap = new Map<number, string>([
  [1, "审核中"],
  [2, "通过"],
  [3, "拒绝"],
]);

export interface CreditActions {
  // 授信结果
  creditApplyStatus: CreditApplyStatusEnum;
  creditLimit: number;
  refuseCode?: CreditRefuseCodeEnum;
  refuseMsg?: string;
  autoCallback?: boolean;
  callBackDelay: number;
}

export interface CreditInfo {
  // 当前状态
  status: CreditStatus;

  // 状态模拟
  actions: CreditActions;
}

// 授信拒绝原因枚举
export enum CreditRefuseCodeEnum {
  APPLY_FAILED = "200201", // 申请失败，您的申请评估未通过
  AGE_NOT_MET = "200213", // 年龄不满足借贷服务要求
  SYSTEM_ERROR = "200215", // 系统异常
}

// 授信拒绝原因描述
export const creditRefuseMsgMap = new Map([
  [CreditRefuseCodeEnum.APPLY_FAILED, "申请失败，您的申请评估未通过"],
  [CreditRefuseCodeEnum.AGE_NOT_MET, "年龄不满足借贷服务要求"],
  [CreditRefuseCodeEnum.SYSTEM_ERROR, "系统异常"],
]);

// 额度状态 1-正常，2-审核中，3-失效，4-拒绝
export enum CreditStatusEnum {
  NORMAL = 1,
  PENDING = 2,
  EXPIRED = 3,
  REJECTED = 4,
}

export const creditStatusNameMap = new Map<number, string>([
  [1, "正常"],
  [2, "审核中"],
  [3, "失效"],
  [4, "拒绝"],
]);

// 用户注销状态
// 0-正常，1-已注销
export enum UseCancelStatusEnum {
  NORMAL = 0,
  LOGOFF = 1,
}

export const userCancelStatusNameMap = new Map<number, string>([
  [0, "正常"],
  [1, "已注销"],
]);
