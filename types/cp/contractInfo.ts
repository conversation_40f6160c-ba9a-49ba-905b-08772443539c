export interface ContractInfo {
  status: ContractStatus;
}

export interface ContractStatus {
  contractList: Contract[];
}

export interface Contract {
  id: string;
  contractName: string;
  defaultChecked: DefaultCheckedEnum;
  forceRead: ForceReadEnum;
  contractUrl?: string;
  contractContent?: string;
  businessType: BusinessTypeEnum;
  isRead?: boolean;
}

export enum DefaultCheckedEnum {
  // 不勾选
  UNCHECKED = 0,
  // 勾选
  CHECKED = 1,
}

export enum ForceReadEnum {
  // 默认不强制
  NOT_FORCE_READ = 0,
  // 强制用户阅读
  FORCE_READ = 1,
}

//协议类型，1-授信，2-绑卡，3-借款，4-用信补录
export enum BusinessTypeEnum {
  CREDIT = 1,
  BIND_CARD = 2,
  LOAN = 3,
  REOFFER = 4,
}
