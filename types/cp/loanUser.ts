import { JsonValue } from "type-fest";
import { BankCardInfo } from "@/types/cp/bankCard";
import { LoanInfo } from "@/types/cp/loanInfo";
import { CreditInfo } from "@/types/cp/creditInfo";
import { CouponInfo } from "./couponInfo";
import { ClearCertificateInfo } from "@/types/cp/clearCertificateInfo";
import { ContractInfo } from "@/types/cp/contractInfo";
import { LogoffInfo } from "@/types/cp/logoffInfo";
import { InterfaceResult } from "@/types/cp/interfaceResult";

export interface LoanUser {
  id?: number;
  userId: string;
  realName: string;
  ctfCode: string;
  mobileNo: string;
  creditInfo: CreditInfo;
  bankCardInfo: BankCardInfo;
  loanInfo: LoanInfo;
  couponInfo?: CouponInfo;
  clearCertificateInfo?: ClearCertificateInfo;
  contractsInfo?: ContractInfo;
  logoffInfo?: LogoffInfo;
  relatedUserIds?: number[];
  interfaceResults?: InterfaceResult[];
  isFavorite?: boolean;
  createTime: bigint;
  cp: string;
  remark?: string;
}

export const loanUserStatusNameMap = {
  // 已准入未授信
  0: "已准入未授信",
  1: "审核中",
  2: "授信通过",
  3: "授信拒绝",
};

export const limitStatusNameMap = {
  1: "正常",
  2: "审核中",
  3: "失效",
  4: "拒绝",
};

export interface InterfaceLog {
  id?: number;
  userId: number;
  interfaceName: string;
  requestData: JsonValue;
  responseData: JsonValue;
  createTime: Date;
}