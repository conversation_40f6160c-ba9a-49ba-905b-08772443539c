import { RepayMethodEnum } from "@/app/cp/creditInfoQuery";

export interface LoanInfo {
  status: LoanInfoStatus;
  action: LoanInfoAction;
}

interface LoanInfoStatus {
  loanApplyRecords: LoanRecordDetail[];
  loanOrders: LoanOrderDetail[];
  repayRecords: RepayRecord[];
}

interface LoanInfoAction {
  loanApplyStatus: LoanApplyStatusEnum;
  verifyList: LoanVerifyItemEnum[];
  /** 拒绝原因码 */
  refuseCode?: LoanErrorCodeEnum;
  /** 拒绝原因说明 */
  refuseMsg?: string;
  /** 是否自动触发回调 */
  autoCallback?: boolean;
  // 延时时间
  callBackDelay: number;
  // 身份证正面识别结果
  idCardFrontResult: IdCardVerifyResultEnum;
  // 身份证反面识别结果
  idCardBackResult: IdCardVerifyResultEnum;
  // 试算结果
  trialStatus?: TrialStatusEnum;
  // 试算失败错误码
  trialErrorCode?: number;
  // 试算失败错误信息
  trialErrorMsg?: string;
}

export enum LoanApplyStatusEnum {
  /** 初始状态（已交易鉴权未提交，CP订单还在有效期） */
  INIT = 0,
  /** 审核中 */
  PENDING = 1,
  /** 成功 */
  SUCCESS = 2,
  /** 失败（申请审核失败） */
  FAILED = 3,
  /** 拒绝 */
  REJECTED = 4,
  /** 取消（放款失败） */
  CANCELED = 5,
  /** 需鉴权（用信补录，补签协议后再提交借款） */
  NEED_AUTH = 6,
  /** 已交易鉴权未提交，CP缓存订单失效 */
  CACHE_EXPIRED = 7,
}

export const loanApplyStatusNameMap = new Map<number, string>([
  [1, "审核中"],
  [2, "成功"],
  [3, "失败"],
  [4, "拒绝"],
  [5, "取消"],
  [6, "需鉴权"],
  [7, "已交易鉴权未提交，CP缓存订单失效"],
]);

export enum LoanVerifyItemEnum {
  /** 活体，通过fileInfos传给渠道 */
  FACE_CHECK = "FACE_CHECK",

  /** 身份证OCR，通过fileInfos传给渠道 */
  ID_CARD_OCR = "ID_CARD_OCR",

  /** 短信验证，发送短信验证码，完成短信验证码验证 */
  SMS = "SMS",

  /** 联系人，通过relationInfos传给渠道 */
  BASICINFO_FILL = "BASICINFO_FILL",

  /** 联系人2，通过relationInfos传给渠道（联系人2） */
  BASICINFO_FILL2 = "BASICINFO_FILL2",

  /** 居住地，通过residentialAddr传给渠道（会返回省+市+县+详细地址） */
  RESIDENTIAL_ADDR = "RESIDENTIAL_ADDR",

  /** 家庭住址，通过familyAddr传给渠道（会返回省+市+县+详细地址），如果需要详细地址，需要同时下发RESIDENTIAL_ADDR和FAMILY_ADDR */
  FAMILY_ADDR = "FAMILY_ADDR",

  /** 教育程度，通过education传给渠道 */
  EDUCATION = "EDUCATION",

  /** 工作单位，通过company传给渠道 */
  COMPANY = "COMPANY",

  /** 职业收入，通过career，income传给渠道 */
  CAREER_INCOME = "CAREER_INCOME",

  /** 职业，通过career传给渠道 */
  CAREER = "CAREER",

  /** 婚姻状态，通过marriage传给渠道 */
  MARRIAGE = "MARRIAGE",

  /** 通用征信授权，获取借款协议，并完成阅读和同意 */
  AGREEMENT_COMMON = "AGREEMENT_COMMON",

  /** 借款协议，获取借款协议，并完成阅读和同意 */
  AGREEMENT_LOAN = "AGREEMENT_LOAN",
}

export enum LoanStatusEnum {
  /** 申请中 */
  APPLYING = 1,
  /** 正常还款中 */
  REPAYING = 2,
  /** 已逾期 */
  OVERDUE = 3,
  /** 已结清 */
  CLEARED = 4,
  /** 借款失败 */
  FAILED = 5,
}

// 借款申请
export interface LoanRecordDetail {
  // 荣耀方用户id
  userId: string;
  // 荣耀侧借款申请订单号
  applyNo: string;
  /** 渠道方借款申请流水号(已交易鉴权未提交场景无需返回） */
  outOrderNo?: string;
  /** 申请状态
   * 0 - 初始状态（已交易鉴权未提交，CP订单还在有效期）
   * 1 - 审核中
   * 2 - 成功
   * 3 - 失败（申请审核失败）
   * 4 - 拒绝
   * 5 - 取消（放款失败）
   * 6 - 需鉴权（用信补录，补签协议后再提交借款）
   * 7 - (已交易鉴权未提交，CP缓存订单失效）
   */
  applyStatus: LoanApplyStatusEnum;
  /** 借款金额，单位：分 */
  loanAmount: number;

  /**
   * 还款方式
   * 1-等额本息(灵活还)
   * 2-等额本金(灵活还)
   * 3-先息后本(灵活还)
   * 4-等额本息(按期还)
   */
  repayMethod: number;

  /** 借款期数 */
  totalTerm: number;

  /** 绑卡Id */
  bankCardId: string;

  /**
   * 借款用途
   * RCXF - 个人日常消费
   * ZX - 房屋装修
   * LY - 旅游出行
   * JX - 在职深造
   * JKYL - 健康医疗
   * Others - 其他消费
   */
  loanUse: string;

  /** 优惠券id（可选） */
  couponNo?: string;

  /** 联合建模模型分（Map<key,value>的json字符串，可选） */
  apiModelScoreMap?: string;

  /** 用户标签（Map<key,value>的json字符串，可选） */
  apiUserTagMap?: string;
  /** 借款申请时间，毫秒 (已交易鉴权未提交场景无需返回） */
  applyTime: number;
  /** 拒绝原因码（失败必传） */
  refuseCode?: string;
  /** 拒绝原因说明（失败必传） */
  refuseMsg?: string;
  /** 拒绝具体原因说明（失败必传） */
  refuseMsgData?: string;
  /** 拒绝管控期，单位：天 */
  refuseControlDays?: number;
  /** 需鉴权列表，参照1.4.4借款交易鉴权项
   * （状态是审核中，如返回有鉴权列表，需完成鉴权后再提交借款）
   */
  verifyList?: string[];
  /** 机构名称，多个资方以"&"分割 */
  institutionNames?: string;
}

export interface LoanOrderDetail {
  // 荣耀方用户id
  userId: string;
  /** 荣耀侧借款申请订单号 */
  applyNo: string;
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 借款申请日期，yyyyMMdd */
  applyDate: string;
  /** 借款申请时间，毫秒 */
  applyTime: number;
  /** 借款起息日 */
  effectiveDate?: string;
  /** 借款金额，单位：分 */
  loanAmount: number;
  /** 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败 */
  status: LoanStatusEnum;
  /** 借款期数 */
  totalTerm: number;
  /** 还款方式 */
  repayMethod: RepayMethodEnum;
  /** 借款来源，appId */
  loanSource?: string;
  /** 机构名称，多个资方以"&"分割 */
  institutionNames?: string;
  /** 结清时间(未结清：0)，毫秒 */
  clearTime: number;
  /** 已还总金额，单位：分 */
  paidAmount: number;
  /** 已还本金总额，单位：分 */
  paidPrinAmount: number;
  /** 已还利息总额，单位：分 */
  paidInterAmount: number;
  /** 已还服务费总额，单位：分 */
  paidServiceFee: number;
  /** 已还罚息，单位：分 */
  paidPenalty: number;
  /** 收款卡号（后四位） */
  bindCardNo: string;
  /** 收款卡发卡行code */
  bindBankCode: string;
  /** 收款卡发卡行名称 */
  bindBankName: string;
  /** 优惠券id */
  couponNo?: string;
  /** 日利率 */
  dayRate: string;
  /** 月利率 */
  monthRate: string;
  /** 年利率 */
  apr: string;
  /** 优惠金额，单位：分 */
  reductionAmount: number;
  /** 提前还款违约金，单位：分 */
  prePenalty: number;
  /** 还款计划列表，借款失败可不传 */
  repayPlanTerms?: RepayPlanTermDto[];
  /** 已签署借款协议列表，借款失败可不传 */
  contractList?: ContractDto[];
  /** 借据逾期天数，有逾期必传 */
  overdueDays?: number;
  /** 借据逾期未还金额，有逾期必传 */
  overdueAmount?: number;
  /** 是否需要重新签约 */
  needResign?: boolean;
  /** 重新签约检查结果 */
  resignCheckResult?: boolean;
  /** 重新签约检查失败错误码 */
  resignCheckErrorCode?: number;
  /** 重新签约检查失败错误信息 */
  resignCheckErrorMsg?: string;
  /** 支持的还款类型列表，不支持还款时返回空列表 */
  supportRepayType?: number[];
  /** 创建时间 */
  createTime: number;
  // 还款结果
  repayResultStatus: RepaySubmitEnum;
  // 是否自动触发回调
  autoCallback: boolean;
  // 回调延时时长
  callBackDelay: number;
  // 失败原因
  refuseCode: RepayErrorCodeEnum;
  // 是否可开具结清证明
  canOpenClearCertificate: boolean;
  // 不可开具结清证明错误码
  notOpenClearCertificateCode?: string;
  // 不可开具结清证明错误信息
  notOpenClearCertificateMsg?: string;
  // 还款查询结果
  repayQueryResult: RepayStatusEnum;
  // 还款查询错误码
  repayQueryResultErrorCode?: number;
  // 还款查询错误信息
  repayQueryResultErrorMsg?: string;
}

export interface RepayRecord {
  /** 荣耀侧还款交易流水号 */
  repayNo: string;
  /** 渠道方还款交易订单号 */
  outRepayNo?: string;
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 还款状态 */
  repayStatus: RepayStatusEnum;
  /** 返回失败原因，失败时必传 */
  repayResult?: string;

  /** 还款金额,单位：分，还款成功必传 */
  repayAmount?: number;

  /** 优惠券减免金额，单位：分，还款成功有优惠时必传 */
  reductionAmount?: number;

  /** 实际还款时间，毫秒，还款成功必传 */
  repayTime?: number;

  /** 还款期次明细，还款成功必传 */
  repayTerms?: RepayTermDto[];

  /** 临时还款订单 */
  tempRepayOrder?: LoanOrderDetail;
}

export interface RepayTermDto {
  /** 期次 */
  termNo: number;

  /** 本期还款总额,单位：分 */
  termAmount: number;

  /** 本期还款本金，单位：分 */
  termPrincipal: number;

  /** 本期还款利息，单位：分 */
  termInterest: number;

  /** 本期还款优惠券减免金额，单位：分 */
  termReductionAmount: number;

  /** 本期还款罚息 */
  termPenalty: number;

  /** 本期还款本金罚息，单位：分 */
  termPrinPenalty: number;

  /** 本期还款利息罚息，单位：分 */
  termInterPenalty: number;

  /** 本期还款逾期费 */
  termOverdueFee: number;

  /** 本期还款违约金 */
  termViolateFee: number;

  /** 本期还款服务费 */
  termServiceFee: number;

  /** 逾期天数（可选） */
  overdueDays?: number;
}

export interface ContractDto {
  /** 协议ID */
  contractId: string;
  /** 合同名称 */
  contractName: string;
  /** 合同地址 */
  contractUrl: string;
}

export interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 当期状态 1-已结清，2-待还款，3-部分已还，4-逾期，5-宽限期 */
  termStatus: TermStatusEnum;
  /** 还款类型 1-主动还款，2-银行卡代扣 */
  repayCategory?: number;
  /** 本期应还总额,单位：分 */
  termAmount: number;
  /** 本期应还本金，单位：分 */
  termPrincipal: number;
  /** 本期应还利息，单位：分 */
  termInterest: number;
  /** 本期应还罚息，单位：分 */
  termPenalty: number;
  /** 本期应还本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期应还利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期应还逾期费，单位：分 */
  termOverdueFee: number;
  /** 本期应还服务费，单位：分 */
  termServiceFee: number;
  /** 本期应还违约金，单位：分 */
  termViolateFee: number;
  /** 本期优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期实际还款日，yyyyMMdd，有还款时必传 */
  paidTime?: number;
  /** 本期实还总额，单位：分，有还款时必传 */
  paidTermAmount?: number;
  /** 本期实还本金，单位：分，有还款时必传 */
  paidTermPrincipal?: number;
  /** 本期实还利息，单位：分，有还款时必传 */
  paidTermInterest?: number;
  /** 本期实还优惠券额，单位：分，有还款时必传 */
  paidTermReductionAmount?: number;
  /** 本期实还罚息，单位：分，有还款时必传 */
  paidTermPenalty?: number;
  /** 本期实还本金罚息，单位：分，有还款时必传 */
  paidTermPrinPenalty?: number;
  /** 本期实还利息罚息，单位：分，有还款时必传 */
  paidTermInterPenalty?: number;
  /** 本期实还逾期费，单位：分，有还款时必传 */
  paidTermOverdueFee?: number;
  /** 本期实还服务费，单位：分，有还款时必传 */
  paidTermServiceFee?: number;
  /** 本期实还违约金，单位：分，有还款时必传 */
  paidTermViolateFee?: number;
  /** 剩余应还还款金额，单位：分，有待还时必传 */
  payableTermAmount?: number;
  /** 剩余应还还款本金，单位：分，有待还时必传 */
  payableTermPrincipal?: number;
  /** 剩余应还还款利息，单位：分，有待还时必传 */
  payableTermInterest?: number;
  /** 剩余应还罚息，单位：分，有待还时必传 */
  payableTermPenalty?: number;
  /** 剩余应还本金罚息，单位：分，有待还时必传 */
  payableTermPrinPenalty?: number;
  /** 剩余应还利息罚息，单位：分，有待还时必传 */
  payableTermInterPenalty?: number;
  /** 剩余应还逾期费，单位：分，有待还时必传 */
  payableTermOverdueFee?: number;
  /** 剩余应还服务费，单位：分，有待还时必传 */
  payableTermServiceFee?: number;
  /** 剩余应还违约金，单位：分，有待还时必传 */
  payableTermViolateFee?: number;
  /** 逾期天数，有逾期必传 */
  overdueDays?: number;
  /** 逾期金额，单位：分，有逾期必传 */
  overdueAmount?: number;
  /** 是否提前还款 */
  preRepay?: boolean;
  /** 是否逾期 */
  overdue: boolean;
  /** 优惠信息说明 */
  reductionAmountDesc?: string;
}

export const repayTypes = [
  { key: 1, label: "提前还款" },
  { key: 2, label: "全部结清" },
  { key: 3, label: "还本期" },
  { key: 4, label: "还部分逾期" },
  { key: 5, label: "还逾期" },
];

export enum RepayTypeEnum {
  // 提前还款
  PrePayment = 1,

  // 全部结清
  FullSettlement = 2,

  // 还本期
  PayThisTerm = 3,

  // 还部分逾期
  PartialOverdue = 4,

  // 还逾期
  Overdue = 5,
}

export enum TermStatusEnum {
  // 已结清
  Settled = 1,
  // 待还款
  Unpaid = 2,
  // 部分已还
  PartialPaid = 3,
  // 逾期
  Overdue = 4,
  // 宽限期
  GracePeriod = 5,
}

export enum RepaySubmitEnum {
  // 提交成功
  Success = 2,
  // 提交失败
  Failed = 4,
}

export enum RepayStatusEnum {
  // 还款中
  Repaying = 1,
  // 还款成功
  Success = 2,
  // 还款失败
  Failed = 4,
}

export enum LoanErrorCodeEnum {
  /** 借款失败，您的申请评估未通过 */
  LOAN_REJECTED = "201003",
  /** 信息不完整，请重新上传 */
  INFO_INCOMPLETE_1 = "201007",
  /** 证件审核不通过，请重新上传 */
  ID_VERIFICATION_FAILED = "201008",
  /** 信息不完整，请重新上传 */
  INFO_INCOMPLETE_2 = "201012",
  /** ⽤⼾流程状态异常 */
  USER_PROCESS_STATUS_ERROR = "201001",
  /** 用户不存在 */
  USER_NOT_EXIST = "200002",
  /** 可用额度不足，无法借款 */
  INSUFFICIENT_QUOTA = "200802",
  /** 参数错误 */
  PARAM_ERROR = "201016",
  /** 人脸比对失败，请重新上传 */
  FACE_VERIFICATION_FAILED = "201020",
  /** 不支持当前期数，请更换后重试 */
  PERIOD_NOT_SUPPORTED = "201024",
  /** 贷款金额过小，请重新输入 */
  LOAN_AMOUNT_TOO_SMALL = "201025",
  /** 银行卡校验失败，请重新绑卡 */
  BANK_CARD_VERIFICATION_FAILED = "201026",
  /** 系统异常 */
  SYSTEM_ERROR = "201028",
  /** 当前订单不支持该优惠券 */
  COUPON_NOT_SUPPORTED = "201029",
  /** 订单已失效，请重新发起⼀笔新的订单 */
  ORDER_EXPIRED = "201030",
}

export const LoanErrorMessageMap: Record<LoanErrorCodeEnum, string> = {
  [LoanErrorCodeEnum.LOAN_REJECTED]: "借款失败，您的申请评估未通过",
  [LoanErrorCodeEnum.INFO_INCOMPLETE_1]: "信息不完整，请重新上传",
  [LoanErrorCodeEnum.ID_VERIFICATION_FAILED]: "证件审核不通过，请重新上传",
  [LoanErrorCodeEnum.INFO_INCOMPLETE_2]: "信息不完整，请重新上传",
  [LoanErrorCodeEnum.USER_PROCESS_STATUS_ERROR]: "⽤⼾流程状态异常",
  [LoanErrorCodeEnum.USER_NOT_EXIST]: "用户不存在",
  [LoanErrorCodeEnum.INSUFFICIENT_QUOTA]: "可用额度不足，无法借款",
  [LoanErrorCodeEnum.PARAM_ERROR]: "参数错误",
  [LoanErrorCodeEnum.FACE_VERIFICATION_FAILED]: "人脸比对失败，请重新上传",
  [LoanErrorCodeEnum.PERIOD_NOT_SUPPORTED]: "不支持当前期数，请更换后重试",
  [LoanErrorCodeEnum.LOAN_AMOUNT_TOO_SMALL]: "贷款金额过小，请重新输入",
  [LoanErrorCodeEnum.BANK_CARD_VERIFICATION_FAILED]:
    "银行卡校验失败，请重新绑卡",
  [LoanErrorCodeEnum.SYSTEM_ERROR]: "系统异常",
  [LoanErrorCodeEnum.COUPON_NOT_SUPPORTED]: "当前订单不支持该优惠券",
  [LoanErrorCodeEnum.ORDER_EXPIRED]: "订单已失效，请重新发起⼀笔新的订单",
};

export enum RepayErrorCodeEnum {
  // 存在未结束的还款订单
  REPAYMENT_IN_PROGRESS = "201708",

  // 重复还款
  REPAYMENT_DUPLICATE = "201702",

  // 支付失败，请确认信息后重试
  PAYMENT_FAILED = "201714",

  // 未查询到相关交易
  TRANSACTION_NOT_FOUND = "201705",

  // 系统异常
  SYSTEM_ERROR = "200001",

  // 余额不足，请使用其他支付方式
  INSUFFICIENT_BALANCE = "201718",

  // 本期借据已结清，请勿重复还款
  LOAN_SETTLED = "201701",

  // 新锁期借据部分，已被拦截
  LOCKED_LOAN_INTERCEPTED = "201709",

  // 无效操作
  INVALID_OPERATION = "200004",

  // 还款金额过大
  REPAYMENT_AMOUNT_TOO_LARGE = "201706",
}

export const RepayErrorMessageMap: Record<RepayErrorCodeEnum, string> = {
  [RepayErrorCodeEnum.REPAYMENT_IN_PROGRESS]: "存在未结束的还款订单",
  [RepayErrorCodeEnum.REPAYMENT_DUPLICATE]: "重复还款",
  [RepayErrorCodeEnum.PAYMENT_FAILED]: "支付失败，请确认信息后重试",
  [RepayErrorCodeEnum.TRANSACTION_NOT_FOUND]: "未查询到相关交易",
  [RepayErrorCodeEnum.SYSTEM_ERROR]: "系统异常",
  [RepayErrorCodeEnum.INSUFFICIENT_BALANCE]: "余额不足，请使用其他支付方式",
  [RepayErrorCodeEnum.LOAN_SETTLED]: "本期借据已结清，请勿重复还款",
  [RepayErrorCodeEnum.LOCKED_LOAN_INTERCEPTED]: "新锁期借据部分，已被拦截",
  [RepayErrorCodeEnum.INVALID_OPERATION]: "无效操作",
  [RepayErrorCodeEnum.REPAYMENT_AMOUNT_TOO_LARGE]: "还款金额过大",
};

//识别结果，0：识别失败，1：识别成功
export enum IdCardVerifyResultEnum {
  FAIL = 0,
  SUCCESS = 1,
}

export const idCardVerifyResultNameMap = new Map<number, string>([
  [1, "识别成功"],
  [0, "识别失败"],
]);

export enum TrialStatusEnum {
  SUCCESS = 1,
  FAILED = 0,
}
