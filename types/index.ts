import { SVGProps } from "react";

export type IconSvgProps = SVGProps<SVGSVGElement> & {
  size?: number;
};

export interface Scene {
  id: number;
  userId?: number;
  name?: string;
  supplier: number;
  accessStatus: number;
  status: number;
  limitUseErrStatus?: number;
  creditLimit?: number;
  remainLimit?: number;
  minLoan?: number;
  repayDay?: number;
  productInfos?: ProductInfo[];
  loanOrders?: LoanOrder[];
  coupons?: Coupon[];
  signInfo: SignInfo;
  reofferInfo: ReofferInfo;
  discountAmountInfo: DiscountAmountInfo;
  smsInfo: SmsInfo;
  modifyPhone: ModifyPhone;
  repayResultStatus: RepayResultStatuses;
}

export interface AtParam {
  grant_type: "authorization_code";
  code: "";
  redirect_uri: "honorid://redirect_url";
  client_id: "dfcc855a69974195b11381e624f24f8a";
  client_secret: "9MV9Fg5iRQTDXeZq8MVgnsS1p1IVCTpq";
}

export interface AtInfo {
  access_token: string;
}

export interface DiscountAmountInfo {
  isSupport: number;
  tempDiscount: number;
  scheduleDiscount: number;
}
export interface ProductInfo {
  apr: string;
  dayRate: string;
  repayMethod: number;
  repayMethodName: string;
  earlyRepay: boolean;
  termNums: number[];
  tempApr: string;
  tempDayRate: string;
  tempPriceValidDays: number;
}

/** 贷款详情接口 */
export interface LoanOrder {
  /** 内部订单号 */
  applyNo?: string;
  /** 还款编号 */
  repayNo: string;
  /** 外部订单号 */
  outOrderNo?: string;
  /** 申请日期 YYYYMMDD */
  applyDate?: string;
  /** 放款日期 YYYYMMDD */
  paydate?: string;
  /** 申请时间戳 */
  applyTime?: number;
  /** 生效日期 YYYYMMDD */
  effectiveDate?: string;
  /** 贷款金额（单位：分） */
  loanAmount?: number;
  /** 贷款状态 1:待审核 2:审核通过 3:审核拒绝 */
  status?: number;
  /** 总期数 */
  totalTerm: number;
  /** 还款方式 1:等额本息 2:等额本金 */
  repayMethod?: number;
  /** 还款方式名称 */
  repayMethodName?: string;
  /** 当前期数 */
  currentTerm?: number;
  /** 应还金额 YYYYMMDD */
  dueRepay: number;
  /** 结清时间 */
  clearTime?: number;
  /** 已还总金额（单位：分） */
  paidAmount?: number;
  /** 已还本金（单位：分） */
  paidPrinAmount?: number;
  /** 已还利息（单位：分） */
  paidInterAmount?: number;
  /** 已还费用（单位：分） */
  paidFeeAmount?: number;
  /** 已还罚息（单位：分） */
  paidPenalty?: number;
  /** 绑定卡号（后四位） */
  bindCardNo?: string;
  /** 绑定银行编码 */
  bindBankCode?: string;
  /** 绑定银行名称 */
  bindBankName?: string;
  /** 优惠券编号 */
  couponNo?: string;
  /** 日利率（百分比） */
  dayRate?: string;
  /** 年化利率（百分比） */
  apr?: string;
  /** 减免金额（单位：分） */
  reductionAmount?: number;
  /** 提前还款违约金（单位：分） */
  prePenalty?: number;
  /** 还款计划列表 */
  repayPlanTerms?: RepayPlanTerm[];
  /** 合同列表 */
  contractList?: Contract[];
  /** 机构名称 */
  institutionNames?: string;
  /** 供应商ID */
  supplier?: number;
  supportRepayTypeDetails: SupportRepayTypeDetail[];
}

export interface SupportRepayTypeDetail {
  /**
   * 1-提前还款
   * 2-全部结清
   * 3-还本期
   * 4-还部分逾期
   * 5-还逾期
   */
  type: number;

  /**
   是否置灰展示
   */
  disabled: boolean;
}
/** 还款计划期数接口 */
export interface RepayPlanTerm {
  /** 期数 */
  termNo: number;
  /** 应还日期 YYYYMMDD */
  shouldRepayDate: string;
  /** 期数状态 1:待还款 2:已还款 3:逾期 */
  termStatus: number;
  /** 还款类别 1:正常还款 2:提前还款 3:逾期还款 */
  repayCategory: number;
  /** 期数应还总额（单位：分） */
  termAmount: number;
  /** 期数应还本金（单位：分） */
  termPrincipal: number;
  /** 期数应还利息（单位：分） */
  termInterest: number;
  /** 期数应还费用（单位：分） */
  termFee: number;
  /** 期数减免金额（单位：分） */
  termReductionAmount: number;
  /** 期数逾期费用（单位：分） */
  termOverdueFee: number;
  /** 期数服务费（单位：分） */
  termServiceFee: number;
  /** 期数手续费（单位：分） */
  termCharges: number;
  /** 期数本金罚息（单位：分） */
  termPrinPenalty: number;
  /** 期数利息罚息（单位：分） */
  termInterPenalty: number;
  /** 实际还款时间 */
  paidTime: number;
  /** 已还总额（单位：分） */
  paidTermAmount: number;
  /** 已还本金（单位：分） */
  paidTermPrincipal: number;
  /** 已还利息（单位：分） */
  paidTermInterest: number;
  /** 已还手续费（单位：分） */
  paidTermCharges: number;
  /** 已还减免金额（单位：分） */
  paidTermReductionAmount: number;
  /** 已还本金罚息（单位：分） */
  paidTermPrinPenalty: number;
  /** 已还利息罚息（单位：分） */
  paidTermInterPenalty: number;
  /** 已还逾期费用（单位：分） */
  paidTermOverdueFee: number;
  /** 已还费用（单位：分） */
  paidTermFee: number;
  /** 已还服务费（单位：分） */
  paidTermServiceFee: number;
  /** 应还总额（单位：分） */
  payableTermAmount: number;
  /** 应还本金（单位：分） */
  payableTermPrincipal: number;
  /** 应还利息（单位：分） */
  payableTermInterest: number;
  /** 应还手续费（单位：分） */
  payableTermCharges: number;
  /** 应还本金罚息（单位：分） */
  payableTermPrinPenalty: number;
  /** 应还利息罚息（单位：分） */
  payableTermInterPenalty: number;
  /** 应还逾期费用（单位：分） */
  payableTermOverdueFee: number;
  /** 应还费用（单位：分） */
  payableTermFee: number;
  /** 应还服务费（单位：分） */
  payableTermServiceFee: number;
  /** 逾期天数 */
  overdueDays: number;
  /** 逾期金额（单位：分） */
  overdueAmt: number;
  /** 是否可以提前还款 */
  preRepay: boolean;
  /** 是否逾期 */
  overdue: boolean;
}

/** 优惠券 */
export interface Coupon {
  /** 优惠券编号 */
  couponNo: string;
  /** 优惠券名称 */
  couponName: string;
  /** 优惠券类型 */
  couponType: number;
  /** 优惠券状态 */
  status: number;
  /** 有效开始时间 */
  startTime?: number;
  /** 有效结束时间 */
  endTime?: number;
  /** 优惠金额 */
  discountAmount?: number;
  /** 不可用原因 */
  unusableReason?: string;
  useRule?: string;
}

export interface SignInfo {
  /** 签约状态 0:不支持 1:未签约 2:已签约 3:已解约 */
  signStatus: number;
  signUrl: string;
  signUrlValidTime?: string;
  contractUrl: string;
  contractName: string;
}

export interface SmsInfo {
  status: number;
  errorDesc: string;
  serialNo: string;
}

export interface ModifyPhone {
  status: string;
  errorDesc: string;
}

export interface ReofferInfo {
  supprtReoffer: number;
}

export const couponTypes = [
  { key: 1, label: "放款券" },
  { key: 2, label: "还款券" },
];

export const couponStatuses = [
  { key: 1, label: "未使用" },
  { key: 2, label: "已使用" },
  { key: 3, label: "已过期" },
];

/** 合同信息接口 */
export interface Contract {
  /** 合同ID */
  contractId: string;
  /** 合同名称 */
  contractName: string;
}

export const accessStatuses = [
  { key: 1, label: "成功" },
  { key: 2, label: "拒绝" },
  { key: 3, label: "注销" },
];

export const suppliers = [
  { key: 1, label: "度小满" },
  { key: 5, label: "京东金条" },
  { key: 6, label: "乐信" },
  { key: 7, label: "奇富" },
];

export const creditStatuses = [
  { key: 0, label: "未授信" },
  { key: 1, label: "正常" },
  { key: 2, label: "审核" },
  { key: 3, label: "过期" },
  { key: 4, label: "拒绝" },
  { key: 5, label: "再分发升级" },
];

// 1-存在放款中借款，2-存在逾期订单不可借款，3-暂时无法支用，4-其他原因
export const limitUseErrStatuses = [
  { key: 1, label: "存在放款中借款" },
  { key: 2, label: "存在逾期订单不可借款" },
  { key: 3, label: "暂时无法支用" },
  { key: 4, label: "其他原因" },
];

export const loanStatuses = [
  { key: 1, label: "申请中" },
  { key: 2, label: "正常还款中" },
  { key: 3, label: "已逾期" },
  { key: 4, label: "已结清" },
  { key: 5, label: "借款失败" },
];

export const loanApplyStatuses = [
  { key: 0, label: "初始状态" },
  { key: 1, label: "审核中" },
  { key: 2, label: "成功" },
  { key: 3, label: "失败" },
  { key: 4, label: "拒绝" },
  { key: 5, label: "取消" },
  { key: 6, label: "需鉴权" },
  { key: 7, label: "已交易鉴权未提交，CP缓存订单失效" },
];

export const repayMethods = [
  { key: 1, label: "等额本息(灵活还)" },
  { key: 2, label: "等额本金(灵活还)" },
  { key: 3, label: "先息后本(灵活还)" },
  { key: 4, label: "等额本息(按期还)" },
];

export const repayTypes = [
  { key: 1, label: "提前还款" },
  { key: 2, label: "全部结清" },
  { key: 3, label: "还本期" },
  { key: 4, label: "还部分逾期" },
  { key: 5, label: "还逾期" },
];

export const signStatuses = [
  { key: 0, label: "不支持" },
  { key: 1, label: "未签约" },
  { key: 2, label: "已签约" },
  { key: 3, label: "已解约" },
];

export const sendSmsScene = [
  { key: 0, label: "验证码发送成功" },
  { key: 173402, label: "60秒内请勿重复发送" },
  { key: 173403, label: "超过1天发送次数限制，请明日再试" },
  { key: 173404, label: "验证码已发送，请勿频繁操作" },
  { key: 170009, label: "验证码连续输错超过限制次数，请2小时后再试" },
  { key: 173411, label: "请输入与当前手机号不同的号码" },
];

export const modifyPhone = [
  { key: 0, label: "更换成功" },
  { key: 170002, label: "验证码错误" },
  { key: 170009, label: "短信验证码连续输错超过限制次数，请2小时后稍后再试" },
  { key: 175510, label: "手机号修改失败，请稍后重试" },
  { key: 175511, label: "操作超时，请重试" },
];

export const reofferSupportStatuses = [
  { key: 0, label: "不支持" },
  { key: 1, label: "支持" },
];

export const discountSupportStatuses = [
  { key: 0, label: "不支持" },
  { key: 1, label: "支持" },
];

// 1-还款中，2-还款成功，3-部分还款成功，4-还款失败
export enum RepayResultStatuses {
  REPAYING = 1,
  SUCCESS = 2,
  PARTIAL_SUCCESS = 3,
  FAILED = 4,
}

export const repaySubmitResultStatuses = [
  { key: 2, label: "还款成功" },
  { key: 4, label: "还款失败" },
];

export const repayResultStatuses = [
  { key: 1, label: "还款中" },
  { key: 2, label: "还款成功" },
  { key: 4, label: "还款失败" },
];
