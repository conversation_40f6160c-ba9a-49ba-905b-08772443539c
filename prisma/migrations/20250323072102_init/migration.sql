/*
  Warnings:

  - You are about to drop the column `accessStatus` on the `scene` table. All the data in the column will be lost.
  - You are about to drop the column `creditLimit` on the `scene` table. All the data in the column will be lost.
  - You are about to drop the column `minLoan` on the `scene` table. All the data in the column will be lost.
  - You are about to drop the column `remainLimit` on the `scene` table. All the data in the column will be lost.
  - You are about to drop the column `repayDay` on the `scene` table. All the data in the column will be lost.
  - You are about to drop the column `supplierId` on the `scene` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `scene` DROP COLUMN `accessStatus`,
    DROP COLUMN `creditLimit`,
    DROP COLUMN `minLoan`,
    DROP COLUMN `remainLimit`,
    DROP COLUMN `repayDay`,
    DROP COLUMN `supplierId`,
    ADD COLUMN `access_status` VARCHAR(191) NULL,
    ADD COLUMN `credit_limit` INTEGER NULL,
    ADD COLUMN `min_loan` INTEGER NULL,
    ADD COLUMN `remain_limit` INTEGER NULL,
    ADD COLUMN `repay_day` INTEGER NULL,
    ADD COLUMN `supplier_id` VARCHAR(191) NULL;
