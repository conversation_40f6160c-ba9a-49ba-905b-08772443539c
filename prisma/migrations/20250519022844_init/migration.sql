/*
  Warnings:

  - Made the column `real_name` on table `loan_user` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ctf_code` on table `loan_user` required. This step will fail if there are existing NULL values in that column.
  - Made the column `mobile_no` on table `loan_user` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE `loan_user` MODIFY `real_name` VARCHAR(191) NOT NULL,
    MODIFY `ctf_code` VARCHAR(191) NOT NULL,
    MODIFY `mobile_no` VARCHAR(191) NOT NULL;
