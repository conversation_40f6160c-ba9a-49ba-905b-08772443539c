-- CreateTable
CREATE TABLE `SmsRecord` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `mobile` VARCHAR(191) NOT NULL,
    `code` VARCHAR(191) NOT NULL,
    `serialNo` VARCHAR(191) NOT NULL,
    `type` INTEGER NOT NULL DEFAULT 0,
    `ip` VARCHAR(191) NULL,
    `status` INTEGER NOT NULL DEFAULT 0,
    `expiredAt` DATETIME(3) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `SmsRecord_serialNo_key`(`serialNo`),
    INDEX `SmsRecord_userId_idx`(`userId`),
    INDEX `SmsRecord_mobile_idx`(`mobile`),
    INDEX `SmsRecord_serialNo_idx`(`serialNo`),
    INDEX `SmsRecord_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `DailyInspection` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `interface` VARCHAR(191) NOT NULL,
    `errorCode` VARCHAR(191) NOT NULL,
    `errorMessage` VARCHAR(191) NOT NULL,
    `originalErrorCode` VARCHAR(191) NOT NULL,
    `originalErrorMessage` VARCHAR(191) NOT NULL,
    `count` INTEGER NOT NULL,
    `uniqueCount` INTEGER NOT NULL,
    `traceId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `DailyInspection_interface_errorCode_errorMessage_idx`(`interface`, `errorCode`, `errorMessage`),
    INDEX `DailyInspection_traceId_idx`(`traceId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
