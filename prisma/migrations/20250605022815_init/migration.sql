/*
  Warnings:

  - You are about to drop the `dailyinspection` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE `dailyinspection`;

-- CreateTable
CREATE TABLE `daily_inspection` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `interface` VARCHAR(191) NOT NULL,
    `errorCode` VARCHAR(191) NOT NULL,
    `errorMessage` VARCHAR(191) NOT NULL,
    `originalErrorCode` VARCHAR(191) NOT NULL,
    `originalErrorMessage` VARCHAR(191) NOT NULL,
    `count` INTEGER NOT NULL,
    `uniqueCount` INTEGER NOT NULL,
    `traceId` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `daily_inspection_interface_errorCode_errorMessage_idx`(`interface`, `errorCode`, `errorMessage`),
    INDEX `daily_inspection_traceId_idx`(`traceId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
