generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model user {
  id       Int    @id @default(autoincrement())
  username String @unique
  autoCollectLoanUserRules String? @map("auto_collect_loan_user_rules") @default("")
}

model scene {
  id           Int     @id @default(autoincrement())
  userId       Int?
  name         String?
  supplier     Int?    @map("supplier")
  accessStatus Int?    @map("access_status")
  status       Int?
  limitUseErrStatus Int?
  creditLimit  Int?    @map("credit_limit")
  remainLimit  Int?    @map("remain_limit")
  minLoan      Int?    @map("min_loan")
  repayDay     Int?    @map("repay_day")
  productInfos Json?   @map("product_infos")
  loanOrders   Json?   @map("loan_orders")
  coupons      Json?   @map("coupons")
  signInfo Json? @map("sign_info")
  reofferInfo Json? @map("reoffer_info")
  discountAmountInfo Json? @map("discount_amountInfo")
  smsInfo Json? @map("sms_info")
  modifyPhone Json? @map("modify_phone")
  repayResultStatus Int? @map("repay_status")
}

model loan_user {
  id       Int     @id @default(autoincrement())
  userId   String  @map("user_id")
  realName String @map("real_name")
  ctfCode  String @map("ctf_code")
  mobileNo String @map("mobile_no")
  creditInfo Json? @map("credit_info")
  bankCardInfo Json? @map("back_card_info")
  loanInfo Json? @map("loan_info")
  couponInfo Json? @map("coupon_info")
  clearCertificateInfo Json? @map("clear_certificate_info")
  contractsInfo Json? @map("contracts_info")
  logoffInfo Json? @map("logoff_info")
  relatedUserIds Json? @map("releated_user_ids") @default("[]")
  interfaceResults Json? @map("interface_results") @default("[]")
  createTime BigInt @map("create_time")
  cp String? @map("cp")
  remark String? @map("remark") @default("")
}

model interface_log {
  id            Int    @id @default(autoincrement())
  userId        Int   @map("user_id")
  interfaceName String @map("interface_name")
  requestData   Json  @map("request_data")
  responseData  Json  @map("response_data")
  createTime    DateTime @map("create_time")

  @@index([userId])
}

model event_tracking {
  id            Int    @id @default(autoincrement())
  userId        Int   @map("user_id")
  userName      String   @map("user_name")
  eventName     String @map("event_name")
  createTime    DateTime @map("create_time")
}

model SmsRecord {
  id          String   @id @default(cuid())
  userId      String
  mobile      String
  code        String
  serialNo    String   @unique
  type        Int      @default(0) // 0-借款，1-还款
  ip          String?
  status      Int      @default(0) // 0-未验证，1-已验证
  expiredAt   DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([mobile])
  @@index([serialNo])
  @@index([createdAt])
}

model daily_inspection {
  id                 String      @id
  interface          String
  errorCode          String
  errorMessage       String
  originalErrorCode  String?
  originalErrorMessage String?
  count              Int
  uniqueCount        Int
  traceId            String
  processedBy        String?
  processReason      String?
  processMeasures    String?
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@index([interface, errorCode, errorMessage])
  @@index([traceId])
}

model UploadSession {
  id          String   @id @default(cuid())
  name        String
  creatorName String? // 会话的创建者
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  files       File[]
  order Int @default(0) // 排序字段
}
model File {
  id              String           @id @default(cuid())
  path            String
  originalContent String           @db.Text // 原始文件内容，用于对比和恢复
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  uploadSessionId String
  uploadSession   UploadSession    @relation(fields: [uploadSessionId], references: [id], onDelete: Cascade)
  // 关系：一个文件包含多行有归属信息的内容
  lines           AttributedLine[]

  @@unique([uploadSessionId, path])
}
// 新增模型：带归属信息的行
model AttributedLine {
  id             String    @id @default(cuid())
  lineNumber     Int // 行号，用于排序
  content        String    @db.Text
  isModified     Boolean
  modifiedByName String? // 修改这一行的人
  modifiedAt     DateTime? // 修改这一行的时间
  // 关系：指明这一行属于哪个文件
  fileId         String
  file           File      @relation(fields: [fileId], references: [id], onDelete: Cascade)
  // 约束：在同一个文件中，行号是唯一的
  @@unique([fileId, lineNumber])
}
