import { create } from "zustand";

import {LoanOrder, ProductInfo, Scene, Coupon, signStatus} from "@/types";

interface SceneState {
  SceneDto: Scene;
  scenes: Scene[];
  currentScene: Partial<Scene>;
  setSceneDto: (sceneDto: Scene) => void;
  setScenes: (scenes: Scene[]) => void;
  setCurrentScene: (scene: Scene) => void;
  setCurrentSceneProductInfos: (productInfos: ProductInfo[]) => void;
  setCurrentSceneLoanOrders: (loanOrders: LoanOrder[]) => void;
  setCurrentSceneCoupons: (coupons: any[]) => void;
  setSceneDtoProductInfos: (productInfos: ProductInfo[]) => void;
  setSceneDtoLoanOrders: (loanOrders: LoanOrder[]) => void;
  setSceneDtoCoupons: (coupons: any[]) => void;
}
const useSceneStore = create<SceneState>((set) => ({
  SceneDto: {
    supplier: 1,
    accessStatus: 1,
    status: 1,
    creditLimit: 20000000,
    remainLimit: 20000000,
    minLoan: 50000,
    repayDay: 20,
    productInfos: [
      {
        dayRate: "0.001",
        apr: "24.00",
        tempApr: "12.00",
        tempPriceValidDays: 5,
        repayMethod: 1,
        repayMethodName: "等额本息",
        termNums: [3, 6, 12],
        earlyRepay: true,
      },
    ],
    loanOrders: [
      {
        applyNo: "1234567890",
        outOrderNo: "1234567890",
        status: 2,
        applyDate: "20250324",
        paydate: "20250324",
        loanAmount: 10000000,
        totalTerm: 12,
        repayMethod: 1,
        currentTerm: 1,
        dueRepay: 10000000,
        clearTime: 1716633600000,
        supportRepayTypeDetails: [{type: 2, disabled: false}],
      },
    ],
    coupons: [
      {
        couponNo: "1",
        couponName: "满1000减100",
        couponType: 1,
        status: 1,
        discountAmount: 100,
      },
    ],
    signInfo: {
      signStatus: 0,
      signUrl: "",
      signUrlValidTime: "",
      contractUrl: "",
      contractName: ""
    },
    reofferInfo: {
      supprtReoffer: 0
    },
    smsInfo:{
      status: 0,
      errorDesc: ""
    },
    modifyPhone:{
      status: "",
      errorDesc: ""
    }
  },
  loanOrderDtos: [],
  currentScene: {},
  scenes: [],

  setSceneDto: (sceneDto: Scene) => set({ SceneDto: sceneDto }),
  setScenes: (scenes: Scene[]) => set({ scenes }),
  setCurrentScene: (scene: Scene) => set({ currentScene: scene }),
  setCurrentSceneProductInfos: (productInfos: ProductInfo[]) =>
    set((state) => ({
      currentScene: { ...state.currentScene, productInfos },
    })),
  setCurrentSceneLoanOrders: (loanOrders: LoanOrder[]) =>
    set((state) => ({
      currentScene: { ...state.currentScene, loanOrders },
    })),
  setCurrentSceneCoupons: (coupons: Coupon[]) =>
    set((state) => ({
      currentScene: { ...state.currentScene, coupons },
    })),

  setSceneDtoProductInfos: (productInfos: ProductInfo[]) =>
    set((state) => ({
      SceneDto: { ...state.SceneDto, productInfos },
    })),
  setSceneDtoLoanOrders: (loanOrders: LoanOrder[]) =>
    set((state) => ({
      SceneDto: { ...state.SceneDto, loanOrders },
    })),
  setSceneDtoCoupons: (coupons: Coupon[]) =>
    set((state) => ({
      SceneDto: { ...state.SceneDto, coupons },
    })),
}));

export default useSceneStore;
