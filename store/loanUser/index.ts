import { createStore } from "zustand/vanilla";

import { LoanUser } from "@/types/cp/loanUser";
import { getLoanUserById } from "@/actions/cp/loanUserActions";
import {CreditInfo} from "@/types/cp/creditInfo";

interface LoanUserState {
  currentLoanUser?: LoanUser;
  getCurrentLoanUser: (id: number) => LoanUser | undefined;
}

const useLoanUserStore = createStore<LoanUserState>((set) => ({
  currentLoanUser: undefined,
  getCurrentLoanUser: (id): LoanUser | undefined => {
    const { currentLoanUser }: { currentLoanUser?: LoanUser } =
      useLoanUserStore.getState();

    if (!currentLoanUser) {
      getLoanUserById(id).then((loanUser) => {
        if (loanUser && loanUser.creditInfo !== null) {
          set({
            currentLoanUser: {
              ...loanUser,
              creditInfo: loanUser.creditInfo as unknown as CreditInfo,
            },
          });
        } else {
          set({
            currentLoanUser: undefined,
          });
        }
      });
    }

    return currentLoanUser;
  },
}));
