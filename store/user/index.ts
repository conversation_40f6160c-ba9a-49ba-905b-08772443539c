import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";

import { findOrCreateUser } from "@/actions/userActions";

interface UserState {
  currentUser: User | null;
  isLoadingUser: boolean;
  error: string | null;
  chooseSystem: string | null;
}

interface UserActions {
  loginUser: (loginData: User) => Promise<void>;
  logoutUser: () => void;
  // checkAuthStatus 已移除
  setCurrentUser: (user: User | null) => void;
  setIsLoadingUser: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentSystem: (system: string) => void;
}

export const useUserStore = create<UserState & UserActions>()(
  persist(
    (set, get) => ({
      currentUser: null,
      isLoadingUser: true,
      error: null,
      chooseSystem: null,

      setIsLoadingUser: (loading) =>
        set({ isLoadingUser: loading, error: loading ? null : get().error }),
      setError: (error) => set({ error: error, isLoadingUser: false }),
      setCurrentUser: (user) =>
        set({ currentUser: user, isLoadingUser: false, error: null }),

      setCurrentSystem: (system: string) => set({ chooseSystem: system }),

      loginUser: async (loginData: User) => {
        set({
          isLoadingUser: true,
          error: null,
          chooseSystem: loginData.chooseSystem,
        });
        try {
          const dbUser = await findOrCreateUser(loginData);

          set({ currentUser: dbUser, isLoadingUser: false, error: null });
          console.log("Zustand: User context updated and persisted.");
        } catch (err) {
          console.error("Zustand: Failed to process login:", err);
          const errorMessage = err instanceof Error ? err.message : "登录失败";

          set({ currentUser: null, isLoadingUser: false, error: errorMessage });
        }
      },

      logoutUser: () => {
        set({
          currentUser: null,
          isLoadingUser: false,
          error: null,
          chooseSystem: null,
        });
      },
    }),
    {
      name: "user-storage",
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        currentUser: state.currentUser,
        chooseSystem: state.chooseSystem,
      }),
      onRehydrateStorage: () => {
        return (state) => {
          if (state) {
            state.isLoadingUser = false;
          }
        };
      },
    },
  ),
);
