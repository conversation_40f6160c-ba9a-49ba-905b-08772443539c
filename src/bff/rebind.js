import { request } from "../helpers/utils";

export default async function rebind(data, options) {
  const realNameRes = await request("/loan/api/user/v2/getUserRealNameInfo", {}, options);
  if (realNameRes && realNameRes.data?.status === 1) {
    const res = await request("/loan/api/user/v2/bind", {
      encryptedParams: {
        realNameStatus: realNameRes.data.status,
        realName: realNameRes.data.realName,
        ctfCode: realNameRes.data.ctfCode,
        mobileNo: realNameRes.data.mobileNo,
        verifySms: 0,
        ...data,
      },
    });
    return res;
  }
  if (realNameRes && realNameRes.data?.status === 0) {
    return { code: 1 };
  }
  return realNameRes || { code: -1 };
}
