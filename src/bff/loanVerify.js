import Big from "big.js";
import { request } from "../helpers/utils";
import { LOAN_VERIFY_ERROR_CODE_MAP } from "../helpers/constants";

const TRIAL_VERIFY_TIME_UNIT = 60 * 1000;

export default async function loanVerify(data, options) {
  if (typeof data.trialValidTime === "number" && data.trialValidTime !== -1) {
    const now = new Date().getTime();
    if (
      Big(now)
        .minus(Big(data.trialTime))
        .gte(Big(data.trialValidTime).times(TRIAL_VERIFY_TIME_UNIT))
    ) {
      return {
        code: LOAN_VERIFY_ERROR_CODE_MAP.TRIAL_EXPIRED,
        message: "试算数据需刷新",
      };
    }
  }
  return request(
    "/loan/api/loan/verify",
    {
      encryptedParams: data.encryptedParams,
    },
    options,
  );
}
