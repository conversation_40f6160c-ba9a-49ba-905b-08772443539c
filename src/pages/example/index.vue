<script setup>
import { ref, onServerPrefetch, onMounted } from "vue";
import useStore from "./store";
import { initStore } from "../../helpers/utils";
import { showRetentionDialog } from "../../helpers/retention-dialog-utils";
import {
  goto,
  liveDetection,
  doOCR,
  refreshToken,
  encrypt,
  decrypt,
  updateShortcut,
  hasShortcut,
  setUserImprovementPlan,
  userImprovementPlan,
  setCp,
} from "../../helpers/native-bridge";
import { del } from "../../helpers/storage";
import { getCurrentCpInfo } from "../../helpers/configUtils";
import { getAdActivityListV2 } from "../../api/ad";

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

const flag = ref(false);
function clickMe() {
  store.increment();
  showRetentionDialog({ text: "aaaa" });
}

async function testLiveDetection() {
  const cpInfo = await getCurrentCpInfo();
  setCp(cpInfo?.supplierId || 0);
  const result = await liveDetection({
    fullName: "xxx",
    scenes: "2",
    faceSdk: 1,
    retentionPicurl:
      "https://content-test-drcn.hihonorcdn.com/honorWallet/c600e1441bd1d5f663c915a8fa14d330_20250102114146.jpg",
  });
  store.setRet(JSON.stringify(result));
}

async function testDoOCR() {
  const cpInfo = await getCurrentCpInfo();
  setCp(cpInfo?.supplierId || 0);
  const result = await doOCR();
  store.setRet(JSON.stringify(result));
}

async function testRefreshToken() {
  await refreshToken();
}

async function testEncrypt() {
  const result = await encrypt("test");
  store.setRet(JSON.stringify(result));
}

async function testDecrypt() {
  let result = await encrypt("test");
  result = await decrypt(result);
  store.setRet(JSON.stringify(result));
}

async function updateShortcutHnr() {
  const ret = await updateShortcut(true);
  if (ret) del(`U_POPUP_STORE_${store.param.userId}`);
}

async function hasShortcutHnr() {
  const result = await hasShortcut();
  store.setRet(JSON.stringify(result));
  return result;
}

async function setUserImprovementPlanOpen() {
  const result = await setUserImprovementPlan(true);
  store.setRet(JSON.stringify(result));
}

async function setUserImprovementPlanClose() {
  const result = await setUserImprovementPlan(false);
  store.setRet(JSON.stringify(result));
}

async function userImprovementPlanHnr() {
  const result = await userImprovementPlan();
  store.setRet(JSON.stringify(result));
  return result;
}

const numberScroll = ref();
setTimeout(() => {
  numberScroll.value?.update("3,063");
}, 2000);
setTimeout(() => {
  numberScroll.value?.update("20,000,000");
}, 4000);
setTimeout(() => {
  numberScroll.value?.update("10,900,200");
}, 6000);
setTimeout(() => {
  numberScroll.value?.update("9,800,200");
}, 8000);

function goHome() {
  goto("/wallet-loan-web/pages/index", false, true);
}

function jumpToLoanAdActivity() {
  goto("/wallet-loan-web/redirect/realnameActivity?fromPage=outActivity&activityCode=test");
}
onMounted(async () => {
  const res = await getAdActivityListV2();
  console.log(res);
});
</script>

<template>
  <div class="card">
    <div class="item item-left">
      <div class="item-title">荣耀账号</div>
    </div>
    <div class="item item-content">
      <input type="tel" placeholder="输入手机号/邮箱/帐号名" :value="data.entryInfo?.code" />
    </div>
    <div class="item item-left">
      <div class="item-title">输入参数</div>
      <img class="required right" alt />
    </div>
    <div class="item item-content">
      <input v-model="data.param.country" placeholder="输入参数" />
    </div>
    <div>{{ data.nativeResult }}</div>
    <div class="item item-left">
      <div class="item-title">体验开关</div>
    </div>
    <div class="item item-content">
      <hnr-checkbox v-model="flag">体验开关</hnr-checkbox>
    </div>
  </div>

  <div class="bottom">
    <div class="button" @click="clickMe()">提交</div>
    <!-- <div class="button" @click="testGoto()">跳转</div> -->
    <!-- <div class="button" @click="testBack()">后退</div> -->
    <div class="button" @click="testLiveDetection()">活体验证</div>
    <div class="button" @click="testDoOCR()">身份证OCR</div>
    <div class="button" @click="testRefreshToken()">刷新AT</div>
    <div class="button" @click="testEncrypt()">加密</div>
    <div class="button" @click="testDecrypt()">解密</div>
    <div class="button" @click="updateShortcutHnr()">添加快捷方式</div>
    <div class="button" @click="hasShortcutHnr()">存在快捷方式</div>
    <div class="button" @click="goHome()">返回首页</div>
    <div class="button" @click="userImprovementPlanHnr()">体验开关是否打开</div>
    <div class="button" @click="setUserImprovementPlanOpen()">打开体验开关</div>
    <div class="button" @click="setUserImprovementPlanClose()">关闭体验开关</div>
    <div class="button" @click="jumpToLoanAdActivity()">跳转至实名认证运营页</div>
  </div>
  <no-ssr>
    <!-- <NumverScroll ref="numberScroll" :curr-num="578"></NumverScroll> -->
  </no-ssr>
</template>

<style scoped>
.card {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  border-radius: 1rem;
  padding-top: 0.5rem;
}

.item {
  height: 3rem;
  display: flex;
  line-height: 1rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.item-title {
  padding-top: 1rem;
  padding-right: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-weight: bold;
}

.item-title-1 {
  padding-top: 1rem;
  padding-right: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.8rem;
}

.required {
  width: 1rem;
  height: 1rem;
  border: none;
  margin-top: 1rem;
  text-align: center;
}

.right {
  position: absolute;
  right: 2rem;
}

.item-left {
  width: 7rem;
}

.item-content {
  width: calc(100vw - 12rem);
}

.item-content input {
  height: 90%;
  border-radius: 0.75rem;
  width: 90%;
  border: none;
  text-overflow: ellipsis;
}

.item-content select {
  height: 100%;
  border-radius: 0.75rem;
  width: 100%;
  border: none;
  padding-left: 0.7rem;
  background: transparent;
}
.bottom {
  justify-content: center;
  width: 100%;
  display: flex;
  align-items: center;
  height: 35rem;
  flex-direction: column;
  overflow: scroll;
}
.button {
  justify-content: center;
  align-items: center;
  display: flex;
  margin-top: 1rem;
  background: #256fff;
  font-size: 1.2rem;
  text-align: center;
  color: #fff;
  height: 2.2rem;
  width: 80%;
  border-radius: 1.1rem;
  opacity: 0.6;
}
</style>
