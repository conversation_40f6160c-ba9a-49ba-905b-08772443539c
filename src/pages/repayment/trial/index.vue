<script setup>
import { onServerPrefetch, onMounted, ref, computed } from "vue";
import Big from "big.js";
import { showToast, showDialog } from "@hihonor/hnr/dist/hnr.es.min";
import useStore from "./store";
import { initStore, amountNumberFormat, request } from "../../../helpers/utils";
import { back, goto, regNativeEvent, report } from "../../../helpers/native-bridge";
import { getStore, del, setWithExactExpireUnit } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { repayStatusQuery, transferRepay } from "../../../api/repay";
import { repayStatusMap } from "../../../api/typedef/repay";

const { store, data } = initStore(useStore);
const showViolateFeeBubble = ref(false);

onServerPrefetch(store.initial);

function confirm() {
  data.value.selectedcard = data.value.cardinfo?.bankCardDto[data.value.checked3];
}

const bankinfo = (param) => {
  data.value.bankinfo = param;
  store.isReservePhoneError = false;
};

async function submit() {
  setWithExactExpireUnit(`U_REPAYTERMS_${data.value.param.userId}`, data.value.repayTerms, 10, "M");
  data.value.btnloading = true;
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "repayment_trial_repay_click",
      repayType: store.param.repayType,
    });
  });
  try {
    const resp = await request("/loan/api/repay/v2/sendVerifyCode", {
      totalAmount: data.value.sendamt,
      bankCardId: data.value.bankinfo?.bankCardId,
      outOrderNo: data.value.param.outOrderNo,
    });
    setTimeout(async () => {
      await request("/loan/api/user/operLog", {
        orderNo: data.value.param.outOrderNo,
        operType: 302,
        operResult: resp.code === 0 ? 1 : 2,
        supplier: 1,
        operTime: new Date().getTime(),
      });
    }, 0);
    if (resp.code === 0) {
      if (resp?.data.status === 173402) {
        data.value.btnloading = false;
        showToast({
          message: "还款操作频繁，请稍后再试",
          position: "bottom",
        });
        return;
      }
      if (resp.data.status === 0) {
        if (data.value.param.isdelay === "true") {
          goto(
            "/wallet-loan-web/pages/repayment/sms?isdelay=true&" +
              "repayPart=false&" +
              `totalAmount=${data.value.sendamt}&` +
              `termNo=${data.value.param?.repayTerm}&` +
              `bankCardId=${data.value.bankinfo?.bankCardId}&` +
              `bankCardNo=${data.value.bankinfo?.bankCardNo}&` +
              `outOrderNo=${data.value.param.outOrderNo}&` +
              "repayType=2&" +
              `needResign=${resp.data.needResign}&` +
              `couponNo=${data.value.couponNo}&` +
              `reductionAmount=${data.value.discount}&` +
              `serialNo=${resp.data.serialNo}`,
          );
        } else {
          goto(
            "/wallet-loan-web/pages/repayment/sms?" +
              "repayPart=false&" +
              `totalAmount=${data.value.sendamt}&` +
              `termNo=${data.value.param?.repayTerm}&` +
              `bankCardId=${data.value.bankinfo?.bankCardId}&` +
              `bankCardNo=${data.value.bankinfo?.bankCardNo}&` +
              `outOrderNo=${data.value.param.outOrderNo}&` +
              `repayType=${data.value.param.RepayDate === data.value.repayday ? 2 : 1}&` +
              `isLastTerm=${data.value.param.repayTerm === data.value.allTerm}&` +
              `needResign=${resp.data.needResign}&` +
              `couponNo=${data.value.couponNo}&` +
              `reductionAmount=${data.value.discount}&` +
              `serialNo=${resp.data.serialNo}`,
          );
        }
      }
    } else {
      if (resp?.code === 171911) {
        showDialog({
          message: resp.message,
          messageAlign: "left",
          confirmButtonType: "normal",
          confirmButtonText: "知道了",
          showFilter: true,
          beforeClose: () => {
            data.value.isReservePhoneError = true;
            return true;
          },
        });
      } else {
        showToast({
          message: resp?.message || "还款操作频繁，请稍后再试",
          position: "bottom",
        });
      }
      data.value.btnloading = false;
    }
  } catch (e) {
    data.value.btnloading = false;
  }
}

onMounted(async () => {
  if (getStore(`U_REPAY_FAIL_${data.value.param.userId}`)) {
    del(`U_REPAY_FAIL_${data.value.param.userId}`);
  }
  del(`U_REPAYMENT_SMS_${data.value.param.userId}`);
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);
  report("wallet_page_view", {
    page_name: "repayment_trial_page",
  });
  const res = await getCurrentCpInfo();
  data.value.repayViolateFeeExplanation = res?.repayViolateFeeExplanation;
  data.value.interestFeeExplanation = res?.interestFeeExplanation;
});

regNativeEvent("onResume", async () => {
  if (getStore(`U_REPAY_FAIL_${data.value.param.userId}`)) {
    del(`U_REPAY_FAIL_${data.value.param.userId}`);
    data.value.btnloading = false;
  }
  del(`U_REPAYMENT_SMS_${data.value.param.userId}`);
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);

  const repayNo = sessionStorage.getItem("repayNo");
  if (repayNo) {
    const bankTransferStartTime = sessionStorage.getItem("bankTransferStartTime");
    sessionStorage.removeItem("bankTransferStartTime");
    sessionStorage.removeItem("repayNo");
    const diff = Date.now() - Number(bankTransferStartTime);
    if (diff > 10000) {
      store.showQueryRepayStatusDialog = true;
      const repayStatusRes = await repayStatusQuery(repayNo);
      store.showQueryRepayStatusDialog = false;
      if (repayStatusRes?.code === 0) {
        const { repayStatus } = repayStatusRes.data;
        if (repayStatus !== repayStatusMap.Repaying) {
          goto(
            `/wallet-loan-web/pages/repayment/record?repayNo=${repayNo}&repayResult=${repayStatus}`,
            false,
          );
        }
      }
    }
  }
});

// 是否展示银行转账卡片
const showBankTransferCard = computed(() => {
  return store.param.sdkVersionCode >= ********* && store?.supplierInfo?.supportTransferRepay === 1;
});

const goToTransferRepayPage = async () => {
  // 保存当前时间戳到session
  sessionStorage.setItem("bankTransferStartTime", String(Date.now()));

  // 调用服务端接口获取订单号和url
  const param = {
    totalAmount: data.value.sendamt,
    repayType:
      data.value.param.isdelay === "true" || data.value.param.RepayDate === data.value.repayday
        ? 2
        : 1,
    repayItemList: [
      {
        couponNo: data.value.couponNo,
        reductionAmount: data.value.discount,
        outOrderNo: data.value.param.outOrderNo,
        repayAmount: data.value.sendamt,
        repayTerms: data.value.repayTerms,
      },
    ],
  };
  const transferRepayRes = await transferRepay(param);

  if (transferRepayRes?.code === 0) {
    sessionStorage.setItem("repayNo", transferRepayRes.data.repayNo);
    await back();
    goto(transferRepayRes.data.repayUrl, true);
  }
};
</script>

<template>
  <div class="bg">
    <no-ssr>
      <div>
        <hnr-nav-bar transparent="true" class="nav-padding-top" title="还款明细">
          <template #left>
            <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
          </template>
        </hnr-nav-bar>
      </div>
      <div>
        <hnr-cell-group class="container" inset>
          <span v-if="data.param.isdelay === 'true'" class="infotitle4"
            >已逾期{{ data.param.delaydays }}天，逾期总应还(含欠款￥{{
              amountNumberFormat(new Big(data.param.repayAmount || 0).div(100))
            }})</span
          >
          <span v-else class="infotitle1"
            >{{ data.param?.repayTerm + "/" + data.param?.allTerm }}期(本期)应还</span
          >
          <span class="infotitle2"
            >￥{{ amountNumberFormat(new Big(data.trialinfo.totalAmount || 0).div(100)) }}</span
          >
          <span
            v-if="
              data.param.isdelay !== 'true' &&
              data.earlyRepayDays !== 0 &&
              data.param.random === 'true'
            "
            class="infotitle3"
          >
            提前{{ data.earlyRepayDays }}天还款，实时利息已更新
          </span>
          <hnr-divider class="supportdivider" line />
          <div class="cellLine">
            <div class="cellLeft">还款本金</div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(new Big(data.trialinfo?.totalPrincipal || 0).div(100)) }}
            </div>
          </div>
          <div v-if="parseFloat(data.trialinfo?.totalInterAndFee)" class="cellLine">
            <div class="cellLeft" style="display: flex; align-items: center">
              息费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble"
                :message="data.interestFeeExplanation"
                placement="bottom"
                class="violateFee-bubble-tip"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight" style="display: flex">
              <hnr-chips
                v-if="
                  data.param.isdelay !== 'true' &&
                  data.param.random === 'true' &&
                  data.param.RepayDate !== data.repayday
                "
                style="display: inline; padding: 4px 4px 2px 4px"
                class="hnchips1"
                >已更新</hnr-chips
              >
              {{
                "￥" + amountNumberFormat(new Big(data.trialinfo?.totalInterAndFee || 0).div(100))
              }}
            </div>
          </div>
          <div
            v-if="!parseFloat(data.trialinfo?.totalInterAndFee)"
            class="cellLine"
            style="line-height: var(--dp20); height: var(--dp20); align-items: center"
          >
            <div class="cellLeft">利息</div>
            <div class="cellRight" style="display: flex">
              <hnr-chips
                v-if="
                  data.param.isdelay !== 'true' &&
                  data.param.random === 'true' &&
                  data.param.RepayDate !== data.repayday
                "
                style="display: inline; padding: 4px 4px 2px 4px"
                class="hnchips1"
                >已更新</hnr-chips
              >
              {{ "￥" + amountNumberFormat(new Big(data.trialinfo?.totalInterest || 0).div(100)) }}
            </div>
          </div>
          <div
            v-if="
              parseFloat(data.trialinfo?.totalServiceFee) &&
              !parseFloat(data.trialinfo?.totalInterAndFee)
            "
            class="cellLine"
          >
            <div class="cellLeft" style="display: flex; align-items: center">
              服务费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble"
                :message="data.repayViolateFeeExplanation"
                placement="bottom"
                class="violateFee-bubble-tip"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(new Big(data.trialinfo.totalServiceFee || 0).div(100)) }}
            </div>
          </div>
          <div v-show="data.param.isdelay === 'true'" class="cellLine">
            <div class="cellLeft">逾期天数</div>
            <div class="cellRight">{{ data.param.delaydays }}</div>
          </div>
          <div
            v-show="
              data.param.isdelay === 'true' &&
              data.trialinfo?.totalPenalty !== '0.00' &&
              data.trialinfo?.totalPenalty !== 0
            "
            class="cellLine"
          >
            <div class="cellLeft">逾期罚息</div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(new Big(data.trialinfo.totalPenalty || 0).div(100)) }}
            </div>
          </div>
          <div v-show="!Big(data.trialinfo?.totalDiscount || 0).eq(0)" class="cellLine">
            <div class="cellLeft">优惠券</div>
            <div class="cellRight">
              {{ "-￥" + amountNumberFormat(new Big(data.trialinfo?.totalDiscount || 0).div(100)) }}
            </div>
          </div>
          <myBank
            class="bankstyle"
            title="还款银行卡"
            label="还款银行卡"
            agree="false"
            :apply-no="data.param.outOrderNo"
            :is-reserve-phone-error="data.isReservePhoneError"
            :show-bank-transfer-card="showBankTransferCard"
            @bankinfo="bankinfo"
            @go-to-transfer-repay-page="goToTransferRepayPage"
          ></myBank>
          <hnr-divider
            v-if="data.trialinfo?.reductionAmountDesc"
            style="padding: 0 var(--dp12); width: auto"
            line
          />
          <div
            v-if="data.trialinfo?.reductionAmountDesc"
            style="
              border-radius: var(--hnr-default-corner-radius-xs);
              padding: var(--dp12) var(--dp12) var(--dp16) var(--dp12);
              font-size: var(--hnr-caption);
              color: var(--hnr-text-color-tertiary);
            "
            v-html="data.trialinfo?.reductionAmountDesc"
          ></div>
        </hnr-cell-group>
      </div>
      <div class="footer">
        <hnr-button
          type="primary"
          standard-width="true"
          :disabled="data.istoday || !data.bankinfo?.bankCardId"
          class="subbtn"
          @click="submit"
        >
          确认还款{{
            "（￥" + amountNumberFormat(new Big(data.trialinfo.totalAmount || 0).div(100)) + "）"
          }}
        </hnr-button>
      </div>
      <hnr-dialog
        v-model:show="data.showcards"
        title="还款银行卡"
        :lazy-render="false"
        confirm-button-type="primary"
        narrow-padding
        @confirm="confirm"
      >
      </hnr-dialog>
    </no-ssr>
    <div v-show="data.btnloading">
      <hnr-dialog
        v-model:show="data.btnloading"
        title=""
        message=""
        button-direction="row"
        :before-close="disabledOverlayClose"
      >
        <template #footer>
          <div class="loading">
            <div class="loading-text">正在提交</div>
            <div class="loading-logo"><hnr-loading /></div>
          </div>
        </template>
      </hnr-dialog>
    </div>
  </div>
  <!--还款状态查询弹窗-->
  <hnr-dialog
    v-model:show="store.showQueryRepayStatusDialog"
    show-filter
    :show-confirm-button="false"
    :show-cancel-button="false"
  >
    <div class="query-dialog">正在获取还款信息<hnr-loading /></div>
  </hnr-dialog>
</template>

<style scoped>
/* 深色模式下的图标反色处理 */
@media (prefers-color-scheme: dark) {
  .imgs {
    filter: invert(1);
  }
}

.imgs {
  vertical-align: middle;
  margin-top: var(--dp3) !important;
  margin-left: var(--hnr-elements-margin-horizontal-S);
  width: var(--dp14);
  height: var(--dp14);
  transform: translateY(2px);
}

.bg {
  user-select: none;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  height: 100%;
  width: 100%;
}
.container {
  display: flex;
  flex-direction: column;
  margin-left: calc(24px - var(--hnr-list-card-padding-end)) !important;
  margin-right: calc(24px - var(--hnr-list-card-padding-end)) !important;
  width: calc(
    (100% - 48px) + var(--hnr-list-card-padding-end) + var(--hnr-list-card-padding-start)
  ) !important;
}
.infotitle1 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.infotitle4 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-color-error);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.infotitle3 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.hnchips1 {
  background: #ffebee;
  border-radius: var(--hnr-text-click-effect-corner-radius);
  color: var(--hnr-color-error) !important;
  font-size: var(--hnr-caption) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
  margin: 0 var(--hnr-elements-margin-horizontal-S) !important;
}
.subbtn {
  max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}
.infotitle2 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  font-size: var(--hnr-headline-8);
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-medium);
}
.bankstyle {
  padding: 0 var(--hnr-list-card-padding-start) 0 var(--hnr-list-card-padding-end);
}
.supportdivider {
  padding: 0 var(--dp12);
  width: auto;
}
.cellLine {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-start);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.cellLeft {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.cellRight {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 99;
  justify-content: center;
  display: flex;
}
.rightvalue {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.tips {
  font-weight: var(--hnr-font-weight-medium);
  font-size: small;
}
:deep(.hnr-cell__value) {
  font-weight: bold;
  /* min-width: 20% !important; */
}
:deep(.hnr-dialog__title) {
  font-weight: bold;
}
:deep(.hnr-cell__prefix--title) {
  color: #256fff;
}
.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  padding-top: var(--dp6);
  padding-bottom: var(--dp14);
}
.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

.query-dialog {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
