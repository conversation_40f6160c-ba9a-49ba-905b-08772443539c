import { defineStore } from "pinia";
import { request } from "../../../helpers/utils";
import { getCpConfig } from "../../../api/config";

export default defineStore("repayment/trial", {
  state: () => ({
    // 页面初始数据
    showcards: false,
    param: {},
    checked3: 0,
    trialinfo: {},
    bankinfo: {},
    sendamt: "0",
    repayday: "",
    // 优惠券编号
    couponNo: "",
    // 优惠券金额
    discount: "",
    btnloading: false,
    // 本期还款提前天数
    earlyRepayDays: "",
    repayTerms: [], // 本次还款的期次明细
    showQueryRepayStatusDialog: false,
    // 是否预留手机号错误
    isReservePhoneError: false,
    // CP信息
    supplierInfo: {},
  }),
  actions: {
    async initial() {
      const [res, supplierInfoRes] = await Promise.all([
        request("/loan/api/repay/trial", this.param, { mock: false }),
        getCpConfig(),
      ]);
      this.supplierInfo = supplierInfoRes?.data || {};
      if (res.code !== 0) {
        this.errorCode = res.code;
        return;
      }
      setTimeout(async () => {
        await request("/loan/api/user/operLog", {
          orderNo: this.param.outOrderNo,
          operType: 301,
          operResult: res.code === 0 ? 1 : 2,
          supplier: 1,
          operTime: new Date().getTime(),
        });
      }, 0);
      if (res.code === 0) {
        this.trialinfo = res.data;
        this.repayTerms = this.trialinfo?.repayTerms;
        if (this.trialinfo?.usedCoupons?.length > 0) {
          this.couponNo = this.trialinfo.usedCoupons[0].couponNo;
          this.discount = this.trialinfo.usedCoupons[0].discount;
        }
        this.sendamt = this.trialinfo.totalAmount;
        // 账务日期
        this.repayday = `${this.trialinfo.accountDate.substring(0, 4)}年${this.trialinfo.accountDate.substring(4, 6)}月${this.trialinfo.accountDate.substring(6, 8)}日`;
        this.earlyRepayDays = this.getdiffdate(this.repayday, this.param.RepayDate);
      }
    },
    // 计算日期差
    getdiffdate(startdate, enddate) {
      const date1 = Date.parse(startdate.replace(/[^\d]/g, "-").slice(0, -1));
      const date2 = Date.parse(enddate.replace(/[^\d]/g, "-").slice(0, -1));
      if (date2 > date1) {
        return (date2 - date1) / (1 * 24 * 60 * 60 * 1000);
      }
      return 0;
    },
  },
});
