import { defineStore } from "pinia";
import Big from "big.js";
import { request } from "../../../helpers/utils";

export default defineStore("repayment/apply", {
  state: () => ({
    param: {},
    // 是否借款当天
    istoday: false,
    // 随借随还true 固定期限false
    random: false,
    // 本期是否还清
    clear: true,
    // 是否逾期
    isdelay: false,
    delayinfos: {
      // 还款本金
      Principal: "",
      // 利息
      Interest: "",
      // 逾期期数
      Terms: [],
      // 逾期罚息
      OverdueFee: "",
      // 优惠券
      ReductionAmount: "",
      repayTerm: 0,
      // 服务费
      serviceFee: 0,
      // 息费
      interAndFee: 0,
    },
    // 展开收起控制
    activeNames11: "",
    activeNames12: "",
    activeNames2: "0",
    // 本期详情/逾期详情
    issuedetail: {},
    // 展示用总金额
    totalamount: 0,
    // 通讯用总金额
    sendtotalamt: 0,
    active1: {
      shouldRepayAmount: "",
      repayPlanTerms: [],
    },
    // 逾期天数
    delaydays: "0",
    // 展示用逾期金额
    delayamt: "0",
    // 通讯用逾期金额
    senddelayamt: "0",
    btn1: "提前还款",
    btn2: "还本期",
    totalTerm: "",
    // 下方最大高度
    maxheight: "calc(100vh - 22rem)",
    resdata: [],
    supportRepayType: [], // 支持的还款类型列表，不支持还款时返回空列表:1-提前还款 2-全部结清 3-还本期 4-还部分逾期 5-还逾期
    deductionEarlyMorning: 0, // 是否凌晨自动还款
    currentDueDate: false, // 乐信还逾期按钮的提示语是否为还款日
    showDueTip: 0,
    isTokenExpired: false,
    isLastTerm: false,

    repayTypeMaps: {
      1: "提前还款",
      2: "全部结清",
      3: "还本期",
      4: "还部分逾期",
      5: "还逾期",
    },
  }),
  actions: {
    async initial() {
      function* f() {
        yield;
      }
      const generator = f();
      let isTokenExpireNum = 0;
      this.totalamount = 0;
      this.sendtotalamt = 0;
      if (this.param.outOrderNo) {
        // loan/detail用于查询还款方式
        const resp = await Promise.all([
          request("/loan/api/repay/plan", { outOrderNo: this.param.outOrderNo }, { mock: false }),
          request("/loan/api/loan/detail", { outOrderNo: this.param.outOrderNo }, { mock: false }),
        ]);
        if (resp[0].code === 1201 || resp[1].code === 1201) {
          isTokenExpireNum += 1;
          this.isTokenExpired = true;
        }
        if ((resp[0].code === 1201 || resp[1].code === 1201) && isTokenExpireNum === 1) {
          generator.next();
        }
        if (resp[0].code !== 0) {
          this.errorCode = resp[0].code;
          return;
        }
        if (resp[1].code !== 0) {
          this.errorCode = resp[1].code;
          return;
        }
        const res = resp[0];
        if (res.code === 0 && resp[1].code === 0) {
          this.totalTerm = res.data[0].totalTerm;
          // istoday今天是不可以还款
          this.istoday = res.data[0].status === 7;
          this.isdelay = res.data[0].loanOverDueDays > 0;
          this.currentDueDate = res.data[0]?.currentDueDate;
          this.supportRepayType = res.data[0]?.supportRepayTypeDetails;
          if (this.isdelay) {
            this.clear = false;
            this.senddelayamt = res.data[0].loanOverDueAmount;
            this.delayamt = Big(res.data[0].loanOverDueAmount || 0)
              .div(100)
              .toFixed(2);
            this.delaydays = res.data[0].loanOverDueDays;
            this.btn1 = "还部分逾期";
            this.btn2 = "还逾期";
            console.log(resp[1].data.repayPlanTerms);
            resp[1].data.repayPlanTerms.forEach((item) => {
              const result = { ...item };
              if (result.overdue) {
                this.resdata.push(result);
                // 逾期
                // 本金
                this.delayinfos.Principal = Big(this.delayinfos.Principal || 0).plus(
                  Big(result.payableTermPrincipal || 0)
                    .div(100)
                    .toFixed(2),
                );
                // 息费
                this.delayinfos.interAndFee = Big(this.delayinfos.interAndFee || 0).plus(
                  Big(result.payableInterAndFee || 0)
                    .div(100)
                    .toFixed(2),
                );
                // 利息
                this.delayinfos.Interest = Big(this.delayinfos.Interest || 0).plus(
                  Big(result.payableTermInterest || 0)
                    .div(100)
                    .toFixed(2),
                );
                // 服务费
                this.delayinfos.serviceFee = Big(this.delayinfos?.serviceFee || 0).plus(
                  Big(result?.termServiceFee || 0)
                    .div(100)
                    .toFixed(2),
                );
                this.delayinfos.Terms.push(result.termNo);
                // 逾期罚息:应还本金罚息+应还利息罚息
                let termOverdueFee = Big(result.payableTermPrinPenalty || 0)
                  .plus(result.payableTermInterPenalty || 0)
                  .div(100)
                  .toFixed(2);
                if (
                  (termOverdueFee === 0 || termOverdueFee === "0.00") &&
                  result.payableTermPenalty
                ) {
                  termOverdueFee = Big(termOverdueFee || 0)
                    .plus(result.payableTermPenalty || 0)
                    .div(100)
                    .toFixed(2);
                }
                this.delayinfos.OverdueFee = Big(this.delayinfos.OverdueFee || 0).plus(
                  termOverdueFee,
                );
                // 优惠券
                this.delayinfos.ReductionAmount = Big(this.delayinfos.ReductionAmount || 0).plus(
                  Big(result.termReductionAmount || 0)
                    .minus(Big(result.paidTermReductionAmount || 0))
                    .div(100)
                    .toFixed(2),
                );
                // 试算期次
                this.delayinfos.repayTerm =
                  this.delayinfos.repayTerm > result.termNo
                    ? this.delayinfos.repayTerm
                    : result.termNo;
              } else if (result.termStatus !== 1) {
                result.title = `${result.termNo}/${resp[1].data.totalTerm}期 | ${result.shouldRepayDate.substring(4, 6)}月待还`;
                result.shouldRepayDate = `${result.shouldRepayDate.substring(0, 4)}年${result.shouldRepayDate.substring(4, 6)}月${result.shouldRepayDate.substring(6, 8)}日`;
                result.sendamt = Big(result.payableTermAmount || 0).minus(
                  Big(result.termReductionAmount || 0).minus(
                    Big(result.paidTermReductionAmount || 0),
                  ),
                );
                result.termAmount = Big(result.sendamt).div(100).toFixed(2);
                result.termPrincipal = Big(result.payableTermPrincipal || 0)
                  .div(100)
                  .toFixed(2);
                result.termInterAndFee = Big(result.payableInterAndFee || 0)
                  .div(100)
                  .toFixed(2);
                result.termInterest = Big(result.payableTermInterest || 0)
                  .div(100)
                  .toFixed(2);
                result.termReductionAmount = Big(result.termReductionAmount || 0)
                  .minus(Big(result.paidTermReductionAmount || 0))
                  .div(100)
                  .toFixed(2);
                result.termPenalty = Big(result.termPenalty || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermPrincipal = Big(result.paidTermPrincipal || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermInterest = Big(result.paidTermInterest || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermAmount = Big(result.paidTermAmount || 0)
                  .div(100)
                  .toFixed(2);
                result.termServiceFee = Big(result.termServiceFee || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermServiceFee = Big(result.paidTermServiceFee || 0)
                  .div(100)
                  .toFixed(2);
                result.paidInterAndFee = Big(result.paidInterAndFee || 0)
                  .div(100)
                  .toFixed(2);
                this.active1.repayPlanTerms.push(result);
                this.totalamount = Big(this.totalamount || 0).plus(Number(result.termAmount));
                this.sendtotalamt = Big(this.sendtotalamt || 0).plus(Number(result.sendamt));
              }
            });
          } else {
            resp[1].data.repayPlanTerms.forEach((item) => {
              const result = { ...item };
              if (result.termStatus !== 1) {
                this.resdata.push(item);
                result.title = `${item.termNo}/${resp[1].data.totalTerm}期 | ${item.shouldRepayDate.substring(4, 6)}月待还`;
                result.shouldRepayDate = `${result.shouldRepayDate.substring(0, 4)}年${result.shouldRepayDate.substring(4, 6)}月${result.shouldRepayDate.substring(6, 8)}日`;
                result.sendamt = Big(result.payableTermAmount || 0).minus(
                  Big(result.termReductionAmount || 0).minus(
                    Big(result.paidTermReductionAmount || 0),
                  ),
                );
                result.termAmount = Big(result.sendamt).div(100).toFixed(2);
                result.termPrincipal = Big(result.payableTermPrincipal || 0)
                  .div(100)
                  .toFixed(2);
                result.termInterAndFee = Big(result.payableInterAndFee || 0)
                  .div(100)
                  .toFixed(2);
                result.termInterest = Big(result.payableTermInterest || 0)
                  .div(100)
                  .toFixed(2);
                result.termReductionAmount = Big(result.termReductionAmount || 0)
                  .minus(Big(result.paidTermReductionAmount || 0))
                  .div(100)
                  .toFixed(2);
                result.termPenalty = Big(result.termPenalty || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermPrincipal = Big(result.paidTermPrincipal || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermInterest = Big(result.paidTermInterest || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermAmount = Big(result.paidTermAmount || 0)
                  .div(100)
                  .toFixed(2);
                result.termServiceFee = Big(result.termServiceFee || 0)
                  .div(100)
                  .toFixed(2);
                result.paidTermServiceFee = Big(result.paidTermServiceFee || 0)
                  .div(100)
                  .toFixed(2);
                result.paidInterAndFee = Big(result.paidInterAndFee || 0)
                  .div(100)
                  .toFixed(2);
                if (result?.termNo === res.data[0].currentTerm) {
                  this.clear = false;
                  this.issuedetail = { ...result };
                } else {
                  this.active1.repayPlanTerms.push(result);
                  this.totalamount = Big(this.totalamount || 0).plus(Number(result.termAmount));
                  this.sendtotalamt = Big(this.sendtotalamt || 0).plus(Number(result.sendamt));
                }
                if (res.data[0].currentTerm === resp[1].data.repayPlanTerms.length) {
                  this.isLastTerm = true;
                }
              }
            });
          }
        }
        // 锁期固定，其他随借随还
        if (resp[1].code === 0) {
          this.random = resp[1].data?.repayMethod !== 4;
          if (this.btn1 === "提前还款" && this.supportRepayType.find((item) => item.type === 2))
            this.btn1 = "全部结清";
          // 本期还清,本期数据来自借款详情
          if (this.clear) {
            this.issuedetail = { ...resp[1].data.repayPlanTerms[res.data[0].currentTerm - 1] };
            this.issuedetail.sendamt = Big(this.issuedetail.payableTermAmount || 0).minus(
              Big(this.issuedetail.termReductionAmount || 0).minus(
                Big(this.issuedetail.paidTermReductionAmount || 0),
              ),
            );
            this.issuedetail.sendpaidTermAmount = Big(this.issuedetail.paidTermAmount || 0).minus(
              Big(this.issuedetail.termReductionAmount || 0),
            );
            this.issuedetail.termAmount = Big(this.issuedetail.sendamt || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.termInterAndFee = Big(this.issuedetail.payableInterAndFee || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.termPrincipal = Big(this.issuedetail.payableTermPrincipal || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.termInterest = Big(this.issuedetail.payableTermInterest || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.termReductionAmount = Big(
              this.issuedetail.paidTermReductionAmount || 0,
            )
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermPrincipal = Big(this.issuedetail.paidTermPrincipal || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidInterAndFee = Big(this.issuedetail.paidInterAndFee || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermInterest = Big(this.issuedetail.paidTermInterest || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermAmount = Big(this.issuedetail.sendpaidTermAmount || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermPrinPenalty = Big(this.issuedetail.paidTermPrinPenalty || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermInterPenalty = Big(this.issuedetail.paidTermInterPenalty || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidTermServiceFee = Big(this.issuedetail.paidTermServiceFee || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.termServiceFee = Big(this.issuedetail.termServiceFee || 0)
              .div(100)
              .toFixed(2);
            this.issuedetail.paidPenalty = Big(this.issuedetail.paidTermPrinPenalty || 0).plus(
              this.issuedetail.paidTermInterPenalty || 0,
            );
          }
        }
      }
    },

    parseSupportRepayType(supportRepayType) {
      return supportRepayType.map((item) => this.repayTypeMaps[item.type]).join("&");
    },

    getSupportRepayEnable(supportRepayType) {
      const enable = supportRepayType.find((item) => !item.disabled) !== undefined;
      return enable ? "YES" : "NO";
    },
  },
});
