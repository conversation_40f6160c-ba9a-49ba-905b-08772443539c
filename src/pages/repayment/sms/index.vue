<script setup>
import { computed, onMounted, onServerPrefetch, onUnmounted, ref } from "vue";
import lodash from "lodash";
import useStore from "./store";
import { amountNumberFormat, initStore, request } from "../../../helpers/utils";
import { back, goto, regNativeEvent, report } from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import successicon from "/GeneralStatus_GreenSuccess.svg";
import failicon from "/GeneralStatus_RedFailed.svg";
import waiticon from "/GeneralStatus_YellowProcessing.svg";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import CreditAdjustIncentiveCard from "./components/CreditAdjustIncentiveCard.vue";
import { RepayStatus } from "./util";
import CouponIncentiveCard from "./components/CouponIncentiveCard.vue";
import { reportDoneClick, reportLoanMoreButtonClick, reportLoanMoreButtonView } from "./report";
import BankTransferIncentiveCard from "./components/BankTransferIncentiveCard.vue";
import { repayStatusQuery } from "../../../api/repay";
import { repayStatusMap } from "../../../api/typedef/repay";

const { store, data } = initStore(useStore);
// 提交请求发送断网未取到状态标志
const disconAfter = ref(false);

onServerPrefetch(store.initial);

function gotohome() {
  reportDoneClick();
  if (
    data.value.queryresult?.repayResult &&
    (data.value.queryresult.repayStatus === 2 || data.value.queryresult.repayStatus === 3)
  ) {
    setWithExactExpireUnit("U_HOME_HIDE_SHOW_ANIMATION", true, 7, "D");
  }
  if (
    data.value.queryresult.repayStatus === 1 ||
    (data.value.queryresult?.repayResult &&
      (data.value.queryresult.repayStatus === 2 || data.value.queryresult.repayStatus === 3))
  ) {
    goto("/wallet-loan-web/pages/index", false, true);
  } else {
    // 还款失败返回还款页
    setWithExactExpireUnit(`U_REPAY_FAIL_${data.value.param.userId}`, "true", 1, "D");
    back();
  }
}

function onClickLeft() {
  gotohome();
}

const savecountdown = () => {
  if (data.value.countdown > 0) {
    setWithExactExpireUnit(
      `U_REPAYMENT_SMS_${data.value.param.userId}`,
      new Date().getTime(),
      7,
      "D",
    );
  }
};
let intervalId = null;
const getLastCountdown = () => {
  const smsTime = getStore(`U_REPAYMENT_SMS_${data.value.param.userId}`) || "";
  const newTime = new Date().getTime();
  const difftime = parseInt((newTime - smsTime) / 1000, 10) + 1;
  if (difftime > 0 && difftime < 60) {
    data.value.countdown = 60 - difftime;
  } else {
    data.value.countdown = 59; // 倒计时60秒
    savecountdown();
  }
  intervalId = setInterval(() => {
    if (data.value.countdown > 0) {
      data.value.countdown -= 1;
    } else {
      clearInterval(intervalId);
    }
  }, 1000);
};

regNativeEvent("onBack", () => {
  gotohome();
});

const startCountdown = () => {
  if (data.value.countdown > 0) {
    return;
  }
  store.sendVerifyCode();
  if (data.value.param.needResign === "false") {
    data.value.sendmessage = `已发送至手机号 ${data.value.phonenumber}`;
  } else {
    data.value.sendmessage = `已向您的银行卡(${data.value.param.bankCardNo})的预留手机号发送了一条验证码`;
  }
  getLastCountdown();
};

const handleNetwork = () => {
  if (disconAfter.value) {
    disconAfter.value = false;
    goto("/wallet-loan-web/pages/index", false, true);
  }
};

async function submit() {
  data.value.smserror = "";
  if (data.value.smscode.length < 4) {
    return;
  }
  const param = {
    totalAmount: data.value.param.totalAmount,
    repayType: data.value.param.repayType,
    bankCardId: data.value.param.bankCardId,
    smsCode: data.value.smscode,
    needResign: data.value.needResign,
    repayPart: data.value.param.repayPart === "true",
    repayItemList: [
      {
        couponNo: data.value.param.couponNo,
        reductionAmount: data.value.param.reductionAmount,
        outOrderNo: data.value.param.outOrderNo,
        repayAmount: data.value.param.totalAmount,
        repayTerms: getStore(`U_REPAYTERMS_${data.value.param.userId}`) || [],
      },
    ],
    serialNo: data.value.param.serialNo,
  };
  if (data.value.param.repayPart === "false") {
    if (data.value.param.termNo) {
      // 逾期还全部
      if (data.value.param.isdelay === "true") {
        param.repayItemList = [
          {
            couponNo: data.value.param.couponNo,
            reductionAmount: data.value.param.reductionAmount,
            outOrderNo: data.value.param.outOrderNo,
            repayAmount: data.value.param.totalAmount,
            repayTerms: getStore(`U_REPAYTERMS_${data.value.param.userId}`) || [],
          },
        ];
      } else {
        // 正常还本期
        param.repayItemList = [
          {
            couponNo: data.value.param.couponNo,
            reductionAmount: data.value.param.reductionAmount,
            outOrderNo: data.value.param.outOrderNo,
            repayAmount: data.value.param.totalAmount,
            repayTerms: getStore(`U_REPAYTERMS_${data.value.param.userId}`) || [],
          },
        ];
      }
    }
  }
  data.value.queryresult.repayStatus = 1;
  const res = await request("/loan/api/repay/submit", { encryptedParams: param });
  if (res.code === 0) {
    // 正常还款 缓存repayNo,重新加载进来后有的话直接查询结果
    setWithExactExpireUnit(
      `U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`,
      res.data?.repayNo,
      7,
      "D",
    );
    data.value.issend = true;
    store.Polling(res.data?.repayNo);
  } else if (res?.message === "无法连接服务器，请稍后重试！") {
    data.value.queryresult.repayStatus = 99;
    // 断网后重连无还款结果，跳转回首页
    disconAfter.value = true;
    setNetwork();
  } else if (store.ignoreSmsCode) {
    data.value.queryresult.repayStatus = 4;
    store.isLoading = false;
  } else {
    data.value.queryresult.repayStatus = 99;
    data.value.smserror = res.message;
    if (data.value.param.needResign === "false") {
      data.value.sendmessage = `请输入发送至手机号 ${data.value.phonenumber}的验证码`;
    } else {
      data.value.sendmessage = `请输入发送至银行卡(${data.value.param.bankCardNo})的预留手机号的验证码`;
    }
  }
}

const gotosms = lodash.debounce(() => {
  submit();
}, 1000);

regNativeEvent("onResume", async () => {
  const repayNo = sessionStorage.getItem("repayNo");
  if (repayNo) {
    const bankTransferStartTime = sessionStorage.getItem("bankTransferStartTime");
    sessionStorage.removeItem("bankTransferStartTime");
    sessionStorage.removeItem("repayNo");
    const diff = Date.now() - Number(bankTransferStartTime);
    if (diff > 10000) {
      store.showQueryRepayStatusDialog = true;
      const repayStatusRes = await repayStatusQuery(repayNo);
      store.showQueryRepayStatusDialog = false;
      if (repayStatusRes?.code === 0) {
        const { repayStatus } = repayStatusRes.data;
        if (repayStatus !== repayStatusMap.Repaying) {
          goto(
            `/wallet-loan-web/pages/repayment/record?repayNo=${repayNo}&repayResult=${repayStatus}`,
            false,
          );
        }
      }
    }
  }
});

onMounted(async () => {
  window.addEventListener("online", handleNetwork);
  const storageRepayNo = getStore(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`) || "";
  if (storageRepayNo) {
    // 重新加载出出来的
    data.value.issend = true;
    store.Polling(storageRepayNo);
  }
  if (store.ignoreSmsCode) {
    submit();
  } else {
    getLastCountdown();
  }

  report("wallet_page_view", {
    page_name: " repayment_sms_page",
  });
});

onUnmounted(() => {
  clearInterval(intervalId);
});

regNativeEvent("onNetChange", () => {
  if (window?.navigator?.onLine && !data.value.issend) {
    gotosms();
  }
});

// 还款状态
const status = computed(() => data.value.queryresult?.repayStatus);

// 是否还款成功或部分成功
const isSuccessOrPartial = computed(() => {
  const s = status.value;
  return (
    s !== undefined &&
    [RepayStatus.SUCCESS, RepayStatus.PARTIAL_SUCCESS].indexOf(s) !== -1 &&
    !store.isLoading
  );
});

// 是否还款中
const isPending = computed(() => status.value === RepayStatus.PENDING || store.isLoading);

// 是否还款失败
const isFailure = computed(() => status.value === RepayStatus.FAILURE && !store.isLoading);

const currentIcon = computed(() => {
  if (isSuccessOrPartial.value) return successicon;
  if (isPending.value) return waiticon;
  if (isFailure.value) return failicon;
  return null;
});

const currentHeading = computed(() => {
  if (isSuccessOrPartial.value) return "还款成功";
  if (isPending.value) return "还款处理中";
  if (isFailure.value) return "还款失败";
  return "";
});

const currentSubHeading = computed(() => {
  if (isPending.value) return "正在处理，请稍等";
  return null;
});

const failureMessage = computed(() => store?.queryresult?.repayResult);

// 是否展示下期还款信息
const showNextPaymentInfo = computed(() => {
  return isSuccessOrPartial.value && store?.remainLimit !== 0;
});

// 是否展示银行转账卡片
const showBankTransferCard = computed(() => {
  return (
    store.param.sdkVersionCode >= ********* &&
    store?.supplierInfo?.supportTransferRepay === 1 &&
    isFailure.value
  );
});

const showDivider = computed(() => store?.nextamt !== 0 && store?.lastdate !== "");

const showCompletionButton = computed(() => {
  return !isPending.value && status.value !== undefined;
});

const showCreditAdjustIncentiveCard = computed(
  () =>
    isSuccessOrPartial.value && !!store?.changeType && !store.isLoading && store?.remainLimit !== 0,
);

const showCouponIncentiveCard = computed(
  () =>
    isSuccessOrPartial.value &&
    store?.usableCoupons?.length > 0 &&
    !store.isLoading &&
    !(
      ([1].includes(store.creditInfo?.accessResult) &&
        [1].includes(store.creditInfo?.status) &&
        [1, 2, 3, 4].includes(store.creditInfo?.limitUseErrStatus)) ||
      store.loanOrderStatus?.applyStatus === 1 ||
      store.totalAvailableLimit / 100 < 500
    ),
);

const showLoanMoreButton = computed(() => {
  const isShow =
    isSuccessOrPartial.value &&
    !showCreditAdjustIncentiveCard.value &&
    !showCouponIncentiveCard.value &&
    !(
      ([1].includes(store.creditInfo?.accessResult) &&
        [1].includes(store.creditInfo?.status) &&
        [1, 2, 3, 4].includes(store.creditInfo?.limitUseErrStatus)) ||
      store.loanOrderStatus?.applyStatus === 1 ||
      store.totalAvailableLimit / 100 < 500
    );

  if (isShow) {
    reportLoanMoreButtonView();
  }

  return isShow;
});

const doneButtonType = computed(() => {
  if (showLoanMoreButton.value) return "default";

  return "primary";
});

function gotoCalc() {
  reportLoanMoreButtonClick();
  const { creditInfo } = data.value;
  if (
    ([1].includes(creditInfo.accessResult) &&
      [1].includes(creditInfo.status) &&
      [1, 2, 3, 4].includes(creditInfo.limitUseErrStatus)) ||
    data.value.loanOrderStatus?.applyStatus === 1 ||
    data.value.totalAvailableLimit / 100 < 500
  ) {
    goto("/wallet-loan-web/pages/index", false, true);
  } else {
    goto(
      `/wallet-loan-web/pages/loan/calc?amount=${data.value.totalAvailableLimit / 100}&fromPage=repayResult`,
    );
  }
}
</script>

<template>
  <div class="bg">
    <hnr-nav-bar transparent="true" class="nav-padding-top" :title="data.issend ? '' : '短信验证'">
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div v-if="!data.issend" class="loading">
      <div class="head1">输入短信验证码</div>
      <div class="head2">{{ data.sendmessage }}</div>
      <hnr-card
        type="empty"
        style="
          width: calc(
            100% - var(--hnr-list-card-margin-middle) - var(--hnr-list-card-margin-middle)
          ) !important;
        "
      >
        <!-- <hnr-field border type="number" v-model="data.smscode" placeholder="验证码" :withMargin="false" @update:model-value="onInput" class="fileds"> -->
        <hnr-field
          v-model="data.smscode"
          border
          type="digit"
          placeholder="验证码"
          :error-message="data.smserror"
          :with-margin="false"
          class="fileds"
          @update:model-value="gotosms"
        >
          <template #button>
            <hnr-button
              type="text"
              size="mini"
              :disabled="data.countdown > 0"
              @click="startCountdown"
              >{{ data.countdown === 0 ? "获取验证码" : `${data.countdown}s` }}</hnr-button
            >
          </template>
        </hnr-field>
      </hnr-card>
    </div>
    <div v-else class="loading">
      <!-- 状态图标区域 -->
      <div class="pubimg">
        <img v-if="currentIcon" class="icon" :src="currentIcon" alt="状态图标" />
      </div>

      <!-- 主标题 -->
      <div v-if="currentHeading" class="suchead1">{{ currentHeading }}</div>

      <div
        v-if="data?.reductionAmountDesc"
        class="cellLeft"
        style="
          text-align: center;
          margin: var(--hnr-elements-margin-vertical-M2) var(--hnr-max-padding-end) 0
            var(--hnr-max-padding-start);
        "
        v-html="data?.reductionAmountDesc"
      ></div>
      <!-- 副标题/错误信息 -->
      <div v-if="currentSubHeading" class="suchead2">{{ currentSubHeading }}</div>

      <div v-if="isFailure && failureMessage" class="suchead2">
        <div class="errmessage">{{ failureMessage }}</div>
      </div>

      <BankTransferIncentiveCard v-if="showBankTransferCard" />

      <!-- 下期还款信息区域 -->
      <div v-if="showNextPaymentInfo">
        <hnr-divider v-if="showDivider" class="supportdivider" line />

        <div v-if="data?.nextamt !== 0" class="cellLine1">
          <div class="cellLeft">下期还款金额</div>
          <div class="cellRight">{{ "￥" + amountNumberFormat(data.nextamt) }}</div>
        </div>
        <div v-if="data?.lastdate !== ''" class="cellLine2">
          <div class="cellLeft">下期还款时间</div>
          <div class="cellRight">{{ data.lastdate }}</div>
        </div>
      </div>

      <!-- 调额降息卡片 -->
      <div v-if="showCreditAdjustIncentiveCard" class="increase-box">
        <CreditAdjustIncentiveCard />
      </div>

      <!-- 优惠券卡片 -->
      <div v-else-if="showCouponIncentiveCard" class="increase-box">
        <CouponIncentiveCard />
      </div>

      <!-- 底部按钮 -->
      <div v-if="showCompletionButton" class="footer">
        <hnr-button :type="doneButtonType" class="subbtn" @click="gotohome"> 完成 </hnr-button>
        <hnr-button v-if="showLoanMoreButton" type="primary" class="subbtn" @click="gotoCalc">
          再借一笔
        </hnr-button>
      </div>

      <!--还款状态查询弹窗-->
      <hnr-dialog
        v-model:show="store.showQueryRepayStatusDialog"
        show-filter
        :show-confirm-button="false"
        :show-cancel-button="false"
      >
        <div class="query-dialog">正在获取还款信息<hnr-loading /></div>
      </hnr-dialog>
    </div>
  </div>
</template>

<style scoped>
.bg {
  user-select: none;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  height: 100%;
  width: 100%;
}
.loading {
  display: contents;
  align-items: center;
  justify-content: center;
}
.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 99;
  justify-content: center;
  display: flex;
  box-sizing: border-box;
  padding: 0 var(--dp24);
  gap: var(--dp12);
}

.subbtn {
  flex: 1;
  max-width: 271px !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}

.supportdivider {
  margin-top: var(--dp32);
  padding: 0 var(--dp24);
  width: auto;
}

.head1 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  margin-left: var(--hnr-max-padding-start);
  margin-right: var(--hnr-max-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L2);
  padding-bottom: var(--dp4);
  display: flex;
  justify-content: center;
}
.head2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  padding-bottom: var(--dp36);
  display: flex;
  justify-content: center;
}
.successcell {
  margin-top: var(--hnr-elements-margin-vertical-L2);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.pubimg {
  display: flex;
  justify-content: center;
  padding-top: var(--hnr-element-margin-vertical-XL);
  padding-bottom: var(--hnr-elements-margin-vertical-XL);
}
.suchead1 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  margin-left: var(--hnr-max-padding-start);
  margin-right: var(--hnr-max-padding-end);
  padding-top: var(--hnr-elements-margin-vertical-L);
  display: flex;
  justify-content: center;
  line-height: 1;
}
.suchead2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  justify-content: center;
  line-height: 1;
  margin-top: 12px;
}
.hnr-cell-box {
  background: var(--hnr-color-background-cardview);
}
:deep(.hnr-field__body) {
  padding: 0 var(--dp12);
}
:deep(.hnr-field__body--border .hnr-field__input-area) {
  height: var(--dp56);
  border-radius: var(--hnr-input-box-corner-radius);
  background: none;
  padding: 0;
}
:deep(.bg .hnr-cell__title) {
  color: var(--hnr-text-color-secondary) !important;
  font-size: var(--hnr-body-2) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
}
:deep(.bg .hnr-cell__value) {
  color: var(--hnr-text-color-secondary) !important;
  font-size: var(--hnr-body-2) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
}
.cellLine1 {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--dp24);
  padding-right: var(--dp24);
  margin-top: var(--hnr-elements-margin-vertical-L2);
}
.cellLine2 {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--dp24);
  padding-right: var(--dp24);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.cellLeft {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.errmessage {
  background: var(--hnr-color-background-cardview);
  display: flex;
  justify-content: center;
}
.cellRight {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
::v-deep(.hnr-field__error-message) {
  position: absolute;
  bottom: -27px;
  left: 12px;
}

.increase-box {
  padding: 0 12px;
  margin-top: var(--hnr-elements-margin-vertical-L2);
}

.bank-transfer-section {
  margin: 0 var(--hnr-elements-margin-horizontal-M2);
}

.query-dialog {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
