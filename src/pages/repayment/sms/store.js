import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import Big from "big.js";
import { request } from "../../../helpers/utils";
import { goto, report } from "../../../helpers/native-bridge";
import { getStore } from "../../../helpers/storage";
import { getCouponList } from "../../../api/coupon";
import { getCpConfig } from "../../../api/config";

export default defineStore("repayment/sms", {
  state: () => ({
    // 页面初始数据
    issend: false,
    needResign: false,
    param: {},
    smscode: "",
    countdown: 0,
    // 验证码报错内容
    smserror: "",
    // 提交结果
    result: {},
    afterresult: {},
    // 提交后查询结果
    queryresult: {},
    phonenumber: "",
    // 轮询结果状态
    repayStatus: 99,
    // 可借额度，提交完毕后查询到最新值
    remainLimit: 0,
    // 下期还款金额
    nextamt: 0,
    // 最晚还款时间
    lastdate: "",
    sendmessage: "",
    currentRate: 25,
    timer: null,
    // 1：仅调额，2：仅降息，3：调额并降息
    changeType: 0,
    // 调整临额时返回总额度（临额+固额）
    totalAmount: 0,
    // 调额以前的旧额度，单位：分
    oldLimit: 0,
    // 调整临额时返回总的可用额度（临额+固额）
    changeRemainLimit: 0,
    // 临额剩余天数
    tempLimitValidDays: -1,
    // 临价剩余天数
    tempPriceValidDays: -1,
    // 新年利率，单位：%，调整临额时不返回
    newApr: 0,
    // 老年利率，单位：%
    oldApr: 0,
    // 新日利率，单位：%
    newDayRate: 0,
    // 可用额度
    totalAvailableLimit: 0,
    loanProcess: null,
    loanOrderStatus: null,
    creditInfo: null,
    supprtReoffer: -1, // 还款成功是否支持reoffer
    promotion_type: [],
    // 可用优惠券
    usableCoupons: [],
    isLoading: true,
    ignoreSmsCode: false,
    reductionAmountDesc: "",
    // 是否展示还款状态查询弹窗
    showQueryRepayStatusDialog: false,
    // CP信息
    supplierInfo: {},
  }),
  actions: {
    async initial() {
      this.needResign = this.param.needResign;
      if (this.needResign === "false") {
        const [res, supplierInfoRes] = await Promise.all([
          request("/loan/api/credit/info", {}, { mock: false }),
          getCpConfig(),
        ]);
        this.supplierInfo = supplierInfoRes?.data || {};
        if (supplierInfoRes?.data?.needRepaySms === 0) {
          this.issend = true;
          this.smscode = 111111;
          this.ignoreSmsCode = true;
        } else if (res.code === 0) {
          this.$patch({ info: res.data });
          this.hidePhoneMiddleFour(res.data?.mobileNo);
        }
      } else {
        this.sendmessage = `已向您的银行卡(${this.param.bankCardNo})的预留手机号发送了一条验证码`;
      }
    },
    async queryApplyStatus() {
      this.loanProcess = getStore(`U_LOAN_PROCESS_${this.param.userId}`) || {};
      if (this.loanProcess && this.loanProcess.applyNo && this.loanProcess.isApplied) {
        try {
          const { applyNo } = this.loanProcess;
          const res = await request("/loan/api/loan/queryApplyStatus", { applyNo });
          if (res) {
            this.loanOrderStatus = res.data;
            return true;
          }
        } catch (error) {
          return false;
        }
      }
      return false;
    },
    async sendVerifyCode() {
      if (this.param.totalAmount && this.param.bankCardId && this.param.outOrderNo) {
        const resp = await request("/loan/api/repay/v2/sendVerifyCode", this.param);
        setTimeout(async () => {
          await request("/loan/api/user/operLog", {
            orderNo: this.param.outOrderNo,
            operType: 302,
            operResult: resp.code === 0 ? 1 : 2,
            supplier: 1,
            operTime: new Date().getTime(),
          });
        }, 0);
        if (resp.code === 0) {
          this.needResign = resp.data.needResign;
          if (resp.data.status === 0) {
            showToast({
              message: "验证码发送成功",
              position: "bottom",
            });
          }
        } else {
          showToast({
            message: resp.message,
            position: "bottom",
          });
        }
      }
    },

    hidePhoneMiddleFour(phoneNumber) {
      if (!phoneNumber) return;
      if (phoneNumber.length !== 11) {
        throw new Error("电话号码必须是11位");
      }
      // 提取前三位和后四位
      const firstThree = phoneNumber.substring(0, 3);
      const lastFour = phoneNumber.substring(7);
      // 生成4个星号代替中间四位
      const middleStars = "*".repeat(4);
      // 拼接结果
      this.phonenumber = firstThree + middleStars + lastFour;
      this.sendmessage = `已发送至手机号 ${this.phonenumber}`;
    },

    async query(repayNo) {
      const res = await request("/loan/api/repay/queryStatus", { repayNo }, { mock: false });
      if (res.data?.supprtReoffer === 0) {
        this.supprtReoffer = res.data?.supprtReoffer;
      }
      // status:1-查询中 2-还款成功 3-部分成功 4-还款失败
      if (res?.code === 0) {
        this.queryresult = res.data;
      } else if (res.message === "无法连接服务器，请稍后重试！" || res?.code === 171706) {
        // 网络连接断开 继续查询，请求内断网会自动弹出网络设置
        this.queryresult.repayStatus = 1;
      } else {
        this.queryresult.repayStatus = 4;
        this.queryresult.repayResult = res.message;
      }
      if (this.queryresult.repayStatus === 4) {
        clearInterval(this.timer);
        setTimeout(() => {
          report("wallet_page_view", {
            page_name: "repayment_result_failed_page",
          });
        }, 0);
        await this.handleStatus();
        this.isLoading = false;
      }
      if (this.queryresult.repayStatus === 2 || this.queryresult.repayStatus === 3) {
        clearInterval(this.timer);
        setTimeout(() => {
          report("wallet_page_view", {
            page_name: "repayment_result_success_page",
          });
        }, 0);
        const state = await Promise.all([
          this.queryCreditChangeFunc(repayNo),
          this.handleStatus(),
          this.queryApplyStatus(),
          this.queryUsableCoupons(),
        ]);
        this.changeType = state[0].changeType;
        this.isLoading = false;
      }
    },

    async queryUsableCoupons() {
      const queryUsableCouponsRes = await getCouponList();
      if (queryUsableCouponsRes?.code !== 0) {
        return;
      }

      this.usableCoupons = queryUsableCouponsRes.data.filter((item) => item.status === 1);
    },

    async queryCreditChangeFunc(repayNo) {
      if (this.supprtReoffer === 0) {
        return { changeType: 0 };
      }
      const res = await request("/loan/api/credit/queryCreditChange", { repayNo }, { mock: false });
      if (res.code !== 0) {
        return { changeType: 0 };
      }
      const resData = res.data;
      // 1：仅调额，2：仅降息，3：调额并降息
      let changeType = 0;
      if (resData?.limitChangeType === 1 && resData?.rateChangeType !== 2) {
        changeType = 1;
        this.promotion_type = ["limit"];
      }
      if (resData?.limitChangeType !== 1 && resData?.rateChangeType === 2) {
        changeType = 2;
        this.promotion_type = ["rate"];
      }
      if (resData?.limitChangeType === 1 && resData?.rateChangeType === 2) {
        changeType = 3;
        this.promotion_type = ["rate", "limit"];
      }
      report("wallet_page_view", {
        page_name: "loan_repay_page_promotion_area",
        promotion_type: this.promotion_type,
      });
      this.totalAmount = resData?.totalAmount;
      this.oldLimit = resData?.oldLimit;
      this.changeRemainLimit = resData?.remainLimit;
      this.tempLimitValidDays = resData?.tempLimitValidDays;
      this.tempPriceValidDays = resData?.tempPriceValidDays;
      this.newApr = resData?.newApr;
      this.oldApr = resData?.oldApr;
      this.newDayRate = resData?.newDayRate;
      return { changeType };
    },
    // 状态处理
    async handleStatus() {
      if (this.queryresult.repayStatus === 2 || this.queryresult.repayStatus === 3) {
        // 还款成功/部分还款成功,还本期的查下期额度 删除缓存
        const requestList = [request("/loan/api/credit/info", {})];
        const status = !(
          this.param.repayPart === "true" ||
          (this.param.repayType === "2" &&
            this.param.isLastTerm &&
            this.param.isLastTerm === "true") ||
          (this.param.repayType === "2" && !this.param.isLastTerm)
        ); // 逾期还部分不做处理
        if (status) {
          requestList.push(request("/loan/api/repay/plan", { outOrderNo: this.param.outOrderNo }));
          requestList.push(request("/loan/api/loan/detail", { outOrderNo: this.param.outOrderNo }));
        }
        const res = await Promise.all(requestList);
        this.remainLimit = Big(Number(res[0].code === 0 ? res[0].data.remainLimit : "0"))
          .div(100)
          .toFixed(2);
        if (res[0].code === 0) {
          this.creditInfo = res[0].data;
          if (res[0].data.totalAvailableLimit) {
            this.totalAvailableLimit = Number(res[0].data.totalAvailableLimit);
          } else {
            this.totalAvailableLimit = Number(res[0].data.remainLimit);
          }
        }
        if (status && res[1].code === 0 && res[1]?.data[0]?.repayPlanTerms.length > 0) {
          const nextdata = res[1]?.data[0]?.repayPlanTerms[0];
          this.nextamt = Big(Number(nextdata.termAmount)).div(100).toFixed(2);
          if (res[2].code === 0 && res[2]?.data?.repayPlanTerms.length > 0) {
            const reductionTermNo = nextdata.termNo - 1;
            if (reductionTermNo - 1 >= 0) {
              this.reductionAmountDesc =
                res[2]?.data?.repayPlanTerms[reductionTermNo - 1]?.reductionAmountDesc;
            }
          }
          this.lastdate = `${nextdata.shouldRepayDate.substring(4, 6)}月${nextdata.shouldRepayDate.substring(6, 8)}日`;
        }
      }
    },
    async Polling(repayNo) {
      await this.query(repayNo);
      this.timer = setInterval(() => {
        if (this.currentRate > 1) {
          this.currentRate -= 1;
          if (this.currentRate % 5 === 0 && this.queryresult.repayStatus === 1) {
            this.query(repayNo);
          }
        } else {
          clearInterval(this.timer);
          if (this.queryresult.repayStatus === 1) {
            showToast({
              message: "未查询到还款结果,即将返回首页",
              position: "bottom",
            });
            setTimeout(() => {
              goto("/wallet-loan-web/pages/index", false, true);
            }, 1000);
          }
        }
      }, 1000);
    },
  },
});
