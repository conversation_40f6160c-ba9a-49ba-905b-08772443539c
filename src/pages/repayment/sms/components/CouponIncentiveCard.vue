<script setup>
import { computed, onMounted } from "vue";
import { initStore } from "../../../../helpers/utils";
import useStore from "../store";
import { extractCouponUsageInstructions, formatTimestamp } from "../util";
import { reportLoanCouponModuleClick, reportLoanCouponModuleView } from "../report";
import { goto } from "../../../../helpers/native-bridge";

const { data, store } = initStore(useStore);

onMounted(() => {
  reportLoanCouponModuleView();
});

const earliestCoupon = computed(() => store.usableCoupons[0]);

const couponUsageInstruction = computed(() => {
  return extractCouponUsageInstructions(earliestCoupon.value);
});

function gotoCalc() {
  reportLoanCouponModuleClick();
  const { creditInfo } = data.value;
  if (
    ([1].includes(creditInfo.accessResult) &&
      [1].includes(creditInfo.status) &&
      [1, 2, 3, 4].includes(creditInfo.limitUseErrStatus)) ||
    data.value.loanOrderStatus?.applyStatus === 1 ||
    data.value.totalAvailableLimit / 100 < 500
  ) {
    goto("/wallet-loan-web/pages/index", false, true);
  } else {
    goto(
      `/wallet-loan-web/pages/loan/calc?amount=${data.value.totalAvailableLimit / 100}&fromPage=repayResult`,
    );
  }
}
</script>

<template>
  <div class="increase-con">
    <div class="increase-con-title">恭喜你，获得借款专属优惠券</div>
    <div>
      <div class="coupon-content">
        <div class="top_circle"></div>

        <div class="coupon-info">
          <h3 class="coupon-title">{{ earliestCoupon.couponName }}</h3>
          <div class="coupon-date">
            {{
              `${formatTimestamp(earliestCoupon.startTime)} - ${formatTimestamp(earliestCoupon.endTime)}`
            }}
          </div>
          <hnr-text-ellipsis class="coupon-usage" :content="`${couponUsageInstruction}`" />

          <div class="coupon-mark">注：利息券使用请以借款时为准</div>
        </div>

        <div class="coupon-action">
          <button class="relend-btn2" @click="gotoCalc">再借一笔</button>
        </div>

        <div class="bottom_circle"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.top_circle {
  z-index: 2;
  width: 12px;
  height: 12px;
  background: var(--hnr-color-card-background);
  position: absolute;
  left: 69%;
  top: -6.5px;
  border-radius: 6px;
}

.top_circle::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 6px;
  border: 1px solid rgba(219, 87, 26, 0.2);
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-sizing: border-box;
}

.bottom_circle {
  z-index: 2;
  width: 12px;
  height: 12px;
  background: var(--hnr-color-card-background);
  position: absolute;
  left: 69%;
  bottom: -6.5px;
  border-radius: 6px;
}

.bottom_circle::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 6px;
  border: 1px solid rgba(219, 87, 26, 0.2);
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  box-sizing: border-box;
}
.increase-con {
  background: var(--hnr-color-card-background);
  border-radius: var(--hnr-default-corner-radius-m);
  padding: var(--hnr-elements-margin-vertical-M2);
}
.increase-con-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--dp16);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}

.coupon-content {
  position: relative;
  color: var(--hnr-text-color-primary);
  background: rgba(250, 107, 25, 0.08);
  border-radius: var(--hnr-default-corner-radius-m);
  display: flex;
  border: 1px solid rgba(219, 87, 26, 0.2);
}

.coupon-info {
  width: 70%;
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-horizontal-M2);
}

.coupon-title {
  font-size: var(--dp14);
  font-weight: 700;
  margin: 0;
}

.coupon-date,
.coupon-usage {
  font-size: var(--dp10);
  color: var(--hnr-text-color-hint);
  margin: 0;
}
.coupon-mark {
  font-size: var(--dp10);
  color: var(--hnr-text-color-tertiary);
  margin: 0;
}
.coupon-action {
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: var(--dp12);
}

.relend-btn2 {
  background: var(--hnr-magic-color-9);
  font-weight: 500;
  border: none;
  border-radius: 999px;
  color: #fff;
  font-size: var(--dp12);
  padding: 6px 12px;
}

:deep(.hnr-text-ellipsis__action svg g) {
  fill: currentColor !important;
}

:deep(.hnr-text-ellipsis__action .hnr-icon) {
  height: var(--dp10);
}

:deep(.hnr-text-ellipsis) {
  max-height: 20vh;
  overflow: auto;
}

@media (prefers-color-scheme: dark) {
  .coupon-content {
    background: rgba(219, 87, 26, 0.2);
  }
}
</style>
