<template>
  <div style="margin-top: 24px">
    <hnr-sub-title :sticky="false">
      <hnr-index-anchor subtitle="推荐还款方式" />
      <hnr-cell round title="银行 APP 转账" label="无额度限制，推荐大额还款时使用">
        <template #value>
          <hnr-button size="mini" @click="handleTransferClick">去转帐</hnr-button>
        </template>
      </hnr-cell>
    </hnr-sub-title>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import lodash from "lodash";
import { transferRepay } from "../../../../api/repay";
import { initStore } from "../../../../helpers/utils";
import useStore from "../store";
import { getStore } from "../../../../helpers/storage";
import { goto } from "../../../../helpers/native-bridge";
import { reportBankTransferView, reportBankTransferClick } from "../report";

const { data } = initStore(useStore);

onMounted(() => {
  // 上报银行卡转账功能入口曝光
  reportBankTransferView();
});

// 处理转账按钮点击事件
const handleTransferClick = lodash.debounce(
  async () => {
    // 上报银行卡转账功能入口点击
    reportBankTransferClick();

    // 调用服务端接口获取订单号和url
    const param = {
      totalAmount: data.value.param.totalAmount,
      repayType: data.value.param.repayType,
      repayItemList: [
        {
          couponNo: data.value.param.couponNo,
          reductionAmount: data.value.param.reductionAmount,
          outOrderNo: data.value.param.outOrderNo,
          repayAmount: data.value.param.totalAmount,
          repayTerms: getStore(`U_REPAYTERMS_${data.value.param.userId}`) || [],
        },
      ],
    };
    const transferRepayRes = await transferRepay(param);

    if (transferRepayRes?.code === 0) {
      sessionStorage.setItem("bankTransferStartTime", String(Date.now()));
      sessionStorage.setItem("repayNo", transferRepayRes.data.repayNo);
      goto(transferRepayRes.data.repayUrl, true);
    }
  },
  2000,
  { leading: true, trailing: false },
);
</script>

<style scoped></style>
