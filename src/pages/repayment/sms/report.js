import { reportClick, reportView } from "../../../helpers/reportUtil";

/**
 * 出现优惠券模块
 * @returns {Promise<void>}
 */
export async function reportLoanCouponModuleView() {
  reportView({
    page_name: "repayment_success_loan_coupon_module",
  });
}

/**
 * 点击优惠券模块
 * @returns {Promise<void>}
 */
export async function reportLoanCouponModuleClick() {
  reportClick({
    click_name: "repayment_success_loan_coupon_module_click",
  });
}

/**
 * 完成按钮点击
 * @returns {Promise<void>}
 */
export async function reportDoneClick() {
  reportClick({
    click_name: "repayment_success_page_done_click",
  });
}

/**
 * 再借一笔按钮曝光
 * @returns {Promise<void>}
 */
export async function reportLoanMoreButtonView() {
  reportView({
    page_name: "repayment_success_loan_more_button",
  });
}

/**
 * 再借一笔按钮点击
 * @returns {Promise<void>}
 */
export async function reportLoanMoreButtonClick() {
  reportClick({
    click_name: "repayment_success_loan_more_button",
  });
}

// 银行卡转账功能入口曝光
export async function reportBankTransferView() {
  reportView({
    page_name: "repayment_result_bank_transfer_view",
  });
}

// 银行卡转账功能入口点击
export async function reportBankTransferClick() {
  reportClick({
    click_name: "repayment_result_bank_transfer_click",
  });
}
