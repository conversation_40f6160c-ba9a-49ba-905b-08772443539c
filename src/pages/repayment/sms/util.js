// 1-还款中，2-还款成功，3-部分还款成功，4-还款失败
import dayjs from "dayjs";

export const RepayStatus = {
  PENDING: 1,
  SUCCESS: 2,
  PARTIAL_SUCCESS: 3,
  FAILURE: 4,
};

// 提取出优惠券的使用说明
export function extractCouponUsageInstructions(coupon) {
  if (!coupon.useRules || coupon.useRules.length === 0) {
    return "使用说明：";
  }

  let instruction = "";
  coupon.useRules.forEach((item) => {
    instruction += item;
  });
  return `使用说明：${instruction}`;
}

export function formatTimestamp(timestamp) {
  return dayjs(timestamp).format("YYYY/M/D HH:mm");
}
