import { defineStore } from "pinia";
import { repayRecordQuery } from "../../../api/repay";

export default defineStore("repayment/record", {
  state: () => ({
    // 页面初始数据
    recordArr: [],
    param: {},
  }),
  actions: {
    async initial() {
      try {
        const data = await repayRecordQuery(this.param.loanOrderId, this.param.repayNo);

        if (data.code !== 0) {
          return;
        }
        const tempList = data.data;
        const keyArr = [];
        // 提取月份
        tempList.forEach((item) => {
          const key = item.repayTime?.substring(0, 7);
          if (keyArr.indexOf(key) === -1) {
            keyArr.push(key);
          }
        });
        // 月份倒序排列
        keyArr.sort((a, b) => {
          const [yearA, monthA] = a.split("-").map(Number);
          const [yearB, monthB] = b.split("-").map(Number);
          if (yearA === yearB) {
            return monthB - monthA;
          }
          return yearB - yearA;
        });
        keyArr.forEach((keyItem) => {
          const arr = tempList.filter((item) => item.repayTime.includes(keyItem));
          this.recordArr.push({
            yearMonth: keyItem,
            list: arr,
          });
        });
      } catch (error) {
        throw new Error(error);
      }
    },

    getRepayStatusNum(type) {
      // 使用 filter 方法筛选出 repayStatus 等于指定 type 的元素
      let num = 0;
      (this.recordArr || []).forEach((records) => {
        const filteredRecords = (records?.list || []).filter(
          (record) => record.repayStatus === type,
        );
        num += filteredRecords.length;
      });
      // 返回筛选后的数组长度，即符合条件的元素数量
      return num;
    },

    getTotalNum() {
      // 使用 filter 方法筛选出 repayStatus 等于指定 type 的元素
      let num = 0;
      (this.recordArr || []).forEach((records) => {
        num += (records?.list || []).length;
      });
      // 返回筛选后的数组长度，即符合条件的元素数量
      return num;
    },
  },
});
