import Big from "big.js";
import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request } from "../../../helpers/utils";
import { getLoanOrderDetail } from "../../../api/loan";
import { getCpConfig } from "../../../api/config";

export default defineStore("repayment/settle", {
  state: () => ({
    // 页面初始数据
    isall: false,
    showcards: false,
    showtips: false,
    param: {},
    checked3: 0,
    repayamt: "",
    repaydelayamt: "",
    wholeamt: 0,
    trialinfo: {},
    bankcardid: "",
    // 卡号
    bankCardNo: "",
    amterror: "",
    // 按钮loading
    btnloading: false,
    // 计算loading
    termloading: true,
    // 优惠券编号
    couponNo: "",
    // 优惠券金额
    discount: "",
    clickflag: false,
    initTotalInterest: "",
    repayTerms: [], // 本次还款的期次明细
    repayViolateFeeExplanation: "", // 违约金说明
    repayViolateInterestExplanation: "", // 违约利息说明
    loanOrderDetail: {},
    // 是否预留手机号错误
    isReservePhoneError: false,
    // CP信息
    supplierInfo: {},
  }),
  actions: {
    async initial() {
      this.isall = this.param.random === "false";
      const allparm = { ...this.param };
      if (this.param.isdelay !== "true") {
        allparm.repayTerm = "-1";
      }
      const [resp, loanOrderDetailRes, supplierInfoRes] = await Promise.all([
        request("/loan/api/repay/trial", allparm, { mock: false }),
        getLoanOrderDetail(this.param.outOrderNo),
        getCpConfig(),
      ]);

      this.supplierInfo = supplierInfoRes?.data || {};

      if (loanOrderDetailRes?.code === 0) {
        this.loanOrderDetail = loanOrderDetailRes.data;
      }

      if (resp.code !== 0) {
        this.errorCode = resp.code;
        return;
      }
      if (resp.code === 0) {
        this.trialinfo = resp.data;
        this.repayTerms = this.trialinfo?.repayTerms;
        this.wholeamt = Big(resp.data.totalAmount || 0).plus(resp.data.totalDiscount || 0);
        this.trialinfo.totalPrincipal = Big(this.trialinfo.totalPrincipal || 0)
          .div(100)
          .toFixed(2);
        this.trialinfo.totalInterest = Big(this.trialinfo.totalInterest || 0)
          .div(100)
          .toFixed(2);
        if (this.initTotalInterest === "") {
          this.initTotalInterest = this.trialinfo.totalInterest;
        }
        this.trialinfo.totalPenalty = Big(this.trialinfo.totalPenalty || 0)
          .div(100)
          .toFixed(2);
        this.trialinfo.totalDiscount = Big(this.trialinfo.totalDiscount || 0)
          .div(100)
          .toFixed(2);
        this.trialinfo.totalViolateFee = Big(this.trialinfo.totalViolateFee || 0)
          .div(100)
          .toFixed(2);
        this.trialinfo.totalServiceFee = Big(this.trialinfo.totalServiceFee || 0)
          .div(100)
          .toFixed(2);
        this.trialinfo.totalInterAndFee = Big(this.trialinfo.totalInterAndFee || 0)
          .div(100)
          .toFixed(2);
        if (this.trialinfo?.usedCoupons?.length > 0) {
          this.couponNo = this.trialinfo.usedCoupons[0].couponNo;
          this.discount = this.trialinfo.usedCoupons[0].discount;
        }
      }
      setTimeout(async () => {
        await request("/loan/api/user/operLog", {
          orderNo: this.param.outOrderNo,
          operType: 301,
          operResult: resp.code === 0 ? 1 : 2,
          supplier: 1,
          operTime: new Date().getTime(),
        });
      }, 0);
      this.termloading = false;
    },
    async prepayTrial() {
      // 部分提前还款期数送0
      this.couponNo = "";
      this.discount = "";
      const res = await request("/loan/api/repay/trial", {
        outOrderNo: this.param.outOrderNo,
        repayTerm: this.param.repayTerm,
        repayAmount: Big(Number(this.repayamt)).times(100),
        repayPart: Big(Number(this.repayamt)).times(100) !== this.wholeamt,
      });
      const allparm = { ...this.param };
      if (this.param.isdelay !== "true") {
        allparm.repayTerm = "-1";
      }
      const res2 = await request("/loan/api/repay/trial", allparm, { mock: false });
      let wholeamtTemp = 0;
      if (res2.code === 0) {
        wholeamtTemp = Big(res2.data.totalAmount || 0).plus(res2.data.totalDiscount || 0);
      }
      setTimeout(async () => {
        await request("/loan/api/user/operLog", {
          orderNo: this.param.outOrderNo,
          operType: 301,
          operResult: res?.code === 0 ? 1 : 2,
          supplier: 1,
          operTime: new Date().getTime(),
        });
      }, 0);
      if (res?.code !== 0) {
        return;
      }
      this.trialinfo = res?.code === 0 ? res.data : {};
      if (this.wholeamt > wholeamtTemp) {
        this.wholeamt = wholeamtTemp;
        this.repayamt = wholeamtTemp.div(100).toFixed(2);
        showToast({
          message: "剩余待还金额已更新",
          position: "bottom",
        });
      }
      this.repayTerms = this.trialinfo?.repayTerms;
      this.trialinfo.totalAmount = Big(this.trialinfo.totalAmount || 0);
      this.trialinfo.totalPrincipal = Big(this.trialinfo.totalPrincipal || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalInterest = Big(this.trialinfo.totalInterest || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalPenalty = Big(this.trialinfo.totalPenalty || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalDiscount = Big(this.trialinfo.totalDiscount || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalServiceFee = Big(this.trialinfo.totalServiceFee || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalInterAndFee = Big(this.trialinfo.totalInterAndFee || 0)
        .div(100)
        .toFixed(2);
      if (this.trialinfo?.usedCoupons?.length > 0) {
        this.couponNo = this.trialinfo.usedCoupons[0].couponNo;
        this.discount = this.trialinfo.usedCoupons[0].discount;
      }
      this.termloading = false;
    },
    async repayall() {
      this.couponNo = "";
      this.discount = "";
      const allparm = { ...this.param };
      if (this.param.isdelay !== "true") {
        allparm.repayTerm = "-1";
      }
      const res = await request("/loan/api/repay/trial", allparm, false);
      setTimeout(async () => {
        await request("/loan/api/user/operLog", {
          orderNo: this.param.outOrderNo,
          operType: 301,
          operResult: res?.code === 0 ? 1 : 2,
          supplier: 1,
          operTime: new Date().getTime(),
        });
      }, 0);
      if (res?.code !== 0) {
        return;
      }
      this.trialinfo = res?.code === 0 ? res.data : {};
      if (
        this.wholeamt > Big(this.trialinfo.totalAmount || 0).plus(this.trialinfo.totalDiscount || 0)
      ) {
        this.wholeamt = Big(this.trialinfo.totalAmount || 0).plus(
          this.trialinfo.totalDiscount || 0,
        );
        this.repayamt = Big(this.trialinfo.totalAmount || 0)
          .plus(this.trialinfo.totalDiscount || 0)
          .div(100)
          .toFixed(2);
        showToast({
          message: "剩余待还金额已更新",
          position: "bottom",
        });
      }
      this.repayTerms = this.trialinfo?.repayTerms;
      this.trialinfo.totalPrincipal = Big(this.trialinfo.totalPrincipal || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalInterest = Big(this.trialinfo.totalInterest || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalDiscount = Big(this.trialinfo.totalDiscount || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalPenalty = Big(this.trialinfo.totalPenalty || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalServiceFee = Big(this.trialinfo.totalServiceFee || 0)
        .div(100)
        .toFixed(2);
      this.trialinfo.totalInterAndFee = Big(this.trialinfo.totalInterAndFee || 0)
        .div(100)
        .toFixed(2);
      if (this.trialinfo?.usedCoupons?.length > 0) {
        this.couponNo = this.trialinfo.usedCoupons[0].couponNo;
        this.discount = this.trialinfo.usedCoupons[0].discount;
      }
      this.termloading = false;
    },
  },
});
