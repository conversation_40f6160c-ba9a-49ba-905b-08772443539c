import { defineStore } from "pinia";
import Big from "big.js";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request } from "../../../helpers/utils";
import { report } from "../../../helpers/native-bridge";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { getLoanRecord } from "../../../api/loan";

export default defineStore("repayment/list", {
  state: () => ({
    loaninfo: [],
    loanlist: [],
    param: {},
    totalAmount: 0,
    loading: true,
    requestType: 1,
    deductionEarlyMorning: 0, // 是否凌晨自动还款
    currentTermDisplay: 1, // 适配乐信的本期还款状态
    errorStatus: false,
    firstReport: {
      pendingRepay: false,
      repayClear: false,
    },
    clearRecordPageInfo: {
      loading: false,
      finished: false,
      currentPage: 1,
      pageSize: 6,
    },
    signStatus: 0, // 是否显示代扣资源位
    signResourceInfo: {}, // 代扣资源位信息
    isOnResume: false,
    signUrlValidTime: null,
    timer: null,
  }),
  actions: {
    getOrderStatus(item) {
      if (item.loanOverDueDays > 0) {
        return "已逾期";
      }
      if (!this.currentTermDisplay) {
        return "未结清";
      }
      if (item.currentTerm >= item.repayPlanTerms[0].termNo) {
        return "本期未还";
      }
      return "本期已还";
    },
    async getRepayPlan(generator) {
      let isTokenExpireNum = 0;
      const res = await request("/loan/api/repay/plan", {}, { mock: false });
      if (res?.code === 1201) {
        isTokenExpireNum += 1;
      }
      if (res?.code === 1201 && isTokenExpireNum === 1) {
        generator.next();
      }
      if (this.requestType === 2) {
        return;
      }
      setTimeout(() => {
        this.loading = false;
      }, 0);
      this.totalAmount = 0;
      if (res?.code === 0) {
        this.loanlist = res.data;
        this.loanlist?.forEach((item) => {
          Object.assign(item, {
            paydate: `${item.paydate.substring(0, 4)}年${item.paydate.substring(4, 6)}月${item.paydate.substring(6, 8)}日`,
            outstandingAmt: `每月${item.repayPlanTerms[0].shouldRepayDate.substring(6, 8)}日`,
            loanAmount: Big(item.loanAmount || 0)
              .div(100)
              .toFixed(2),
            duerepay: Big(
              (Array.isArray(item.repayPlanTerms) ? item.repayPlanTerms : []).reduce(
                (sum, term) => sum.plus(term.termAmount || 0),
                Big(0),
              ),
            )
              .div(100)
              .toFixed(2),
            loanOverDueAmount: Big(item.loanOverDueAmount || 0)
              .div(100)
              .toFixed(2),
            orderStatus: this.getOrderStatus(item),
          });
          this.totalAmount = Big(this.totalAmount)
            .plus(Number(item.duerepay || 0))
            .toFixed(2);
        });
      } else {
        this.errorStatus = true;
        showToast({
          message: res.message,
          position: "bottom",
        });
      }
    },
    async getResourcePositionInfo() {
      const sdkVersionCode = this.param?.sdkVersionCode;
      if (sdkVersionCode < 90007000) return;
      const res = await request(
        "/loan/api/user/withholdSignUrl",
        { returnUrl: "" },
        { mock: false },
      );
      if (res.code !== 0) {
        return;
      }
      this.signStatus = res.data?.signStatus;
      this.signResourceInfo = res.data?.signResourceInfo;
      this.signUrlValidTime = res.data?.signResourceInfo?.timeOut || 0;
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      if (this.signUrlValidTime > 0) {
        this.timer = setTimeout(() => this.getResourcePositionInfo(), this.signUrlValidTime);
      }
    },

    handleTodoRepaymentReport() {
      if (!this.firstReport?.pendingRepay) {
        if (this.errorStatus) {
          report("wallet_page_view", {
            page_name: "repayment_list_page",
            type: "待还款",
            loadingResult: "FAILED",
          });
        } else {
          this.firstReport.pendingRepay = true;
          // PV埋点事件
          report("wallet_page_view", {
            page_name: "repayment_list_page",
            type: "待还款",
            loadingResult: "SUCCESS",
            pendingRepayOrders: this.loanlist.length > 0 ? "YES" : "NO",
            overdueOrders:
              this.loanlist.findIndex((n) => n.loanOverDueDays > 0) >= 0 ? "YES" : "NO",
          });
        }
      }
    },

    async initial(type = 1) {
      function* f() {
        yield;
      }
      const generator = f();
      this.requestType = type;
      this.loanlist = [];
      this.loading = true;
      try {
        await Promise.all([this.getRepayPlan(generator), this.getResourcePositionInfo()]);
      } catch (error) {
        this.errorStatus = true;
        throw new Error(error);
      }
      this.loading = false;
      if (typeof window !== "undefined") {
        this.handleTodoRepaymentReport();
      }
    },
    async queryclear(type, isInit = false) {
      this.requestType = type;
      this.loanlist = [];
      if (isInit) {
        this.clearRecordPageInfo.currentPage = 1;
        this.clearRecordPageInfo.finished = false;
        this.loaninfo = [];
        this.loading = true;
      }
      try {
        // 如果cp不支持分页则不传分页参数
        const cpConfig = await getCurrentCpInfo();
        if (!cpConfig.supportLoanRecordPage) {
          this.clearRecordPageInfo.currentPage = null;
          this.clearRecordPageInfo.pageSize = null;
          this.clearRecordPageInfo.finished = true;
        }

        const res = await getLoanRecord({
          status: 4,
          currentPage: this.clearRecordPageInfo.currentPage,
          pageSize: this.clearRecordPageInfo.pageSize,
        });
        if (this.requestType === 1) {
          return;
        }
        setTimeout(() => {
          this.loading = false;
        }, 0);
        if (res?.code === 0) {
          const recordDtos = res.data?.records;
          recordDtos?.forEach((item) => {
            Object.assign(item, {
              applyDate: `${item.applyDate.substring(0, 4)}年${item.applyDate.substring(4, 6)}月${item.applyDate.substring(6, 8)}日`,
              loanAmount: Big(item.loanAmount || 0)
                .div(100)
                .toFixed(2),
              orderStatus: "已结清",
            });
          });

          this.loaninfo.push(...recordDtos);

          this.clearRecordPageInfo.loading = false;
          if (recordDtos?.length < this.clearRecordPageInfo.pageSize) {
            this.clearRecordPageInfo.finished = true;
          } else {
            this.clearRecordPageInfo.currentPage += 1;
          }
        } else {
          this.errorStatus = true;
          showToast({
            message: res.message,
            position: "bottom",
          });
        }
      } catch (error) {
        this.errorStatus = true;
        throw new Error(error);
      }
      if (typeof window !== "undefined") {
        if (!this.firstReport?.repayClear) {
          if (this.errorStatus) {
            report("wallet_page_view", {
              page_name: "repayment_list_page",
              type: "已结清",
              loadingResult: "FAILED",
            });
          } else {
            this.firstReport.repayClear = true;
            // PV埋点事件
            report("wallet_page_view", {
              page_name: "repayment_list_page",
              type: "已结清",
              loadingResult: "SUCCESS",
              clearOrders: this.loaninfo?.length > 0 ? "YES" : "NO",
            });
          }
        }
      }
    },
  },
});
