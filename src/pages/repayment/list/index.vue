<script setup>
import { onServerPrefetch, ref, onMounted, computed } from "vue";
import useStore from "./store";
import { initStore, amountNumberFormat } from "../../../helpers/utils";
import { goto, back, regNativeEvent, report } from "../../../helpers/native-bridge";
import noinfosicon from "/repayment/noinfos.svg";
import networkicon from "/repayment/network.svg";
import reloadingComponent from "../../../components/reloadingComponent.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import resourcePosition from "../../../components/resourcePosition.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

const { store, data } = initStore(useStore);

const element = ref(null);
const scroll = ref(null);
const ifFoldScreen = ref(false);

onServerPrefetch(store.initial);
function onClickLeft() {
  if (data.value.param.type === "1" || data.value.param.from === "push") {
    goto("/wallet-loan-web/pages/index", false, true);
    return;
  }
  back();
}
// 是否有网
const isOnLine = ref(true);
const tabActive = ref(0);
function onClickTab(tab) {
  if (window?.navigator?.onLine) {
    isOnLine.value = true;
  } else {
    isOnLine.value = false;
  }
  if (tab === 1) {
    store.queryclear(2, true);
  } else {
    store.initial(1);
  }
}

function gotoapply(outOrderNo, orderStatus) {
  if (outOrderNo) {
    report("wallet_page_click", {
      click_name: "repayment_list_repay_apply_click",
      orderStatus,
    });
    goto(`/wallet-loan-web/pages/repayment/apply?outOrderNo=${outOrderNo}`);
  }
}

// 查看借款详情
function gotoDetail(outOrderNo, orderStatus) {
  report("wallet_page_click", {
    click_name: "repayment_list_loan_detail_click",
    orderStatus,
  });
  goto(`/wallet-loan-web/pages/repayment/detail?loanOrderId=${outOrderNo}`);
}
// 查看还款记录
function gotoRepaymentRecord(outOrderNo, orderStatus) {
  report("wallet_page_click", {
    click_name: "repayment_list_repay_record_click",
    orderStatus,
  });
  goto(`/wallet-loan-web/pages/repayment/record?loanOrderId=${outOrderNo}`);
}

regNativeEvent("onBack", () => {
  if (data.value.param.type === "1" || data.value.param.from === "push") {
    goto("/wallet-loan-web/pages/index", false, true);
  } else {
    back();
  }
});

regNativeEvent("onResume", () => {
  store.isOnResume = true;
  store.getResourcePositionInfo();
});
function reload() {
  data.value.errorStatus = false;
  data.value.loading = true;
  onClickTab(tabActive.value);
}

// 数据量不足一页时不展示提示文案
const noMoreText = computed(() => {
  return data.value.loaninfo?.length < 6 ? "" : "没有更多了";
});

onMounted(async () => {
  store.handleTodoRepaymentReport();
  ifFoldScreen.value = window.matchMedia("(min-width: 719px)").matches;
  const res = await getCurrentCpInfo();
  // 是否凌晨自动还款
  data.value.deductionEarlyMorning = res?.deductionEarlyMorning;
  data.value.currentTermDisplay = res?.currentTermDisplay;
});
</script>

<template>
  <div class="bg">
    <div ref="element" style="height: 100%">
      <hnr-nav-bar transparent="true" title="我的借款" class="nav-padding-top">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <div class="infos">
        <no-ssr>
          <hnr-segmented-picker v-model="tabActive" class="segmented-box" @change="onClickTab">
            <hnr-segmented-picker-item>待还款</hnr-segmented-picker-item>
            <hnr-segmented-picker-item>已结清</hnr-segmented-picker-item>
          </hnr-segmented-picker>

          <template v-if="tabActive === 0">
            <div v-if="!isOnLine" class="noinfos scroll-container1">
              <div class="noinfos">
                <img v-show="!isOnLine" :src="networkicon" alt srcset class="imgs" />
                <text class="emptyct">网络未连接</text>
              </div>
            </div>
            <div v-else-if="data.loanlist?.length > 0" ref="scroll" class="scroll-container1">
              <div class="infotitle1">
                共{{ data.loanlist.length }}笔借款&nbsp;&nbsp;剩余待还总额
              </div>
              <div>
                <span class="infotitle2"> ¥ {{ amountNumberFormat(data.totalAmount) }}</span>
              </div>
              <div v-for="loanOrder in data.loanlist" :key="loanOrder.outOrderNo" class="card">
                <div class="line1">
                  <div class="title">
                    {{ loanOrder.paydate + " 借款 " }}
                    <span style="white-space: nowrap"
                      >¥ {{ amountNumberFormat(loanOrder.loanAmount) }}</span
                    >
                  </div>
                  <div class="title">
                    <hnr-chips
                      v-if="data.currentTermDisplay"
                      :class="
                        loanOrder.currentTerm >= loanOrder.repayPlanTerms[0].termNo
                          ? 'hnchips1'
                          : 'hnchips2'
                      "
                    >
                      {{
                        loanOrder.currentTerm >= loanOrder.repayPlanTerms[0].termNo
                          ? "本期未还"
                          : "本期已还"
                      }}</hnr-chips
                    >
                    <hnr-chips v-else class="hnchips1"> 未结清</hnr-chips>
                  </div>
                </div>
                <div v-if="!loanOrder.loanOverDueDays" class="container">
                  <span class="container1"
                    >剩余待还总额 ¥ {{ amountNumberFormat(loanOrder.duerepay) }}</span
                  >
                  <span class="container2">自动还款日期：{{ loanOrder.outstandingAmt }}</span>
                </div>
                <div v-else class="container">
                  <div style="margin-bottom: var(--hnr-elements-margin-vertical-S)">
                    <span class="container1"
                      >剩余待还总额 ¥ {{ amountNumberFormat(loanOrder.duerepay) }}，</span
                    >
                    <span class="overwarn"
                      >存在逾期金额
                      <span style="white-space: nowrap"
                        >¥ {{ amountNumberFormat(loanOrder.loanOverDueAmount) }}</span
                      >
                    </span>
                  </div>
                  <span class="container2"
                    >逾期后，每日<span v-if="data.deductionEarlyMorning === 1">(凌晨)</span
                    >将自动扣款</span
                  >
                </div>
                <div class="buttongroup">
                  <hnr-button
                    type="default"
                    size="mini"
                    @click="gotoDetail(loanOrder.outOrderNo, store.getOrderStatus(loanOrder))"
                    >借款详情</hnr-button
                  >
                  <hnr-button
                    type="default"
                    size="mini"
                    @click="
                      gotoRepaymentRecord(loanOrder.outOrderNo, store.getOrderStatus(loanOrder))
                    "
                    >还款记录</hnr-button
                  >
                  <hnr-button
                    class="buttongroup-last-btn"
                    type="primary"
                    size="mini"
                    @click="gotoapply(loanOrder.outOrderNo, store.getOrderStatus(loanOrder))"
                    >去还款</hnr-button
                  >
                </div>
              </div>
              <resourcePosition
                v-if="store.signStatus !== 0"
                class="resource-position"
                :style="{
                  height: store.signStatus === 2 ? '67px' : ifFoldScreen ? 'auto' : '130px',
                }"
                :sign-status="store.signStatus"
                :sign-resource-info="store.signResourceInfo"
                location="repayment_list"
                :is-on-resume="store.isOnResume"
                :if-fold-screen="ifFoldScreen"
              />
            </div>
            <div v-else class="noinfos scroll-container1">
              <no-ssr>
                <div v-if="!data.loading" class="noinfos">
                  <hnr-icon :name="noinfosicon" class="imgs icon" />
                  <text class="emptyct">暂无待还借款</text>
                </div>
                <div v-else class="noinfos">
                  <reloading-component
                    :loading-status="data.loading"
                    :error-status="!data.loading && data.errorStatus"
                    @try-click="reload"
                  />
                </div>
              </no-ssr>
            </div>
          </template>
          <template v-if="tabActive === 1">
            <div ref="scroll" class="scroll-container1">
              <div v-if="!isOnLine" class="noinfos">
                <div class="noinfos">
                  <img :src="networkicon" alt srcset class="imgs" />
                  <text class="emptyct">网络未连接</text>
                </div>
              </div>
              <div v-else-if="data.loaninfo?.length > 0">
                <hnr-list
                  v-model:loading="store.clearRecordPageInfo.loading"
                  :finished="store.clearRecordPageInfo.finished"
                  :immediate-check="false"
                  :finished-text="noMoreText"
                  @load="store.queryclear(2)"
                >
                  <div v-for="loanOrder in data.loaninfo" :key="loanOrder.outOrderNo" class="card">
                    <div class="line1">
                      <div class="title1">{{ loanOrder.applyDate + " 借款" }}</div>
                      <div><hnr-chips class="hnchips2">已结清</hnr-chips></div>
                    </div>
                    <div class="container9">¥ {{ amountNumberFormat(loanOrder.loanAmount) }}</div>
                    <div class="buttongroup">
                      <hnr-button
                        type="default"
                        size="mini"
                        @click="gotoDetail(loanOrder.outOrderNo, '已结清')"
                        >借款详情</hnr-button
                      >
                      <hnr-button
                        type="default"
                        size="mini"
                        @click="gotoRepaymentRecord(loanOrder.outOrderNo, '已结清')"
                        >还款记录</hnr-button
                      >
                    </div>
                  </div>
                </hnr-list>
              </div>
              <div v-else class="noinfos">
                <div v-if="!data.loading" class="noinfos">
                  <hnr-icon :name="noinfosicon" class="imgs icon" />
                  <text class="emptyct">暂无已结清借款</text>
                </div>
                <div v-else class="noinfos">
                  <reloading-component
                    :loading-status="data.loading"
                    :error-status="!data.loading && data.errorStatus"
                    @try-click="reload"
                  />
                </div>
              </div>
            </div>
          </template>
        </no-ssr>
      </div>
    </div>
  </div>
  <!-- 用于无网络的时候，提前加载无网络图标 -->
  <img :src="networkicon" alt srcset class="load-network-icon" />
</template>

<style scoped>
.resource-position {
  margin: var(--hnr-elements-margin-horizontal-M2);
}

#hnr-tab-2 {
  height: 100%;
}
#hnr-tab-3 {
  height: 100%;
}
.bg {
  word-spacing: normal;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  height: 100%;
  width: 100%;
  user-select: none;
}
.title {
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.title1 {
  margin-bottom: var(--hnr-elements-margin-vertical-S);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
:deep(.hnr-tabs__wrap) {
  background-color: var(--hnr-color-background-cardview) !important;
}
.infos {
  height: 100%;
}
:deep(.hnr-tabs__content) {
  height: 100%;
}

.hnr-tabs {
  height: 100%;
}
.hnr-swipe-item {
  align-items: baseline;
}
.overwarn {
  color: var(--hnr-color-error);
  font-weight: var(--hnr-font-weight-medium);
}
.infotitle1 {
  padding-left: var(--hnr-list-card-padding-start);
  margin-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-M2);
  margin-bottom: var(--hnr-elements-margin-vertical-S);
  display: flex;
  justify-content: flex-start;
  font-weight: var(--hnr-font-weight-regular);
  font-size: var(--hnr-subtitle-3);
  color: var(--hnr-text-color-primary);
}
.infotitle2 {
  padding-left: var(--hnr-list-card-padding-start);
  margin-left: var(--hnr-list-card-padding-start);
  margin-right: var(--hnr-list-card-padding-end);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  color: var(--hnr-text-color-primary);
}
.noinfos {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}
.card {
  background-color: var(--hnr-card-background);
  margin: var(--hnr-elements-margin-horizontal-M2);
  padding: var(--hnr-elements-margin-horizontal-M2);
  border-radius: var(--hnr-list-card-corner-radius);
}
.line1 {
  display: flex;
  justify-content: space-between;
}
.infotitle3 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  color: var(--hnr-text-color-secondary);
}
.container {
  display: flex;
  flex-direction: column;
}
.scroll-container {
  width: 100%;
  height: calc(
    100vh - var(--hnr-tabs-line-height) - 32px - var(--hnr-default-padding-bottom-fixed) -
      var(--hnr-nav-bar-height)
  );
  overflow-y: auto;
}
.scroll-container1 {
  width: 100%;
  height: calc(100vh - 148px);
  overflow-y: auto;
}
.scroll-container::-webkit-scrollbar {
  display: none;
}
.imgs {
  vertical-align: middle;
  margin: 0 var(--hnr-elements-margin-horizontal-S);
  width: var(--dp72);
  height: var(--dp72);
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
}
.emptyct {
  margin-top: var(--hnr-elements-margin-vertical-M);
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.container1 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-S);
}
.container2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}
.container9 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}
:deep(.hnr-swipe__track) {
  height: 100%;
  align-items: baseline;
}

:deep(.hnr-card__content--function) {
  max-height: 7rem;
}
.hnchips1 {
  background: #fcede8;
  border-radius: var(--hnr-default-corner-radius-xs);
  color: var(--hnr-color-warning) !important;
  font-size: var(--hnr-caption) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
  padding: 1.5px var(--hnr-elements-margin-horizontal-S) 0px !important;
  white-space: nowrap;
}
.hnchips2 {
  background: #e4f8e7;
  border-radius: var(--hnr-default-corner-radius-xs);
  color: var(--hnr-magic-color-4) !important;
  font-size: var(--hnr-caption) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
  padding: 1.5px var(--hnr-elements-margin-horizontal-S) 0px !important;
  white-space: nowrap;
}
.hnr-button {
  margin-right: var(--hnr-elements-margin-vertical-M) !important;
}
.hnr-tabs__navLeft {
  display: none !important;
  background: var(--hnr-color-background-cardview) !important;
}

:deep(.hnr-tabs__navLeft) {
  display: none !important;
  background: var(--hnr-color-background-cardview) !important;
}

.hnr-tabs__navRight {
  display: none !important;
  background: var(--hnr-color-background-cardview) !important;
}

:deep(.hnr-tabs__navRight) {
  display: none !important;
  background: var(--hnr-color-background-cardview) !important;
}
:deep(.hnr-tab:active) {
  background-color: var(--hnr-color-background-cardview) !important;
}
.buttongroup-last-btn {
  margin-right: 0 !important;
}
.load-network-icon {
  position: absolute;
  width: 1px;
  opacity: 0;
}
:deep(.icon svg g g g g rect) {
  fill: transparent;
}
:deep(.icon svg g g g g path) {
  fill: var(--hnr-color-foreground);
}
.segmented-box {
  padding: 16px 0 8px;
}
:deep(.hnr-loading__spinner--large) {
  width: 72px;
  height: 72px;
}
</style>
