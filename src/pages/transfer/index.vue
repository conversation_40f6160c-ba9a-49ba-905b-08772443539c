<script setup>
import { onMounted } from "vue";
import useStore from "./store";
import { initStore } from "../../helpers/utils";
import { goto, report, back } from "../../helpers/native-bridge";

const { store } = initStore(useStore);

onMounted(() => {
  document.title = "借钱";
  if (store.param.canBack) {
    back();
  } else if (store.param.targetUrl) {
    const targetUrl = decodeURIComponent(store.param.targetUrl);
    goto(targetUrl, false, false);
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "loan_transfer_defined_page",
        targetUrl:
          targetUrl.indexOf("?") !== -1
            ? targetUrl.substring(0, targetUrl.indexOf("?"))
            : targetUrl,
      });
    }, 0);
  } else {
    goto(store.param.initialTargetUrl, false, true);
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "loan_transfer_home_page",
        targetUrl: store.param.initialTargetUrl,
      });
    }, 0);
  }
});
</script>

<template>
  <hnr-loading
    style="display: flex; justify-content: center; align-items: center; height: 100vh; width: 100vw"
    size="large"
  />
</template>
