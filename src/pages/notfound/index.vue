<script setup>
import { onServerPrefetch, ref } from "vue";
import { back } from "../../helpers/native-bridge";
import useStore from "./store";
import { initStore } from "../../helpers/utils";
import IcsvgPublicBackFilled from "../../components/svg/icsvgPublicBackFilled.vue";

const { store } = initStore(useStore);
const ifloaded = ref(false);

onServerPrefetch(() => {});
const hangleRetry = () => {
  store.hangleRetry();
};
</script>

<template>
  <div>
    <no-ssr>
      <hnr-nav-bar class="nav-padding-top" title="借钱" transparent="true">
        <template #left>
          <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>

      <div class="notFoundServe-content" @click="hangleRetry">
        <div
          v-show="!store.loading"
          style="display: flex; flex-direction: column; align-items: center"
        >
          <hnr-icon
            class="icon-notServe"
            name="/wallet-loan-web/pages/entrance/UniversalIcon_ServerError.svg"
          />
          <img
            v-if="!ifloaded"
            class="icon-notServe"
            src="/public/entrance/UniversalIcon_ServerError.svg"
            @load="ifloaded = true"
          />
          <div v-show="ifloaded" class="notNetwork">服务异常，请点击屏幕重试</div>
        </div>

        <div v-show="store.loading" class="loading-content">
          <hnr-loading />
          <span class="notNetwork">正在加载...</span>
        </div>
      </div>
    </no-ssr>
  </div>
</template>

<style scoped>
:deep(.hnr-loading__spinner) {
  height: 72px;
  width: 72px;
}

.loading-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.notFoundServe-content {
  height: calc(100vh - 90px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.icon-notServe {
  width: var(--dp72);
  height: auto;
  color: var(--hnr-color-control-normal-2);
}
:deep(.icon-notServe svg g g polygon) {
  fill: var(--hnr-color-foreground);
}

.notNetwork {
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  color: var(--hnr-text-color-secondary);
  margin-top: var(--hnr-elements-margin-vertical-M);
}
</style>
