import { defineStore } from "pinia";

export default defineStore("notfound", {
  state: () => ({
    param: {},
    loading: false,
  }),
  actions: {
    hangleRetry() {
      document.cookie = `access_fail=0; expires=${new Date(0)}; path=/;`;
      this.loading = true;
      if (
        this.param.href &&
        this.param.href.startsWith(`${window.location.origin}/wallet-loan-web/pages/`)
      ) {
        window.location.replace(this.param.href);
      } else {
        window.location.replace("/wallet-loan-web/pages/index");
      }
      setTimeout(() => {
        this.loading = false;
      }, 1000);
    },
  },
});
