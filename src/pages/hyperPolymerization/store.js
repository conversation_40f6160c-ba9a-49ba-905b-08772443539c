import { defineStore } from "pinia";
import { request } from "../../helpers/utils";
import { getAdActivityListV2 } from "../../api/ad";

export default defineStore("hyperPolymerization", {
  state: () => ({
    param: {}, // 页面收到的参数
    walletBanners: [], // 运营信息
    suggestProducts: [],
    translationData: null,
  }),
  actions: {
    async initial() {
      const spaceCodes = ["loan_suggest_banner"];
      const res = await Promise.all([
        request("/loan/api/config/suggest/list", {}),
        getAdActivityListV2(spaceCodes),
      ]);

      const suggestProducts = res[0].data || [];
      const walletBanners = this.parseActivityData(res[1].data);
      this.$patch({
        walletBanners,
      });
      this.suggestProducts = suggestProducts;
    },
    parseActivityData(activityData) {
      return activityData?.loan_suggest_banner.activityList || [];
    },
    async loadTranslations(lang) {
      let translationData;
      if (lang.startsWith("en")) {
        translationData = await import("../entrance/res/en_US");
      } else {
        translationData = await import("../entrance/res/zh_CN");
      }
      return translationData.default;
    },
    handleSuppliers(suppliers) {
      if ((this.param.language || "zh-CN").startsWith("en")) {
        return suppliers.map((item) => ({
          ...item,
          supplierName: item.supplierEnName || item.supplierName,
        }));
      }
      return suppliers;
    },
  },
});
