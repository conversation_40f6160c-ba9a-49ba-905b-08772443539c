<script setup>
import { onServerPrefetch, onMounted, ref } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay } from "swiper/modules";
import lodash from "lodash";
import useStore from "./store";
import { initStore } from "../../helpers/utils";
import { back, goto, report, regNativeEvent } from "../../helpers/native-bridge";
import SuggestProductCard from "../component/SuggestProductCard.vue";
import "swiper/css";
import "swiper/css/autoplay";
import IcsvgPublicBackFilled from "../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

const navigationLeftTitle = "信贷严选";
const subTitle = "严选推荐";
const isRunBackground = ref(false);

/**
 * banner功能
 */
const autoplayOptions = {
  delay: 5000,
  stopOnLastSlide: false,
  disableOnInteraction: false,
  reverseDirection: false,
  waitForTransition: true,
};

const handleSlideChange = (event) => {
  if (isRunBackground.value) {
    return;
  }
  if (event.isEnd) {
    setTimeout(() => {
      report(
        "wallet_page_view",
        {
          page_name: "loan_banner",
          activityName: store.walletBanners[event.realIndex].activityName,
          policy_id: store.walletBanners[event.realIndex]?.abTestPolicy?.expConfCode,
        },
        -1,
      );
    }, 0);
  }
};

const onSwiperInit = (event) => {
  setTimeout(() => {
    report(
      "wallet_page_view",
      {
        page_name: "loan_banner",
        activityName: store.walletBanners[event.realIndex].activityName,
        policy_id: store.walletBanners[event.realIndex]?.abTestPolicy?.expConfCode,
      },
      -1,
    );
  }, 0);
};

/**
 * goto方法的改进
 */
const defineGoto = (url, showTitle, isClearTask) => {
  isRunBackground.value = true;
  goto(url, showTitle, isClearTask);
};

const clickBanner = (banner, index) => {
  defineGoto(banner.targetUrl, true);
  setTimeout(() => {
    report(
      "wallet_page_click",
      {
        click_name: "loan_banner_click",
        activityName: banner.activityName,
        index,
        policy_id: banner?.abTestPolicy?.expConfCode,
      },
      -1,
    );
  }, 0);
};

const clickBannerDebounce = lodash.debounce(clickBanner, 1000, { leading: true });

const onResume = () => {
  isRunBackground.value = false;
  // banner长度为1时上报PV行为
  if (store.walletBanners?.length === 1) {
    setTimeout(() => {
      report(
        "wallet_page_view",
        {
          page_name: "loan_banner",
          activityName: store.walletBanners[0].activityName,
          policy_id: store.walletBanners[0]?.abTestPolicy?.expConfCode,
        },
        -1,
      );
    }, 0);
  }
};

regNativeEvent("onResume", () => {
  onResume();
});

onMounted(() => {
  if (!store.suggestProducts?.length || !store.walletBanners?.length) {
    store.initial();
  }
  report("wallet_page_view", { page_name: "loan_hyper_polymerization_page" });
  onResume();
});

// 返回
const onClickLeft = () => {
  back();
};
const LoadingStatus = ref(false);
const ErrorStatus = ref(false);

const tryInitial = async () => {
  ErrorStatus.value = false;
  LoadingStatus.value = true;
  await store.initial();
  if (data.value.suggestProducts.length) {
    LoadingStatus.value = false;
    ErrorStatus.value = false;
  } else {
    LoadingStatus.value = false;
    ErrorStatus.value = true;
  }
};
</script>

<template>
  <div>
    <div class="navigation-box">
      <hnr-nav-bar class="nav-padding-top" transparent="true" :title="navigationLeftTitle">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
    </div>
    <div style="height: calc(100vh - 88px); overflow-y: scroll">
      <template v-if="data.suggestProducts.length">
        <div v-if="data.walletBanners.length" class="banner-container">
          <template v-if="data.walletBanners?.length > 1">
            <Swiper
              :modules="[Autoplay]"
              :autoplay="autoplayOptions"
              :loop="true"
              class="banner-wrapper"
              :slides-per-view="1"
              :space-between="0"
              @slide-change="handleSlideChange"
              @swiper-init="onSwiperInit"
            >
              <SwiperSlide
                v-for="(banner, index) in data.walletBanners"
                :key="banner.activityName"
                class="banner-slide"
              >
                <img
                  :src="banner.picUrl"
                  :alt="banner.name"
                  style="border-radius: var(--hnr-default-corner-radius-m); width: 100%"
                  @click="clickBanner(banner, index + 1)"
                />
              </SwiperSlide>
            </Swiper>
          </template>
          <template v-else-if="data.walletBanners?.length === 1">
            <div class="banner-slide">
              <img
                :src="data.walletBanners[0].picUrl"
                :alt="data.walletBanners[0].name"
                style="border-radius: var(--hnr-default-corner-radius-m); width: 100%"
                @click="clickBannerDebounce(data.walletBanners[0], 1)"
              />
            </div>
          </template>
        </div>
        <div
          style="
            color: var(--hnr-index-anchor-title-color);
            padding: 19px 24px 8px;
            box-sizing: border-box;
            font-size: calc(var(--hnr-index-anchor-title-font-size) * var(--hnr-large-rate));
            font-weight: var(--hnr-index-anchor-title-font-weight);
          "
        >
          {{ subTitle }}
        </div>
        <div>
          <SuggestProductCard :suggest-products="data.suggestProducts" location="贷超聚合页" />
        </div>
      </template>
      <template v-else>
        <reloading-component
          :loading-status="LoadingStatus"
          :error-status="ErrorStatus"
          @try-click="tryInitial"
        />
      </template>
    </div>
  </div>
</template>

<style scoped>
.banner-slide {
  flex: 0 0 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  border-radius: var(--hnr-default-corner-radius-m);
  /* 使用CSS变量设置圆角 */
}

.banner-wrapper {
  display: flex;
  transition: transform 0.5s ease;
}

.banner-slide {
  flex: 0 0 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  border-radius: var(--hnr-default-corner-radius-m);
  /* 使用CSS变量设置圆角 */
}

/* 设置宽高比为14:3 */
.banner-slide::before {
  content: "";
  float: left;
  padding-top: calc(9 / 21);
  /* (9 / 21) * 100% */
}

.banner-slide img {
  width: 100%;
  /* 使图片宽度适应容器宽度 */
  height: auto;
  /* 自动调整图片高度以保持宽高比 */
  aspect-ratio: calc(29 / 6);
  object-fit: cover;
  /* 覆盖整个容器，可能的话会裁剪图片以保持宽高比 */
}

.banner-container {
  position: relative;
  overflow: hidden;
  width: calc(100% - 2 * var(--hnr-elements-margin-horizontal-M2));
  margin-top: var(--hnr-elements-margin-horizontal-M2);
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
}
</style>
