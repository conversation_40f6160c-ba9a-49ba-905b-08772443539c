import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request, userBehaviorLogReporting } from "../../../helpers/utils";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { creditNext } from "../../../helpers/next";
import { encrypt, report } from "../../../helpers/native-bridge";
import { getLoanAdActivityConfig } from "../../../api/loanAdActivity";
import { urlMap } from "../../../helpers/constants";
import { getUserInfo } from "../../../api/user";

export default defineStore("credit/realname", {
  state: () => ({
    // 页面初始数据
    pageStatus: "1",
    nameErrorMessage: "",
    idCardErrorMessage: "",
    phoneErrorMessage: "",
    codeErrorMessage: "",
    smsCode: "",
    countdown: 0,
    radioCheck: false,
    dialogStatus: false,
    buttonDiable: true,
    nextLoading: false,
    failMessage: "",
    realNameInfo: {},
    readonlyStatus: false,
    param: {},
    suggestList: [],
    isSmsCode: false,
    phoneNum: "",
    activityInfo: {},
    fullErrorMessage: "",
    buttonHide: true,
    mobileNoReadonlyStatus: false,
    flowNo: "",
    supplierId: "",
    isDataLoading: false,
    isLoginLoading: false,
    redirectUrl: "",
  }),
  actions: {
    showToastFun(val) {
      showToast({
        message: val,
        wordBreak: "break-all",
      });
    },
    async doreport(orderNo, operType, operResult) {
      const params = {
        orderNo, // apply_no
        operType,
        operResult, // 1-成功，2-失败，3-取消
        operParams: "",
        supplier: 1,
        operTime: new Date(),
      };
      await request("/loan/api/user/operLog", params);
    },
    async userBind() {
      try {
        this.nextLoading = true;
        const event = { click_name: "realname_bind_click" };
        if (this.pageStatus === "3") {
          event.supplierId = this.activityInfo.preDivisionSupplier || 0;
          event.activityCode = this.activityInfo.activityCode;
        }
        event.isLogin = !!this.param.userId;
        event.isRealName = this.realNameInfo?.status === 1;
        report("wallet_page_click", event);
        const newSupplierId =
          this.pageStatus === "3" ? this.activityInfo.preDivisionSupplier || 0 : this.supplierId;
        setTimeout(() => {
          userBehaviorLogReporting("", 101, 2, 1, newSupplierId, new Date().getTime());
        }, 0);
        const storageData = getStore(`U_CREDIT_PROCESS_V2_${this.param.userId}`) || {};
        let params = {};
        if (this.realNameInfo.status !== 1) {
          params = { ...this.realNameInfo };
          params.mobileNo = this.realNameInfo.mobileNo;
          params.realNameStatus = params.status;
          delete params.status;
          params.smsCode = this.smsCode;
        } else {
          params.realNameStatus = this.realNameInfo.status;
          params.mobileNo = this.realNameInfo.mobileNo;
          params.smsCode = this.smsCode;
        }

        if (this.pageStatus === "3") {
          params.preDivisionSupplier = this.activityInfo?.preDivisionSupplier;
        }

        if (this.flowNo) {
          params.flowNo = this.flowNo;
        }

        params.verifySms = this.isSmsCode ? 1 : 0;
        // console.log("明文准入-绑定-入参", params)
        const res = await request(
          "/loan/api/user/v2/bind",
          { encryptedParams: params },
          { mock: false },
        );
        if (res.data?.accessResult === 0) {
          setTimeout(() => {
            this.doreport("", 101, 1);
            report("wallet_page_result", {
              page_name: "realname_bind_success",
            });
          }, 0);

          const newStorageData = {
            isFromSign: storageData.isFromSign,
            realInfo: await encrypt(JSON.stringify(this.realNameInfo)),
            supplier: res.data?.supplier,
            supplierName: res.data?.supplierName,
            verifyList: res.data?.verifyList,
            firstCp: res.data?.firstCp,
            flowNo: res.data?.flowNo,
          };
          setWithExactExpireUnit(
            `U_CREDIT_PROCESS_V2_${this.param.userId}`,
            newStorageData,
            7,
            "D",
          );
          creditNext(this.param.userId);
        } else if (res.data?.accessResult === 1) {
          setTimeout(() => {
            this.doreport("", 101, 2);
          }, 0);

          if (res.data?.errorCode === "170002") {
            this.nameErrorMessage = "";
            this.idCardErrorMessage = "";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "验证码错误";
            this.buttonDiable = true;
          } else if (res.data?.errorCode === "170211") {
            this.nameErrorMessage = "";
            this.idCardErrorMessage = "身份证非法或与已实名的身份证不匹配";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "";
            this.smsCode = "";
            this.buttonDiable = true;
          } else if (res.data?.errorCode === "170212") {
            this.nameErrorMessage = "姓名错误";
            this.idCardErrorMessage = "";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "";
            this.smsCode = "";
            this.buttonDiable = true;
          } else if (res.data?.errorCode === "170213") {
            this.nameErrorMessage = "";
            this.idCardErrorMessage = "有儿童帐号，请使用大于18岁身份证进行实名认证";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "";
            this.smsCode = "";
            this.buttonDiable = true;
          } else if (res.data?.errorCode === "170214") {
            this.nameErrorMessage = "";
            this.idCardErrorMessage = "暂不给儿童账号提供服务";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "";
            this.smsCode = "";
            this.buttonDiable = true;
          } else if (res.data?.errorCode === "170215") {
            this.nameErrorMessage = "";
            this.idCardErrorMessage = "已注册为非儿童帐号，请使用大于14岁身份证进行实名认证";
            this.phoneErrorMessage = "";
            this.codeErrorMessage = "";
            this.smsCode = "";
            this.buttonDiable = true;
          } else {
            if (res.data?.errorCode === "170221") {
              this.failMessage = "该身份证号已在其他荣耀账号申请借钱服务";
            } else if (res.data?.errorCode === "170222") {
              this.failMessage = "账号已经实名，不能重复实名";
            } else {
              this.failMessage = res.data?.errorDesc;
            }
            this.pageStatus = "2";
            this.getAdActivityList();
          }
          this.fullErrorMessage = [
            this.nameErrorMessage,
            this.idCardErrorMessage,
            this.phoneErrorMessage,
            this.codeErrorMessage,
          ]
            .filter((item) => item && item !== "")
            .join("，");
          setTimeout(() => {
            this.fullErrorMessage = "";
          }, 3000);
          this.nextLoading = false;
          this.dialogStatus = false;
        } else {
          setTimeout(() => {
            this.doreport("", 101, 2);
          }, 0);

          this.showToastFun(res.message);
          this.nameErrorMessage = "";
          this.idCardErrorMessage = "";
          this.phoneErrorMessage = "";
          this.codeErrorMessage = "";
          if (this.realNameInfo.status !== 1) {
            this.updateRealNameStatus();
            return;
          }
          this.nextLoading = false;
          this.dialogStatus = false;
        }
      } catch (error) {
        this.nextLoading = false;
        this.dialogStatus = false;
      }
    },
    async sendVerifyCode() {
      try {
        const str = this.realNameInfo.mobileNo;
        const res = await request("/loan/api/sms/v2/sendVerifyCode", {
          encryptedParams: { mobileNo: str, type: 1 },
        });
        if (res && res.message === "success") {
          this.countdown = 60; // 倒计时60秒
          const intervalId = setInterval(() => {
            if (this.countdown > 0) {
              this.countdown -= 1;
            } else {
              clearInterval(intervalId);
            }
          }, 1000);

          setTimeout(() => {
            this.doreport("", 105, 1);
          }, 0);
        } else {
          this.showToastFun(res.message);
          setTimeout(() => {
            this.doreport("", 105, 2);
          }, 0);
        }
      } catch (error) {
        console.log(error);
      }
    },
    async getAdActivityList() {
      report("wallet_page_view", { page_name: "credit_realname_fail_page" });
      const res = await request("/loan/api/config/suggest/list", {});
      if (res && res.data) {
        this.suggestList = res.data || [];
      }
    },
    async updateRealNameStatus() {
      const res = await request("/loan/api/user/v2/getUserRealNameInfo", {}, { mock: false });
      if (res && res.message === "success" && res.data?.status === 1) {
        this.readonlyStatus = res.data?.status === 1;
        this.phoneNum = res.data?.mobileNo;
        this.realNameInfo = res.data;
        this.isSmsCode = false;
        this.smsCode = "";
        this.countdown = 0;
      }
      this.nextLoading = false;
      this.dialogStatus = false;
    },
    // 用户是否为冷登录状态
    isBadLoginStatus() {
      return this.param.isLogin && !this.param.userId;
    },

    async activityPageAndNotLogin(ifColdStart) {
      this.redirectUrl = "";
      const activityRes = await getLoanAdActivityConfig(this.param.activityCode);
      if (activityRes?.data) {
        this.activityInfo = activityRes.data;
        this.pageStatus = "3";
      }
      if (activityRes?.data?.activityStatus !== 1 && this.param?.sdkVersionCode >= 90004000) {
        this.redirectUrl = urlMap.loan_index;
      }
      if (ifColdStart) {
        // 上报用户行为
        setTimeout(() => {
          userBehaviorLogReporting(
            "",
            101,
            1,
            1,
            this.activityInfo?.preDivisionSupplier || 0,
            new Date().getTime(),
          );
        }, 0);
      }
    },

    async activityPageAndLogin() {
      // 查询运营页配置
      this.redirectUrl = "";
      const [userInfo, activityRes] = await Promise.all([
        getUserInfo(),
        getLoanAdActivityConfig(this.param.activityCode),
        this.getUserRealNameInfo(),
      ]);
      if (activityRes?.data) {
        this.activityInfo = activityRes.data;
        this.pageStatus = "3";

        // 上报用户行为
        setTimeout(() => {
          userBehaviorLogReporting(
            "",
            101,
            1,
            1,
            this.activityInfo?.preDivisionSupplier || 0,
            new Date().getTime(),
          );
        }, 0);
      }
      if (
        userInfo?.code !== 0 ||
        [1, 2, 3].includes(userInfo?.data?.creditStatus) ||
        activityRes?.data?.activityStatus !== 1
      ) {
        this.redirectUrl = urlMap.loan_index;
      }
    },

    async realnamePageAndLogin() {
      const reqArray = [getCurrentCpInfo()];
      if (this.param.userId) reqArray.push(this.getUserRealNameInfo());
      const [cpInfo] = await Promise.all(reqArray);
      this.supplierId = cpInfo?.supplierId || 0;

      setTimeout(() => {
        userBehaviorLogReporting("", 101, 1, 1, cpInfo?.supplierId || 0, new Date().getTime());
      }, 0);
    },

    async getUserRealNameInfo() {
      const res = await request("/loan/api/user/v2/getUserRealNameInfo", {}, { mock: false });
      if (res?.data?.flowNo) {
        this.mobileNoReadonlyStatus = true;
        this.flowNo = res.data.flowNo;
      }

      if (res && res.message === "success") {
        this.readonlyStatus = res.data?.status === 1;
        this.phoneNum = res.data?.mobileNo;
        this.realNameInfo = res.data;
      }
    },

    async initial(val) {
      this.nextLoading = false;
      this.dialogStatus = false;
      const requestArray = [];
      // 如果用户在其它用户已经全部准入失败，则跳转到准入结果页面
      if (this.param.accessResult === "1") {
        setTimeout(() => {
          this.doreport("", 101, 2);
        }, 0);

        this.pageStatus = "2";
        this.getAdActivityList();
        return;
      }

      // 实名认证运营页冷启动
      if (this.param.activityCode && this.param.isLogin && !this.param.userId) {
        requestArray.push(this.activityPageAndNotLogin(true));
      }

      // 实名认证运营页已登录状态
      if (this.param.activityCode && this.param.isLogin && this.param.userId && !val) {
        requestArray.push(this.activityPageAndLogin());
      }
      // 实名认证运营页未登录状态
      if (this.param.activityCode && !this.param.isLogin) {
        requestArray.push(this.activityPageAndNotLogin());
      }

      if (!this.param.activityCode) {
        // 实名准入页已登录状态
        requestArray.push(this.realnamePageAndLogin());
      }

      this.smsCode = "";
      this.countdown = 0;
      if (!val || this.realNameInfo.status === 1) {
        this.isSmsCode = false;
        this.phoneErrorMessage = "";
      }

      await Promise.all(requestArray);
    },
  },
});
