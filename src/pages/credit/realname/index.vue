<script setup>
import { onMounted, onServerPrefetch, ref } from "vue";
import lodash from "lodash";
import { setTheme } from "@hihonor/hnr/lib/hnr.es.min";
import { onBeforeMount } from "vue-demi";
import { showRetentionDialog } from "../../../helpers/retention-dialog-utils";
import useStore from "./store";
import {
  getNetworkStatus,
  initStore,
  request,
  storeMarketingTracking,
  userBehaviorLogReporting,
} from "../../../helpers/utils";
import {
  getUserProtocalUrl,
  getPrivacyProtocalUrl,
  USER_PROTOCAL_CODE,
  PRIVACY_PROTOCAL_CODE,
  DEBOUNCE_OPTIONS,
  urlMap,
} from "../../../helpers/constants";
import {
  goto,
  report,
  regNativeEvent,
  back,
  getUserId,
  doLogin,
} from "../../../helpers/native-bridge";
import SuggestProductCard from "../../component/SuggestProductCard.vue";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { getUserInfo } from "../../../api/user";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { setNetwork } from "../../../helpers/network-helper";
import reloadingComponent from "../../../components/reloadingComponent.vue";

const flagFirst = ref({});
const showBackground = ref(false);

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

const fieldsChange = (val) => {
  if (!flagFirst.value[val]) {
    report("wallet_page_result", { fields: [val], page_name: "credit_realname_page" });
    flagFirst.value[val] = true;
  }
};

const buttonDiableStatus = () => {
  const { realName, ctfCode, mobileNo, status } = data.value.realNameInfo;
  const { smsCode, isSmsCode } = data.value;
  const { nameErrorMessage, idCardErrorMessage, phoneErrorMessage, codeErrorMessage } = data.value;
  if (
    realName &&
    ctfCode &&
    mobileNo &&
    !nameErrorMessage &&
    !idCardErrorMessage &&
    !phoneErrorMessage &&
    !codeErrorMessage &&
    ((isSmsCode && smsCode) || !isSmsCode)
  ) {
    data.value.buttonDiable = false;
  } else {
    data.value.buttonDiable = true;
  }
  if (data.value.realNameInfo.status !== 1) {
    const storageData = getStore(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`) || {};
    storageData.realNameInfoNew = { realName, ctfCode, mobileNo, status };
    storageData.isSmsCode = isSmsCode;
    setWithExactExpireUnit(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`, storageData, 7, "D");
  }
};

const lookUpSignStorage = (flag) => {
  const storageData = getStore(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`) || {};
  storageData.isFromSign = flag;
  setWithExactExpireUnit(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`, storageData, 7, "D");
};

const setInfo = () => {
  const storageData = getStore(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`) || {};
  if (data.value.realNameInfo.status !== 1) {
    if (storageData.realNameInfoNew && Object.keys(storageData.realNameInfoNew).length > 0) {
      data.value.realNameInfo = storageData.realNameInfoNew;
      data.value.isSmsCode =
        storageData.realNameInfoNew.mobileNo === data.value.phoneNum
          ? false
          : storageData.isSmsCode;
    }
  } else {
    data.value.phoneErrorMessage = "";
    data.value.isSmsCode = false;
  }
  buttonDiableStatus();
};

const onResumeForActivity = () => {
  const oldUserId = data.value.param.userId;
  if (oldUserId) {
    return;
  }

  getUserId().then((userId) => {
    if (userId) {
      data.value.param.userId = userId;
    }

    if (!oldUserId && data.value.param.userId) {
      // pv上报
      storeMarketingTracking(store.param.marketing_tracking);
      report("wallet_page_view", {
        page_name: "credit_realname_marketing_page",
        supplierId: data.value.activityInfo?.preDivisionSupplier || 0,
        activityCode: data.value.param.activityCode,
        isLogin: !!data.value.param.userId,
        isRealName: store.realNameInfo?.status === 1,
      });

      // 用户行为上报
      setTimeout(() => {
        userBehaviorLogReporting(
          "",
          101,
          1,
          1,
          data.value.activityInfo?.preDivisionSupplier || 0,
          new Date().getTime(),
        );
      }, 0);

      // 查看用户授信状态，已授信/审核中/锁定期的用户进入首页
      getUserInfo()
        .then((userInfoRes) => {
          if (userInfoRes?.code !== 0 || [1, 2, 3].includes(userInfoRes?.data?.creditStatus)) {
            goto(urlMap.loan_index, false, true);
          } else {
            // 获取实名三要素信息
            request("/loan/api/user/v2/getUserRealNameInfo", {}, { mock: false }).then((res) => {
              store.isLoginLoading = false;
              if (res?.data?.flowNo) {
                data.value.mobileNoReadonlyStatus = true;
                data.value.flowNo = res.data.flowNo;
              }
              if (res && res.message === "success") {
                data.value.readonlyStatus = res.data?.status === 1;
                data.value.phoneNum = res.data?.mobileNo;
                data.value.realNameInfo = res.data;
              }
            });
          }
        })
        .finally((store.isLoginLoading = false));
    }
  });
};

regNativeEvent("onResume", () => {
  const storageData = getStore(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`) || {};
  // 协议返回
  if (storageData.isFromSign) {
    lookUpSignStorage("");
    buttonDiableStatus();
  } else {
    store.initial(true).finally(() => {
      setInfo();
    });
    buttonDiableStatus();
  }

  // 实名认证运营页onResume逻辑
  if (data.value.param.versionCode >= "90004000" && data.value.pageStatus === "3") {
    onResumeForActivity();
  }
});

const backClick = () => {
  const event = { click_name: "credit_realname_back" };
  if (data.value.pageStatus === "3") {
    event.supplierId = data.value.activityInfo.preDivisionSupplier || 0;
    event.activityCode = data.value.activityInfo.activityCode;
  }
  event.isLogin = !!store.param.userId;
  event.isRealName = store.realNameInfo?.status === 1;
  setTimeout(() => {
    report("wallet_page_click", event);
  }, 0);
};

regNativeEvent("onBack", () => {
  backClick();
  if (data.value.pageStatus === "2") {
    if (
      data.value.param.fromPage === "creditResult" ||
      data.value.param.fromPage === "logOffSuccess" ||
      data.value.param.fromPage === "outActivity"
    ) {
      goto("/wallet-loan-web/pages/index", false, true);
    } else {
      back();
    }
  } else showRetentionDialog();
});

const isBtnFixedOnBottom = ref(false);
const activityNextStepBtn = ref();
const loginBtn = ref();
const setSpecStyleForActivity = () => {
  const activityNavDom = document.querySelector(".activity-nav");
  const activityNextStepBtnDom = activityNextStepBtn.value;
  const loginBtnDom = loginBtn.value;

  // 为按钮增加滚动置底效果
  document.addEventListener("scroll", () => {
    const currentBtnDom = data.value.param.userId ? activityNextStepBtnDom : loginBtnDom;
    const newIsBtnFixedOnBottom =
      currentBtnDom.getBoundingClientRect().bottom < activityNavDom.getBoundingClientRect().bottom;
    // 只有在新值与当前值不同时才更新
    if (isBtnFixedOnBottom.value !== newIsBtnFixedOnBottom) {
      isBtnFixedOnBottom.value = newIsBtnFixedOnBottom;
    }
  });

  // 顶部navbar滑动变色
  window.addEventListener("scroll", () => {
    if (window.scrollY > 10) {
      if (!activityNavDom.classList.contains("activity-nav-background")) {
        activityNavDom.classList.add("activity-nav-background");
      }
    } else {
      activityNavDom.classList.remove("activity-nav-background");
    }
  });
};

onBeforeMount(() => {
  if (data.value.pageStatus === "3") {
    // 强制设置为浅色模式
    setTheme("light");
  }
});

onMounted(async () => {
  data.value.buttonHide = false;
  showBackground.value = true;

  setInfo();
  buttonDiableStatus();

  // 实名认证运营页时设置特殊逻辑
  if (data.value.pageStatus === "3") {
    if (store.isBadLoginStatus()) {
      store.isDataLoading = true;
      store.param.userId = await getUserId();

      const resp = await Promise.all([
        request("/loan/api/user/v2/getUserRealNameInfo", {}, { mock: false }),
        getUserInfo(),
      ]);

      // 用户授信状态
      const userInfoRes = resp[1];
      if (userInfoRes?.code !== 0 || [1, 2, 3].includes(userInfoRes?.data?.creditStatus)) {
        await goto(urlMap.loan_index, false, true);
      }

      // 用户实名信息
      const userRealNameInfoRes = resp[0];
      if (userRealNameInfoRes?.data?.flowNo) {
        store.mobileNoReadonlyStatus = true;
        store.flowNo = userRealNameInfoRes.data.flowNo;
      }

      if (userRealNameInfoRes && userRealNameInfoRes.message === "success") {
        store.readonlyStatus = userRealNameInfoRes.data?.status === 1;
        store.phoneNum = userRealNameInfoRes.data?.mobileNo;
        store.realNameInfo = userRealNameInfoRes.data;
      }

      buttonDiableStatus();
      store.isDataLoading = false;
    } else if (data.value.param.userId) {
      const userInfoRes = await getUserInfo();
      if (userInfoRes?.code !== 0 || [1, 2, 3].includes(userInfoRes?.data?.creditStatus)) {
        await goto(urlMap.loan_index, false, true);
      }
    }

    // PV上报
    report("wallet_page_view", {
      page_name: "credit_realname_marketing_page",
      supplierId: data.value.activityInfo?.preDivisionSupplier || 0,
      activityCode: data.value.param.activityCode,
      isLogin: !!data.value.param.userId,
      isRealName: store.realNameInfo?.status === 1,
    });

    setSpecStyleForActivity();
  }
});

const agreeProtocal = async () => {
  await request("/general/api/ams/user/signAgreement", {
    userId: store.param.userId,
    signInfo: [
      {
        agrType: USER_PROTOCAL_CODE,
        country: "CN",
        branchId: 0,
        language: "zh-CN",
        isAgree: true,
      },
      {
        agrType: PRIVACY_PROTOCAL_CODE,
        country: "CN",
        branchId: 0,
        language: "zh-CN",
        isAgree: true,
      },
    ],
    deviceInfo: {
      deviceId: "",
      deviceModel: store.param.deviceModel,
      deviceType: "",
    },
  });
};

const onClickLeft = () => {
  backClick();
  if (data.value.pageStatus === "2") {
    if (
      data.value.param.fromPage === "creditResult" ||
      data.value.param.fromPage === "logOffSuccess" ||
      data.value.param.fromPage === "outActivity"
    ) {
      goto("/wallet-loan-web/pages/index", false, true);
    } else {
      back();
    }
  } else showRetentionDialog();
};

const completeBtn = () => {
  report("wallet_page_click", { click_name: "realname_fail_click" });
  if (
    data.value.param.fromPage === "creditResult" ||
    data.value.param.fromPage === "logOffSuccess" ||
    data.value.param.fromPage === "outActivity"
  ) {
    goto("/wallet-loan-web/pages/index", false, true);
  } else {
    back();
  }
};

const nameBlur = (val) => {
  const reg = /^[\u4e00-\u9fa5·]+$/;
  if (reg.test(val.target.value) && val.target.value.length !== 1) {
    data.value.nameErrorMessage = "";
  } else {
    data.value.nameErrorMessage = "请输入正确的姓名";
  }
  buttonDiableStatus();
};
const nameChange = (val) => {
  const reg = /^[\u4e00-\u9fa5·]+$/;
  if (reg.test(val)) {
    data.value.nameErrorMessage = "";
  } else {
    data.value.nameErrorMessage = "请输入正确的姓名";
  }
  buttonDiableStatus();
  fieldsChange("realName");
};

/**
 * 身份证15位编码规则：dddddd yymmdd xx p dddddd：6位地区编码 yymmdd: 出生年(两位年)月日，如：910215 xx:
 * 顺序编码，系统产生，无法确定 p: 性别，奇数为男，偶数为女
 *
 * 身份证18位编码规则：dddddd yyyymmdd xxx y dddddd：6位地区编码 yyyymmdd:
 * 出生年(四位年)月日，如：19910215 xxx：顺序编码，系统产生，无法确定，奇数为男，偶数为女 y: 校验码，该位数值可通过前17位计算获得
 *
 * 前17位号码加权因子为 Wi = [ 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 ] 验证位
 * Y = [ 1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2 ] 如果验证码恰好是10，为了保证身份证是十八位，那么第十八位将用X来代替
 * 校验位计算公式：Y_P = mod( ∑(Ai×Wi),11 ) i为身份证号码1...17 位; Y_P为校验码Y所在校验码数组位置
 */
const idCardBlur = (val) => {
  // 15位和18位身份证号码的正则表达式
  const regIdCard =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[X])$)$/;

  // 如果通过该验证，说明身份证格式正确，但准确性还需计算
  if (regIdCard.test(val.target.value)) {
    if (val.target.value.length === 18) {
      const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]; // 将前17位加权因子保存在数组里
      const idCardY = [1, 0, 10, 9, 8, 7, 6, 5, 4, 3, 2]; // 这是除以11后，可能产生的11位余数、验证码，也保存成数组
      let idCardWiSum = 0; // 用来保存前17位各自乖以加权因子后的总和
      for (let i = 0; i < 17; i += 1) {
        idCardWiSum += val.target.value.substring(i, i + 1) * idCardWi[i];
      }

      const idCardMod = idCardWiSum % 11; // 计算出校验码所在数组的位置
      const idCardLast = val.target.value.substring(17); // 得到最后一位身份证号码

      // 如果等于2，则说明校验码是10，身份证号码最后一位应该是X
      if (idCardMod === 2) {
        if (idCardLast === "X") {
          data.value.idCardErrorMessage = "";
        } else {
          data.value.idCardErrorMessage = "请输入正确的身份证号码";
        }
      } else if (Number(idCardLast) === Number(idCardY[idCardMod])) {
        data.value.idCardErrorMessage = "";
      } else {
        data.value.idCardErrorMessage = "请输入正确的身份证号码";
      }
    } else {
      data.value.idCardErrorMessage = "";
    }
  } else {
    data.value.idCardErrorMessage = "请输入正确的身份证号码";
  }
  buttonDiableStatus();
};

const idCardChange = () => {
  data.value.idCardErrorMessage = "";
  buttonDiableStatus();
  fieldsChange("ctfCode");
};
const phoneBlur = (val) => {
  if (
    /^1[3-9]\d{9}$/.test(val.target.value) ||
    (!data.value.isSmsCode && data.value.phoneNum === val.target.value)
  ) {
    data.value.phoneErrorMessage = "";
  } else {
    data.value.phoneErrorMessage = "请输入正确的手机号";
  }
  buttonDiableStatus();
};

const ifChangeInputType = ref(false);
const len = data.value?.realNameInfo?.mobileNo?.length || 0;
const phoneChange = (value) => {
  const currentValue = value;
  if (!ifChangeInputType.value && currentValue.length < len) {
    data.value.realNameInfo.mobileNo = "";
    ifChangeInputType.value = true;
  }
  data.value.phoneErrorMessage = "";
  data.value.isSmsCode = true;
  buttonDiableStatus();
  fieldsChange("mobileNo");
};
const codeChange = () => {
  data.value.codeErrorMessage = "";
  buttonDiableStatus();
  fieldsChange("smsCode");
};
const radioCheckChange = () => {
  buttonDiableStatus();
};

const startCountdown = lodash.debounce(() => {
  if (data.value.countdown > 0) {
    return;
  }
  if (data.value.realNameInfo.status !== 1) {
    const phone = { target: { value: data.value.realNameInfo.mobileNo } };
    phoneBlur(phone);
    if (data.value.phoneErrorMessage) {
      return;
    }
  }
  store.sendVerifyCode();
}, 500);

const agreeAndContinue = () => {
  lookUpSignStorage("");
  agreeProtocal();
  store.userBind();
};

const nextStep = () => {
  if (!getNetworkStatus()) {
    setNetwork();
    return;
  }
  if (store.param.activityCode) {
    if (store.isDataLoading) {
      return;
    }
  }

  if (data.value.realNameInfo.status !== 1) {
    const name = { target: { value: data.value.realNameInfo.realName } };
    const idCard = { target: { value: data.value.realNameInfo.ctfCode } };
    const phone = { target: { value: data.value.realNameInfo.mobileNo } };
    nameBlur(name);
    idCardBlur(idCard);
    if (data.value.phoneNum) {
      phoneBlur(phone);
    }
    if (data.value.buttonDiable) {
      return;
    }
  }

  if (data.value.radioCheck) {
    agreeAndContinue();
  } else {
    data.value.dialogStatus = true;
  }
};
const lookUpSign = (val) => {
  if (val === 1) {
    goto(getUserProtocalUrl(store.param.theme), true, false, true);
  } else {
    goto(getPrivacyProtocalUrl(store.param.theme), true, false, true);
  }
  lookUpSignStorage(true);
};

const flipToTop = () => {
  window.scrollTo(0, 0);
};

const login = lodash.debounce(
  () => {
    data.value.isLoginLoading = true;
    report("wallet_page_click", { click_name: "credit_realname_login_click" });

    doLogin();
  },
  1000,
  DEBOUNCE_OPTIONS,
);
</script>

<template>
  <div v-if="data.pageStatus === '1'" class="page">
    <hnr-nav-bar
      transparent="true"
      class="content-top nav-padding-top"
      :style="data.dialogStatus ? { zIndex: 'unset' } : ''"
      title="实名信息申请额度"
    >
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div class="content">
      <div class="content-tip">
        <span class="icon-credit_realname_confidentiality"></span
        >以下身份证号等信息将用于合作金融机构信贷服务申请，资料将被严格保密。
      </div>
      <div class="content-title">个人基础信息</div>
      <hnr-card type="empty" :class="['pt8', { pb10: data.codeErrorMessage }]">
        <hnr-field
          v-model="data.realNameInfo.realName"
          :formatter="(value) => value.replace(/\s/g, '')"
          placeholder="真实姓名"
          :error-message="data.nameErrorMessage"
          :disabled="data.readonlyStatus"
          class="divider-line"
          @update:model-value="nameChange"
          @blur="nameBlur"
        />
        <hnr-field
          v-model="data.realNameInfo.ctfCode"
          :formatter="(value) => value.replace(/\s/g, '')"
          placeholder="身份证号"
          :error-message="data.idCardErrorMessage"
          :disabled="data.readonlyStatus"
          class="divider-line"
          @update:model-value="idCardChange"
          @blur="idCardBlur"
        />
        <hnr-field
          v-model="data.realNameInfo.mobileNo"
          :formatter="(value) => value.replace(/\s/g, '')"
          :type="ifChangeInputType ? 'number' : 'text'"
          clearable
          clear-icon="/wallet-loan-web/pages/loan/close_doublelayer_filled.svg"
          :error-message="data.phoneErrorMessage"
          placeholder="手机号"
          :class="{ 'mobileNo-code': !data.isSmsCode, 'divider-line': true }"
          @update:model-value="phoneChange"
          @blur="phoneBlur"
        />
        <hnr-field
          v-if="data.isSmsCode"
          v-model="data.smsCode"
          :error-message="data.codeErrorMessage"
          type="digit"
          placeholder="短信验证码"
          class="verification-code"
          @update:model-value="codeChange"
        >
          <template #button>
            <hnr-button
              :disabled="
                !(data.realNameInfo.mobileNo && !data.phoneErrorMessage) || data.countdown > 0
              "
              type="text"
              size="mini"
              @click="startCountdown"
              >{{ data.countdown === 0 ? "获取验证码" : `${data.countdown}s` }}</hnr-button
            >
          </template>
        </hnr-field>
      </hnr-card>
    </div>
    <div class="content-bottom">
      <div v-show="!data.buttonHide" class="checkbox-area">
        <hnr-checkbox v-model="data.radioCheck" @change="radioCheckChange"></hnr-checkbox>
        <div class="content-bottom-radio">
          已阅读并同意签署
          <span class="content-bottom-agreement" @click="lookUpSign(1)">荣耀借钱服务用户协议</span>
          <span>和</span>
          <span class="content-bottom-agreement" @click="lookUpSign(2)"
            >关于荣耀借钱服务与隐私的声明</span
          >
        </div>
      </div>
      <div v-show="!data.buttonHide" class="content-bottom-btn">
        <hnr-button
          :disabled="data.buttonDiable"
          :style="data.buttonDiable ? { opacity: '0.38' } : ''"
          :loading="data.nextLoading"
          standard-width="true"
          type="primary"
          loading-text="加载中..."
          @click="nextStep"
          >下一步</hnr-button
        >
      </div>
      <no-ssr>
        <hnr-dialog v-model:show="data.dialogStatus" title="请阅读并同意以下条款">
          <template #default>
            <div>
              <span class="content-bottom-agreement" @click="lookUpSign(1)"
                >荣耀借钱服务用户协议</span
              >
              <span>和</span>
              <span class="content-bottom-agreement" @click="lookUpSign(2)"
                >关于荣耀借钱服务与隐私的声明</span
              >
            </div>
            <div class="dialog-tip">点击“同意并继续”即代表您同意协议内容并提交申请。</div>
          </template>
          <template #footer>
            <hnr-button type="default" class="dialog-btn" @click="data.dialogStatus = false"
              >取消</hnr-button
            >
            <hnr-button
              type="primary"
              class="ml10 dialog-btn"
              :disabled="data.buttonDiable"
              :style="data.buttonDiable ? { opacity: '0.38' } : ''"
              :loading="data.nextLoading"
              loading-text="加载中..."
              @click="agreeAndContinue"
              >同意并继续</hnr-button
            >
          </template>
        </hnr-dialog>
      </no-ssr>
    </div>
  </div>
  <div v-if="data.pageStatus === '2'" class="page">
    <hnr-nav-bar transparent="true" title="申请失败" class="top-navigation nav-padding-top">
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div class="fail-content">
      <div class="fail-image"><span class="icon-credit_realname_fail"></span></div>
      <div class="fail-title">申请失败</div>
      <div class="fail-tip fail-tips">
        {{ data.failMessage ? data.failMessage : "您的申请评估未通过" }}
      </div>
      <div v-if="data.suggestList.length > 0" class="content-title">可尝试申请以下严选借款产品</div>
      <div class="fail-content-cards">
        <SuggestProductCard :suggest-products="data.suggestList" location="准入失败" />
      </div>
      <div class="fail-content-bottom">
        <hnr-button
          standard-width="true"
          type="primary"
          class="fail-content-bottom-btn"
          @click="completeBtn"
          >完成</hnr-button
        >
      </div>
    </div>
  </div>
  <div v-if="data.pageStatus === '3' && !data.isLoginLoading">
    <!--    <hnr-loading color="#256fff" class="activity-loading" />-->

    <div ref="activityContainer" class="activity-container">
      <no-ssr>
        <hnr-notify
          class="notify"
          type="info"
          :text="data.fullErrorMessage"
          :is-show="data.fullErrorMessage !== ''"
        />
      </no-ssr>
      <img
        v-show="showBackground"
        :src="
          data.param.userId || store.isBadLoginStatus()
            ? data.activityInfo.backPicUrl
            : data.activityInfo.loginBackPicUrl
        "
        alt="Background Image"
        class="background-image"
      />
      <hnr-nav-bar ref="activityNav" transparent="true" class="activity-nav">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <div v-show="data.param.userId || store.isBadLoginStatus()" class="activity-content">
        <hnr-card type="empty" :class="['inputCard', 'pt8']">
          <hnr-field
            v-model="data.realNameInfo.realName"
            :formatter="(value) => value.replace(/\s/g, '')"
            placeholder="真实姓名"
            :error="data.nameErrorMessage !== ''"
            :disabled="data.readonlyStatus"
            @update:model-value="nameChange"
            @blur="nameBlur"
          />
          <hnr-field
            v-model="data.realNameInfo.ctfCode"
            :formatter="(value) => value.replace(/\s/g, '')"
            placeholder="身份证号"
            :error="data.idCardErrorMessage !== ''"
            :disabled="data.readonlyStatus"
            @update:model-value="idCardChange"
            @blur="idCardBlur"
          />
          <hnr-field
            v-model="data.realNameInfo.mobileNo"
            :disabled="data.mobileNoReadonlyStatus"
            :formatter="(value) => value.replace(/\s/g, '')"
            :type="ifChangeInputType ? 'number' : 'text'"
            :clearable="!data.mobileNoReadonlyStatus"
            clear-icon="/wallet-loan-web/pages/loan/close_doublelayer_filled.svg"
            :error="data.phoneErrorMessage !== ''"
            placeholder="手机号"
            :class="{ 'mobileNo-code': !data.isSmsCode }"
            @update:model-value="phoneChange"
            @blur="phoneBlur"
          />
          <hnr-field
            v-if="data.isSmsCode"
            v-model="data.smsCode"
            :error="data.codeErrorMessage !== ''"
            type="digit"
            placeholder="短信验证码"
            class="verification-code"
            @update:model-value="codeChange"
          >
            <template #button>
              <hnr-button
                :disabled="
                  !(data.realNameInfo.mobileNo && !data.phoneErrorMessage) || data.countdown > 0
                "
                type="text"
                size="mini"
                @click="startCountdown"
                >{{ data.countdown === 0 ? "获取验证码" : `${data.countdown}s` }}
              </hnr-button>
            </template>
          </hnr-field>
        </hnr-card>

        <div class="contract">
          <hnr-checkbox
            v-model="data.radioCheck"
            class="check"
            @change="radioCheckChange"
          ></hnr-checkbox>
          <div>
            已阅读并同意签署<span class="agreement" @click="lookUpSign(1)"
              >荣耀借钱服务用户协议</span
            ><span>和</span
            ><span class="agreement" @click="lookUpSign(2)">关于荣耀借钱服务与隐私的声明，</span
            >以上身份证号等信息将用于合作金融机构信贷服务申请，资料将被严格保密。
          </div>
        </div>
        <!--确认按钮-->
        <div ref="activityBtnContainer" class="btn-container">
          <img
            v-if="data.activityInfo.labelImageUrl"
            :src="data.activityInfo.labelImageUrl"
            class="label"
            alt="label"
          />
          <img
            v-if="data.activityInfo.buttonHandIconUrl"
            :src="data.activityInfo.buttonHandIconUrl"
            class="finger"
            alt="finger"
          />
          <img
            ref="activityNextStepBtn"
            :src="data.activityInfo.buttonImageUrl"
            class="next-step-btn"
            :class="{ gray: data.nextLoading }"
            alt="confirm"
            @click="nextStep"
          />
        </div>
      </div>

      <div v-show="!data.param.userId && !store.isBadLoginStatus()" class="login-content">
        <hnr-card type="empty" class="login-input-card pt8">
          <hnr-field placeholder="真实姓名 请输入真实姓名" />
          <hnr-field placeholder="身份证号 请输入身份证号" />
          <hnr-field placeholder="手机号码 请输入你的手机号码，获取验证码" />
        </hnr-card>

        <div class="login-cover-layer">
          <div class="login-content-title">{{ data.activityInfo.loginPromptText }}</div>
          <div ref="loginContentBtnContainer" class="login-content-btn-container">
            <img
              v-if="data.activityInfo.labelImageUrl"
              :src="data.activityInfo.labelImageUrl"
              class="label"
              alt="label"
            />
            <img
              ref="loginBtn"
              :src="data.activityInfo.loginButtonImageUrl"
              class="login-btn"
              alt="login"
              @click="login"
            />
          </div>
        </div>
      </div>

      <div v-if="isBtnFixedOnBottom" ref="fixedBtnContainer" class="fixed-btn-container">
        <img
          ref="fixedNextStepBtn"
          :src="
            data.param.userId || store.isBadLoginStatus()
              ? data.activityInfo.buttonImageUrl
              : data.activityInfo.loginButtonImageUrl
          "
          class="fixed-btn"
          style="pointer-events: auto"
          alt="confirm"
          @click="flipToTop"
        />
      </div>

      <no-ssr>
        <hnr-dialog v-model:show="data.dialogStatus" title="请阅读并同意以下条款">
          <template #default>
            <div>
              <span class="content-bottom-agreement" @click="lookUpSign(1)"
                >荣耀借钱服务用户协议</span
              >
              <span>和</span>
              <span class="content-bottom-agreement" @click="lookUpSign(2)"
                >关于荣耀借钱服务与隐私的声明</span
              >
            </div>
            <div class="dialog-tip">点击“同意并继续”即代表您同意协议内容并提交申请。</div>
          </template>
          <template #footer>
            <hnr-button type="text" class="next-btn" @click="data.dialogStatus = false"
              >取消</hnr-button
            >
            <hnr-button
              type="primary"
              class="next-btn"
              :disabled="data.buttonDiable"
              :style="data.buttonDiable ? { opacity: '0.38' } : ''"
              :loading="data.nextLoading"
              loading-text="加载中..."
              @click="agreeAndContinue"
              >同意并继续</hnr-button
            >
          </template>
        </hnr-dialog>
      </no-ssr>
    </div>
  </div>
  <!-- 登录过渡加载 -->
  <div v-if="data.isLoginLoading" style="height: calc(100vh)">
    <reloading-component style="flex: 1" :loading-status="data.isLoginLoading" />
  </div>
</template>

<style lang="scss" scoped>
.page {
  background: var(--hnr-color-background-cardview);
  height: 100vh;
}
.content-top {
  background: var(--hnr-color-background-cardview);
}
.content {
  height: calc(100vh - 88px - 109px);
  overflow-y: scroll;
  // :deep(.hnr-field__body) {
  // height: 64px;
  // }
  :deep(.hnr-field__input-area) {
    // height: 48px;
    padding-bottom: 8px;
  }
}
.content-tip {
  background: #e3f1ff;
  border-radius: var(--hnr-default-corner-radius-xs);
  margin: 0 12px var(--hnr-elements-margin-vertical-M2) 12px;
  padding: var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2);
  color: var(--hnr-text-color-primary-activated);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  align-items: center;
  border-radius: var(--hnr-default-corner-radius-m);
}
.content-title {
  margin-left: 15px;
  padding: 6px var(--hnr-elements-margin-horizontal-M2);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
}
.content-tip img {
  width: 16px;
  margin-right: 6px;
}
:deep(.hnr-card__content) {
  display: block;
}
.mobileNo-code :deep(.hnr-field__input-area:before) {
  height: 0;
}
.verification-code :deep(.hnr-field__input-area:before) {
  height: 0;
}
.content-bottom {
  background: var(--hnr-color-background-cardview);
  padding-top: 5px;
  position: fixed;
  bottom: 0;
  /* margin: 0 12px; */
  /* padding: 0 var(--hnr-elements-margin-horizontal-M2); */
  width: 100%;
}
:deep(.hnr-radio) {
  align-items: flex-start;
}
.content-bottom-radio {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.content-bottom-agreement {
  color: var(--hnr-text-color-primary-activated);
}
.content-bottom-btn {
  // max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
  // width: 80%;
  // margin-left: 10%;
  text-align: center;
}
.dialog-tip {
  margin-top: var(--hnr-elements-margin-vertical-M);
}
.fail-image {
  display: flex;
  justify-content: center;
  padding-top: var(--hnr-elements-margin-vertical-XXL);
  padding-bottom: var(--hnr-element-margin-vertical-XL);
}
.fail-image img {
  width: 64px;
  height: 64px;
}
.fail-title {
  color: var(--hnr-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  display: flex;
  justify-content: center;
}
.fail-tips {
  margin: var(--hnr-elements-margin-vertical-M) 0 36px 0;
}
.fail-tip {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  /* display: flex;
  justify-content: center; */
  padding: 0 15px;
  text-align: center;
}
.fail-card-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.fail-card-title {
  display: flex;
  align-items: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
  margin: var(--hnr-elements-margin-vertical-L) 0 var(--hnr-elements-margin-vertical-M) 0;
}
.fail-card-title img {
  width: 16px;
  height: 16px;
  margin-right: var(--hnr-elements-margin-horizontal-M);
}
.fail-card-info {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.fail-card-num {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  margin: var(--hnr-elements-margin-vertical-XS) 0 var(--hnr-elements-margin-vertical-L) 0;
}
.fail-content-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  z-index: 100;
  background: var(--hnr-color-background-cardview);
}
.fail-content-bottom-btn {
  // width: calc(
  //   100% - 12px - var(--hnr-elements-margin-horizontal-M2) - 12px - var(
  //       --hnr-elements-margin-horizontal-M2
  //     )
  // );
  // max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}
.top-navigation {
  // position: fixed;
  // top: 0;
  // width: 100%;
  // z-index: 100;
  background: var(--hnr-color-background-cardview);
}
.fail-content {
  // padding-top: 88px;
  height: calc(100vh - 88px - 80px);
  overflow-y: scroll;
}
.fail-content-cards {
  // padding-bottom: 72px;
  background: var(--hnr-color-background-cardview);
}
.icon-credit_realname_confidentiality {
  font-size: 16px;
  margin-right: var(--hnr-elements-margin-horizontal-M);
}
.icon-credit_realname_fail {
  font-size: 80px;
}
.card-img {
  width: 95%;
  margin-left: 2.5%;
  margin-bottom: 6px;
}
.checkbox-area {
  display: flex;
  align-items: center;
  width: calc(100vw - var(--hnr-max-padding-start) - var(--hnr-max-padding-end));
  margin-left: var(--hnr-max-padding-start);
}
.hnr-checkbox {
  padding-right: var(--hnr-elements-margin-horizontal-M2);
}
.pt8 {
  padding-top: 8px;
}
.pt10 {
  padding-top: 10px;
}
.pb10 {
  padding-bottom: 10px;
}
.hnr-field--withMargin {
  width: calc(100% - var(--dp34));
  margin: 0 var(--hnr-elements-margin-horizontal-L);
}
.hnr-checkbox {
  -webkit-tap-highlight-color: transparent;
}
.hnr-card {
  width: calc(
    100% - var(--hnr-list-card-margin-middle) - var(--hnr-list-card-margin-middle)
  ) !important;
}
.next-btn {
  margin-bottom: var(--dp8);
  width: 48%;
  min-width: unset !important;
  max-width: unset !important;
}
.ml10 {
  margin-left: 10px;
}
::v-deep(.hnr-dialog__footer) {
  padding: var(--dp10) var(--dp20) var(--dp20) !important;
}
.dialog-btn {
  min-width: 50% !important;
}
@media (prefers-color-scheme: dark) {
  .divider-line :deep(.hnr-field__input-area:before) {
    background: #e5e5e5;
  }
  .content-tip {
    background: #112840;
  }
}

// 实名认证营销页加载状态
//.activity-loading {
//  position: fixed;
//  top: 50%;
//  left: 50%;
//  transform: translateX(-50%);
//}
// 实名认证营销页
.activity-container {
  //visibility: hidden;
  width: 100%;
  min-height: 100vh;
  margin: 0 auto;
  position: relative;

  .notify {
    position: absolute;
    z-index: 1000;
    top: 50vh;
    left: 50%;
    transform: translateX(-50%);
    :deep(.hnr-notify) {
      width: 280px;
    }
    :deep(.hnr-notify__box--info) {
      background-color: #7e7e7e !important;
      color: white !important;
      height: 40px !important;
    }
    :deep(.hnr-notify__centerMessage) {
      margin: 0 10px !important;
      padding: 0 !important;
      justify-content: center;
    }
  }

  //背景图片
  .background-image {
    width: 100%;
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  //导航条
  .activity-nav {
    width: 100%;
    padding-top: 32px;
    position: fixed;

    :deep(svg) {
      color: black !important;
    }
  }

  .activity-nav-background {
    background: white !important;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .activity-content {
    position: absolute;
    top: 96vw;
    width: 92.2vw;
    height: 96vw;
    left: 3.8vw;
    display: flex;
    flex-direction: column;
    align-items: center;

    //输入框
    .inputCard {
      width: 100% !important;
      margin-bottom: 100px;

      :deep(.hnr-field__input-area) {
        padding-bottom: 8px;
      }

      :deep(.hnr-field__input-area:before) {
        height: 2px;
      }
    }

    //协议
    .contract {
      margin: 12px auto 0;
      display: flex;
      align-items: flex-start;
      width: 92%;
      color: var(--hnr-color-secondary);
      font-weight: var(--hnr-font-weight-regular);
      font-size: var(--hnr-body-3);

      // 确认按钮
      .check {
        padding-right: 24px;
      }

      // 协议
      .agreement {
        color: var(--hnr-text-color-primary-activated);
      }
    }

    //确认按钮组
    .btn-container {
      width: 92%;
      position: relative;
      margin-top: 24px;
      // 角标
      .label {
        width: 80px;
        height: auto;
        position: absolute;
        left: 75%;
        top: -35%;
      }

      // 手指
      .finger {
        width: 100px;
        height: auto;
        position: absolute;
        top: -10%;
        left: 60%;
        pointer-events: none;
      }

      // 按钮
      .next-step-btn {
        width: 100%;
      }

      // 按钮置灰
      .gray {
        pointer-events: none;
      }
    }
  }

  .login-content {
    position: absolute;
    top: 95.5vw;
    width: 93.3vw;
    height: 48vw;
    left: 3.3vw;

    .login-input-card {
      position: absolute;
      width: 100% !important;

      :deep(.hnr-field__input-area) {
        padding-bottom: 8px;
      }

      :deep(.hnr-field__input-area:before) {
        height: 2px;
      }
    }

    .login-cover-layer {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(5px);
      border-radius: var(--hnr-card-border-radius);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;

      .login-content-title {
        text-align: center;
        margin: 0 var(--hnr-body-1);
        color: var(--hnr-text-color-primary);
        font-weight: var(--hnr-font-weight-medium);
        font-size: var(--hnr-body-1);
        white-space: pre-wrap;
      }

      .login-content-btn-container {
        position: relative;
        display: flex;
        justify-content: center;

        // 角标
        .label {
          width: 80px;
          height: auto;
          position: absolute;
          left: 56%;
          top: -35%;
          z-index: 2;
        }

        .login-btn {
          width: 50%;
        }
      }
    }
  }

  // 按钮置底
  .fixed-btn-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: white;
    position: fixed;
    width: 100%;
    bottom: 0;
    height: 70px;

    .fixed-btn {
      max-width: 85%;
      max-height: 80%;
    }
  }
}

@media (prefers-color-scheme: dark) {
  .activity-container {
    .activity-nav {
      :deep(svg) {
        color: white !important;
      }
    }

    .activity-nav-background {
      background: black !important;
    }
  }
}

@media only screen and (min-width: 480px) {
  .activity-container {
    width: 400px;

    .activity-nav {
      width: 400px;
    }

    .activity-content {
      top: 380px;
      left: 12px;
      width: 94%;
    }

    .login-content {
      top: 390px;
      left: 12px;
      height: 176px;
      width: 94%;
    }

    .fixed-btn-container {
      width: 400px;
    }

    :deep(.hnr-dialog) {
      width: 376px !important;
    }
  }
}
</style>
