<script setup>
import { ref, onMounted, watch, computed } from "vue";
import lodash from "lodash";
import useStore from "./store";
import {
  initStore,
  upgradeCreditProcessStore,
  contractClickCallback,
} from "../../../helpers/utils";
import { regNativeEvent, goto, report } from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { showRetentionDialog } from "../../../helpers/retention-dialog-utils";
import SignReadButton from "../../../components/SignReadButton.vue";
import reloadingComponent from "../../../components/reloadingComponent.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { supplierIdMap } from "../../../helpers/constants";

const { store, data } = initStore(useStore);

const scrollContainer = ref(null);
const iframeRef = ref(null);
const isShowMore = ref(false);
const ifReading = ref([]);
const supplierId = ref(0);
const isDarkMode = ref(false);

async function reload() {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }
  if (store.contractFail) {
    store.contractFail = false;
    store.contractList();
  }
}

const continueLater = () => {
  if (data.value.dialogShow) {
    report("wallet_page_click", { click_name: "credit_another_dialog_abandon" });
  }
  report("wallet_page_click", { page_name: "credit_agreement_back" });
  goto("/wallet-loan-web/pages/index", false, true);
};

const continueApply = () => {
  report("wallet_page_click", { click_name: "credit_another_dialog_continue" });
  data.value.dialogShow = false;
  if (data.value.messageStatus === 2) {
    const obj = JSON.parse(window.name || "{}");
    obj.continue = true;
    window.name = JSON.stringify(obj);
    data.value.readingSecondsTime = data.value.readingSeconds;
  }
};

const dialogJumpTo = () => {
  if (data.value.storageDataV2.retainUser) {
    // 判断param里是否存在fromPage参数为'creditResult'，如果是，采用goto的方式回到首页，如果否，则back()回到上一页。
    continueLater();
  } else {
    // 弹出挽留弹窗，在U_CREDIT_PROCESS_V2_{userId}缓存中写入retainUser=true，
    data.value.messageStatus = 1;
    showRetentionDialog({
      text: data.value.dialogMessageOne,
      cancelFunc: () => {
        report("wallet_page_click", { page_name: "credit_agreement_back" });
      },
    });
    data.value.storageDataV2.retainUser = true;
    setWithExactExpireUnit(
      `U_CREDIT_PROCESS_V2_${data.value.param.userId}`,
      data.value.storageDataV2,
      7,
      "D",
    );
  }
};

const goLivingBodyFun = (val) => {
  const obj = JSON.parse(window.name || "{}");
  obj.goLivingBody = val;
  window.name = JSON.stringify(obj);
};

const onResume = () => {
  data.value.storageDataV2 = getStore(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`) || {};
  // 获取未完成的授信流程需要认证的步骤
  const objV2 = data.value.storageDataV2;
  let steps = 0;
  (objV2.verifyList || []).forEach((item) => {
    if (!objV2[item]) {
      steps += 1;
    }
  });
  data.value.dialogMessageOne = `仅差${Math.max(steps - 1, 1)}步即可完成申请，确定离开？`;

  if (data.value.param.fromPage === "creditResult") {
    const obj = JSON.parse(window.name || "{}");
    if (!obj.continue) {
      data.value.messageStatus = 2;
      data.value.dialogShow = true;
      report("wallet_page_view", { page_name: "credit_another_dialog_show" });
    }
  }

  const obj = JSON.parse(window.name || "{}");
  if (obj.goLivingBody) {
    if (!window?.RETAIN_NEXT_LOADING_STATUS) {
      window.nextLoadingStatus(false);
    } else {
      window.RETAIN_NEXT_LOADING_STATUS = null;
    }
    goLivingBodyFun(false);
  }
};

regNativeEvent("onBack", () => {
  dialogJumpTo();
});

regNativeEvent("onResume", () => {
  onResume();
});

const onClickLeft = () => {
  dialogJumpTo();
};
const afterReadingIt = () => {
  const arr = data.value.iframeArr.filter((item) => item.status);
  const arr2 = data.value.iframeArr.map((item) => {
    const { contractUrl, contractContent, ...rest } = item;
    return rest;
  });

  if (arr.length === data.value.iframeArr.length) {
    data.value.afterReadingItStatus = true;
  }
  const storageData = data.value.storageDataV2;
  storageData.allRead = data.value.afterReadingItStatus;
  storageData.iframeArr = arr2;
  setWithExactExpireUnit(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`, storageData, 7, "D");
};

const allReaded = () => {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }
  goLivingBodyFun(true);
  store.goNext();
};

const scrollPosition = ref({ st: 0, ed: 0, top: 0, bottom: 0, init: "top" });
let startY = 0;

const iframeClick = lodash.debounce(
  (index, scrollInit = "top") => {
    scrollPosition.value.init = scrollInit;
    if (!data.value.iframeArr[index].status) {
      ifReading.value[index] = true;
    }
    data.value.currentIndex = index;
    data.value.iframeArr[index].then((res) => {
      if (res.contractType === "pdf" || res.contractType === "html") {
        iframeRef.value.removeAttribute("srcdoc");
        iframeRef.value.src = res.contractUrl;
      }
      if (res.contractType === "content") {
        iframeRef.value.removeAttribute("src");
        iframeRef.value.srcdoc = res.contractUrl;
      }
    });
  },
  1000,
  { leading: true },
);

const handleTouchStart = (event) => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  if (iframeDocument) {
    scrollPosition.value.st =
      iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
  }
  // 记录初始触摸位置
  const touch = event.touches[0];
  startY = touch.clientY;
};

const swipeTop = () => {
  if (
    scrollPosition.value.st === scrollPosition.value.top &&
    scrollPosition.value.ed === scrollPosition.value.top
  ) {
    data.value.iframeArr[data.value.currentIndex].status = true;
    if (data.value.currentIndex - 1 >= 0) {
      iframeClick(data.value.currentIndex - 1, "bottom");
    }
    if (data.value.readAll === 1) {
      afterReadingIt();
    }
  }
};

const swipeBottom = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  const scrollHeight =
    iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
  const windowHeight = iframeDocument.defaultView.innerHeight;
  scrollPosition.value.bottom = scrollHeight - windowHeight - 1;
  if (
    scrollPosition.value.st > scrollPosition.value.bottom &&
    scrollPosition.value.ed > scrollPosition.value.bottom
  ) {
    data.value.iframeArr[data.value.currentIndex].status = true;
    if (data.value.currentIndex + 1 < data.value.iframeArr.length) {
      iframeClick(data.value.currentIndex + 1, "top");
    }
    if (data.value.readAll === 1) {
      afterReadingIt();
    }
  }
};

const handleScroll = lodash.debounce(
  (event) => {
    const iframeDocument =
      iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;

    // 计算滑动距离
    const touch = event.touches[0];
    const deltaY = touch.clientY - startY;
    if (iframeDocument && (iframeDocument.documentElement || iframeDocument.body)) {
      scrollPosition.value.ed =
        iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
    }

    // 根据滑动距离执行相应的逻辑
    if (deltaY > 30) {
      swipeTop();
    } else if (deltaY < -30) {
      swipeBottom();
    }
  },
  600,
  { leading: true },
);

const handleClick = (event) => {
  const callback = contractClickCallback(event);
  if (callback?.next === "goto") {
    goto(callback.href, true);
  }
};

const handleIframeLoad = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  if (!iframeDocument.documentElement && !iframeDocument.body) {
    return;
  }
  const scrollHeight =
    iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
  const windowHeight = iframeDocument.defaultView.innerHeight;
  scrollPosition.value = {
    ...scrollPosition.value,
    st: 0,
    ed: 0,
    top: 0,
    bottom: scrollHeight - windowHeight,
  };
  iframeDocument.removeEventListener("touchstart", handleTouchStart);
  iframeDocument.addEventListener("touchstart", handleTouchStart);
  iframeDocument.removeEventListener("touchmove", handleScroll);
  iframeDocument.addEventListener("touchmove", handleScroll);
  iframeDocument.removeEventListener("click", handleClick);
  iframeDocument.addEventListener("click", handleClick);
  if (isShowMore.value) {
    data.value.iframeHeight = `${window.innerHeight - 88 - 25 - 86 - (data.value.iframeArr.length > 11 ? 248 : data.value.iframeArr.length * 24)}px`;
  } else {
    data.value.iframeHeight = `${window.innerHeight - 88 - 25 - 86 - 24}px`;
    scrollContainer.value.scrollTop = 0;
  }
  data.value.contentHeight = `${window.innerHeight - 88 - 25 - 24}px`;
};

const changeList = () => {
  isShowMore.value = !isShowMore.value;
};

const getIframeArr = computed(() =>
  isShowMore.value ? data.value.iframeArr : data.value.iframeArr.slice(0, 1),
);

watch(
  () => data.value.currentIndex,
  () => {
    handleIframeLoad();
  },
  {
    deep: true,
  },
);
watch(
  () => isShowMore,
  () => {
    if (isShowMore.value) {
      data.value.iframeHeight = `${window.innerHeight - 88 - 25 - 86 - (data.value.iframeArr.length > 11 ? 248 : data.value.iframeArr.length * 24)}px`;
    } else {
      data.value.iframeHeight = `${window.innerHeight - 88 - 25 - 86 - 24}px`;
    }
  },
  {
    deep: true,
  },
);
watch(
  () => data.value.iframeArr,
  (n) => {
    if (n && n.length) {
      if (n === 1) {
        store.iframeArr[0].then(() => {
          store.signLoading = false;
        });
      }
      store.iframeArr.forEach((item, index) => {
        item.then((res) => {
          store.iframeArr[index].contractName = res.contractName;
          store.iframeArr[index].status = res.status;
        });
      });
      iframeClick(0);
    }
  },
);

onMounted(async () => {
  isDarkMode.value = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
  const res = await getCurrentCpInfo();
  supplierId.value = res?.supplierId || supplierId.value;
  try {
    upgradeCreditProcessStore(data.value.param.userId, "AGREEMENT_CREDIT");
  } catch (error) {
    console.error(error);
  }
  goLivingBodyFun(false);
  onResume();

  await store.contractList();
  isShowMore.value = store.expendAll || isShowMore.value;
});
</script>

<template>
  <div class="page">
    <div class="content-top">
      <hnr-nav-bar transparent="true" class="nav-padding-top" title="协议签署">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <div v-if="data.supplierName" class="content-title">
        以下协议由{{ data.supplierName }}提供，请仔细阅读：
      </div>
    </div>
    <div class="content" :style="{ height: data.contentHeight }">
      <div class="content-iframe" :style="{ height: data.contentHeight }">
        <iframe
          ref="iframeRef"
          frameborder="0"
          style="
            border-radius: var(--hnr-default-corner-radius-m) var(--hnr-default-corner-radius-m) 0 0 !important;
          "
          :style="{ background: isDarkMode && supplierId === supplierIdMap.ppd ? 'black' : '' }"
          width="100%"
          :height="data.iframeHeight"
          @load="handleIframeLoad"
        ></iframe>
      </div>
      <div class="btn-area">
        <div
          ref="scrollContainer"
          :style="{
            overflowY: isShowMore ? 'auto' : 'hidden',
            'max-height': '248px',
          }"
        >
          <!-- 多份协议 -->
          <div v-if="data.iframeArr.length > 1" class="header" @click="changeList">
            <span>{{ `共${data.iframeArr.length}份协议` }}</span>
            <span :class="isShowMore ? 'icon-down' : 'icon-up'"></span>
          </div>
          <!-- 多份协议 -->
          <div v-for="(item, index) in getIframeArr" :key="index" class="btn-area-agreement">
            <div class="btn-area-title" @click="iframeClick(index)">
              {{ store.contractNameList[index] }}
            </div>
            <!-- 单份协议 -->
            <div v-if="data.iframeArr.length === 1" class="btn-area-fold">
              <div class="item-right light-color">
                <span>{{ `共${data.iframeArr.length}份协议` }}</span>
              </div>
            </div>
            <!-- 单份协议 -->
          </div>
        </div>
        <SignReadButton
          :force-pull-down="data.readAll"
          :read-already="data.afterReadingItStatus"
          :mandatory-reading-seconds="data.readingSecondsTime"
          @click="allReaded"
        />
      </div>
    </div>
    <div class="entrance-loading">
      <reloading-component
        :loading-status="data.signLoading && !data.contractFail"
        :error-status="data.signLoading && data.contractFail"
        @try-click="reload"
      />
    </div>
    <no-ssr>
      <hnr-dialog v-model:show="data.dialogShow" :before-close="() => false">
        <template #default>
          <div class="dialog-message">
            {{ data.dialogMessageFail }}
          </div>
        </template>
        <template #footer>
          <hnr-button type="default" class="dialog-btn" @click="continueLater">稍后继续</hnr-button>
          <hnr-button type="primary" class="ml10 dialog-btn" @click="continueApply"
            >继续申请</hnr-button
          >
        </template>
      </hnr-dialog>
    </no-ssr>
  </div>
</template>

<style scoped>
.page {
  background: var(--hnr-color-background-cardview);
  height: 100vh;
}
.content-title {
  color: var(--hnr-text-color-secondary);
  padding: 0 24px 8px 24px;
  font-size: 13px;
}
.content-top {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--hnr-color-background-cardview);
}
.content {
  padding-top: 117px;
}
.btn-area {
  position: fixed;
  overflow: hidden;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background: var(--hnr-color-background-cardview);
  padding: 16px 24px var(--hnr-default-padding-bottom-fixed) 24px;
  box-sizing: border-box;
  color: var(--hnr-text-color-primary);
}
.btn-area ::-webkit-scrollbar {
  display: none;
}
.btn-area-agreement {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.btn-area-title {
  color: var(--hnr-text-color-link);
  /* color: var(--hnr-text-color-primary-activated); */
  padding-bottom: var(--hnr-elements-margin-vertical-M);
  width: 70%;
  word-wrap: break-word !important;
}
.btn-area-btn {
  width: 100%;
  margin-top: 8px;
  max-width: none !important;
}
.btn-area-fold {
  display: flex;
  justify-content: flex-start;
  margin-bottom: var(--hnr-elements-margin-vertical-S);
  gap: var(--hnr-elements-margin-vertical-S);
}
.item-right {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.light-color {
  color: var(--hnr-text-color-secondary);
}
.icon-size {
  width: 12px;
  height: 12px;
  color: var(--hnr-color-secondary);
}
.content-iframe {
  margin: 0 var(--dp12);
  border-radius: var(--hnr-default-corner-radius-m);
  background: var(--hnr-color-card-background);
}
.header {
  position: absolute;
  top: 8px;
  right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--hnr-text-color-secondary);
  z-index: 1;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  height: 30px;
}
.icon-down:before {
  content: "\e900";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.icon-up:before {
  content: "\e901";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.ml10 {
  margin-left: 10px;
}
::v-deep(.hnr-dialog__footer) {
  padding: var(--dp10) var(--dp20) var(--dp20) !important;
}
.dialog-message {
  display: flex;
  justify-content: center;
}
.entrance-loading {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  height: calc(100vh - 133px);
  display: flex;
  width: 100%;
}
.entrance-loading-text {
  margin-top: var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-regular);
}
.icon-notServe {
  width: var(--dp64);
  height: auto;
}
:deep(.icon-notServe svg g g polygon) {
  fill: var(--hnr-color-foreground);
}
.dialog-btn {
  min-width: 50% !important;
}
</style>
