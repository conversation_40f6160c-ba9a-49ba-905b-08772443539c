import { defineStore } from "pinia";
import {
  request,
  handleHtmlText,
  fetchHtml,
  isPdfUrl,
  showPdfUrl,
  userBehaviorLogReporting,
  isSupportForIframeSrc,
} from "../../../helpers/utils";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { report } from "../../../helpers/native-bridge";
import { creditNext } from "../../../helpers/next";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

export default defineStore("credit/sign", {
  state: () => ({
    // 页面初始数据
    contentHeight: 0,
    iframeHeight: 0,
    iframeArr: [],
    currentIndex: 0,
    afterReadingItStatus: false,
    param: {},
    supplierName: "",
    readingSeconds: "",
    readingSecondsTime: "",
    readAll: "",
    storageDataV2: {},
    dialogShow: false,
    messageStatus: "",
    dialogMessageOne: "",
    dialogMessageFail: "申请额度失败，已为您更换金融服务机构，可重新签署协议继续申请。",
    expendAll: false,
    signLoading: false,
    contractFail: false,
    contractNameList: [],
    supplierId: 0,
  }),
  actions: {
    async doreport(orderNo, operType, operResult) {
      const params = {
        orderNo, // apply_no
        operType,
        operResult, // 1-成功，2-失败，3-取消
        operParams: "",
        supplier: 1,
        operTime: new Date(),
      };
      await request("/loan/api/user/operLog", params);
    },
    async goNext() {
      if (!window.navigator.onLine) {
        window.nextLoadingStatus(false);
      }
      report("wallet_page_click", { click_name: "credit_agreement_read_click" });
      const storageData = getStore(`U_CREDIT_PROCESS_V2_${this.param.userId}`) || {};
      storageData.AGREEMENT_CREDIT = true;

      setTimeout(() => {
        this.doreport("", 103, 1);
      }, 0);

      setWithExactExpireUnit(`U_CREDIT_PROCESS_V2_${this.param.userId}`, storageData, 7, "D");
      creditNext(this.param.userId);
    },
    async contractList() {
      const cpInfo = await getCurrentCpInfo();
      this.supplierId = cpInfo?.supplierId || 0;
      const storageData = getStore(`U_CREDIT_PROCESS_V2_${this.param.userId}`) || {};
      const arr = storageData.iframeArr || [];
      this.afterReadingItStatus = storageData.allRead || false;
      this.supplierName = storageData.supplierName || cpInfo?.supplierName || "";
      this.signLoading = true;

      setTimeout(() => {
        userBehaviorLogReporting("", 104, 1, 1, storageData.supplier || 0, new Date().getTime());
      }, 0);

      const res = await request("/loan/api/contract/v2/list", { type: 1 }, { mock: false });
      if (res) {
        if (res.code !== 0) {
          this.contractFail = true;
          return;
        }
        this.contractNameList = res.data.contractList.map((contract) => contract.contractName);
        this.iframeArr = res.data.contractList.map(async (item) => {
          let contractType;
          let contractUrl;
          if (!item.contractContent && isPdfUrl(item.contractUrl || "")) {
            contractType = "pdf";
            contractUrl = showPdfUrl(item.contractUrl);
          } else if (item.contractContent) {
            contractType = "content";
            contractUrl = handleHtmlText(item.contractContent);
          } else if (isSupportForIframeSrc(this.supplierId)) {
            const innerUrl = new URL(item.contractUrl);
            const queryString = innerUrl.search;
            contractType = "html";
            contractUrl = `${window.location.origin}/wallet-loan-web/uri?href=${encodeURIComponent(innerUrl.origin + innerUrl.pathname)}${queryString}&isSupportIframeSrcUrl=${true}`;
          } else {
            contractType = "content";
            contractUrl = await fetchHtml(
              `${window.location.origin}/wallet-loan-web/uri?href=${encodeURIComponent(item.contractUrl)}`,
            );
          }
          let status = this.afterReadingItStatus ? "1" : "";
          arr.forEach((arrItem) => {
            if (arrItem.status && arrItem.contractName === item.contractName) {
              status = "1";
            }
          });
          return {
            ...item,
            contractUrl,
            contractType,
            status,
          };
        });
        const obj = JSON.parse(window.name || "{}");
        if (this.param.fromPage === "creditResult" && !obj.continue) {
          this.readingSecondsTime = 0;
        } else {
          this.readingSecondsTime = res.data.readingSeconds || 0;
        }
        this.readingSeconds = res.data.readingSeconds;
        this.readAll = res.data.readAll;
        this.expendAll = !!res.data.expendAll;
        this.signLoading = false;
        this.contractFail = false;
      }
    },
  },
});
