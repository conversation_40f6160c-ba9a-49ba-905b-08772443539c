<script setup>
import { onMounted, defineEmits, defineProps } from "vue";
import lodash from "lodash";
import { report } from "../../../../helpers/native-bridge";

const emit = defineEmits(["continueApply"]);
const props = defineProps({
  reapplyLoading: {
    type: <PERSON><PERSON><PERSON>,
  },
  reapplyDisabled: {
    type: <PERSON><PERSON>an,
  },
});

onMounted(async () => {
  report("wallet_page_view", {
    page_name: "reactive_apply_fail_banner",
  });
});

const handleClick = lodash.debounce(() => {
  report("wallet_page_click", {
    page_name: "reactive_apply_fail_banner_click",
  });
  emit("continueApply");
}, 500);
</script>

<template>
  <div class="increase-con">
    <div class="increase-con-title">您可继续申请新额度</div>
    <div class="increase-con-card increase-con-card-single">
      <div class="increase-con-card-con">
        <div class="increase-con-card-con-top increase-con-card-con-top-single">
          <div class="increase-con-card-con-top-item">
            <div class="increase-con-card-con-top-item-top">
              <span>最高额度(元)</span>
            </div>
            <div class="increase-con-card-con-top-item-mid">
              <span>300,000</span>
            </div>
          </div>
          <div class="increase-con-card-con-top-right">
            <hnr-button
              size="mini"
              class="relend-btn2"
              :loading="props.reapplyDisabled"
              :disabled="props.reapplyLoading"
              @click="handleClick"
              >继续申请</hnr-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.increase-con {
  background-image: url("/public/loan/LoanTempImage.png");
  border-radius: var(--hnr-default-corner-radius-m);
  background-position: right top;
  background-repeat: no-repeat;
  background-size: 100% auto;
  width: calc(100% - 24 * var(--dp));
  margin-top: calc(36 * var(--dp));
  margin-left: calc(12 * var(--dp));
  margin-right: calc(12 * var(--dp));
}
.increase-con-title {
  padding: var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2);
  color: #734c16;
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}
.increase-con-card {
  background: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-default-corner-radius-m);
  padding: var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-horizontal-M2);
}
.increase-con-card-single {
  padding-right: var(--hnr-elements-margin-horizontal-L);
}
.relend-btn2 {
  background-color: #e5ae4e;
  width: 80px;
  max-width: none !important;
  min-width: 80px !important;
  color: #fff;
  padding: 0;
}
::v-deep(.hnr-loading__spinner--small) {
  width: var(--dp16) !important;
  height: var(--dp16) !important;
}
.increase-con-card-con-top {
  display: flex;
}
.increase-con-card-con-top-single {
  align-items: center;
  justify-content: space-between;
}
.increase-con-card-con-top-item:nth-child(1) {
  min-width: 50%;
}
.increase-con-card-con-top-item:nth-child(2) {
  margin-left: var(--hnr-elements-margin-horizontal-XL);
}
.increase-con-card-con-top-item-top {
  color: #734c16;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.increase-con-card-con-top-item-mid {
  color: #734c16;
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  margin-top: var(--hnr-elements-margin-vertical-XS);
}
.increase-con-card-con-top-item-mid del {
  color: #734c16;
  opacity: 0.38;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-S);
}
@media (prefers-color-scheme: dark) {
  .increase-con-card-con-top-item-top {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-mid {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-mid del {
    color: #fdc373;
    opacity: 0.6;
  }
  .increase-con-card-con-top-item-bottom {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-bottom-opacity {
    opacity: 0.6;
  }
}
</style>
