<script setup>
import { onMounted } from "vue";
import { initStore } from "../../../../helpers/utils";
import useStore from "../store";
import { formatCurrencyAmount } from "../../../../helpers/mathUtils";
import { goto } from "../../../../helpers/native-bridge";
import {
  reportCreditSuccessLoanMoreButtonClick,
  reportCreditSuccessLoanMoreButtonView,
} from "../report";

const { data } = initStore(useStore);

onMounted(() => {
  reportCreditSuccessLoanMoreButtonView();
});

function gotoCalc() {
  reportCreditSuccessLoanMoreButtonClick();

  goto(
    `/wallet-loan-web/pages/loan/calc?amount=${(data.value.creditInfo.totalAvailableLimit || data.value.creditInfo.remainLimit) / 100}&fromPage=creditResult`,
  );
}
</script>

<template>
  <div class="increase-con">
    <div v-if="data.changeType === 1" class="increase-con-title">
      恭喜您获得<span v-if="data.tempLimitValidDays && data.tempLimitValidDays >= 0">限时</span
      >额度提高
    </div>
    <div v-else-if="data.changeType === 2" class="increase-con-title">
      恭喜您获得<span v-if="data.tempPriceValidDays && data.tempPriceValidDays >= 0">限时</span
      >利率优惠
    </div>
    <div v-else class="increase-con-title">恭喜您获得专享借款优惠</div>
    <div class="increase-con-card" :class="{ 'increase-con-card-single': data.changeType !== 3 }">
      <div v-if="data.changeType === 1" class="increase-con-card-con">
        <div class="increase-con-card-con-top increase-con-card-con-top-single">
          <div class="increase-con-card-con-top-item">
            <div class="increase-con-card-con-top-item-top">
              <span>可用额度(元)</span>
              <span
                v-if="data.tempLimitValidDays > 0"
                class="increase-con-card-con-top-item-top-label"
                >限时{{ data.tempLimitValidDays }}天</span
              >
              <span
                v-if="data.tempLimitValidDays === 0"
                class="increase-con-card-con-top-item-top-label"
                >今日到期</span
              >
            </div>
            <div class="increase-con-card-con-top-item-mid">
              <span>{{ formatCurrencyAmount(data.totalAmount) }}</span>
              <del>{{ formatCurrencyAmount(data.oldLimit) }}</del>
            </div>
            <div
              class="increase-con-card-con-top-item-bottom increase-con-card-con-top-item-bottom-opacity"
            >
              剩余可借额度{{ formatCurrencyAmount(data.changeRemainLimit) }}元
            </div>
          </div>
          <div class="increase-con-card-con-top-right">
            <hnr-button size="mini" class="relend-btn2" @click="gotoCalc">借一笔</hnr-button>
          </div>
        </div>
      </div>
      <div v-else-if="data.changeType === 2" class="increase-con-card-con">
        <div class="increase-con-card-con-top increase-con-card-con-top-single">
          <div class="increase-con-card-con-top-item">
            <div class="increase-con-card-con-top-item-top">
              <span>年利率低至</span>
              <span
                v-if="data.tempPriceValidDays > 0"
                class="increase-con-card-con-top-item-top-label"
                >限时{{ data.tempPriceValidDays }}天</span
              >
              <span
                v-if="data.tempPriceValidDays === 0"
                class="increase-con-card-con-top-item-top-label"
                >今日到期</span
              >
            </div>
            <div class="increase-con-card-con-top-item-mid">
              <span>{{ data.newApr }}%</span>
              <del>{{ data.oldApr }}%</del>
            </div>
            <div
              class="increase-con-card-con-top-item-bottom increase-con-card-con-top-item-bottom-opacity"
            >
              借1万用1天仅{{ (10000 * data.newDayRate) / 100 }}元
            </div>
          </div>
          <div class="increase-con-card-con-top-right">
            <hnr-button size="mini" class="relend-btn2" @click="gotoCalc">借一笔</hnr-button>
          </div>
        </div>
      </div>
      <div v-else class="increase-con-card-con">
        <div class="increase-con-card-con-top">
          <div class="increase-con-card-con-top-item">
            <div class="increase-con-card-con-top-item-top">
              <span>额度提高至(元)</span>
              <span
                v-if="data.tempLimitValidDays > 0"
                class="increase-con-card-con-top-item-top-label"
                >限时{{ data.tempLimitValidDays }}天</span
              >
              <span
                v-if="data.tempLimitValidDays === 0"
                class="increase-con-card-con-top-item-top-label"
                >今日到期</span
              >
            </div>
            <div class="increase-con-card-con-top-item-mid">
              {{ formatCurrencyAmount(data.totalAmount) }}
            </div>
            <div class="increase-con-card-con-top-item-bottom">
              <del>￥{{ formatCurrencyAmount(data.oldLimit) }}</del
              >，剩余{{ formatCurrencyAmount(data.changeRemainLimit) }}可借
            </div>
          </div>
          <div class="increase-con-card-con-top-item">
            <div class="increase-con-card-con-top-item-top">
              <span>年利率低至</span>
              <span
                v-if="data.tempPriceValidDays > 0"
                class="increase-con-card-con-top-item-top-label"
                >限时{{ data.tempPriceValidDays }}天</span
              >
              <span
                v-if="data.tempPriceValidDays === 0"
                class="increase-con-card-con-top-item-top-label"
                >今日到期</span
              >
            </div>
            <div class="increase-con-card-con-top-item-mid">{{ data.newApr }}%</div>
            <div class="increase-con-card-con-top-item-bottom">
              <del>{{ data.oldApr }}%</del>
            </div>
          </div>
        </div>
        <div class="increase-con-card-con-bottom">
          <hnr-button class="relend-btn" size="size" @click="gotoCalc">借一笔</hnr-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.increase-con {
  background-image: url("/public/loan/LoanTempImage.png");
  border-radius: var(--hnr-default-corner-radius-m);
  background-position: right top;
  background-repeat: no-repeat;
  background-size: 100% auto;
}
.increase-con-title {
  padding: var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2);
  color: #734c16;
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}
.increase-con-card {
  background: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-default-corner-radius-m);
  padding: var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-horizontal-M2);
}
.increase-con-card-single {
  padding-right: var(--hnr-elements-margin-horizontal-L);
}
.increase-con-card-con-bottom {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.relend-btn {
  background-color: #e5ae4e;
  width: 180px;
  max-width: none !important;
  min-width: none !important;
}
.relend-btn:active {
  background-color: #d9a54a;
}
.relend-btn2 {
  background-color: #e5ae4e;
  width: 80px;
  max-width: none !important;
  min-width: 80px !important;
  color: #fff;
  padding: 0;
}
.increase-con-card-con-top {
  display: flex;
}
.increase-con-card-con-top-single {
  align-items: center;
  justify-content: space-between;
}
.increase-con-card-con-top-item:nth-child(1) {
  min-width: 50%;
}
.increase-con-card-con-top-item:nth-child(2) {
  margin-left: var(--hnr-elements-margin-horizontal-XL);
}
.increase-con-card-con-top-item-top {
  color: #734c16;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.increase-con-card-con-top-item-top-label {
  background-color: #e5ae4e;
  border-radius: var(--hnr-default-corner-radius-s);
  border-bottom-left-radius: 0;
  color: var(--hnr-text-color-primary-inverse);
  font-size: var(--hnr-caption);
  font-weight: var(--hnr-font-weight-regular);
  padding: 1px var(--hnr-elements-margin-horizontal-S);
  margin-left: var(--hnr-elements-margin-horizontal-S);
  transform: translateY(-1.5px);
  display: inline-block;
  white-space: nowrap;
}
.increase-con-card-con-top-item-mid {
  color: #734c16;
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  margin-top: var(--hnr-elements-margin-vertical-XS);
}

.increase-con-card-con-top-item-mid del {
  color: #734c16;
  opacity: 0.38;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-S);
}
.increase-con-card-con-top-item-bottom {
  color: #734c16;
  opacity: 0.38;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-XS);
}
.increase-con-card-con-top-item-bottom-opacity {
  opacity: 0.68;
}
@media (prefers-color-scheme: dark) {
  .increase-con-card-con-top-item-top {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-mid {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-mid del {
    color: #fdc373;
    opacity: 0.6;
  }
  .increase-con-card-con-top-item-bottom {
    color: #fdc373;
  }
  .increase-con-card-con-top-item-bottom-opacity {
    opacity: 0.6;
  }
}
</style>
