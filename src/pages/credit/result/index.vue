<script setup>
import { ref, computed, onMounted } from "vue";
import Big from "big.js";
import lodash from "lodash";
import useStore from "./store";
import { amountNumberFormat, initStore, request } from "../../../helpers/utils";
import {
  goto,
  hasShortcut,
  updateShortcut,
  report,
  enableBackPress,
  regNativeEvent,
  toastMessage,
} from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { del, setWithExactExpireUnit } from "../../../helpers/storage";
import SuggestProductCard from "../../component/SuggestProductCard.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { getAdActivityListV2 } from "../../../api/ad";
import CreditAdjustIncentiveCard from "./components/CreditAdjustIncentiveCard.vue";
import ReactiveCard from "./components/ReactiveCard.vue";

const { store, data } = initStore(useStore);
const suggestProducts = computed(() => data.value.suggestProducts);
const applyNo = computed(() => data.value?.param?.applyNo);
const duration = 30; // **倒计时总秒数**
const countdown = ref(duration);
const currentRate = ref(0); // **初始为0，表示圆环起点**
const flag = ref(false); // 审核/结果页面切换
const updateShortcutChecked = ref(true);
const applyStatus = computed(
  () =>
    // // 1-审核中，2-成功，3-失败，4-拒绝，5-取消，6-需鉴权（用信补录，补签协议后再提交借款）
    data.value.applyInfo?.applyStatus,
);
const message = computed(() => data.value.param?.message);
const creditLimit = computed(() => {
  const amount = Big(data.value.applyInfo?.remainLimit || 0).div(100);
  return amount;
});
const showShortCut = ref(); // 快捷方式是否存在
const refuseControlDays = computed(() => data.value?.applyInfo?.refuseControlDays);
const setUpdateShortcut = () => {
  if (
    showShortCut.value === false &&
    applyStatus.value === 2 &&
    store.param.versionCode >= 81300350 &&
    updateShortcutChecked.value
  ) {
    updateShortcut(true).then((ret) => {
      if (ret) del(`U_POPUP_STORE_${store.param.userId}`);
    });
  }
};
const toHome = () => {
  setUpdateShortcut();
  // 返回首页
  if (applyStatus.value === 3) {
    report("wallet_page_click", { click_name: "credit_apply_fail_finish_click" });
  }
  if (applyStatus.value === 2) {
    setWithExactExpireUnit("U_HOME_HIDE_SHOW_ANIMATION", true, 7, "D");
  }
  goto("/wallet-loan-web/pages/index", false, true);
};
const borrow = () => {
  setUpdateShortcut();
  // 去借钱
  if (applyStatus.value === 2) {
    report("wallet_page_click", { click_name: "credit_apply_success_finish_click" });
    setWithExactExpireUnit("U_HOME_HIDE_SHOW_ANIMATION", true, 7, "D");
  }
  goto(
    `/wallet-loan-web/pages/loan/calc?amount=${data.value.applyInfo?.remainLimit}&fromPage=creditResult`,
  );
};
const reapplyLoading = ref(false);
const reapplyDisabled = ref(false);
const reapply = lodash.debounce(async (type = 0) => {
  if (type === 1) {
    reapplyLoading.value = true;
  } else {
    reapplyDisabled.value = true;
  }
  if (type === 1) {
    report("wallet_page_click", { click_name: "credit_reactive_fail_contininue_click" });
  }
  await store.rebindFlowNo(1);
  reapplyLoading.value = false;
  reapplyDisabled.value = false;
}, 500);

const onClickLeft = () => {
  // 返回按钮
  goto("/wallet-loan-web/pages/index", false, true);
};
regNativeEvent("onBack", async () => {
  goto("/wallet-loan-web/pages/index", false, true);
});
const updateNetwork = () => {
  setNetwork();
};
const handleNetwork = () => {
  if (!flag.value) {
    // 仅在倒计时阶段可刷新
    window.location.reload();
  }
};

const activityBanner = ref([]);
const getThirdPartnerBannerUrl = async () => {
  const ans = {};
  const res = await request("/loan/api/activity/getMarkingActivityUrl", {});
  if (res.code === 0) {
    ans.markingActivityUrl = res.data?.markingActivityUrl;
    if (res.data?.validSeconds) {
      ans.validSeconds = res.data.validSeconds;
      ans.validStatus = true;
      ans.timeout = setTimeout(
        () => {
          ans.validStatus = false;
        },
        ans.validSeconds * 1000 - 10,
      );
    }
  }
  return ans;
};

const isWideScreen = computed(() => {
  if (typeof window !== "undefined") {
    return window.innerWidth > 600;
  }
  return false;
});

const getAdActivityBanner = async () => {
  const cpInfo = await getCurrentCpInfo();
  if (cpInfo?.supplierId) {
    const spaceCodes = ["loan_credit_success_banner", "loan_credit_success_banner_widescreen"];
    getAdActivityListV2(spaceCodes).then(async (activityData) => {
      activityBanner.value = (activityData?.data || {})[spaceCodes[0]].activityList || [];
      if (
        activityBanner.value.length > 0 &&
        (activityData?.data || {})[spaceCodes[1]].activityList?.[0]?.picUrl
      ) {
        activityBanner.value[0].widePicUrl = (activityData?.data || {})[
          spaceCodes[1]
        ].activityList?.[0]?.picUrl;
      }
      if (activityBanner.value?.[0]?.targetUrl) {
        setTimeout(() => {
          report("wallet_page_view", {
            page_name: "loan_credit_success_banner",
            activityName: activityBanner.value[0].activityName,
            policy_id: activityBanner.value[0]?.abTestPolicy?.expConfCode,
          });
        }, 0);
      }
    });
  }
};

onMounted(async () => {
  window.addEventListener("online", handleNetwork);
  enableBackPress(false);
  window.addEventListener("hashchange", () => {
    flag.value = !!window.location.hash;
  });
  showShortCut.value = await hasShortcut();
  if (applyNo.value === "undefined" || !applyNo.value) {
    data.value.applyInfo.applyStatus = 3;
    report("wallet_page_view", { page_name: "credit_apply_fail_page" });
  }
  store.initial();
  const timer = setInterval(async () => {
    showShortCut.value = await hasShortcut();
    if (countdown.value > 1) {
      if (!window?.navigator?.onLine) {
        data.value.applyFlag = !window.navigator.onLine;
        clearInterval(timer);
        return;
      }
      if (data.value.errorCode) {
        flag.value = true;
        clearInterval(timer);
        return;
      }
      countdown.value -= 1;
      currentRate.value = ((duration - countdown.value) / (duration - 1)) * 30;
      if (
        applyStatus.value === 2 ||
        applyStatus.value === 3 ||
        (applyStatus.value === 1 && data.value.applyInfo?.supplier)
      ) {
        if (applyStatus.value === 3) {
          window.location.hash = "3";
          del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
          await store.getAdActivity(); // 申请失败推荐产品
          flag.value = true;
          clearInterval(timer);
        } else if (applyStatus.value === 2) {
          window.location.hash = "2";
          del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
          flag.value = true;
          clearInterval(timer);
          getAdActivityBanner();
        }
        enableBackPress(true);
      } else if (countdown.value % 5 === 0) {
        await store.initial();
        if (applyStatus.value === 3) {
          window.location.hash = "3";
          del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
          await store.getAdActivity(); // 申请失败推荐产品
          flag.value = true;
          enableBackPress(true);
          clearInterval(timer);
        } else if (applyStatus.value === 2) {
          window.location.hash = "2";
          del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
          flag.value = true;
          getAdActivityBanner();
          enableBackPress(true);
          clearInterval(timer);
        }
      }
    } else if (countdown.value === 1) {
      currentRate.value = 30;
      // 倒计时30s后，如 授信申请查询状态applyStatus依旧为1--审核中,关闭倒计时，展示正在审核页面
      if (
        (store.applyInfo.applyStatus === 1 && !store.applyInfo.supplier) ||
        data.value.errorCode
      ) {
        await store.initial();
      }
      if (applyStatus.value === 3) {
        window.location.hash = "3";
        del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
        await store.getAdActivity(); // 申请失败推荐产品
        flag.value = true;
      } else if (applyStatus.value === 2) {
        window.location.hash = "2";
        del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
        flag.value = true;
        getAdActivityBanner();
      } else if (applyStatus.value === 1 || data.value.errorCode) {
        flag.value = true;
        window.location.hash = "1";
      }
      enableBackPress(true);
      clearInterval(timer);
    }
  }, 1000);
});

const clickBanner = async (banner, index) => {
  let targetUrl;
  if (banner.targetUrl !== "activity") {
    targetUrl = banner.targetUrl;
  } else if (!banner.validStatus) {
    activityBanner.value[0] = { ...activityBanner.value[0], ...(await getThirdPartnerBannerUrl()) };
    if (!activityBanner.value[0].markingActivityUrl) {
      toastMessage("当前活动异常，请稍后重试");
      return;
    }
    targetUrl = activityBanner.value[0].markingActivityUrl;
  } else {
    targetUrl = banner.markingActivityUrl;
  }
  goto(targetUrl, true);
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "loan_credit_success_banner_click",
      activityName: banner.activityName,
      index,
      policy_id: banner?.abTestPolicy?.expConfCode,
    });
  }, 0);
};

const clickBannerDebounce = lodash.debounce(clickBanner, 1000, { leading: true });
</script>
<template>
  <div class="result-body">
    <hnr-nav-bar
      v-if="!flag"
      transparent="true"
      title="申请额度"
      class="nav-padding-top top-navigation no-arrow"
    />
    <hnr-nav-bar
      v-if="flag"
      transparent="true"
      title="申请额度"
      class="nav-padding-top top-navigation"
    >
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div class="body-content">
      <div v-if="!flag && !data.applyFlag" class="body-content">
        <div class="circleStyle">
          <hnr-circle
            v-model:current-rate="currentRate"
            :rate="30"
            :text="currentRate"
            :clockwise="true"
            size="64"
            stroke-width="4"
          >
            <template #default>
              <div class="circle-text">
                {{ `${countdown}` }}
                <div style="font-size: var(--dp18); margin-top: var(--dp5)">s</div>
              </div>
            </template>
          </hnr-circle>
        </div>
        <div class="textStyle1">正在快速处理您的请求</div>
        <div class="boxStyle">
          <div class="iconStyle">
            <img class="icon" src="/Credit_Result_Successful.svg" />
            <div class="verticalLine1"></div>
            <img class="icon" src="/Credit_Result_Successful.svg" />
            <div class="verticalLine2"></div>
            <span class="icon-Credit_Result_Examine"></span>
          </div>
          <div class="right">
            <div class="textStyle2">您的申请已提交</div>
            <div class="textStyle3">平台已接受</div>
            <div class="textStyle4">正在进行审核</div>
          </div>
        </div>
      </div>
      <div v-else class="body-content">
        <template v-if="data.param.reActive === '1'">
          <div v-if="applyStatus === 2 && !data.applyFlag" class="main">
            <div class="result-content">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Successful.svg" />
              </div>
              <div class="rusult-message">申请成功，可借额度</div>
              <div v-if="Big(creditLimit || 0).gt(0)" class="limit-money">
                ￥{{ amountNumberFormat(creditLimit) }}
              </div>
              <div v-if="data.applyInfo?.apr" class="limit-percent">
                年利率 {{ data.applyInfo?.apr }}%起（日利率{{ data.applyInfo?.dayRate }}%）
              </div>
              <div
                v-show="data.changeType"
                style="box-sizing: border-box; width: 100%; margin-top: 50px; padding: 0 12px"
              >
                <CreditAdjustIncentiveCard />
              </div>
              <div
                v-show="!data.changeType && activityBanner.length > 0"
                class="activity-banner-container"
              >
                <div
                  v-show="activityBanner?.[0]?.targetUrl"
                  class="content"
                  :style="{
                    width: !isWideScreen
                      ? 'calc(336 * var(--dp1))'
                      : 'calc(100% - var(--hnr-default-padding-start) - var(--hnr-default-padding-end))',
                  }"
                  @click="clickBannerDebounce(activityBanner[0], 1)"
                >
                  <div v-show="isWideScreen" class="image-container-wide">
                    <img :src="activityBanner?.[0]?.widePicUrl" />
                  </div>
                  <div v-show="!isWideScreen" class="image-container">
                    <img :src="activityBanner?.[0]?.picUrl" />
                  </div>
                </div>
              </div>
            </div>
            <hnr-checkbox
              v-if="!showShortCut && store.param.versionCode >= 81300350"
              v-model="updateShortcutChecked"
              >添加“借钱服务”快捷方式到桌面</hnr-checkbox
            >
            <div class="successBtn">
              <hnr-row type="gird">
                <hnr-col span="12">
                  <hnr-button type="default" @click="toHome">完成</hnr-button>
                </hnr-col>
                <hnr-col span="12">
                  <hnr-button type="primary" @click="borrow">去借款</hnr-button>
                </hnr-col>
              </hnr-row>
            </div>
          </div>
          <div v-if="(applyStatus === 1 || data.errorCode) && !data.applyFlag" class="main">
            <div class="result-content">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Wait.svg" />
              </div>
              <div class="rusult-message">正在为您获取审批结果</div>
              <div class="message">结果将以短信通知，请耐心等待</div>
            </div>
            <div class="loseBtn">
              <hnr-row type="gird" justify="center">
                <hnr-col span="24">
                  <hnr-button standard-width="true" type="primary" @click="toHome">完成</hnr-button>
                </hnr-col>
              </hnr-row>
            </div>
          </div>
          <div v-if="applyStatus === 3 || data.applyFlag" class="main">
            <div class="result-content pb80">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Lose.svg" />
              </div>
              <div class="lost-Comtent">
                <div class="rusult-message">额度激活失败</div>
                <div v-if="applyNo !== 'undefined' && data.applyFlag" class="message">
                  网络未连接，请检查网络设置
                </div>
                <div
                  v-if="(applyNo === 'undefined' || !applyNo) && !data.applyFlag"
                  class="message"
                >
                  {{
                    message?.includes("timeout")
                      ? "请求超时"
                      : message === "undefined"
                        ? ""
                        : message
                  }}
                </div>
              </div>
              <ReactiveCard
                :reapply-loading="reapplyLoading"
                :reapply-disabled="reapplyDisabled"
                @continue-apply="reapply()"
              />
            </div>
            <div class="loseBtn">
              <div class="successBtn">
                <hnr-row type="gird">
                  <hnr-col span="12">
                    <hnr-button type="default" @click="toHome">完成</hnr-button>
                  </hnr-col>
                  <hnr-col span="12">
                    <hnr-button
                      type="primary"
                      :disabled="reapplyDisabled"
                      :loading="reapplyLoading"
                      @click="reapply(1)"
                    >
                      继续申请
                    </hnr-button>
                  </hnr-col>
                </hnr-row>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div v-if="applyStatus === 2 && !data.applyFlag" class="main">
            <div class="result-content">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Successful.svg" />
              </div>
              <div class="rusult-message">申请成功</div>
              <div v-if="Big(creditLimit || 0).gt(0)" class="limit">可借额度</div>
              <div v-if="Big(creditLimit || 0).gt(0)" class="limit-money">
                ￥{{ amountNumberFormat(creditLimit) }}
              </div>
              <div v-if="data.applyInfo?.apr" class="limit-percent">
                年利率 {{ data.applyInfo?.apr }}%起（日利率{{ data.applyInfo?.dayRate }}%）
              </div>

              <div
                v-show="data.changeType"
                style="box-sizing: border-box; width: 100%; margin-top: 50px; padding: 0 12px"
              >
                <CreditAdjustIncentiveCard />
              </div>

              <div
                v-show="!data.changeType && activityBanner.length > 0"
                class="activity-banner-container"
              >
                <div
                  v-show="activityBanner?.[0]?.targetUrl"
                  class="content"
                  :style="{
                    width: !isWideScreen
                      ? 'calc(336 * var(--dp1))'
                      : 'calc(100% - var(--hnr-default-padding-start) - var(--hnr-default-padding-end))',
                  }"
                  @click="clickBannerDebounce(activityBanner[0], 1)"
                >
                  <div v-show="isWideScreen" class="image-container-wide">
                    <img :src="activityBanner?.[0]?.widePicUrl" />
                  </div>
                  <div v-show="!isWideScreen" class="image-container">
                    <img :src="activityBanner?.[0]?.picUrl" />
                  </div>
                </div>
              </div>
            </div>
            <hnr-checkbox
              v-if="!showShortCut && store.param.versionCode >= 81300350"
              v-model="updateShortcutChecked"
              >添加“借钱服务”快捷方式到桌面</hnr-checkbox
            >
            <div class="successBtn">
              <hnr-row type="gird">
                <hnr-col span="12">
                  <hnr-button type="default" @click="toHome">完成</hnr-button>
                </hnr-col>
                <hnr-col span="12">
                  <hnr-button type="primary" @click="borrow">去借款</hnr-button>
                </hnr-col>
              </hnr-row>
            </div>
          </div>
          <div v-if="(applyStatus === 1 || data.errorCode) && !data.applyFlag" class="main">
            <div class="result-content">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Wait.svg" />
              </div>
              <div class="rusult-message">正在为您获取审批结果</div>
              <div class="message">结果将以短信通知，请耐心等待</div>
            </div>
            <div class="loseBtn">
              <hnr-row type="gird" justify="center">
                <hnr-col span="24">
                  <hnr-button standard-width="true" type="primary" @click="toHome">完成</hnr-button>
                </hnr-col>
              </hnr-row>
            </div>
          </div>
          <div v-if="applyStatus === 3 || data.applyFlag" class="main">
            <div class="result-content pb80">
              <div class="iconStyle2">
                <img class="icon1" src="/Credit_Result_Lose.svg" />
              </div>
              <div class="lost-Comtent">
                <div class="rusult-message">申请失败</div>
                <div v-if="applyNo !== 'undefined' && !data.applyFlag" class="message">
                  {{
                    refuseControlDays
                      ? "您的申请评估未通过，暂时无法申请额度，" +
                        refuseControlDays +
                        "天后可重新申请"
                      : "您的申请评估未通过，暂时无法申请额度"
                  }}
                </div>
                <div v-if="applyNo !== 'undefined' && data.applyFlag" class="message">
                  网络未连接，请检查网络设置
                </div>
                <div
                  v-if="(applyNo === 'undefined' || !applyNo) && !data.applyFlag"
                  class="message"
                >
                  {{
                    message?.includes("timeout")
                      ? "请求超时"
                      : message === "undefined"
                        ? ""
                        : message
                  }}
                </div>
              </div>
              <div
                v-if="!data.applyFlag && suggestProducts && suggestProducts.length > 0"
                class="recommend-container"
              >
                <div class="list-product">可以尝试以下严选借款产品</div>
                <SuggestProductCard :suggest-products="suggestProducts" location="授信失败" />
              </div>
            </div>
            <div class="loseBtn">
              <hnr-row type="gird" justify="center">
                <hnr-col span="24">
                  <hnr-button
                    v-if="data.applyFlag"
                    standard-width="true"
                    type="primary"
                    @click="updateNetwork"
                    >设置网络</hnr-button
                  >
                  <hnr-button v-else standard-width="true" type="primary" @click="toHome"
                    >完成</hnr-button
                  >
                </hnr-col>
              </hnr-row>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style>
:root {
  --dp: 1px;
}
</style>

<style scoped>
.circle-text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: var(--dp24);
  font-weight: var(--hnr-font-weight-medium);
}

.result-body {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.body-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 88px);
  overflow-y: scroll;
}

.circleStyle {
  display: flex;
  justify-content: center;
  margin-top: var(--hnr-elements-margin-vertical-XXL);
  margin-bottom: 0;
}
::v-deep(.hnr-circle__text) {
  margin-top: 1px;
}

::v-deep(.hnr-circle__textsize) {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}

.circleStyle /deep/ .hnr-circle__textUnit {
  color: var(--hnr-text-color-primary) !important;
  font-size: var(--hnr-subtitle-2) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
}

.contentStyle {
  display: flex;
  justify-content: center;
}

.textStyle1 {
  margin-top: var(--hnr-elements-margin-vertical-L2);
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
}

.textStyle2 {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  display: flex;
  justify-content: flex-start;
  margin-top: -0.08rem;
}

.textStyle3 {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  margin-top: 1.6rem;
  display: flex;
  justify-content: flex-start;
}

.textStyle4 {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  margin-top: 1.56rem;
  display: flex;
  justify-content: flex-start;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.dark-icon {
  width: 1rem;
  height: 1rem;
  fill: #fff;
}

.textStyle {
  line-height: 2rem;
}

.boxStyle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--hnr-elements-margin-vertical-XXL) var(--hnr-max-padding-start)
    var(--hnr-max-padding-start) var(--hnr-max-padding-start);
}

.flowStyle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: var(--hnr-default-padding-start);
  padding-right: var(--hnr-default-padding-end);
}

.iconStyle {
  display: flex;
  flex-direction: column;
}

.iconStyle2 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.verticalLine1 {
  /* 竖线 */
  float: left;
  width: 1px;
  height: 1.5rem;
  margin: var(--hnr-elements-margin-vertical-XS) var(--hnr-elements-margin-vertical-M);
  background-color: var(--hnr-magic-color-4);
}

.right {
  height: 6.5rem;
}

.verticalLine2 {
  float: left;
  width: 1px;
  height: 1.5rem;
  margin: var(--hnr-elements-margin-vertical-XS) var(--hnr-elements-margin-vertical-M);
  background-color: var(--hnr-color-tertiary);
}

.icon1 {
  width: 4rem;
  height: 4rem;
  margin-top: var(--hnr-elements-margin-vertical-XXL);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}

.main {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  width: 100vw;
}

.rusult-message {
  text-align: center;
  padding: 0 var(--hnr-elements-margin-horizontal-M2);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
}

.message {
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: 2.25rem;
  padding: 0 var(--hnr-elements-margin-horizontal-M2);
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.limit {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-L2);
}
.limit-money {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-6);
  font-weight: var(--hnr-font-weight-medium);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
}
.limit-percent {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.footerCheck {
  position: fixed;
  bottom: 4rem;
  margin-bottom: 0.5rem;
}

.successBtn {
  /* width: 89%;
  display: flex;
  align-items: center;
  justify-content: space-around; */
  position: relative;
  margin-top: var(--hnr-elements-margin-vertical-M2);
  margin-bottom: 1.5rem;
}

.loseBtn {
  /* width: 100%;
  display: flex;
  box-sizing: border-box;
  align-items: center;
  justify-content: space-around; */
  width: 100%;
  padding-bottom: 1.5rem;
  position: fixed;
  left: 0;
  bottom: 0;
  background: var(--hnr-color-background-cardview);
  padding-top: var(--hnr-elements-margin-vertical-M2);
  z-index: 1000;
  text-align: center;
}

.recommend-container {
  width: 100vw;
  display: flex;
  position: relative;
  flex-direction: column;
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
  /* padding-bottom: 72px; */
}

.list-product {
  padding: var(--hnr-index-anchor-padding);
  margin: var(--hnr-elements-margin-vertical-M) 0;
  color: var(--hnr-color-app-bar-title);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.recommend-card {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
  border-radius: var(--hnr-card-border-radius);
  background-color: var(--hnr-card-background);
}

.recommend-card-supplier {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-left: var(--hnr-elements-margin-horizontal-M2);
}

.icon2 {
  width: 1rem;
  height: 1rem;
}

.recommend-card-supplier-text {
  margin-left: var(--hnr-elements-margin-horizontal-M);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}

.recommend-card-double-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}

.recommend-card-double-row-left {
  display: flex;
  flex-direction: column;
  width: 58%;
}

.recommend-card-double-row-one {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.recommend-card-double-row-two {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}

.recommend-card-double-row-right {
  margin-left: auto;
  margin-right: 0;
}

.icon-Credit_Result_Examine:before {
  content: "\e90a";
  font-family: "icomoon";
  font-size: var(--dp16);
  color: var(--hnr-color-tertiary);
}
.top-navigation {
  /* position: fixed !important;
  top: 0;
  width: 100%;
  z-index: 100; */
  background: var(--hnr-color-background-cardview);
}
.no-arrow :deep(.hnr-nav-bar__center) {
  margin-left: 0px !important;
}
.hnr-checkbox {
  -webkit-tap-highlight-color: transparent;
}

.activity-banner-container {
  justify-content: center;
  display: flex;
  width: 100%;
}

.activity-banner-container .content {
  margin-left: var(--hnr-default-padding-start);
  margin-right: var(--hnr-default-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-XXL);
  width: calc(100% - var(--hnr-default-padding-start) - var(--hnr-default-padding-end));
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-container-wide {
  width: 756px;
  height: 70px;
  overflow: hidden;
  position: relative;
  border-radius: var(--hnr-default-corner-radius-m);
}
.image-container-wide img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  width: 756px;
  height: 70px;
}

.image-container {
  width: 100%;
}

.image-container img {
  width: 100%;
  border-radius: var(--hnr-default-corner-radius-m);
}

.pb80 {
  padding-bottom: 90px;
}

.increase-box {
  padding: 0 12px;
  margin-top: var(--hnr-elements-margin-vertical-L2);
}
</style>
