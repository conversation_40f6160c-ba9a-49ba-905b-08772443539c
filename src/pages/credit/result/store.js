import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request } from "../../../helpers/utils";
import { report } from "../../../helpers/native-bridge";
import { creditNext } from "../../../helpers/next";
import { del, getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { getCreditInfo } from "../../../api/credit";

export default defineStore("credit/result", {
  state: () => ({
    // 页面初始数据
    applyInfo: {},
    creditInfo: {},
    suggestProducts: [],
    walletBanners: {},
    param: {},
    cpInfo: {},
    applyFlag: false, // 无网络状态true,有网false
    errorCode: null,
    changeType: null,
    oldApr: null,
    newApr: null,
    newDayRate: null,
    tempPriceValidDays: null,
    flowNo: null,
  }),
  actions: {
    async rebindFlowNo(reactive = 0) {
      const obj = getStore(`U_CREDIT_PROCESS_V2_${this.param.userId}`) || {};
      const res = await request(
        "/loan/api/bff/rebind",
        reactive === 1 ? {} : { flowNo: this.flowNo },
      );
      if (res.code === 0 && res.data.accessResult === 0) {
        // 清除缓存(U_CREDIT_PROCESS_V2_)中的字段（保留isFromSign、flowNo、FACE_CHECK和realInfo），并写入缓存接口的这三个字段supplier、supplierName和verifyList；
        const handleObj = {
          isFromSign: obj.isFromSign,
          flowNo: this.flowNo,
          FACE_CHECK: obj.FACE_CHECK,
          realInfo: obj.realInfo || "default",
          supplier: res.data.supplier,
          supplierName: res.data.supplierName,
          verifyList: res.data.verifyList,
          firstCp: res.data.firstCp,
        };
        if (reactive === 1) {
          handleObj.verifyList = ["AGREEMENT_CREDIT", "FACE_CHECK"];
          handleObj.FACE_CHECK = !!getStore(`U_CREDIT_FACECACHE_${this.param.userId}`);
        }
        setWithExactExpireUnit(`U_CREDIT_PROCESS_V2_${this.param.userId}`, handleObj, 7, "D");
        if (reactive === 1) {
          creditNext(this.param.userId, { fromPage: "activeResult" });
        } else {
          creditNext(this.param.userId, { fromPage: "creditResult" });
        }
      } else if (res.code === 1) {
        // 直接清除缓存
        del(`U_CREDIT_PROCESS_V2_${this.param.userId}`);
        creditNext(this.param.userId, { fromPage: "creditResult" });
      } else {
        if (reactive === 1) {
          showToast({
            message: res.message,
            position: "bottom",
          });
        }
        this.$patch({ applyInfo: { applyStatus: 3, refuseControlDays: 0 } });
      }
    },
    async initial() {
      // 授信申请结果查询
      if (this.param.applyNo === "undefined" || !this.param.applyNo) {
        return;
      }
      if (this.param.applyNo === "OLD_CUSTMER") {
        report("wallet_page_view", { page_name: "credit_apply_success_page" });

        this.$patch({
          applyInfo: {
            applyStatus: 2,
            remainLimit: parseInt(this.param.message, 10),
            apr: this.param.apr,
            dayRate: this.param.dayRate,
          },
        });

        await this.getCreditInfo();

        // 如果有临额
        if (this.creditInfo?.productInfos?.some((info) => !!info.tempApr)) {
          const filterTempAprSmallProducts = this.creditInfo?.productInfos?.filter(
            (info) => !!info.tempApr,
          );
          // 比较当前元素的 tempApr 和目前找到的最小元素的 tempApr
          const tempAprSmallProduct = filterTempAprSmallProducts.reduce((minItem, currentItem) => {
            return currentItem.tempApr < minItem.tempApr ? currentItem : minItem;
          }, filterTempAprSmallProducts[0]);

          this.changeType = 2;
          this.tempPriceValidDays = tempAprSmallProduct.tempPriceValidDays;
          this.oldApr = tempAprSmallProduct.apr;
          this.newApr = tempAprSmallProduct.tempApr;
          this.newDayRate = tempAprSmallProduct.tempDayRate;
        }
        return;
      }
      if (typeof window !== "undefined" && !window.navigator.onLine) {
        this.applyFlag = !window.navigator.onLine;
        if (this.applyFlag) {
          return;
        }
      }
      let data;
      if (this.param.applyNo && parseInt(this.param.applyStatus, 10) === 1 && this.param.supplier) {
        data = {
          code: 0,
          data: {
            applyNo: this.param.applyNo,
            applyStatus: parseInt(this.param.applyStatus, 10),
            supplier: this.param.supplier,
          },
        };
      } else {
        data = await request(
          "/loan/api/credit/queryApplyStatus",
          { applyNo: this.param.applyNo },
          { mock: false },
        );
      }
      if (data && data.code !== 0) {
        this.errorCode = data.code;
        return;
      }
      if (data && data.code === 0) {
        this.$patch({ applyInfo: data.data });
      } else {
        this.$patch({ applyInfo: { applyStatus: 3 } });
      }
      if (data && data.data && data.data.applyStatus === 2) {
        report("wallet_page_view", { page_name: "credit_apply_success_page" });
        await this.getCreditInfo();

        // 如果有临额
        if (this.creditInfo?.productInfos?.some((info) => !!info.tempApr)) {
          const filterTempAprSmallProducts = this.creditInfo?.productInfos?.filter(
            (info) => !!info.tempApr,
          );
          // 比较当前元素的 tempApr 和目前找到的最小元素的 tempApr
          const tempAprSmallProduct = filterTempAprSmallProducts.reduce((minItem, currentItem) => {
            return currentItem.tempApr < minItem.tempApr ? currentItem : minItem;
          }, filterTempAprSmallProducts[0]);

          this.changeType = 2;
          this.tempPriceValidDays = tempAprSmallProduct.tempPriceValidDays;
          this.oldApr = tempAprSmallProduct.apr;
          this.newApr = tempAprSmallProduct.tempApr;
          this.newDayRate = tempAprSmallProduct.tempDayRate;
        }
      }
      if (data && data.data && data.data.applyStatus === 3) {
        this.flowNo = data.data.flowNo;
        report("wallet_page_view", { page_name: "credit_apply_fail_page" });
      }
      // 如果applyStatus是1-审核中，要进一步判断supplier和verifyList是否存在，
      if (data && data.data && data.data.applyStatus === 1) {
        if (data.data.supplier) {
          report("wallet_page_view", { page_name: "credit_apply_fail_page" });
          this.flowNo = data.data.flowNo;
          this.rebindFlowNo(0);
        }
      }
    },
    async getAdActivity() {
      const data = await request("/loan/api/config/suggest/list", {});
      this.$patch({ suggestProducts: data.data });
    },
    async getCreditInfo() {
      // 获取用户授信额度信息
      const creditInfoRes = await getCreditInfo();
      if (creditInfoRes?.code !== 0) {
        return;
      }
      this.$patch({ creditInfo: creditInfoRes.data });
    },
  },
});
