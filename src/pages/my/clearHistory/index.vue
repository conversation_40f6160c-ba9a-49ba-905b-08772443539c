<script setup>
import { onServerPrefetch, ref, watchEffect, computed, onMounted, onUnmounted } from "vue";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import useStore from "./store";
import { initStore, request } from "../../../helpers/utils";
import { back, callPhone, regNativeEvent } from "../../../helpers/native-bridge";
import { getStore, setWithExactExpireUnit, del } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);
/*
eslint no-param-reassign: ["error", { "props": false }]
*/
const queryList = computed(() =>
  data.value?.queryList?.data?.filter((item) => {
    if (item.sendResult === 0) {
      item.sendResultLabel = "未发送";
    } else if (item.sendResult === 1) {
      item.sendResultLabel = "发送成功";
    } else {
      item.sendResultLabel = "发送失败";
    }
    item.createTimeLabel = store.timestampToTime(item.createTime);
    return item.sendResult === 1;
  }),
);
// 对象数组排序
function compare(property) {
  return function sortArray(a, b) {
    const value1 = a[property];
    const value2 = b[property];
    return value2 - value1; // 升序,降序为value2 - value1
  };
}
queryList.value?.sort(compare("createTime"));
const isLoading = ref(true);
// 顶部标题
const title = ref("申请记录");
// 是否是详情页
const isDetail = ref(false);
// 是否有申请结清证明的的历史记录
const hasRecord = computed(() => queryList.value?.length > 0);
// svg路径
const svgPath = ref();

// 详情发送状态label
const sendResultLabel = ref("");
// 详情记录
const settlementDetail = ref({});
// 申请结清证明的的历史记录list
const applyOrderList = ref([]);
const companyInfo = ref(null); // cp信息

// 适应折叠屏切换屏幕不刷新
function getStorageIsDetail() {
  return getStore(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`) || "";
}
// 详情接口
async function requestDetail(params, isFoldingScreen = false) {
  if (isFoldingScreen) {
    isLoading.value = false;
  } else {
    isLoading.value = true;
  }
  const res = await request(
    "/loan/api/settlement/detail",
    {
      id: params,
    },
    {
      mock: false,
    },
  );
  if (res.code === 0 && res.data !== "{}") {
    settlementDetail.value = res.data;
    if (settlementDetail.value.sendResult === 0) {
      sendResultLabel.value = "未发送";
    } else if (settlementDetail.value.sendResult === 1) {
      sendResultLabel.value = "发送成功";
    } else {
      sendResultLabel.value = "发送失败";
    }
    applyOrderList.value = res.data?.orders;
    isLoading.value = false;
  } else {
    isLoading.value = false;
    showToast({ message: res.message });
  }
}
// 点击详情>
async function gotoDetail(params) {
  setWithExactExpireUnit(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`, params, 7, "D");
  window.location.hash = "isDetail";
  isDetail.value = true;
  requestDetail(params);
}

// 判断回哪个页面
function onClickLeft() {
  if (isDetail.value) {
    isDetail.value = false;
    window.history.back();
    setWithExactExpireUnit(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`, "", 7, "D");
    del(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`);
    settlementDetail.value = {};
    applyOrderList.value = [];
  } else {
    back();
  }
}
// 判断显示哪种标题
watchEffect(() => {
  title.value = isDetail.value ? "申请记录详情" : "申请记录";
});
// 调用拨号功能
function call(phoneNumber) {
  callPhone(phoneNumber);
}
regNativeEvent("onBack", () => {
  back();
});
onMounted(async () => {
  companyInfo.value = await getCurrentCpInfo();
  const isDarkTheme = window.matchMedia("(prefers-color-scheme: dark)"); // 是深色
  if (isDarkTheme.matches) {
    // 是深色
    svgPath.value = "../../../../public/my/detail-logo-deep.svg";
  } else {
    // 不是深色
    svgPath.value = "../../../../public/my/detail-logo.svg";
  }
  const storageIsDetail = await getStorageIsDetail();
  window.addEventListener("hashchange", () => {
    isDetail.value = !!window.location.hash;
    if (!window.location.hash) {
      setWithExactExpireUnit(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`, "", 7, "D");
      del(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`);
    }
  });
  setTimeout(() => {
    if (!window.location.hash && storageIsDetail) {
      window.location.hash = "isDetail";
    }
  }, 0);

  if (storageIsDetail) {
    isDetail.value = true;
    hasRecord.value = true;
    requestDetail(storageIsDetail, true);
  } else {
    isDetail.value = false;
    hasRecord.value = true;
    isLoading.value = false;
  }
});
onUnmounted(() => {
  del(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`);
});
</script>

<template>
  <hnr-loading
    v-if="isLoading"
    style="
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      vertical-align: middle;
      justify-content: center;
    "
  >
    加载中...
  </hnr-loading>
  <div v-else class="main">
    <div class="area">
      <div class="header">
        <div class="header-title">
          <hnr-nav-bar :title="title" transparent="true" class="nav-padding-top">
            <template #left>
              <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
            </template>
          </hnr-nav-bar>
        </div>
      </div>
      <div v-if="!isDetail && hasRecord" class="content content-hasRecord">
        <div
          v-for="item in queryList"
          :key="item.id"
          class="list-item"
          @click="gotoDetail(item.id)"
        >
          <div class="item-left">
            <div class="item-left-title">申请开具结清证明</div>
            <div class="item-left-time">
              {{ item.createTimeLabel }}
            </div>
          </div>
          <div class="item-right">
            <span class="item-right-result">申请成功</span>
            <!-- <img src="../../../../public/my/right.svg" alt="" class="right-svg"> -->
            <span class="icon-right right-svg"></span>
          </div>
        </div>
      </div>
      <div v-if="isDetail && hasRecord" class="content content-isDetail">
        <div class="content-top">
          <div class="logo">
            <!-- <span class="icon-detail-logo"></span>                      -->
            <img src="/public/my/clearoff.svg" alt="" />
          </div>
          <div class="title-text">结清证明</div>
          <div class="title-tips">
            <!-- {{ sendResultLabel }} -->
            申请成功
          </div>
        </div>
        <hnr-divider line class="divider" />
        <div class="item apply-time">
          <div class="label apply-time-label">申请时间</div>
          <div class="value apply-time-value">
            {{ store.timestampToTime(settlementDetail.createTime) }}
          </div>
        </div>
        <div class="item apply-order">
          <div class="label apply-order-label">
            <span class="apply-order-label-top">申请借款订单</span>
            (共{{ applyOrderList?.length || 0 }}笔)
          </div>
          <div class="value apply-order-list">
            <div v-for="item in applyOrderList" :key="item.applyNo" class="apply-order-list-item">
              {{
                item.applyDate
                  ? store.dateFormat(item.applyDate)
                  : store.timestampToTime(item.clearTime, 1)
              }}&nbsp;借款￥{{ store.addCommas(item.loanAmount) }}
            </div>
          </div>
        </div>
        <div class="item email">
          <div class="label email-adress-label">证明接收邮箱</div>
          <div class="value email-adress-value">
            {{ settlementDetail.mailAddr }}
          </div>
        </div>
        <div class="item other">
          <div class="label other-label">其他</div>
          <div class="value other-value">
            {{ `如果没有收到邮件或对结清证明有疑问,请联系${companyInfo?.supplierName}客服` }}
            <a class="other-tel" @click="call(`${companyInfo?.supplierContact}`)">{{
              companyInfo?.supplierContact
            }}</a>
          </div>
        </div>
      </div>
      <div v-if="!hasRecord" class="content no-record">
        <div class="no-record-log">
          <!-- <img src="../../../../public/my/norecord.svg" alt=""> -->
          <span class="icon-norecord"></span>
        </div>

        <div class="no-record-tips">
          <span class="tips-one">暂无申请记录</span>
        </div>
      </div>
    </div>
  </div>
</template>
<style>
:root {
  --dp: 1px;
}
</style>

<style scoped>
.main {
  user-select: none;
}
.area {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  top: 0.5rem;
}
.content {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-bottom: 1rem;
  flex: 1;
  overflow: auto;
}

.header {
  width: auto;
  box-sizing: border-box;
}
.content-hasRecord {
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.content-isDetail {
  margin-top: var(--hnr-elements-margin-vertical-L2);
}
.list-item {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  background-color: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-default-corner-radius-l);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
}
.item-left {
  height: calc(64 * var(--dp));
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
}
.item-left-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: calc(5 * var(--dp));
}
.item-left-time {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.item-right {
  flex: 1;
  line-height: calc(64 * var(--dp));
  text-align: right;
}
.item-right-result {
  display: inline-block;
  height: calc(24 * var(--dp));
  line-height: calc(24 * var(--dp));
  color: var(--hnr-text-color-secondary);
  font-size: calc(var(--hnr-body-2) * var(--hnr-large-rate));
  font-weight: var(--hnr-font-weight-regular);
}
.right-svg {
  display: inline-block;
  width: calc(12 * var(--dp));
  height: calc(24 * var(--dp));
  transform: translate(0, calc(5 * var(--dp)));
  text-align: right;
}

.icon-right:before {
  content: "\e928";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-quaternary);
}
.item {
  display: flex;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.content-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.logo {
  display: block;
  height: calc(56 * var(--dp));
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}

.title-text {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-S);
}
.title-tips {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-subtitle-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
}
.divider {
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
}
.label {
  width: 28%;
  margin-right: var(--hnr-elements-margin-horizontal-L);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.value {
  width: 65%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.apply-order-list-item {
  margin-bottom: var(--hnr-elements-margin-vertical-M);
}
.apply-order-label-top {
  display: block;
  width: calc(84 * var(--dp));
}
.other-tel {
  color: var(--hnr-text-color-primary-activated);
  font-size: var(--hnr-button-2);
  font-weight: var(--hnr-font-weight-medium);
  text-decoration: none;
}
.no-record {
  width: 100%;
  height: calc(100vh - 68 * var(--dp) - 0.5rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.no-record-log {
  width: calc(72 * var(--dp));
  margin-bottom: var(--hnr-elements-margin-vertical-M);
}
.no-record-tips {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.icon-detail-logo:before {
  content: "\e949";
  font-family: "icomoon";
  font-size: calc(56 * var(--dp));
  color: var(--hnr-color-tertiary);
}
.icon-norecord:before {
  content: "\e955";
  font-family: "icomoon";
  font-size: var(--dp72);
  color: var(--hnr-color-quaternary);
}
.tips-one {
  color: var(--hnr-text-color-secondary);
}
</style>
