<script setup>
import {
  onServerPrefetch,
  computed,
  ref,
  onMounted,
  watchEffect,
  onUnmounted,
  nextTick,
} from "vue";
import { showDialog } from "@hihonor/hnr/dist/hnr.es.min";
import useStore from "./store";
import { initStore, request } from "../../../helpers/utils";
import {
  report,
  back,
  goto,
  hideLoading,
  regNativeEvent,
  callPhone,
} from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import allClickPage from "./components/allClickPage.vue";
import moreClickPage from "./components/moreClickPage.vue";
import singleClickPage from "./components/singleClickPage.vue";
import cardCell from "./components/cardCell.vue";
import closeFilled from "/my/icsvg_public_close_filled.svg";

import { getStore, setWithExactExpireUnit, del } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);
hideLoading();
const isClearOff = ref(false);
// 已经结清记录总数
const totalNum = computed(() => data.value?.loanInfo?.data?.totalNum);
// 已经结清记录list
/*
eslint no-param-reassign: ["error", { "props": false }]
*/
const loanList = computed(() =>
  data.value?.loanInfo?.data?.records?.map((item) => {
    if (item.applyDate) {
      item.applyDateLabel = store.dateFormat(item.applyDate);
    }
    if (item.loanAmount) {
      item.loanAmountLabel = store.addCommas(item.loanAmount);
    }
    return item;
  }),
);
// 存放缓存的页面ID
const localPageId = ref("");
// 邮箱地址
const emailAddress = ref("");
// 多选list
const loanListChecked = ref([]);
// 开具证明后loading
const showLoading = ref(false);
// 开具失败后对话框
const showFailDialog = ref(false);
// 开具失败后对话框文本提示
const showFailDialogText = ref("");
// 开具失败后对话框按钮文本提示
const btnText = ref("重试");
// 是否有网
const isOnLine = ref(true);
// DXM借款订单号
const outOrderNo = computed(() => Array.from(new Set(loanListChecked.value)));
// 开具按钮disabled
const disabledBtn = ref(true);
const isLoading = ref(true);
const successPage = ref(false); // 成功页
const allPageComponent = ref(null);
const morePageComponent = ref(null);
const singlePageComponent = ref(null);
const allCardCellInfo = ref({
  title: "全部借款结清证明",
  content: "",
});
const singleCardCellInfo = ref({
  title: "单笔借款结清证明",
  content: "",
});
const singleLoanListChecked = (param) => {
  loanListChecked.value = param;
};
const moreLoanListChecked = (param) => {
  loanListChecked.value = param;
};

const companyInfo = ref(null);
// 邮箱和订单号校验
function IsEmail(str) {
  const reg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/;
  return reg.test(str);
}
function rulesValidateField(value) {
  let flag;
  const reg = IsEmail(value);
  if (outOrderNo.value.length === 0) {
    showDialog({ message: "请选择订单号" });
    return "";
  }
  if (!reg) {
    // Toast("请输入正确的邮箱")
    showDialog({ message: "请输入正确的邮箱" });
    flag = false;
  } else {
    disabledBtn.value = false;
    flag = true;
  }
  return flag;
}
function doreport(params = {}) {
  setTimeout(() => {
    request("/loan/api/user/operLog", params, {
      mock: false,
      isHideOffline: true,
    });
  }, 0);
}
// 邮箱输入校验
const codeChange = (val) => {
  const reg = IsEmail(val);
  if (reg) {
    data.value.codeErrorMessage = "";
  } else {
    data.value.codeErrorMessage = "请输入正确的邮箱";
  }
};

const setLocalMethod = (type) => {
  setWithExactExpireUnit(`U_CLEAROFF_${data.value.param.userId}`, type, 7, "D");
};
const getLocalMethod = () => {
  return getStore(`U_CLEAROFF_${data.value.param.userId}`) || "";
};
const setLocalPageId = (type) => {
  setWithExactExpireUnit(`U_CLEAROFF_PAGE_ID_${data.value.param.userId}`, type, 7, "D");
};
const getLocalPageId = () => {
  return getStore(`U_CLEAROFF_PAGE_ID_${data.value.param.userId}`) || "";
};

const outOrderNoMethod = (array) => {
  const arr = [];
  if (array && array?.length) {
    for (let index = 0; index < array.length; index += 1) {
      const element = array[index];
      arr.push(element.outOrderNo);
    }
  }
  return arr;
};

// 点击开具证明
async function openDocument() {
  if (companyInfo.value.supplierId === 1 && loanListChecked.value.length >= 16) {
    showDialog({ message: "最多只能选择15笔结清订单" });
    return "";
  }
  codeChange();
  const flag = rulesValidateField(emailAddress.value);
  if (flag) {
    showFailDialog.value = false;
    showLoading.value = true;
    if (window?.navigator?.onLine) {
      isOnLine.value = true;
    } else {
      isOnLine.value = false;
      showFailDialogText.value = "网络未连接，请检查网络。";
      btnText.value = "设置网络";
      showLoading.value = false;
      showFailDialog.value = true;
      return "";
    }
    const res = await request(
      "/loan/api/settlement/send",
      {
        encryptedParams: {
          outOrderNos: outOrderNoMethod(outOrderNo.value),
          email: emailAddress.value,
        },
      },
      {
        mock: false,
        isHideOffline: true,
      },
    );
    const params = {
      orderNo: outOrderNoMethod(outOrderNo.value).toString(),
      operType: 310,
      operResult: 1,
      operParams: "",
      supplier: data.value.supplierId,
      operTime: new Date().getTime(),
    };
    if (res.code === 0) {
      if (res.data?.sendStatus) {
        doreport(params);
        showLoading.value = false;
        showFailDialog.value = false;
        // 跳转到成功页面
        isClearOff.value = false;
        successPage.value = true;
        await setLocalPageId(3);
        localPageId.value = await getLocalPageId();
        setWithExactExpireUnit(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`, true, 7, "D");
      } else {
        params.operResult = 2;
        doreport(params);
        btnText.value = "重试";
        showFailDialogText.value =
          res.data?.failReason || res?.message || "无法连接服务器，请重试 。";
        showLoading.value = false;
        showFailDialog.value = true;
      }
    } else {
      params.operResult = 2;
      doreport(params);
      btnText.value = "重试";
      showFailDialogText.value =
        res.data?.failReason || res?.message || "无法连接服务器，请重试 。";
      showLoading.value = false;
      showFailDialog.value = true;
    }
  }
  return "";
}

// 跳转到申请结清记录历史页面
function applyRecord() {
  goto("/wallet-loan-web/pages/my/clearHistory");
}
// 回退
function onClickLeft() {
  back();
}
// 回退
function onClickLeftFirstPage() {
  del(`U_CLEAROFF_PAGE_ID_${data.value.param.userId}`);
  back();
}
// 回退
const onClickLeftSuccessPage = async () => {
  isClearOff.value = true;
  successPage.value = false;
  del(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`);
  await setLocalPageId(2);
  localPageId.value = getLocalPageId();
};
// 按钮是否高亮监听
watchEffect(() => {
  if (loanListChecked.value?.length === 0) {
    disabledBtn.value = true;
  } else {
    disabledBtn.value = false;
  }
});
// 点击完成
function finish() {
  setWithExactExpireUnit(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`, false, 7, "D");
  del(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`);
  goto("/wallet-loan-web/pages/my/clearHistory");
}
// 设置网络
function updateNetwork() {
  showFailDialog.value = false;
  setNetwork();
}

regNativeEvent("onNetChange", (res) => {
  const netStatus = ["ETHERNET", "WIFI", "2G", "3G", "4G", "5G", "UNKNOW"];
  if (netStatus.includes(res.result)) {
    isOnLine.value = true;
    showLoading.value = false;
  }
});
const isBottomPaddingTop = ref(false);

async function fieldFocus() {
  isBottomPaddingTop.value = true;
  await nextTick();
  document.activeElement.scrollIntoView({ behavior: "smooth" });
}
function fieldBlur() {
  isBottomPaddingTop.value = false;
}

const holdClickState = ref(""); // 保存点击的响应式状态
const show = ref(false);

onMounted(async () => {
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_clearoff_page",
      pageStatus: "0",
    });
  });
  companyInfo.value = await getCurrentCpInfo();
  singleCardCellInfo.value.content = companyInfo?.value?.singleSettlementCertificateText;
  allCardCellInfo.value.content = companyInfo?.value?.allSettlementCertificateText;
  del(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`);
  holdClickState.value = (await getLocalMethod()) || ""; // 折叠屏 折叠时查找点击的按钮信息（单笔/全部）

  localPageId.value = await getLocalPageId();
  if (!localPageId.value) {
    await setLocalPageId(1);
  }
  localPageId.value = await getLocalPageId();
  isLoading.value = false;
});
onUnmounted(() => {
  del(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`);
});
const callSupplierContact = () => {
  callPhone(data.value.supplierContact);
};

const allClickMethod = async () => {
  // 点击全选没有数据可开时不可下一步。
  if (loanList.value?.length === 0) {
    show.value = true;
    return;
  }
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_clearoff_page",
      pageStatus: "1",
    });
  });
  loanListChecked.value = loanList.value; // 清空 赋值
  await setLocalMethod(2); // 点击全选按钮时存2
  holdClickState.value = await getLocalMethod();
  await setLocalPageId(2);
  localPageId.value = await getLocalPageId();
};
const singleClickMethod = async () => {
  if (loanList.value?.length === 0) {
    show.value = true;
    return;
  }
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_clearoff_page",
      pageStatus: "2",
    });
  });
  loanListChecked.value = []; // 清空
  await setLocalMethod(1); // // 点击单选按钮时存1
  holdClickState.value = getLocalMethod();
  await setLocalPageId(2);
  localPageId.value = await getLocalPageId();
};
const onClickLeftCloseEntrancePageFlag = async () => {
  del(`U_CLEAROFF_${data.value.param.userId}`); // 清掉点击信息
  await setLocalPageId(1);
  localPageId.value = getLocalPageId();
};

regNativeEvent("onBack", async () => {
  if (localPageId.value === 1) {
    del(`U_CLEAROFF_PAGE_ID_${data.value.param.userId}`);
    back();
  } else if (localPageId.value === 2) {
    await setLocalPageId(1);
    localPageId.value = await getLocalPageId();
    del(`U_CLEAROFF_${data.value.param.userId}`); // 清掉点击信息
  } else if (localPageId.value === 3) {
    await setLocalPageId(2);
    localPageId.value = await getLocalPageId();
  }
});
const clickAllPage = computed(() => holdClickState.value === 2); // 展示点击全选的页面
const clickMorePage = computed(
  () => holdClickState.value === 1 && companyInfo.value?.issueSettlementMethod.charAt(0) === "0",
); // 展示点击多选的页面
const clickSinglePage = computed(
  () => holdClickState.value === 1 && companyInfo.value?.issueSettlementMethod.charAt(0) === "1",
); // 展示点击单选的页面
</script>

<template>
  <hnr-loading
    v-if="isLoading"
    style="
      width: 100vw;
      height: 100vh;
      display: flex;
      align-items: center;
      vertical-align: middle;
      justify-content: center;
    "
  >
    加载中...
  </hnr-loading>
  <div v-else>
    <!--可开具结清证明页(度小满)-->
    <template v-if="data.supportSettlementCertificate === 1">
      <template v-if="localPageId === 1">
        <div style="display: flex; flex: 1; justify-content: space-between">
          <hnr-nav-bar transparent="true" title="开具结清证明" class="nav-padding-top">
            <template #left>
              <icsvg-public-back-filled @click="onClickLeftFirstPage"></icsvg-public-back-filled>
            </template>
          </hnr-nav-bar>
          <div class="header-application-record-btn" style="margin-right: 12px">
            <hnr-button type="text" style="margin-top: 32px" @click="applyRecord"
              >申请记录</hnr-button
            >
          </div>
        </div>
        <div class="introduce">
          <div
            style="
              font-weight: 500;
              font-size: 16px;
              color: var(--hnr-text-color-primary);
              margin-left: 24px;
              margin-top: 12px;
            "
          >
            选择开具证明的内容
          </div>
          <div
            style="
              font-weight: 400;
              font-size: 14px;
              margin-top: 3px;
              margin-left: 24px;
              margin-bottom: 12px;
              color: var(--hnr-text-color-secondary);
            "
          >
            {{ companyInfo?.settlementTimeExplanation }}
          </div>
        </div>
        <div style="margin-bottom: var(--hnr-elements-margin-vertical-M2)">
          <card-cell :info="allCardCellInfo">
            <template #right>
              <hnr-button
                style="width: 66px; padding: 0 12px"
                type="primary"
                size="mini"
                @click="allClickMethod"
                >去开具</hnr-button
              >
            </template>
          </card-cell>
          <card-cell :info="singleCardCellInfo">
            <template #right>
              <hnr-button
                style="width: 66px; padding: 0 12px"
                type="primary"
                size="mini"
                @click="singleClickMethod"
                >去开具</hnr-button
              >
            </template>
          </card-cell>
          <hnr-dialog
            v-model:show="show"
            class="unAccess"
            style="width: 328px"
            :message="companyInfo?.unableIssueSettlementText"
            button-direction="column"
          >
            <template #footer>
              <hnr-button
                style="width: 100%; height: 44px; background-color: var(--hnr-color-button-normal)"
                type="text"
                class="custom-button"
                @click="show = !show"
                >知道了</hnr-button
              >
            </template>
          </hnr-dialog>
        </div>
      </template>
      <template v-if="localPageId === 2">
        <div class="main">
          <div class="area">
            <div class="header">
              <div class="header-title">
                <hnr-nav-bar
                  transparent="true"
                  :title="holdClickState === 2 ? '全部借款结清证明' : '单笔借款结清证明'"
                  class="nav-padding-top"
                >
                  <template #left>
                    <icsvg-public-back-filled
                      @click="onClickLeftCloseEntrancePageFlag"
                    ></icsvg-public-back-filled>
                  </template>
                </hnr-nav-bar>
              </div>
            </div>
            <div class="emaill">
              <p
                class="emaill-tips"
                style="color: var(--hnr-text-color-secondary); font-size: 14px; font-weight: 500"
              >
                证明接收邮箱
              </p>
            </div>
            <hnr-card class="emailAddress">
              <div style="display: flex; align-items: center; width: 100%">
                <span
                  style="
                    position: relative;
                    left: 12px;
                    margin-bottom: 1px;
                    flex: 1;
                    color: var(--hnr-text-color-primary);
                  "
                  >邮箱地址</span
                >
                <span style="flex: 3"
                  ><hnr-field
                    v-model="emailAddress"
                    placeholder="请输入邮箱地址"
                    class="emaill-address"
                    border
                    clearable
                    :clear-icon="closeFilled"
                    clear-trigger="focus"
                    @focus="fieldFocus"
                    @blur="fieldBlur"
                  >
                  </hnr-field>
                </span>
              </div>
            </hnr-card>
            <div class="placeholder"></div>
            <all-click-page
              v-if="clickAllPage"
              ref="allPageComponent"
              :total-num="totalNum"
              :company-info="companyInfo"
              :loan-list="loanList"
            ></all-click-page>
            <more-click-page
              v-if="clickMorePage"
              ref="morePageComponent"
              :total-num="totalNum"
              :loan-list="loanList"
              :company-info="companyInfo"
              @more="moreLoanListChecked"
            ></more-click-page>
            <single-click-page
              v-if="clickSinglePage"
              ref="singlePageComponent"
              :company-info="companyInfo"
              :loan-list="loanList"
              @single="singleLoanListChecked"
            ></single-click-page>
            <div v-if="showLoading" class="show-loading">
              <hnr-dialog
                v-model:show="showLoading"
                title=""
                message=""
                button-direction="row"
                :narrow-padding="true"
              >
                <template #footer>
                  <div class="loading">
                    <div class="loading-text">正在开具结清证明</div>
                    <div class="loading-logo">
                      <hnr-loading color="#256fff" />
                    </div>
                  </div>
                </template>
              </hnr-dialog>
            </div>
            <div v-if="showFailDialog" class="fail-dialog">
              <hnr-dialog
                v-model:show="showFailDialog"
                title="开具失败"
                :message="showFailDialogText"
                button-direction="row"
                :narrow-padding="true"
              >
                <template #footer>
                  <div class="showFailDialog-bottom">
                    <hnr-button
                      type="default"
                      text-color="var(--hnr-color-accent)"
                      class="bottom-top"
                      style="min-width: calc(50% - var(--dp6))"
                      @click="showFailDialog = !showFailDialog"
                      >取消</hnr-button
                    >
                    <hnr-button
                      v-if="isOnLine"
                      type="primary"
                      style="min-width: calc(50% - var(--dp6))"
                      @click="openDocument"
                      >{{ btnText }}</hnr-button
                    >
                    <hnr-button
                      v-if="!isOnLine"
                      type="primary"
                      style="min-width: calc(50% - var(--dp6))"
                      @click="updateNetwork"
                      >{{ btnText }}</hnr-button
                    >
                  </div>
                </template>
              </hnr-dialog>
            </div>
          </div>
        </div>
        <div class="bottom">
          <hnr-row type="gird">
            <hnr-col span="12">
              <hnr-button
                type="primary"
                class="main-button"
                :disabled="disabledBtn || !emailAddress"
                @click="openDocument"
                >开具证明</hnr-button
              >
            </hnr-col>
          </hnr-row>
          <div class="bottom-tips">
            <div class="bottom-tips-text">若因邮箱填写错误导致个人信息泄密</div>
            <div class="bottom-tips-text">荣耀不承担法律责任</div>
          </div>
        </div>
      </template>
      <template v-if="localPageId === 3">
        <hnr-nav-bar title="开具结清证明" class="nav-padding-top" transparent="true">
          <template #left>
            <icsvg-public-back-filled @click="onClickLeftSuccessPage"></icsvg-public-back-filled>
          </template>
        </hnr-nav-bar>
        <div class="content-success">
          <div class="logo-success">
            <img src="../../../../public/my/success.svg" alt="" />
          </div>
          <div class="success-title">申请成功</div>
          <div class="tips-success">
            <span class="tips-one"
              >结清证明将于一周内发送到您的邮箱，具体发送时间以出资方为准。请注意查收</span
            >
          </div>
          <div class="bottom-success">
            <hnr-button type="primary" class="bottom-success-main-button" @click="finish"
              >完成</hnr-button
            >
          </div>
        </div>
      </template>
    </template>
    <!--不可开具结清证明页(京东)-->
    <template v-if="data.supportSettlementCertificate === 0">
      <div class="main">
        <div class="area">
          <div class="header">
            <div class="header-title">
              <hnr-nav-bar title="开具结清证明" transparent="true" class="nav-padding-top">
                <template #left>
                  <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
                </template>
              </hnr-nav-bar>
            </div>
          </div>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              flex: 1;
              overflow: auto;
              margin-top: var(--hnr-elements-margin-vertical-M2);
            "
          >
            <span class="jd-icon"></span>
            <span class="content-line1"> 荣耀借钱暂无法直接为您提供开具结清证明服务，</span>
            <span class="content-line2">
              如需开具结清证明，请咨询{{ data.supplierName }}客服<span
                class="call-supplier-contact"
                @click="callSupplierContact"
                >{{ data.supplierContact }}</span
              ></span
            >
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style>
:root {
  --dp: 1px;
}
</style>

<style scoped>
.main {
  display: flex;
  height: calc(100vh - 112 * var(--dp));
  user-select: none;
}

.area {
  /* width: calc(100% - 1rem); */
  overflow: auto;
  display: flex;
  /* flex: 1; */
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  /* position: relative;
  top: 0.5rem;
  left: 0.5rem;
  right: 0.5rem; */
  margin-bottom: calc(20 * var(--dp));
}

.header {
  width: 100%;
  box-sizing: border-box;
  /* padding-right: var(--hnr-default-padding-end);
  padding-left: var(--hnr-default-padding-start); */
  display: flex;
}

.header-title {
  flex: 1;
}

.content {
  flex: 1;
  overflow: auto;
}

.jd-icon:before {
  content: "\e949";
  font-family: "icomoon";
  font-size: var(--dp72);
  color: var(--hnr-color-tertiary);
}

.content-line1 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}

.content-line2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.call-supplier-contact {
  font-size: 14px;
  color: #256fff;
  text-align: center;
  line-height: 19px;
  font-weight: 500;
}

::v-deep(.hnr-nav-bar__content) {
  /* margin: 0; */
}

::v-deep(.hnr-nav-bar) {
  background-color: transparent;
}

::v-deep(.hnr-nav-bar .hnr-icon) {
  /* padding: 0; */
}

.header-application-record-btn {
  display: flex;
}

.header-application-record-btn .hnr-button {
  min-width: 0 !important;
}

.header-application-record-btn ::v-deep(.hnr-button__text) {
  color: var(--hnr-text-color-activated);
  font-size: var(--hnr-button-2);
  font-weight: var(--hnr-font-weight-medium);
}

.content-text {
  box-sizing: border-box;
  padding-right: 25px;
  padding-left: 25px;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}

.content-text-title {
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-medium);
}

.content-text-tips {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.all-choose-label {
  height: calc(48 * var(--dp));
  display: flex;
  align-items: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
}

::v-deep(.hnr-checkbox__label) {
  margin-left: var(--hnr-elements-margin-horizontal-L);
  width: 200%;
}

.loan-list {
  width: 100%;
}

.loan-list-item {
  width: 100%;
  height: calc(64 * var(--dp));
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
}

.loan-list-item-time {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.loan-list-item-limit {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
}

.emaill-tips {
  width: 100%;
  box-sizing: border-box;
  padding: 0 25px;
  color: var(--hnr-index-anchor-title-color);
  font-size: calc(var(--hnr-index-anchor-title-font-size) * var(--hnr-large-rate));
  font-weight: var(--hnr-index-anchor-title-font-weight);
}

.emailAddress {
  height: calc(56 * var(--dp));
}

.placeholder {
  width: 100%;
  height: calc(12 * var(--dp));
}

.emaill-text {
  width: 100%;
  height: calc(56 * var(--dp));
  box-sizing: border-box;
  padding: calc(10 * var(--dp));
}

.emaill-text input {
  width: 98%;
  height: calc(56 * var(--dp));
  border-radius: var(--hnr-default-corner-radius-m);
  /* border: none; */
  text-overflow: ellipsis;
  background: transparent;
}

.emailAddress ::v-deep(.hnr-card__content--default) {
  padding: 0;
}

.emailAddress ::v-deep(.hnr-field__input-area) {
  background-color: var(--hnr-card-background);
}

.emailAddress ::v-deep(.hnr-field__error-message) {
  width: 100%;
  margin-top: var(--dp5);
  padding: 0 var(--dp16);
}

::v-deep(.hnr-field--withMargin) {
  width: 100%;
  margin: 0;
}

.bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  user-select: none;
}

.bottom-tips {
  margin-bottom: var(--hnr-default-padding-bottom-fixed);
}

.main-button {
  width: 50%;
  height: calc(36 * var(--dp));
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}

.bottom-tips-text {
  text-align: center;
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  padding-top: var(--hnr-elements-margin-vertical-L2);
  padding-bottom: var(--hnr-elements-margin-vertical-L2);
}

.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

::v-deep(.hnr-dialog__content--narrow-padding) {
  padding: var(--hnr-dialog-header-padding);
  padding-top: 0;
  padding-bottom: 0;
}

.emaill-address /deep/.hnr-field__input-area:before {
  height: 0;
}

.fail-dialog /deep/.hnr-dialog {
  width: 100% !important;
}

.fail-dialog /deep/.hnr-popup--center {
  top: calc(100vh - 90 * var(--dp));
}

.show-loading /deep/.hnr-dialog {
  width: 100% !important;
}

.show-loading /deep/.hnr-popup--center {
  top: calc(100vh - 80 * var(--dp));
}

::v-deep(.hnr-nav-bar__center) {
  width: calc(100% - 12px - 40px) !important;
  margin: 0 12px 0 40px !important;
}

.loan /deep/.hnr-card__content {
  padding: 0 var(--dp16);
}

::v-deep(.hnr-checkbox:active) {
  background-color: transparent !important;
}

::v-deep(.hnr-checkbox) {
  cursor: none;
}

.hnr-checkbox {
  -webkit-tap-highlight-color: transparent;
}

.area-success {
  position: fixed;
  width: calc(100% - 36px);
  margin: auto;
  top: 88px;
  left: 18px;
  right: 18px;
}

.content-success {
  position: relative;
  top: var(--hnr-elements-margin-vertical-XXL);
  width: calc(100% - var(--hnr-max-padding-start) * 2);
  margin: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.header-success {
  width: auto;
  box-sizing: border-box;
  margin-bottom: var(--hnr-elements-margin-vertical-XXL);
}

.logo-success {
  width: calc(64 * var(--dp));
  height: calc(64 * var(--dp));
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
}

.success-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
}

.tips-one,
.tips-two {
  width: 100%;
  display: block;
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.bottom-success {
  width: calc(100% - 2rem);
  position: fixed;
  bottom: var(--hnr-default-padding-bottom-fixed);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  user-select: none;
}

.bottom-success-main-button {
  width: 40%;
  height: calc(40 * var(--dp));
}

::v-deep(.hnr-icon) {
  padding: 0;
}

::v-deep(.hnr-nav-bar__center) {
  width: calc(100% - 12px - 40px) !important;
  margin: 0 12px 0 40px !important;
}

::v-deep(.hnr-nav-bar__content) {
  /* margin: 0; */
}

.bottom-top {
  background-color: var(--hnr-color-toast-background);
}

.showFailDialog-bottom {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.content-bottom {
  padding-bottom: 160px;
}
.boldFont {
  font-size: 16px;
  text-align: left;
  line-height: 21px;
  font-weight: 500;
  color: var(--hnr-text-color-primary);
}
.noBoldFont {
  font-size: 14px;
  text-align: left;
  line-height: 19px;
  font-weight: 400;
  color: var(--hnr-text-color-secondary);
}
/* .hnr-button {
  width: 100% !important;
  background-color: red;
} */
.emailAddress {
  width: calc(100% - 24px) !important;
  margin: 0 auto !important;
}
::v-deep(.hnr-field__body--border .hnr-field__input-area) {
  padding: 0 var(--hnr-elements-margin-horizontal-M2) !important;
}
::v-deep(.hnr-field__right-icon) {
  width: var(--dp20) !important;
  height: var(--dp20) !important;
}
::v-deep(.emaill-address .hnr-icon) {
  width: var(--dp20) !important;
  height: var(--dp20) !important;
}
::v-deep(.hnr-field__right-icon .hnr-icon) {
  font-size: var(--dp20) !important;
}
::v-deep(.hnr-dialog__footer) {
  padding: var(--dp10) var(--dp20) var(--dp20) !important;
}
::v-deep(.unAccess .hnr-dialog__message) {
  text-align: center;
}
::v-deep(.unAccess .hnr-dialog__footer) {
  padding: var(--dp10) var(--dp32) var(--dp20) !important;
}
::v-deep(.unAccess .hnr-dialog__content) {
  padding: var(--hnr-dialog-content-padding) !important;
}
</style>
