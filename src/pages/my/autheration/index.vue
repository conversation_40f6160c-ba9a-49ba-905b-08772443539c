<script setup>
import { onServerPrefetch, ref, onMounted } from "vue";
import useStore from "./store";
import { initStore } from "../../../helpers/utils";
import { agreement, getHostByOrigin, getThemeNameByMediaQuery } from "../../../helpers/signUtils";
import { back, report } from "../../../helpers/native-bridge";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);
const host = ref("");
const themeName = ref("");
const onClickLeft = () => {
  back();
};

onMounted(() => {
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_autheration_page",
      creditStatus: data.value.creditStatus,
    });
  });
  setInterval(() => {
    store.initial();
  }, 540000);
  host.value = getHostByOrigin(window.location.origin);
  themeName.value = getThemeNameByMediaQuery(window.matchMedia("(prefers-color-scheme: dark)"));
});
</script>

<template>
  <div class="main">
    <hnr-nav-bar title="协议与授权" transparent="true" class="nav-padding-top">
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div class="list">
      <hnr-sub-title>
        <hnr-index-anchor subtitle="借钱服务" />
      </hnr-sub-title>
      <hnr-cell-group inset class="basic">
        <div
          class="cell-item"
          @click="
            agreement(
              `https://agreement${host}.honor.com/asm/agrFile/getHtmlFile?agrNo=1493&country=cn&branchId=0&langCode=zh-cn&${themeName}`,
              'forceDark',
            )
          "
        >
          <div class="defined-item">
            <div class="defined-item-title">荣耀借钱服务用户协议</div>
          </div>
          <span class="icon-rightIcon"></span>
        </div>
        <hnr-divider line />
        <div
          class="cell-item"
          @click="
            agreement(
              `https://agreement${host}.honor.com/asm/agrFile/getHtmlFile?agrNo=1492&country=cn&branchId=0&langCode=zh-cn&${themeName}`,
              'forceDark',
            )
          "
        >
          <div class="defined-item">
            <div class="defined-item-title">关于荣耀借钱服务与隐私的声明</div>
          </div>
          <span class="icon-rightIcon"></span>
        </div>
      </hnr-cell-group>
      <div v-if="data.detailList.length">
        <hnr-sub-title>
          <hnr-index-anchor
            :subtitle="data?.companyName ? `${data?.companyName}服务` : '合作机构服务'"
          />
        </hnr-sub-title>
        <hnr-cell-group inset class="basic">
          <template v-for="(item, index) in data.detailList" :key="item.contractName">
            <div
              class="cell-item"
              @click="agreement(item.contractContent, 'forceDark', item.contractName)"
            >
              <div class="defined-item">
                <div class="defined-item-title">{{ item.contractName }}</div>
              </div>
              <span class="icon-rightIcon"></span>
            </div>
            <hnr-divider v-if="index !== data.detailList.length - 1" line />
          </template>
        </hnr-cell-group>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.list {
  height: 100vh;
  flex: 1;
}

::v-deep(.hnr-cell-group--inset) {
  margin-top: 0;
  margin-bottom: 0;
}

.basic {
  display: flex;
  flex-direction: column;
  width: calc(100vw - 24px) !important;
  margin-left: 12px !important;
  color: var(--hnr-text-color-primary) !important;
  font: var(--hnr-body-1) !important;
  font-weight: var(--hnr-font-weight-medium) !important;
}

.cell-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
}

.cell-item2 {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
  padding: 0 6px 0 12px;
}

.topComtent {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.defined-item {
  display: flex;
  align-items: center;
  gap: var(--hnr-elements-margin-horizontal-M);
}

.defined-item-title {
  min-width: 64px;
}

.cell-item-img {
  width: 12px;
  height: 24px;
}

.cell-padding {
  padding: 0 12px !important;
}

.hnr-cell-demo__img__round {
  border-radius: 50%;
  width: var(--dp40);
  height: var(--dp40);
  margin-left: var(--dp12);
}

.custom-prefix {
  display: flex;
  align-items: center;
}

.icon-rightIcon:before {
  content: "\e95e";
  font-family: "icomoon";
  font-size: calc(var(--dp24));
  color: var(--hnr-color-quaternary);
}

.icon-rightIcon {
  line-height: calc(var(--dp48));
}

:deep(.hnr-divider) {
  margin: 0 auto;
  width: calc((100vw - 24px) - 1.5rem);
}

:deep(.cell-item) {
  padding: 0 6px 0 12px;
}
</style>
