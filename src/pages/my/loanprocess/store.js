import { defineStore } from "pinia";
import { getLoanRecord } from "../../../api/loan";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

export default defineStore("my/loanprocess", {
  state: () => ({
    // 页面初始数据
    records: [],
    loading: false,
    finished: false,
    currentPage: 1,
    pageSize: 5,
  }),
  actions: {
    async initial() {
      await this.loadData();
    },
    async loadData() {
      try {
        // 如果cp不支持分页则不进行分页
        const cpConfig = await getCurrentCpInfo();
        if (!cpConfig.supportLoanRecordPage) {
          this.currentPage = null;
          this.pageSize = null;
          this.finished = true;
        }

        const data = await getLoanRecord({
          currentPage: this.currentPage,
          pageSize: this.pageSize,
        });

        if (data.code !== 0) {
          return;
        }

        const validRecords = data?.data?.records?.filter((item) => item.status !== 0);
        this.records.push(...validRecords);
        this.loading = false;
        if (data?.data?.records?.length < this.pageSize) {
          this.finished = true;
          return;
        }
        this.currentPage += 1;
      } catch (error) {
        throw new Error(error);
      }
    },
  },
});
