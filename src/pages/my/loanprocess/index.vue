<script setup>
import { computed, onServerPrefetch } from "vue";
import useStore from "./store";
import { initStore } from "../../../helpers/utils";
import { back, goto } from "../../../helpers/native-bridge";
import Loanprocess from "./components/Loanprocess.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store } = initStore(useStore);

onServerPrefetch(store.initial);

function goback() {
  back();
}
function convertDate(dateString) {
  const year = dateString?.substring(0, 4);
  const month = parseInt(dateString?.substring(4, 6), 10);
  const day = parseInt(dateString?.substring(6, 8), 10);
  return `${year}/${month}/${day}`;
}
// 查看借款详情
function gotoDetail(outOrderNo) {
  goto(`/wallet-loan-web/pages/repayment/detail?loanOrderId=${outOrderNo}`);
}

function addCommas(num) {
  return (num / 100)
    .toFixed(2)
    .toString()
    .replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// 数据量不足一页时不展示提示文案
const noMoreText = computed(() => {
  return store.records?.length <= 4 ? "" : "没有更多了";
});
</script>

<template>
  <div class="loanprocess-container">
    <hnr-nav-bar class="nav-padding-top" transparent="true" title="借款申请进度">
      <template #left>
        <icsvg-public-back-filled @click="goback"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>

    <main v-if="store.records.length > 0">
      <hnr-list
        v-model:loading="store.loading"
        :immediate-check="false"
        :finished="store.finished"
        :finished-text="noMoreText"
        @load="store.loadData"
      >
        <div v-for="item in store.records" :key="item.outOrderNo" class="item">
          <div class="item-con">
            <div class="item-con-top">
              <div class="item-con-top-title">申请借款￥{{ addCommas(item.loanAmount) }}</div>
              <div class="item-con-top-desc">{{ convertDate(item.applyDate) }}</div>
            </div>
            <template v-if="item.status === 1">
              <div class="item-status">申请中</div>
              <loanprocess status="goon" />
            </template>
            <template v-else-if="item.status === 5">
              <div class="item-status item-status-fail">申请失败</div>
              <loanprocess status="fail" />
            </template>
            <template v-else>
              <div class="item-status">
                <div>申请成功</div>
                <div class="item-view-detail" @click="gotoDetail(item.outOrderNo)">
                  查看借款详情
                  <span class="icon-arrow-right"></span>
                </div>
              </div>
              <loanprocess status="success" />
            </template>
          </div>
        </div>
      </hnr-list>
    </main>
    <main v-else class="no-record">
      <span class="icon-no-record"></span>
      <span class="no-record-tip">暂无借款</span>
    </main>
  </div>
</template>

<style scoped>
.loanprocess-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-bottom: var(--hnr-elements-margin-vertical-L);
  box-sizing: border-box;
  user-select: none;
}
:deep(.hnr-nav-bar__title) {
  font-weight: var(--hnr-font-weight-medium);
}
main {
  flex: 1;
  overflow: auto;
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.item {
  padding: 0 var(--hnr-elements-margin-vertical-M2);
  box-sizing: border-box;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.item:last-child {
  margin-bottom: 0;
}
.item-con {
  background: var(--hnr-card-background);
  border-radius: var(--hnr-card-border-radius);
  padding: var(--hnr-elements-margin-vertical-L);
}
.item-con-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-con-top-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-regular);
}
.item-con-top-desc {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-subtitle-3);
  font-weight: var(--hnr-font-weight-regular);
}
.item-status {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
  margin-top: var(--hnr-elements-margin-vertical-L);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-status-fail {
  color: var(--hnr-magic-color-8);
}
.item-view-detail {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-subtitle-3);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  align-items: center;
}
.no-record {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: var(--hnr-elements-margin-vertical-XXL);
}
.icon-no-record:before {
  content: "\e949";
  font-family: "icomoon";
  font-size: var(--dp72);
  color: var(--hnr-color-tertiary);
}
.no-record-tip {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.icon-arrow-right::before {
  content: "\e903";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-quaternary);
  display: inline-block;
  margin-left: 6px;
}
</style>
