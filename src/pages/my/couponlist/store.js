import { defineStore } from "pinia";
import { request } from "../../../helpers/utils";

export default defineStore("my/coupon", {
  state: () => ({
    // 页面初始数据
    response: {}, // 页面原始数据
    param: {}, // 页面收到的参数
    noUse: [], // 未使用的优惠券
    used: [], // 已使用的优惠券
    outTime: [], // 过期的优惠券
  }),
  actions: {
    async initial() {
      const data = await Promise.all([request("/loan/api/coupon/list", {})]);
      this.$patch({ response: data[0] });
      this.noUse = (await this.filterInfo(1)) || [];
      this.used = (await this.filterInfo(2)) || [];
      this.outTime = (await this.filterInfo(3)) || [];
    },
    filterInfo(n) {
      const arr = (this.response?.data || []).filter((item) => item.status === n);
      return arr;
    },
  },
});
