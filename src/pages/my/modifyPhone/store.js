import { defineStore } from "pinia";
import { getCreditInfo } from "../../../api/credit";
import { modifyPhone, sendVerifyCode } from "../../../api/modifyPhone";

export default defineStore("my/modifyPhone", {
  state: () => ({
    param: {},
    newMobileNo: "",
    smsCode: "",
    inputBtmDiable: true,
    smsBtmDiable: true,
    requestLoading: false,
  }),
  actions: {
    async initial() {
      if (!this.param.mobileNo) {
        const res = await getCreditInfo();
        this.param.mobileNo = res.data.mobileNo;
      }
    },
    async sendVerifyCode() {
      const { newMobileNo } = this;
      return sendVerifyCode(newMobileNo);
    },
    async submit() {
      const { newMobileNo, smsCode } = this;
      return modifyPhone(newMobileNo, smsCode);
    },
  },
});
