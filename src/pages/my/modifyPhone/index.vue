<script setup>
import { onMounted, onServerPrefetch, ref, watchEffect } from "vue";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import lodash from "lodash";
import useStore from "./store";
import { initStore } from "../../../helpers/utils";
import {
  back,
  closeLiveGuidePage,
  getUserId,
  liveDetection,
  regNativeEvent,
  report,
  setCp,
} from "../../../helpers/native-bridge";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { getUserRealNameInfo } from "../../../api/user";
import { setWithExactExpire } from "../../../helpers/storage";
import { DEBOUNCE_OPTIONS } from "../../../helpers/constants";

const { store } = initStore(useStore);
onServerPrefetch(store.initial);
const allHash = ["confirm", "input", "sms"];
const allEvent = ["wallet_page_view", "wallet_page_click"];

const title = ref("借款通知手机号");
const page = ref("confirm");
const loading = ref(false);
const supplierName = ref("");
const hideNewMobile = ref("");

// 倒计时秒数
const countdown = ref(0);
const codeErrorMessage = ref("");
const session = `${store.param.userId}-${Date.now()}`;
let intervalId;

// 页面监听
watchEffect(() => {
  if (page.value === "confirm") {
    title.value = "借款通知手机号";
    // 计时器归0
    countdown.value = 0;
  } else {
    title.value = "更换手机号";
  }
});

// 确认按钮Disabled
watchEffect(() => {
  if (!store.smsCode) {
    store.smsBtmDiable = true;
    codeErrorMessage.value = "";
  } else if (!/^\d+$/.test(store.smsCode)) {
    store.smsBtmDiable = true;
    codeErrorMessage.value = "验证码格式不对";
  } else {
    store.smsBtmDiable = false;
  }
});

// 校验输入的手机号格式
watchEffect(() => {
  if (!store.newMobileNo) {
    store.inputBtmDiable = true;
    store.phoneErrorMessage = "";
  } else if (!/^\d+$/.test(store.newMobileNo)) {
    store.inputBtmDiable = true;
    store.phoneErrorMessage = "手机号格式不对";
  }
});

// 倒计时器
const getTimer = () => {
  countdown.value = 59; // 倒计时60秒
  intervalId = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value -= 1;
    } else {
      clearInterval(intervalId);
    }
  }, 1000);
};

// 验证码倒计时
const startCountdown = () => {
  if (countdown.value > 0) {
    return;
  }
  store.sendVerifyCode();
  getTimer();
};

const codeChange = () => {
  codeErrorMessage.value = "";
};

function reportEvent(type) {
  if (allEvent.includes(type) && allHash.includes(page.value)) {
    const event = {};
    if (type === "wallet_page_view") {
      event.page_name = `modify_mobile_${page.value}`;
    } else {
      event.click_name = `modify_mobile_${page.value}`;
    }
    event.session = session;
    report(type, event);
  }
}

async function faceCheck() {
  const realNameRes = await getUserRealNameInfo();
  if (realNameRes) {
    if (realNameRes?.data?.status === 1) {
      const cpInfo = await getCurrentCpInfo();
      setCp(cpInfo?.supplierId || 0);
      const fullName = realNameRes.data.realName;
      report("wallet_page_click", {
        click_name: "h5_live_detection_start",
        scenes: 3,
        faceSdk: 1,
        session,
      });
      const faceInfo = await liveDetection({
        fullName,
        scenes: "3",
        faceSdk: 1,
        retentionPicurl: "",
        diversionMethod: cpInfo?.diversionMethod,
      });
      return faceInfo;
    }
  }
  return false;
}

// 输入手机号
const debounceGoToInput = lodash.debounce(
  async () => {
    reportEvent("wallet_page_click");
    store.inputBtmDiable = true;
    store.newMobileNo = "";
    const faceCheckInfo = await faceCheck();
    if (faceCheckInfo) {
      closeLiveGuidePage();
      window.location.hash = "input";
    }
  },
  1000,
  DEBOUNCE_OPTIONS,
);

// 手机号输入校验
function phoneinput(value) {
  if (/^1[3-9]\d{9}$/.test(value)) {
    store.phoneErrorMessage = "";
    store.inputBtmDiable = false;
  } else {
    store.inputBtmDiable = true;
    store.phoneErrorMessage = "";
  }
}

const phoneBlur = (val) => {
  if (/^1[3-9]\d{9}$/.test(val.target.value)) {
    store.phoneErrorMessage = "";
    store.inputBtmDiable = false;
  } else {
    store.phoneErrorMessage = "请输入正确手机号";
    store.inputBtmDiable = true;
  }
};

const hidePhoneMiddleFour = (phoneNumber) => {
  if (!phoneNumber) {
    return "";
  }
  if (phoneNumber.length !== 11) {
    throw new Error("电话号码必须是11位");
  }

  // 提取前三位和后四位
  const firstThree = phoneNumber.substring(0, 3);
  const lastFour = phoneNumber.substring(7);

  // 生成4个星号代替中间四位
  const middleStars = "*".repeat(4);

  // 拼接结果
  return `${firstThree}${middleStars}${lastFour}`;
};

const debounceGotoSms = lodash.debounce(
  async () => {
    reportEvent("wallet_page_click");
    // 验证码清空
    store.smsCode = "";
    //  调用获取验证码接口
    const resp = await store.sendVerifyCode();
    if (resp.code === 0) {
      const status = resp?.data?.status;
      const errorDesc = resp?.data?.errorDesc;
      if (status === 0) {
        showToast({ message: "发送成功" });
        getTimer();
        hideNewMobile.value = hidePhoneMiddleFour(store.newMobileNo);
        window.location.hash = "sms";
      } else {
        store.phoneErrorMessage = errorDesc;
      }
    } else {
      showToast(resp.message);
    }
  },
  1000,
  DEBOUNCE_OPTIONS,
);

// 输入短信验证码后调用
const debounceGoToSubmit = lodash.debounce(
  async () => {
    reportEvent("wallet_page_click");
    store.requestLoading = true;
    // 调用修改手机号接口
    const resp = await store.submit();
    store.requestLoading = false;
    if (resp.code === 0) {
      const status = resp?.data?.status;
      const errorDesc = resp?.data?.errorDesc;
      if (status === "0") {
        // 返回修改手机号确认页
        setWithExactExpire("U_NEW_MOBILE", hideNewMobile.value, "1H");
        store.param.mobileNo = hideNewMobile.value;
        window.history.go(-2);
        showToast({ message: "更换成功" });
        clearInterval(intervalId);
      } else if (status === "175511" || status === "175510") {
        // 修改借款手机号场景，活体缓存失效 or CP系统异常，请稍后重试 （调用CP修改手机号报错）
        store.smsCode = "";
        store.newMobileNo = "";
        window.history.go(-2);
        showToast(errorDesc);
        clearInterval(intervalId);
      } else if (status === "170002" || status === "170009") {
        // 验证码错误 or 短信验证码连续输错超过限制次数，请2小时后稍后再试
        codeErrorMessage.value = errorDesc;
      }
    } else {
      showToast(resp.message);
    }
  },
  1000,
  DEBOUNCE_OPTIONS,
);

function reportPv() {
  page.value = window.location.hash.replace("#", "");
  reportEvent("wallet_page_view");
}

onMounted(async () => {
  // 获取cpInfo
  const supplier = await getCurrentCpInfo();
  supplierName.value = !store.param.urlref ? "" : `及${supplier?.supplierName}`;

  let errMsg;
  if (
    store.param.sdkVersionCode < 90007000 ||
    // 如果是从管理首页过来，且当前CP不支持更换手机号，则直接返回
    (!supplier?.supportModifyMobile && store.param.urlref) ||
    !store.param.mobileNo
  ) {
    errMsg = "暂不支持更换手机号";
  }
  if (errMsg) {
    showToast({ message: errMsg, duration: 4000, overlay: true, forbidClick: true });
    setTimeout(() => {
      back();
    }, 1500);
    return;
  }
  reportPv();
  window.addEventListener("hashchange", () => {
    reportPv();
  });
});
// 本地测试链接 http://localhost:8080/wallet-loan-web/pages/my/modifyPhone?mobileNo=138****3445#confirm

// 回退
function onClickLeft() {
  back();
}

regNativeEvent("onResume", () => {
  /**
   * 如果用户未登录，则尝试获取用户ID。
   * 获取到用户ID后，刷新页面以重新加载用户状态。
   */
  if (!store.param.isLogin) {
    getUserId().then((userId) => {
      if (userId) {
        window.location.reload();
      }
    });
  }
});

regNativeEvent("onBack", () => {
  back();
});
</script>
<template>
  <div class="main">
    <div class="area">
      <!-- 总标题 -->
      <div class="header">
        <div class="header-title">
          <hnr-nav-bar :title="title" class="nav-padding-top" transparent="true">
            <template #left>
              <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
            </template>
          </hnr-nav-bar>
        </div>
      </div>

      <!-- 更换手机号确认页 -->
      <div v-show="page === 'confirm'" class="confirm">
        <div>
          <span class="confirm-title">当前绑定手机号</span>
        </div>
        <div style="margin-top: 8px">
          <span class="confirm-comment">该手机号用于接收借钱服务短信通知</span>
        </div>
        <div style="margin-top: 16px">
          <span class="confirm-phone">{{ store.param.mobileNo }}</span>
        </div>
      </div>
      <div v-show="page === 'confirm'" class="bottom">
        <hnr-button type="default" class="bottom-top" @click="debounceGoToInput"
          >更换手机号</hnr-button
        >
      </div>

      <!-- 手机输入页 -->
      <div v-show="page === 'input'" class="input">
        <div class="input-text">
          <div style="margin-top: var(--hnr-elements-margin-vertical-L2)">
            <span class="input-title">输入新手机号</span>
          </div>
          <div class="input-comment">
            <span class="input-comment-span">
              更换后，荣耀借钱服务{{ supplierName }}的短信通知将发送至您的新手机号
            </span>
          </div>
          <div class="content">
            <hnr-card type="empty" style="width: 100%">
              <hnr-field
                v-model="store.newMobileNo"
                :formatter="(value) => value.replace(/\s/g, '')"
                type="number"
                :error-message="store.phoneErrorMessage"
                clearable
                clear-icon="/wallet-loan-web/pages/loan/close_doublelayer_filled.svg"
                placeholder="输入手机号"
                class="verification-code"
                @update:model-value="phoneinput"
                @blur="phoneBlur"
              />
            </hnr-card>
          </div>
        </div>

        <div class="inputNextBtm">
          <hnr-button
            :disabled="store.inputBtmDiable"
            type="primary"
            class="content-bottom-btn-next"
            :loading="loading"
            loading-text="加载中..."
            standard-width="true"
            @click="debounceGotoSms"
            >下一步
          </hnr-button>
        </div>
      </div>

      <!-- 验证码验证页面 -->
      <div v-show="page === 'sms'" class="content content-verificationCode">
        <div class="verificationCode-title">验证手机号</div>
        <div class="verificationCode-tips">验证码已发送至{{ hideNewMobile }}</div>
        <hnr-card type="empty">
          <hnr-field
            v-model="store.smsCode"
            placeholder="输入验证码"
            type="number"
            :error-message="codeErrorMessage"
            class="verification-code"
            @update:model-value="codeChange"
          >
            <template #button>
              <hnr-button type="text" size="mini" :disabled="countdown > 0" @click="startCountdown"
                >{{ countdown === 0 ? "重新获取" : `${countdown}s` }}
              </hnr-button>
            </template>
          </hnr-field>
        </hnr-card>
      </div>
      <div v-show="page === 'sms'" class="bottom">
        <hnr-button
          type="primary"
          class="bottom-verificationCode"
          :disabled="store.smsBtmDiable"
          :loading="store.requestLoading"
          loading-text="正在提交"
          @click="debounceGoToSubmit"
          >提交
        </hnr-button>
      </div>
    </div>
  </div>
</template>

<style>
:root {
  --dp: 1px;
}
</style>
<style scoped>
.main {
  position: relative;
  display: flex;
  flex-direction: column;
  user-select: none;
}

.area {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  height: calc(100vh - 36 * var(--dp));
  padding-bottom: var(--hnr-default-padding-bottom-fixed);
  top: 0.5rem;
}

.header {
  box-sizing: border-box;
}

.header-title {
  flex: 1;
}

.header-title {
  flex: 1;
}

.header span {
  font-size: 16px;
}

.confirm {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.confirm-title {
  font-size: 20px;
  font-weight: 500;
  /* Light/前景色/textColorPrimary */
  color: var(--hnr-text-color-primary);
  margin-bottom: calc(8 * var(--dp));
}

.confirm-comment {
  font-size: 14px;
  font-weight: normal;
  /* Light/前景色/textColorSecondary */
  color: var(--hnr-text-color-secondary);
  margin-bottom: calc(16 * var(--dp));
}

.confirm-phone {
  font-size: 24px;
  font-weight: 500;
  color: var(--hnr-text-color-primary);
}

.bottom {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: var(--hnr-elements-margin-vertical-XXL);
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
}

.bottom-top {
  min-width: 264px !important;
  max-width: 264px !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 0 0;
}

.input-text {
  height: 30vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-title {
  font-size: 24px;
  font-weight: 500;
  /* Light/前景色/textColorPrimary */
  color: var(--hnr-text-color-primary);
}

.input-comment {
  margin-top: var(--hnr-elements-margin-vertical-M);
  text-align: center;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
}

.input-comment-span {
  font-size: 14px;
  font-weight: normal;
  /* Light/前景色/textColorSecondary */
  color: var(--hnr-text-color-secondary);
}

.inputNextBtm {
  width: 100%;
  position: fixed;
  bottom: var(--hnr-elements-margin-vertical-XXL);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.content-bottom-btn-next {
  min-width: 264px !important;
  max-width: 264px !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 0 0;
}
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  padding-left: 12px;
  padding-right: 12px;
}

.content-verificationCode {
  padding-left: 0;
  padding-right: 0;
}

.verificationCode-title {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L2);
  margin-bottom: calc(8 * var(--dp));
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}
.verificationCode-tips {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-bottom: calc(42 * var(--dp));
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

::v-deep(.hnr-field__error-message) {
  position: absolute;
  bottom: -24px;
  left: 12px;
}
.verification-code /deep/.hnr-field__input-area:before {
  height: 0;
}
.verification-code {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin: 0;
}
.bottom-verificationCode {
  min-width: 264px !important;
  max-width: 264px !important;
}
</style>
