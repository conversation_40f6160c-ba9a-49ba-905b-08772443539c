import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request } from "../../../helpers/utils";

export default defineStore("my/logoff", {
  state: () => ({
    param: {},
    realNameInfo: {},
    message: "",
    supplierName: undefined,
    supplierContact: undefined,
    requestLoading: false,
  }),
  getters: {},
  actions: {
    async initial() {
      this.supplierName = this.param.supplierName;
      this.supplierContact = this.param.supplierContact;
      const res = await request("/loan/api/credit/info", {}, { mock: false });
      if (res.code === 0) {
        if (res.data) {
          this.realNameInfo = res.data;
        }
      } else {
        this.message = res.message;
      }
    },
    async sendVerifyCode() {
      try {
        const res = await request(
          "/loan/api/sms/v2/sendVerifyCode",
          { encryptedParams: { type: 5, mobileNo: this.realNameInfo.mobileNo } },
          { mock: false },
        );
        if (res.code === 0) {
          showToast({ message: "发送成功" });
        } else {
          showToast({
            message: res.message,
            position: "bottom",
          });
        }
      } catch (error) {
        throw new Error(error);
      }
    },
  },
});
