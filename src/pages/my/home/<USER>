<script setup>
import { ref, computed, onServerPrefetch, onMounted } from "vue";
import lodash from "lodash";
import useStore from "./store";
import { initStore, parseQueryStringToObj } from "../../../helpers/utils";
import { agreement, getHostByOrigin, getThemeNameByMediaQuery } from "../../../helpers/signUtils";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { getStore, setWithExactExpireUnit, del } from "../../../helpers/storage";
import iCSvgPublicMore24Bold from "/icsvg_public_toolbar_more12.svg";
import {
  goto,
  back,
  updateShortcut,
  hasShortcut,
  launchSupport,
  sixClickNative,
  regNativeEvent,
  report,
} from "../../../helpers/native-bridge";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);
const deskTop = ref(false);
const shortCut = ref(false); // 是否展示快捷方式
const navigationLeft = "管理中心";
const customTitle = "常用服务";
const otherTitle = "其他";
const homeViable = ref("hidden");
const inkShow = ref(false);
const basicUsage = ref(false);
const num = ref(0); // 点击次数
const timer0 = ref(""); // 第一次点击的时间
const time6 = ref(""); // 第4次点击的时间
const isShowCouponText = ref(false); // 是否展示橙色领取新人免息券标签
const isShowLogOff = computed(() => data.value.accessResult === 1); // 是否显示注销的插槽(三个点),明文准入后展示
const hasAmount = computed(() => data.value.status === 1); // 是否已授信
const isSettlementCertificateClickable = computed(() => data.value.totalNum > 0);
const supplier = ref({});
// 埋点
// [0, 2, 3, 4].includes(data.value.status) ? "1" : data.value.status === 1 ? "2" : "0"
const creditStatus = computed(() => {
  if ([0, 2, 3, 4].includes(data.value.status)) {
    return "1";
  }
  if (data.value.status === 1) {
    return "2";
  }
  return "0";
});

// 判断桌面快捷键是否被添加
function shortCutFind(cb = false) {
  if ((data.value.param.versionCode || 0) >= 81300350) {
    shortCut.value = true;
    hasShortcut().then((value) => {
      deskTop.value = value;
      if (cb) {
        setTimeout(() => {
          inkShow.value = true;
        });
      }
    });
  }
}
// 清缓存
function emptyStorage() {
  del(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_REASON_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_VERIFI_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_SUCCESS_${data.value.param.userId}`);
  del(`U_MY_CLEAROFF_ISSUCCESS_${data.value.param.userId}`);
  del(`U_MY_CLEARHISTORY_ISDETAI_${data.value.param.userId}`);
  del(`U_CLEAROFF_${data.value.param.userId}`);
  del(`U_CLEAROFF_PAGE_ID_${data.value.param.userId}`);
}
// 端侧onResume钩子
regNativeEvent("onResume", async () => {
  if (window?.navigator?.onLine) {
    await store.initial();
    shortCutFind();
  }
  const newMobile = getStore("U_NEW_MOBILE");
  if (newMobile) {
    store.param.mobileNo = newMobile;
    store.mobileNo = newMobile;
    del("U_NEW_MOBILE");
  }
});
const host = ref("");
const themeName = ref("");
onMounted(async () => {
  host.value = getHostByOrigin(window.location.origin);
  themeName.value = getThemeNameByMediaQuery(window.matchMedia("(prefers-color-scheme: dark)"));
  if (data.value.status === 1) {
    supplier.value = await getCurrentCpInfo();
  }
  isShowCouponText.value =
    data.value.status !== 1 &&
    data.value.myCouponDataTitle &&
    data.value.myCouponDataURL &&
    !getStore(`IS_SHOW_COUPON_TEXT_${data.value.param.userId}`);
  // DTS2024082970986 七天免息券埋点上报
  if (isShowCouponText.value) {
    setTimeout(() => {
      report("wallet_page_view", { page_name: "my_home_page_free_rate_coupon_view" });
    });
  }
  // 管理中心埋点
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_home_page",
      creditStatus: creditStatus.value,
    });
  });
  emptyStorage();
  setTimeout(async () => {
    shortCutFind(true);
  });
  // dom和数据都处理结束再显示页面，解决闪频问题
  setTimeout(() => {
    homeViable.value = "visible";
  });
  // 修改手机号埋点上报
  if (
    supplier.value?.supportModifyMobile &&
    [1, 3].includes(store.status) &&
    store.mobileNo &&
    store.param.sdkVersionCode >= 90007000
  ) {
    report("wallet_page_view", { page_name: "modify_mobile_menu" });
  }
});

// 注销
function logOff() {
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "my_home_logoff_cell_click",
      creditStatus: creditStatus.value,
    });
  });
  basicUsage.value = false;
  goto(
    `/wallet-loan-web/pages/my/logoff?supplierName=${supplier.value?.supplierName}&supplierContact=${supplier.value?.supplierContact}`,
  );
}
// 回退按钮
function onClickLeft() {
  back();
}
// 借款申请进度
function myApplicationStatusMethod() {
  goto("/wallet-loan-web/pages/my/loanprocess");
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "my_home_loan_process_cell_click",
      creditStatus: creditStatus.value,
    });
  });
}

// 我的优惠券
function handleClickMyCoupon() {
  if (hasAmount.value) {
    goto("/wallet-loan-web/pages/my/couponlist");
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "my_home_coupon_cell_click",
        creditStatus: creditStatus.value,
      });
    });
  } else if (!data.value.myCouponDataURL) {
    goto("/wallet-loan-web/pages/my/couponlist");
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "my_home_coupon_cell_click",
        creditStatus: creditStatus.value,
      });
    });
  } else {
    setWithExactExpireUnit(`IS_SHOW_COUPON_TEXT_${store.param.userId}`, "storageData", 10000, "D");
    // DTS2024082970986 七天免息券埋点上报
    setTimeout(() => {
      report("wallet_page_click", { click_name: "my_home_page_free_rate_coupon_click" });
    });
    isShowCouponText.value = false;
    // 留个后门,可以通过后台传来的url控制showTitle属性,为后续的运营更改优惠券页适配标题
    const showTitle = store.myCouponDataURL.startsWith("/wallet-loan-web/pages")
      ? true
      : parseQueryStringToObj(store.myCouponDataURL) === "true";
    goto(store.myCouponDataURL, showTitle);
  }
}
// 开具结清证明
function mySettlementCertificateMethod() {
  if (isSettlementCertificateClickable.value) {
    goto(
      "/wallet-loan-web/pages/my/clearoff?" +
        `supplierName=${supplier.value?.supplierName}` +
        `&supplierContact=${supplier.value?.supplierContact}` +
        `&supplierId=${supplier.value?.supplierId}` +
        `&supportSettlementCertificate=${supplier.value?.supportSettlementCertificate}`,
    );
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "my_home_clearoff_cell_click",
        creditStatus: creditStatus.value,
      });
    });
  }
}
// 帮助与客服
async function myHelpMethod() {
  await launchSupport();
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "my_home_help_cell_click",
      creditStatus: creditStatus.value,
    });
  });
}
// 协议与授权
function myEmpowerMethod() {
  if (supplier.value?.supplierName) {
    goto(
      `/wallet-loan-web/pages/my/autheration?supplierName=${supplier.value.supplierName}&creditStatus=${creditStatus.value}`,
    );
  } else {
    goto(`/wallet-loan-web/pages/my/autheration?&creditStatus=${creditStatus.value}`);
  }
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "my_home_autheration_cell_click",
      creditStatus: creditStatus.value,
    });
  });
}
// 隐私管理
function myPrivacymgrMethod() {
  goto(`/wallet-loan-web/pages/my/privacymgr?creditStatus=${creditStatus.value}`);
}
// 添加到桌面
const myAddDeskMethod = lodash.debounce(
  async () => {
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "my_home_shortcut_cell_click",
        creditStatus: creditStatus.value,
      });
    });
    const res = await updateShortcut(true);
    if (res) {
      del(`U_POPUP_STORE_${store.param.userId}`);
    }
    deskTop.value = await hasShortcut();
  },
  3000,
  { leading: true },
);
// 点击首页下方的度小满文字调出调试
async function sixClick() {
  num.value += 1;
  if (num.value === 1) {
    timer0.value = new Date().getTime() / 1000;
  }
  if (num.value === 6) {
    time6.value = new Date().getTime() / 1000;
    if (time6.value - timer0.value <= 3) {
      await sixClickNative(true);
      num.value = 0;
    } else {
      await sixClickNative(false);
      num.value = 0;
    }
  }
}

function handleChangeMobileNo() {
  report("wallet_page_click", { click_name: "modify_mobile_menu" });
  goto(`/wallet-loan-web/pages/my/modifyPhone?mobileNo=${store.mobileNo}#confirm`);
}
</script>

<template>
  <div class="home-container" :style="{ visibility: homeViable }">
    <div class="navigation-box">
      <hnr-nav-bar class="nav-padding-top" transparent="true" :title="navigationLeft">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
        <template v-if="isShowLogOff" #right>
          <hnr-popover v-model:show="basicUsage" width="120">
            <span style="width: auto; height: auto; line-height: 3rem" @click="logOff"
              >注销借钱服务</span
            >
            <template #reference>
              <hnr-icon class="white-icon" :name="iCSvgPublicMore24Bold" />
            </template>
          </hnr-popover>
        </template>
      </hnr-nav-bar>
    </div>
    <div class="sub-title">
      {{ customTitle }}
    </div>
    <hnr-card type="empty" style="width: calc(100% - var(--hnr-elements-margin-horizontal-M2) * 2)">
      <div class="home-content">
        <hnr-cell-group inset>
          <hnr-cell
            v-if="hasAmount"
            style="width: 100%"
            title="借款申请进度"
            clickable
            border
            @click="myApplicationStatusMethod"
          >
            <template #prefix>
              <span class="icon-myApplicationStatus"></span>
            </template>
            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell
            style="width: 100%"
            clickable
            title="我的优惠券"
            border
            @click="handleClickMyCoupon"
          >
            <template #prefix>
              <span class="icon-myCoupon"></span>
            </template>
            <template #value>
              <span v-if="isShowCouponText" style="color: var(--hnr-color-warning)"
                >{{ data.myCouponDataTitle }}&nbsp;&nbsp;</span
              >
              <span v-if="hasAmount && data.noUseCoupons.length"
                >{{ data.noUseCoupons.length }} 张&nbsp;&nbsp;</span
              >
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell
            v-if="
              supplier?.supportModifyMobile &&
              [1, 3].includes(store.status) &&
              store.mobileNo &&
              store.param.sdkVersionCode >= 90007000
            "
            style="width: 100%"
            left-icon="/wallet-loan-web/pages/my/icsvg_public_phone_regular.svg"
            clickable
            title="借款通知手机号"
            border
            @click="handleChangeMobileNo"
          >
            <template #value>
              <span>{{ data.mobileNo }}&nbsp;&nbsp;</span>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell
            v-if="hasAmount"
            style="width: 100%"
            :clickable="isSettlementCertificateClickable"
            border
            @click="mySettlementCertificateMethod"
          >
            <template #prefix>
              <span
                :class="{
                  'icon-mySettlementCertificate': isSettlementCertificateClickable,
                  'icon-mySettlementCertificate-no': !isSettlementCertificateClickable,
                }"
              ></span>
            </template>
            <template #title>
              <span :class="{ 'gray-font-color': !isSettlementCertificateClickable }"
                >开具结清证明</span
              >
            </template>
            <template #value>
              <span
                v-if="
                  !isSettlementCertificateClickable && supplier?.supportSettlementCertificate === 1
                "
                style="
                  color: var(--hnr-color-tertiary);
                  margin-right: var(--hnr-list-card-padding-end);
                "
                >结清借款后可用</span
              >
              <span
                v-if="
                  isSettlementCertificateClickable || supplier?.supportSettlementCertificate === 0
                "
                class="icon-right"
              ></span>
            </template>
          </hnr-cell>
          <hnr-cell style="width: 100%" clickable title="帮助与客服" border @click="myHelpMethod">
            <template #prefix>
              <span class="icon-myHelp"></span>
            </template>

            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell style="width: 100%" clickable title="协议与授权" @click="myEmpowerMethod">
            <template #prefix>
              <span class="icon-myEmpower"></span>
            </template>

            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
        </hnr-cell-group>
      </div>
    </hnr-card>
    <div class="sub-title" @click="sixClick">
      {{ otherTitle }}
    </div>
    <!-- <hnr-sub-title @click="sixClick">
      <hnr-index-anchor :title="otherTitle" />
    </hnr-sub-title> -->
    <hnr-card type="empty" style="width: calc(100% - var(--hnr-elements-margin-horizontal-M2) * 2)">
      <div class="home-content">
        <hnr-cell-group inset>
          <hnr-cell v-if="shortCut" style="width: 100%" title="添加到桌面" border>
            <template #prefix>
              <span class="icon-myAddDesk"></span>
            </template>
            <template #value>
              <hnr-button v-if="inkShow" style="margin-right: 0" size="mini" :disabled="deskTop">
                <div v-if="deskTop" class="active">已添加</div>
                <div v-else class="add" @click="myAddDeskMethod">添加</div>
              </hnr-button>
            </template>
          </hnr-cell>
          <hnr-cell
            style="width: 100%"
            left-icon="/wallet-loan-web/pages/my/security.svg"
            clickable
            title="隐私管理"
            border
            @click="myPrivacymgrMethod"
          >
            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell
            style="width: 100%"
            clickable
            title="个人信息收集清单"
            border
            @click="
              agreement(
                `https://agreement${host}.honor.com/asm/agrFile/getHtmlFile?agrNo=1495&country=cn&branchId=0&langCode=zh-cn&${themeName}`,
                'forceDark',
              )
            "
          >
            <template #prefix>
              <span class="icon-myEmpower"></span>
            </template>
            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
          <hnr-cell
            style="width: 100%"
            clickable
            title="个人信息共享清单"
            @click="
              agreement(
                `https://agreement${host}.honor.com/asm/agrFile/getHtmlFile?agrNo=1494&country=cn&branchId=0&langCode=zh-cn&${themeName}`,
                'forceDark',
              )
            "
          >
            <template #prefix>
              <span class="icon-myExtendedBusiness"></span>
            </template>
            <template #value>
              <span class="icon-right"></span>
            </template>
          </hnr-cell>
        </hnr-cell-group>
      </div>
    </hnr-card>
    <div v-if="hasAmount" class="bottom-box">
      <span class="cp-logo">
        <img
          style="width: var(--dp16); height: var(--dp16); vertical-align: bottom"
          :src="supplier?.apiLogoUrl"
          :alt="supplier?.supplierName"
        />
      </span>
      <span class="bottom-box-txt">由{{ supplier?.supplierName }}提供服务</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.home-container {
  display: flex;
  flex-direction: column;
  position: fixed;
  width: calc(100%);
  height: calc(100%);
  top: 0;
  left: 0;
  right: 0;
  /* background-color: rgb(242, 243, 246); */
}

.hnr-icon {
  color: var(--hnr-color-secondary);
}

.navigation-box .hnr-icon {
  color: var(--hnr-color-primary);
}

/* .navigation-box {
  display: flex;
  justify-content: space-between;
  padding: 1rem 1.3rem;
}

.navigation-left {
  font-size: calc(20 * var(--dp));
  font-weight: bold;
}

.navigation-right {
  display: flex;
  align-items: center;
} */

.icon {
  padding: 0.25rem 0.25rem;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.home-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* margin-top: 3rem; */
  width: 100%;
}

/* .home-content > .hnr-cell-group {
  width: 100%;
  padding: 0 var(--hnr-list-card-padding-end);
} */

.hnr-cell-box {
  height: 2rem;
  width: 98% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  background-color: unset;
}

:deep(.hnr-cell__value) {
  margin-left: 0 !important;
  margin-right: var(--hnr-list-card-padding-end) !important;
}

:deep(.home-content .hnr-cell__value) {
  margin-right: 0 !important;
}

.hnr-basic-width-margin {
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
}

.hnr-cell-box--clickable {
  width: 100% !important;
  border-radius: var(--hnr-list-card-corner-radius);
}

.hnr-cell-group--disabledCellMargin .hnr-cell-box {
  width: 100% !important;
}

:deep(.hnr-cell-group--disabledCellMargin .hnr-cell-box) {
  width: 100% !important;
}

:deep(.hnr-cell__prefix) {
  margin-left: var(--hnr-list-card-padding-end) !important;
}

:deep(.hnr-cell-box--clickable .hnr-cell__prefix) {
  margin-left: var(--hnr-list-card-padding-end) !important;
}

:deep(.hnr-cell-box .hnr-cell) {
  margin-right: var(--hnr-list-card-padding-end) !important;
}

img {
  width: 24px;
}

.narrow-icon {
  width: var(--dp12);
}

.gray-font-color {
  color: var(--hnr-color-tertiary);
}

:deep(.hnr-popover__content) {
  /* height: 6rem; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

:deep(.hnr-icon svg) {
  width: var(--dp24);
  height: var(--dp24);
}

.bottom-box {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: var(--hnr-default-padding-bottom-fixed);
  width: 100%;
}

:deep(.bottom-box .hnr-icon svg) {
  vertical-align: middle;
}

//.bottom-box .bottom-box-txt {
//  color: var(--hnr-text-color-tertiary);
//  font-size: var(--hnr-caption);
//  font-weight: var(--hnr-font-weight-regular);
//}
.bottom-box {
  .bottom-box-txt {
    color: var(--hnr-text-color-tertiary);
    font-size: var(--hnr-caption);
    font-weight: var(--hnr-font-weight-regular);
  }
}

.icon-myMore:before {
  content: "\e952";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-primary);
}

.icon-myApplicationStatus:before {
  content: "\e94d";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-myCoupon:before {
  content: "\e94e";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-mySettlementCertificate:before {
  content: "\e954";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-mySettlementCertificate-no:before {
  content: "\e954";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-tertiary);
}

.icon-myHelp:before {
  content: "\e951";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-myEmpower:before {
  content: "\e94f";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-myExtendedBusiness:before {
  content: "\e950";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-myAddDesk:before {
  content: "\e94c";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-secondary);
}

.icon-right:before {
  content: "\e928";
  font-family: "icomoon";
  font-size: var(--dp24);
  color: var(--hnr-color-tertiary);
}

:deep(.hnr-cell-group--inset) {
  margin: 0 0;
}

.hnr-button span div {
  color: var(--hnr-text-color-primary-activated) !important;
}

.cp-logo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: var(--hnr-elements-margin-horizontal-S);
}

:deep(.hnr-button span .active) {
  color: var(--hnr-text-color-secondary) !important;
}

.hnr-button--disabled {
  /* opacity: 1 !important; */
  cursor: none !important;
}

:deep(.hnr-button span .add) {
  color: var(--hnr-text-color-secondary-activated) !important;
}
:deep(.icon svg g g path) {
  fill: currentColor !important;
}
.white-icon {
  width: var(--dp12) !important;
}
:deep(.white-icon svg g g) {
  fill: var(--hnr-color-foreground) !important;
}
:deep(.white-icon svg) {
  width: var(--dp12) !important;
}

.sub-title {
  color: var(--hnr-index-anchor-subtitle-color);
  padding: 24px 24px 12px;
  box-sizing: border-box;
  font-size: calc(var(--hnr-index-anchor-subtitle-font-size) * var(--hnr-large-rate));
  font-weight: var(--hnr-index-anchor-title-font-weight);
}
</style>
