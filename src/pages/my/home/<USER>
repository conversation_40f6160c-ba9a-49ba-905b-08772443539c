import { defineStore } from "pinia";
import { request } from "../../../helpers/utils";
import { getLoanRecord } from "../../../api/loan";

export default defineStore("my/home", {
  state: () => ({
    param: {}, // 页面初始数据
    status: undefined, // 授信状态
    accessResult: undefined, // 准入结果
    totalNum: undefined, // 借款记录总条数
    noUseCoupons: [], // 未使用的优惠券
    myCouponDataTitle: undefined, // 我的优惠券文案和链接的接口返回数据
    myCouponDataURL: undefined, // 我的优惠券文案和链接的接口返回数据
    mobileNo: "", // 手机号
  }),
  actions: {
    async initial() {
      const hasQuery = "accessResult" in this.param && "status" in this.param; // 首页有没有传来query
      const requestArray = [];
      if (hasQuery) {
        this.status = Number(this.param?.status);
        this.mobileNo = this.param?.mobileNo;
        requestArray.push(getLoanRecord({ status: 4 })); // 4-已结清
        requestArray.push(request("/loan/api/coupon/list"));
        if (this.status !== 1) {
          requestArray.push(
            request("/loan/api/config/getDictMap", {
              itemNames: ["loan_setting_coupon_info"],
            }),
          );
        }
        requestArray.push(request("/loan/api/user/displayLogoff"));
      } else {
        requestArray.push(getLoanRecord({ status: 4 })); // 4-已结清
        requestArray.push(request("/loan/api/coupon/list"));
        requestArray.push(request("/loan/api/credit/info"));
        requestArray.push(request("/loan/api/user/displayLogoff"));
      }
      const result = await Promise.all(requestArray);
      if (result[0]?.code === 0) {
        this.totalNum = result[0]?.data?.totalNum;
      }
      if (result[1]?.code === 0) {
        this.noUseCoupons = result[1]?.data?.filter((item) => item.status === 1) || [];
      }
      if (hasQuery && this.status !== 1 && result[2]?.code === 0) {
        this.myCouponDataTitle = result[2].data.loan_setting_coupon_info.filter(
          (item) => item.name === "title",
        )[0].value;
        this.myCouponDataURL = result[2].data.loan_setting_coupon_info.filter(
          (item) => item.name === "url",
        )[0].value;
      } else if (!hasQuery && result[2]?.code === 0) {
        this.status = Number(result[2]?.data?.status);
        if (this.status !== 1) {
          const myCouponData = await request("/loan/api/config/getDictMap", {
            itemNames: ["loan_setting_coupon_info"],
          });
          this.myCouponDataTitle = myCouponData.data.loan_setting_coupon_info.filter(
            (item) => item.name === "title",
          )[0].value;
          this.myCouponDataURL = myCouponData.data.loan_setting_coupon_info.filter(
            (item) => item.name === "url",
          )[0].value;
        }
        this.mobileNo = result[2]?.data?.mobileNo;
      }
      if (result[requestArray.length - 1]?.code === 0) {
        this.accessResult = Number(result[requestArray.length - 1]?.data?.displayLogoff);
      }
    },
  },
});
