import { defineStore } from "pinia";
import Big from "big.js";
import { amountNumberFormat, getDiffTime, request } from "../../helpers/utils";
import { getCurrentCpInfo, delCurrentCpInfo } from "../../helpers/configUtils";
import { getStore, setWithExactExpireUnit, setWithExactExpire, del } from "../../helpers/storage";
import { USER_PROTOCAL_CODE, PRIVACY_PROTOCAL_CODE } from "../../helpers/constants";
import { report } from "../../helpers/native-bridge";
import { getSuggestList } from "../../api/config";
import { getAdActivityListV2 } from "../../api/ad";
import { showFloorDialog } from "../../helpers/activity-dialog-utils";

export default defineStore("entrance", {
  state: () => ({
    newGusetCoupon: false,
    isPageDisplay: true,
    cardDisplayStatus: 0, // 0-鱼骨图预显示/1-正常显示/2-服务器异常
    isRransfer: false, // 跳转方法中转空白页
    percentage: 0, // 进度条百分比
    userError: false,
    pageStartTime: null,
    screenSize: {
      screenWidth: 0,
      screenHeight: 0,
    },

    translationData: null,

    // 状态列表
    isNotAccessOrLogout: null, // 是否未准入或者注销
    isAccess: null, // 是否准入成功
    isAccessFailed: null, // 是否准入失败
    isUnknownLimitOrInvalid: null, // 是否未获取额度或者额度失效
    isLimitNormal: null, // 是否额度正常
    isLimitNormalNoLoanApply: null, // 是否授信成功但无借款申请订单
    isLimitNormalLoanUnsubmitted: null, // 是否授信成功但有已填写但未提交借款订单
    isLimitNormalInsufficient: null, // 是否授信成功但可用额度不足
    isLimitNormalZero: null, // 是否授信成功但可用额度为0
    isLimitNormalLoanAppling: null, // 是否授信成功且存在放款中订单
    isRepayOverdue: null, // 是否授信成功但存在逾期订单
    isLimitNormalLoanNotAvailable: null, // 是否授信成功但额度暂不可用
    isLimitNormalLoanOtherErr: null, // 是否授信成功但受到其它原因管控
    isLimitAppling: null, // 是否授信申请审核中
    isLimitApplyFail: null, // 是否授信申请失败
    isWithoutLoan: null, // 是否无还款计划
    isRepaymentInProgress: null, // 是否有还款计划
    isRateDecrease: null, // 是否有降息
    isLimitIncrease: null, // 是否有提额
    isRateDecreaseRecord: null, // 是否有最近降息记录
    isLimitIncreaseRecord: null, // 是否有最近提额记录
    isCouponIncrease: null, // 是否有优惠券
    isLoginHandleDataAfter: false, // 登录之后处理数据之后
    isFromLogin: false, // 是否登录后返回
    isRepaying: false, // 是否还款中

    // 弹窗状态
    showCustomerServiceDialog: false, // 客服弹窗
    firstEnter: false,
    showAddShortcutDialog: false, // 快捷方式弹窗
    showAddShortcutSuccessToast: false,
    showAddShortcutFailedToast: false,
    showWelcomeDialog: false, // 新客欢迎弹窗
    showDiscountDialog: false, // 调额调价弹窗
    showSupplierInfoDialog: false, // 已接入供应商信息弹窗
    clickShowDiscount: false, // 调额调价弹窗（点击弹出）状态
    nonClickShowDiscount: false, // 调额调价弹窗（落地自动弹出）状态
    showMarketingDialog: false, // 营销落地弹窗
    isChangeUserProtocol: false, // 协议弹窗状态（含用户协议）
    isChangePrivacyProtocol: false, // 协议弹窗状态（含隐私协议）
    isShowProtocolDialog: false, // 协议弹窗

    // 文本
    navigationHeader: "", // 主页顶部导航栏标题
    welcomeDialogTitle: "",
    welcomeDialogInfo: "",
    sloganInfo1: "",
    sloganInfo2: "",
    sloganInfo3: "",
    sloganHeader: "", // slogan的标题
    formattedWelcomeButtonText: "",
    sloganText1: "", // slogan展示的元素
    sloganText2: "", // slogan展示的元素
    sloganText3: "", // slogan展示的元素
    addShortcutDialogText: "", // 添加快捷方式弹窗文本
    addShortcutToastText: "", // 添加快捷方式成功的提示框文本
    addShortcutFailedToastText: "",
    formattedOverdueWarning: "", // 逾期状态下推荐位的警示文本
    formattedIntroduction: "", // 未准入下显示借钱服务介绍入口
    customerServiceDialogText: "", // 供应商客服弹窗文本
    loanProductOwner: "", // 厂商文本
    loanSupplierText: "", // 推荐位供应商文本
    loanLimitTitle: "", // 推荐位额度及逾期信息的主标题
    formattedLimitAmount: "", // 推荐位额度信息的具体数额
    formattedOverdueAmount: "", // 推荐位逾期信息的具体数额
    formattedRateTextA: "", // 推荐位利率信息文案A
    formattedRateTextB: "", // 推荐位利率信息文案B
    formattedRateTextC: "", // 推荐位利率信息文案C
    formattedRateTextD: "", // 推荐位利率信息文案D
    formattedOverdueInfo: "", // 推荐位逾期信息文案
    aboveButtonText: "", // 推荐位按钮上方文本
    formattedButtonText: "", // 推荐位主按钮文案
    cellTitleWithoutLoan: "", // 还款计划无借款时标题文案
    cellTextWithoutLoan: "", // 还款计划无借款时信息文案
    cellTitleWithLoan: "", // 还款计划有借款时标题文案
    formattedRepaymentAmount: "", // 还款计划总额的具体数额
    formattedRepaymentText: "", // 还款计划借款及逾期信息文案
    disclaimerText: "", // 首页免责声明文案
    noticeText: "", // 首页注意事项文案
    customerServiceDialogButtonText: "", // 客服弹窗按钮文本
    keepSilentCheckboxText: "", // 添加桌面弹窗不再提示选项框文本
    cancelButtonText: "", // 添加桌面弹窗取消按钮文本
    contactService: "", // 联系客服按钮文本
    addShortButtonText: "", // 添加桌面弹窗按钮文本
    suggestProductTitle: "", // 严选标题
    suggestProductText: "", // 严选文案
    suggestProductButtonText: "", // 严选按钮
    rateDiscountText: "",
    limitIncreaseText: "",
    couponText: "",
    couponText1: "",
    discountDialogTitle: "",
    discountDialogInfo: "",
    formattedDiscountButtonText: "",
    loanLimitIncreaseTitle: "",
    guide2ContinueApplyText: "",
    changeCooperativeProductText: "",
    supplierInfoDescription: "",
    supplierInfoClarSt: "",
    supplierInfoButtonText: "",
    loadingText: "",
    serverErrorText: ["", ""],
    retryText: "",
    userLogoutText: "",
    jumpOutText: "",
    increaseAmountReduceInterestText: "",

    // 持久化状态
    addShortcutInfo: null,

    // 存储
    creditInfo: {}, // 授信信息
    walletBanners: [], // 运营信息
    wideScreenWalletBanners: [], // 运营信息
    topBanner: {},
    currentTopBanner: [],
    suggestProducts: [],
    repayPlans: [], // 还款计划
    supplier: {}, // 供应商
    repaySupplementaryInfo: {}, // 还款计划额外信息
    signInfo: {},
    updateSignInfo: {},
    couponcomeStatus: {},
    creditProcess: null,
    loanProcess: null,
    loanOrderStatus: null,
    discountInfoList: [], // 专享调额调息信息
    storeCreditChangeRecordId: null,
    rateInfos: {},
    discountDialogBackground: "",
    cpInfo: null,
    supplierInfoList: [],
    realNameInfo: {},

    param: {},
    langType: 0, // 语言类型 0-中文 1-英文
    adActivityListCode: {},

    // 提额降息
    retryTimes: 0,
    addUrlTimer: null,
    increaseAmountReduceInterestData: {
      url: "",
      loading: false,
    },
    getCreditChange: true,
    canCreditChange: false,
    isValidVersion: false,
    myCouponData: {},

    creditProcessProperty: {
      emptySpaceHeight: {},
      fontSize: "var(--dp12)",
      shortTextHeight: 24,
      longTextHeight: 24,
      shortTextWidth: 0,
      shortBubbleWidth: 0,
      shortBubbleLeft: 0,
      longTextWidth: 0,
      longBubbleWidth: 0,
      longBubbleLeft: 0,
    },

    animationUtil: {
      enter2Long: {
        next: "long2Short",
        bubbleTop: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
        bubbleBottom: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
        bubbleTopText: "",
        bubbleBottomText: "",
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
      enter2Short: {
        next: "short2Long",
        bubbleTop: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
        bubbleBottom: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
        bubbleTopText: "",
        bubbleBottomText: "",
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
      short2Long: {
        next: "long2Short",
        bubbleTop: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
        bubbleBottom: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
        bubbleTopText: `
          bubble-text-appear-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
        bubbleBottomText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
      long2Short: {
        next: "short2Long",
        bubbleTop: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
        bubbleBottom: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
        bubbleTopText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
        bubbleBottomText: `
          bubble-text-appear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
      shortDisappear: {
        next: "enterPage",
        bubbleTop: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
        bubbleBottom: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
        bubbleTopText: "",
        bubbleBottomText: "",
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
      longDisappear: {
        next: "enterPage",
        bubbleTop: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
        bubbleBottom: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
        bubbleTopText: "",
        bubbleBottomText: "",
        bubbleTopNewTop: "",
        bubbleBottomNewTop: "",
      },
    },
    animationStatus: {},
    infoConAnimationUtil: {
      infoConHide: "infoCon-hide 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
      infoConShow: "infoCon-show 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
      loanConHeightTran: "loanCon-height-tran 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
      loanConShow: "loanCon-show 0.2s cubic-bezier(0.2,0,0.2,1) forwards",
      loanConHide: "loanCon-hide 0.002s cubic-bezier(0.2,0,0.2,1) forwards",
    },
    infoConAnimationStatus: {},
    isLimitExpire: false, // 额度失效
  }),
  actions: {
    /**
     * 弹窗弹出状态
     * @returns {boolean} 弹窗弹出状态 true-存在弹窗弹出 false-不存在弹窗弹出
     */
    checkDialogOpenStatus() {
      return !(
        !this.isShowProtocolDialog &&
        !this.showAddShortcutDialog &&
        !this.showDiscountDialog &&
        !this.showCustomerServiceDialog &&
        !this.showSupplierInfoDialog &&
        !this.showWelcomeDialog &&
        !this.showMarketingDialog
      );
    },

    parseFloatWithPrecision(value) {
      if (value?.includes(".")) {
        const decimalPlaces = value.split(".")[1].length;
        return new Big(value).toFixed(decimalPlaces);
      }
      return new Big(value || "100").toString();
    },

    getAddShortcutDialogSilent() {
      this.addShortcutInfo = getStore(`U_POPUP_STORE_${this.param.userId}`) || {};
    },

    setAddShortcutDialogSilent(updateTime) {
      const addShortcutInfo = {
        count: (this.addShortcutInfo?.count || 0) + 1,
        lastTime: updateTime,
      };
      setWithExactExpireUnit(`U_POPUP_STORE_${this.param.userId}`, addShortcutInfo, 30000, "D");
      this.getAddShortcutDialogSilent();
    },

    showDiscountDialogFunction() {
      if (
        (!this.checkDialogOpenStatus() || this.showDiscountDialog) &&
        (this.isLimitIncreaseRecord || this.isRateDecreaseRecord) &&
        this.storeCreditChangeRecordId !== this.creditInfo.creditChange.creditChangeRecordId
      ) {
        this.clickShowDiscount = false;
        this.nonClickShowDiscount = true;
        this.showDiscountDialog = true;
        setTimeout(() => {
          report("wallet_page_view", {
            page_name: "loan_index_page_promotion_auto_dlg",
            promotion_type: this.discountInfoList.map((item) => item.type),
          });
        }, 0);
      } else if (this.showDiscountDialog && this.nonClickShowDiscount) {
        this.clickShowDiscount = false;
        this.nonClickShowDiscount = false;
        this.showDiscountDialog = false;
      }
    },

    async showMarketingDialogFunction(functions) {
      if (!this.checkDialogOpenStatus()) {
        await showFloorDialog("loan_index", functions);
      }
    },

    showWelcomeDialogFunction() {
      if (
        !this.checkDialogOpenStatus() &&
        this.firstEnter &&
        (this.isUnknownLimitOrInvalid || this.isNotAccessOrLogout)
      ) {
        this.showWelcomeDialog = true;
      }
    },

    async getChangeAgreement() {
      const signInfoDict = {};
      let isChangeUserProtocol = false;
      let isChangePrivacyProtocol = false;
      const resq = await request("/general/api/ams/user/queryAgreement", {
        agrInfo: [
          {
            agrType: USER_PROTOCAL_CODE,
            country: this.param.country,
            branchId: 0,
            language: "zh-CN",
            isAgree: true,
          },
          {
            agrType: PRIVACY_PROTOCAL_CODE,
            country: this.param.country,
            branchId: 0,
            language: "zh-CN",
            isAgree: true,
          },
        ],
      });
      if (resq?.code === 0 && resq?.data?.errorCode === "0") {
        const { signInfo } = resq.data;
        const updateSignInfo = signInfo.filter((item) => item.needSign === "true");
        this.updateSignInfo = updateSignInfo;
        if (updateSignInfo.length > 0) {
          const resg = await request("/general/api/ams/getChangeAgreement", {
            queryInfoList: updateSignInfo.map((item) => ({
              agrType: item.agrType,
              country: item.country,
              branchId: item.branchId,
              language: this.param.language,
              version: item.newestVersion, // 假设你想要使用最新版本
            })),
          });
          const newSignInfo = resg.data?.noticeInfoList || [];
          signInfoDict.userProtocal =
            newSignInfo.filter((item) => item.agrType === USER_PROTOCAL_CODE)[0] || null;
          signInfoDict.privacyProtocal =
            newSignInfo.filter((item) => item.agrType === PRIVACY_PROTOCAL_CODE)[0] || null;
          if (signInfoDict.userProtocal) isChangeUserProtocol = true;
          if (signInfoDict.privacyProtocal) isChangePrivacyProtocol = true;
        }
      }
      if (isChangeUserProtocol || isChangePrivacyProtocol) {
        this.signInfo = { ...this.signInfo, ...signInfoDict };
        this.isChangeUserProtocol = isChangeUserProtocol;
        this.isChangePrivacyProtocol = isChangePrivacyProtocol;
        this.isShowProtocolDialog = true;
      } else {
        this.isShowProtocolDialog = false;
      }
    },

    async getSuggestProducts() {
      const res = await getSuggestList();
      const suggestProducts = res.data || [];
      this.suggestProducts = this.handleSuppliers(suggestProducts);
      this.adActivityListCode.suggestProducts = res.code;
    },

    async getAdActivityList(
      spaceCodes = ["loan_home_banner", "loan_home_top_banner", "loan_home_banner_widescreen"],
    ) {
      const res = await getAdActivityListV2(spaceCodes);
      if (spaceCodes.includes("loan_home_banner")) {
        this.walletBanners = res.data?.loan_home_banner?.activityList || [];
        this.adActivityListCode.walletBanners = res.code;
      }
      if (spaceCodes.includes("loan_home_top_banner")) {
        this.topBanner.loan_home_top_banner = res.data?.loan_home_top_banner?.activityList || [];
        this.adActivityListCode.loan_home_top_banner = res.code;
        this.currentTopBanner = this.topBanner.loan_home_top_banner || [];
      }
      if (spaceCodes.includes("loan_home_banner_widescreen")) {
        this.wideScreenWalletBanners = res.data?.loan_home_banner_widescreen?.activityList || [];
        this.adActivityListCode.wideScreenWalletBanners = res.code;
      }
    },

    async initialSupplierInfoList() {
      const res = await request("/loan/api/config/apiSupplier/list", {});
      this.supplierInfoList = res.data || [];
    },

    async getCouponPageUrl(itemNames = ["loan_home_coupon_info_v2"]) {
      const res = await request("/loan/api/config/getDictMap", {
        itemNames,
      });
      this.myCouponData = {};
      if (itemNames.includes("loan_home_coupon_info_v2")) {
        res?.data?.loan_home_coupon_info_v2?.forEach((item) => {
          this.myCouponData[item.name] = item.value;
        });
      }
      if (itemNames.includes("loan_home_page_redit_info")) {
        this.creditInfo.dictCreditInfo = this.creditInfo.dictCreditInfo || {};
        res?.data?.loan_home_page_redit_info?.forEach((item) => {
          this.creditInfo.dictCreditInfo[item.name] = item.value;
        });
      }
    },

    async initialServer() {
      if (this.param.amount || this.param.targetPage) {
        this.isPageDisplay = false;
        return;
      }
      if (
        this.param.isActivity === "1" &&
        (this.param.sdkVersionCode < 90004000 || this.param.isLogin)
      ) {
        this.isRransfer = true;
      }
      try {
        this.langType = (this.param.language || "zh-CN").startsWith("en") ? 1 : 0;
        this.translationData = await this.loadTranslations();
        const reqList = [this.initialSupplierInfoList(), this.getSuggestProducts()];
        reqList.push(this.getAdActivityList());
        await Promise.all(reqList);
        // 初始化文案
        await this.initialTextServer();
      } catch (error) {
        this.errorCode = 403;
      }
    },

    async queryApplyStatus() {
      if (this.loanProcess && this.loanProcess.applyNo && this.loanProcess.isApplied) {
        try {
          const { applyNo } = this.loanProcess;
          const res = await request("/loan/api/loan/queryApplyStatus", { applyNo });
          if (res) {
            this.loanOrderStatus = res.data;
            return true;
          }
        } catch (error) {
          return false;
        }
      }
      return false;
    },

    async checkCouponcome() {
      const req = await request("/loan/api/coupon/list", {}, { mock: false });
      const couponList = req?.data || [];
      if (req?.code === 0) {
        const firstIndex = couponList.findIndex((item) => item.status === 1);
        // 优惠券状态正常
        if (firstIndex > -1) {
          return {
            isCouponIncrease: true,
            couponTitle: couponList[firstIndex].couponName,
          };
        }
      }
      return {
        isCouponIncrease: false,
      };
    },

    getScreenSize() {
      this.screenSize.screenWidth = window.innerWidth;
      this.screenSize.screenHeight = window.innerHeight;
    },

    async getAddUrlData() {
      const res = await request(
        "/loan/api/credit/addUrl",
        {},
        { isHideOffline: true, timeout: 10000 },
      );
      const { data: addUrlData } = res || {};
      if (res.code !== 0) {
        this.retryTimes += 1;
        if (this.retryTimes > 1) {
          this.increaseAmountReduceInterestData = { loading: false };
          // 3次调用失败，则获取提额降息入口状态失败
          this.getCreditChange = false;
          return;
        }
        setTimeout(() => {
          this.increaseAmountReduceInterestData = { loading: true };
          // 调用失败，或者状态异常，立马重试
          this.getAddUrlData();
        }, 1000);
      } else if (addUrlData?.status === 0) {
        const { validSeconds, url } = addUrlData;
        const delay = (validSeconds - 10) * 1000;
        this.increaseAmountReduceInterestData = { loading: false, url, validSeconds };
        this.getCreditChange = true;
        if (this.addUrlTimer) {
          clearTimeout(this.addUrlTimer);
          this.addUrlTimer = null;
        }
        this.addUrlTimer = setTimeout(() => {
          this.getAddUrlData();
        }, delay);
      } else {
        // 没有资格，则获取提额降息入口状态失败
        this.getCreditChange = false;
      }
    },

    async initialClient(cb) {
      // AR1059DDD 自助提额降息
      this.isValidVersion = this.param.sdkVersionCode && this.param.sdkVersionCode >= 80003000;
      this.retryTimes = 0;
      const res = await Promise.all([
        request("/loan/api/credit/info", {}),
        request("/loan/api/repay/plan", {}),
        this.queryApplyStatus(),
        this.checkCouponcome(),
        this.isValidVersion ? this.getAddUrlData() : Promise.resolve(),
      ]);
      if (res[0] && res[0].code !== 0) {
        this.cardDisplayStatus = 2;
        if (res[0].code === 170004) {
          this.userError = true;
        }
        return;
      }
      const creditInfo = res[0].data;
      this.isLimitExpire = creditInfo?.status === 3 && creditInfo?.cancelStatus === 0;
      if (this.isLimitExpire) {
        delete creditInfo.flowNo;
      }
      this.isRepaying =
        creditInfo?.repayInfo?.repayStatus === 1 && creditInfo?.overdueInfo?.overdueAmount > 0;
      let realNameData = {};
      if ([1].includes(creditInfo.accessResult) && [0, 3].includes(creditInfo.status)) {
        realNameData = await request("/loan/api/user/v2/getUserRealNameInfo", {});
      }
      // 已准入后获取CP信息
      if (
        ([1].includes(creditInfo.accessResult) && ![0, 3, 4].includes(creditInfo.status)) ||
        this.isLimitExpire
      ) {
        this.cpInfo = await getCurrentCpInfo();
        if (this.cpInfo?.supplierId !== creditInfo.supplier) {
          this.cpInfo = null;
          delCurrentCpInfo();
          this.cpInfo = await getCurrentCpInfo();
        }
      } else {
        this.cpInfo = null;
      }
      // 获取顶部Banner
      if (
        typeof this.adActivityListCode.loan_home_top_banner === "number" &&
        this.adActivityListCode.loan_home_top_banner !== 0
      ) {
        await this.getAdActivityList(["loan_home_top_banner"]);
      }
      const isHomeHideShowAnimation = getStore("U_HOME_HIDE_SHOW_ANIMATION") || false;
      if (isHomeHideShowAnimation && cb) {
        cb();
        await new Promise((resolve) => {
          setTimeout(resolve, 300);
        });
        if (this.isFromLogin) {
          this.isLoginHandleDataAfter = true;
          this.isFromLogin = false;
        }
        await this.handleData(res[0].data, res[1].data, res[3], realNameData);
        del("U_HOME_HIDE_SHOW_ANIMATION");
      } else {
        await this.handleData(res[0].data, res[1].data, res[3], realNameData);
      }
    },

    async handleData(creditInfoParam, repayPlans, couponcomeStatus, realNameData) {
      const creditInfo = creditInfoParam;
      this.canCreditChange = creditInfo.canCreditChange;
      // 处理存在临额的情况
      if (creditInfo.tempLimitInfo) {
        creditInfo.creditLimit =
          creditInfo.totalCreditLimit ||
          Big(creditInfo.creditLimit).plus(creditInfo.tempLimitInfo?.creditLimit || 0); // 总额度（+临额）
        creditInfo.remainLimit =
          creditInfo.totalAvailableLimit ||
          Big(creditInfo.remainLimit).plus(creditInfo.tempLimitInfo?.availableLimit || 0); // 总可用额度（+临额）
      }
      this.creditInfo = creditInfo;
      this.repayPlans = repayPlans;
      this.couponcomeStatus = couponcomeStatus;
      // 获取当前用户状态
      this.isNotAccessOrLogout = [0, 3].includes(creditInfo.accessResult) || false;
      this.isAccess = [1].includes(creditInfo.accessResult) || false;
      this.isAccessFailed = [2].includes(creditInfo.accessResult) || false;
      this.isUnknownLimitOrInvalid = this.isAccess && [0, 3].includes(creditInfo.status);
      this.isLimitNormal = this.isAccess && [1].includes(creditInfo.status);
      this.isLimitAppling = this.isAccess && [2].includes(creditInfo.status);
      this.isLimitApplyFail = this.isAccess && [4].includes(creditInfo.status);
      if (this.isUnknownLimitOrInvalid) {
        this.realNameInfo = realNameData.data || {};
      }
      // 如果没有准入管控时间，就认为可以直接重新申请，等同于已准入的状态
      if (
        this.isAccessFailed &&
        this.pageStartTime + (this.creditInfo.refuseControlSeconds || 0) * 1000 <= Date.now()
      ) {
        this.isNotAccessOrLogout = true;
        this.isAccessFailed = false;
      }
      // 如果没有授信管控时间，就认为可以直接重新申请，等同于已准入未授信的状态
      if (
        this.isLimitApplyFail &&
        this.pageStartTime + (this.creditInfo.refuseControlSeconds || 0) * 1000 <= Date.now() &&
        this.pageStartTime + (this.creditInfo.greyExpireSeconds || 0) * 1000 <= Date.now()
      ) {
        this.isUnknownLimitOrInvalid = true;
        this.isLimitApplyFail = false;
      }
      this.repaySupplementaryInfo = (Array.isArray(repayPlans) ? repayPlans : []).reduce(
        (acc, plan) => {
          const orderAmount = (
            Array.isArray(plan.repayPlanTerms) ? plan.repayPlanTerms : []
          ).reduce((sum, term) => sum.plus(term.termAmount || 0), Big(0));
          return {
            ordersNumber: acc.ordersNumber + 1,
            totalDuerepay: acc.totalDuerepay.plus(orderAmount),
          };
        },
        { ordersNumber: 0, totalDuerepay: Big(0) },
      );
      // 获取详细状态
      this.isLimitNormalNoLoanApply = this.isLimitNormal && !creditInfo.limitUseErrStatus;
      this.isLimitNormalLoanAppling = this.isLimitNormal && creditInfo.limitUseErrStatus === 1;
      this.isRepayOverdue = this.isLimitNormal && creditInfo.limitUseErrStatus === 2;
      this.isRepaying =
        creditInfo?.repayInfo?.repayStatus === 1 && creditInfo?.overdueInfo?.overdueAmount > 0;
      this.isLimitNormalLoanNotAvailable = this.isLimitNormal && creditInfo.limitUseErrStatus === 3;
      this.isLimitNormalLoanOtherErr = this.isLimitNormal && creditInfo.limitUseErrStatus === 4;
      this.isWithoutLoan =
        (this.isLimitNormal || this.isLimitExpire) &&
        this.repaySupplementaryInfo.totalDuerepay.toNumber() === 0;
      this.isRepaymentInProgress =
        this.isLimitNormal && this.repaySupplementaryInfo.totalDuerepay.toNumber() !== 0;
      // 如果没有管控时间，就认为可以直接重新借款，等同于已授信的状态
      if (
        this.isLimitNormalLoanOtherErr &&
        this.pageStartTime + (this.creditInfo.greyExpireSeconds || 0) * 1000 <= Date.now()
      ) {
        this.isLimitNormalNoLoanApply = true;
        this.isLimitNormalLoanOtherErr = false;
      }

      this.isLimitNormalInsufficient =
        this.isLimitNormalNoLoanApply &&
        Big(creditInfo.remainLimit).lt(creditInfo.minLoan) &&
        Big(creditInfo.remainLimit).gt(0);
      this.isLimitNormalZero = this.isLimitNormalNoLoanApply && Big(creditInfo.remainLimit).eq(0);

      this.isLimitNormalLoanUnsubmitted =
        !this.isLimitNormalInsufficient && !this.isLimitNormalZero;
      // 读取本地借款中断缓存，审核中同样刷新isLimitNormalNoLoanApply
      this.isLimitNormalNoLoanApply = this.isLimitNormalNoLoanApply && !this.loanProcess.loanInfo;
      if (this.isLimitNormalLoanUnsubmitted && this.loanProcess?.loanInfo) {
        if (this.loanOrderStatus) {
          // 借款订单已提交
          if (this.loanOrderStatus?.applyStatus === 1) {
            this.isLimitNormalLoanUnsubmitted = false; // 不展示未提交订单
            this.isLimitNormalLoanAppling = true; // 展示借款审核中
          } else if (
            this.loanOrderStatus?.applyStatus === 0 ||
            this.loanOrderStatus?.applyStatus === 6
          ) {
            this.isLimitNormalLoanUnsubmitted = true;
          } else {
            del(`U_LOAN_PROCESS_${this.param.userId}`);
            this.isLimitNormalLoanUnsubmitted = false;
          }
        } else {
          // 借款订单未提交
          this.isLimitNormalLoanUnsubmitted = true;
        }
      } else {
        // 没有借款缓存或者借款缓存有异常
        this.isLimitNormalLoanUnsubmitted = false;
      }

      // 授信/额度状态刷新之后，再判断调额/调价/优惠券到账展示的状态
      const tempAvailableLimitCheck =
        (typeof this.creditInfo.tempLimitInfo?.availableLimit === "number" &&
          Big(this.creditInfo.tempLimitInfo?.availableLimit || 0).gt(0)) ||
        (this.creditInfo.tempLimitInfo &&
          typeof this.creditInfo.tempLimitInfo?.availableLimit !== "number");
      this.isLimitIncrease =
        !this.isRepayOverdue &&
        !this.isUnknownLimitOrInvalid &&
        tempAvailableLimitCheck &&
        typeof this.creditInfo.tempLimitInfo.tempLimitValidDays === "number" &&
        this.creditInfo.tempLimitInfo.tempLimitValidDays >= 0;
      this.isRateDecrease =
        !this.isRepayOverdue &&
        !this.isUnknownLimitOrInvalid &&
        this.creditInfo.productInfos?.some(
          (info) => typeof info.tempPriceValidDays === "number" && info.tempPriceValidDays >= 0,
        );
      this.isLimitIncreaseRecord =
        !this.isRepayOverdue &&
        !this.isUnknownLimitOrInvalid &&
        (this.creditInfo.creditChange?.limitChangeType || 0) === 1;
      this.isRateDecreaseRecord =
        !this.isRepayOverdue &&
        !this.isUnknownLimitOrInvalid &&
        (this.creditInfo.creditChange?.rateChangeType || 0) === 2;
      this.isCouponIncrease =
        this.isLimitNormalNoLoanApply &&
        !Big(creditInfo.remainLimit).lt(creditInfo.minLoan) &&
        couponcomeStatus.isCouponIncrease;

      // 设置调额调息优惠弹窗的背景图
      this.setDiscountBackground(this.isLimitIncreaseRecord, this.isRateDecreaseRecord);

      // 已准入后获取CP信息
      if (this.isAccess && !this.isUnknownLimitOrInvalid && !this.isLimitApplyFail) {
        if (!this.cpInfo) {
          this.cardDisplayStatus = 2;
          return;
        }
      }

      this.initialTextClient();
      this.cardDisplayStatus = 1;
      this.newGusetCoupon = this.isNotAccessOrLogout || this.isUnknownLimitOrInvalid;
    },

    setDiscountBackground(limitType, rateType) {
      const theme =
        window?.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches
          ? "dark"
          : "light";
      if (limitType && rateType) {
        this.discountDialogBackground = `url(https://contentplatform-drcn.hihonorcdn.com/honorWallet/loan/imgs/entrance/Discount_Background_LR_${theme}.webp?v=2)`;
      } else if (rateType) {
        this.discountDialogBackground = `url(https://contentplatform-drcn.hihonorcdn.com/honorWallet/loan/imgs/entrance/Discount_Background_R_${theme}.webp?v=2)`;
      } else if (limitType) {
        this.discountDialogBackground = `url(https://contentplatform-drcn.hihonorcdn.com/honorWallet/loan/imgs/entrance/Discount_Background_L_${theme}.webp?v=2)`;
      }
    },

    initialTextServer() {
      this.navigationHeader = this.translationData.navigationHeader;
      this.addShortcutDialogText = this.translationData.addShortcutDialogText;
      this.addShortcutToastText = this.translationData.addShortcutToastText;
      this.addShortcutFailedToastText = this.translationData.addShortcutFailedToastText;
      this.welcomeDialogTitle = this.translationData.welcomeDialogTitle;
      this.welcomeDialogInfo = this.translationData.welcomeDialogInfo;
      this.formattedWelcomeButtonText = this.translationData.formattedWelcomeButtonText;
      this.loanSupplierText = this.translationData.loanSupplierText1;
      this.suggestProductTitle = this.translationData.suggestProductTitle;
      this.disclaimerText = this.translationData.disclaimerText;
      this.noticeText = this.translationData.noticeText;
      this.customerServiceDialogButtonText = this.translationData.customerServiceDialogButtonText;
      this.cancelButtonText = this.translationData.cancelButtonText;
      this.contactService = this.translationData.contactService;
      this.addShortButtonText = this.translationData.addShortButtonText;
      this.suggestProductText = this.translationData.suggestProductText;
      this.suggestProductButtonText = this.translationData.suggestProductButtonText;
      this.sloganHeader = this.translationData.sloganHeader;
      this.sloganText1 = this.translationData.sloganText1;
      this.sloganText2 = this.translationData.sloganText2;
      this.sloganText3 = this.translationData.sloganText3;
      this.keepSilentCheckboxText = this.translationData.keepSilentCheckboxText;
      this.supplierInfoDescription = this.translationData.supplierInfoDescription;
      this.supplierInfoClarSt = this.translationData.supplierInfoClarSt;
      this.supplierInfoButtonText = this.translationData.supplierInfoButtonText;
      this.loadingText = this.translationData.loadingText;
      this.serverErrorText = this.translationData.serverErrorText;
      this.userLogoutText = this.translationData.userLogoutText;
      this.jumpOutText = this.translationData.jumpOutText;
    },

    initialTextClient() {
      // 初始化文案
      this.formattedOverdueWarning = this.translationData.formattedOverdueWarning;
      this.formattedIntroduction = this.translationData.formattedIntroduction;
      this.loanProductOwner = this.translationData.loanProductOwner;
      this.sloganInfo1 = this.formatTemplateString(
        this.translationData.sloganInfo1,
        Big(this.creditInfo.dictCreditInfo?.creditLimit).div(1000000).toString(),
      );
      this.sloganInfo2 = this.formatTemplateString(
        this.translationData.sloganInfo2,
        this.creditInfo.dictCreditInfo?.apr,
      );
      this.sloganInfo3 = this.formatTemplateString(
        this.translationData.sloganInfo3,
        this.creditInfo.dictCreditInfo?.approvalSeconds,
      );

      this.customerServiceDialogText = this.formatTemplateString(
        this.translationData.customerServiceDialogText,
        this.langType === 0 ? this.cpInfo?.supplierName : this.cpInfo?.supplierEnName,
      );

      this.loanSupplierText =
        this.isLimitNormal || this.isLimitExpire
          ? this.formatTemplateString(
              this.translationData.loanSupplierText2,
              this.langType === 0 ? this.cpInfo?.supplierName : this.cpInfo?.supplierEnName,
            )
          : this.translationData.loanSupplierText1;

      if (this.isLimitNormal) {
        if (this.isRepayOverdue) {
          if (this.isRepaying) {
            this.loanLimitTitle = this.translationData.loanLimitTitle6;
          } else {
            this.loanLimitTitle = this.translationData.loanLimitTitle3;
          }
        } else if (
          this.isLimitNormalInsufficient ||
          this.isLimitNormalZero ||
          this.isLimitNormalLoanNotAvailable
        ) {
          this.loanLimitTitle = this.translationData.loanLimitTitle4;
        } else {
          this.loanLimitTitle = this.translationData.loanLimitTitle2;
        }
      } else if (this.isLimitExpire) {
        this.loanLimitTitle = this.translationData.loanLimitTitle2;
      } else {
        this.loanLimitTitle = this.translationData.loanLimitTitle1;
      }
      if (this.param.sdkVersionCode >= 90004000 && !this.param.userId) {
        this.loanLimitTitle = this.translationData.loanLimitTitle5;
      }

      this.formattedLimitAmount = (() => {
        const amount = Big(
          this.isLimitNormal || this.isLimitExpire
            ? this.creditInfo.remainLimit
            : this.creditInfo.dictCreditInfo?.creditLimit || 0,
        )
          .div(100)
          .toString();
        return amountNumberFormat(amount, 0);
      })();

      this.formattedOverdueAmount = (() => {
        const amount = Big(this.creditInfo.overdueInfo?.overdueAmount || 0)
          .div(100)
          .toString();
        return amountNumberFormat(amount);
      })();

      if (this.isRateDecrease) {
        this.rateDiscountText = this.translationData.rateDiscountText;
      }

      if (this.isLimitIncrease) {
        if (typeof this.creditInfo.tempLimitInfo.availableLimit === "number") {
          this.loanLimitIncreaseTitle = this.formatTemplateString(
            this.translationData.loanLimitIncreaseTitle,
            amountNumberFormat(
              Big(this.creditInfo.tempLimitInfo.availableLimit).div(100).toString(),
              0,
            ),
          );
        } else {
          this.loanLimitIncreaseTitle = this.formatTemplateString(
            this.translationData.loanLimitIncreaseTitleWithoutAvailableLimit,
          );
        }
      }

      if (this.isCouponIncrease) {
        this.couponText = this.formatTemplateString(
          this.translationData.couponText,
          this.couponcomeStatus.couponTitle,
        );
      }

      if (this.isLimitNormal || this.isLimitExpire) {
        const productInfos = this.creditInfo.productInfos || [];
        const { proDayRate, proApr, oApr, validDays } = productInfos.reduce(
          (minRates, productInfo) => {
            // 处理productInfo非期数关联的价格
            let itemDayRate = null;
            let itemApr = null;
            let itemOldApr = null;
            let itemValidDays = null;
            if (productInfo.dayRate) {
              itemDayRate =
                productInfo.tempDayRate &&
                Big(this.parseFloatWithPrecision(productInfo.tempDayRate)).lt(
                  this.parseFloatWithPrecision(productInfo.dayRate),
                )
                  ? this.parseFloatWithPrecision(productInfo.tempDayRate)
                  : this.parseFloatWithPrecision(productInfo.dayRate);
              itemApr =
                productInfo.tempApr &&
                Big(this.parseFloatWithPrecision(productInfo.tempApr)).lt(
                  this.parseFloatWithPrecision(productInfo.apr),
                )
                  ? this.parseFloatWithPrecision(productInfo.tempApr)
                  : this.parseFloatWithPrecision(productInfo.apr);
              itemOldApr = this.parseFloatWithPrecision(productInfo.apr);
              itemValidDays =
                typeof productInfo.tempPriceValidDays === "number"
                  ? productInfo.tempPriceValidDays
                  : -1;
            }
            // 处理productInfo期数关联的价格
            if (productInfo.termPriceInfos) {
              productInfo.termPriceInfos.forEach((termPriceInfo) => {
                if (
                  termPriceInfo.dayRate &&
                  Big(this.parseFloatWithPrecision(termPriceInfo.apr)).lt(
                    this.parseFloatWithPrecision(itemApr),
                  )
                ) {
                  itemApr = this.parseFloatWithPrecision(termPriceInfo.apr);
                  itemDayRate = Big.min(
                    itemDayRate,
                    this.parseFloatWithPrecision(termPriceInfo.dayRate),
                  );
                  itemOldApr = Big.min(
                    itemOldApr,
                    this.parseFloatWithPrecision(termPriceInfo.oldApr),
                  );
                  itemValidDays =
                    typeof termPriceInfo.tempPriceValidDays === "number"
                      ? Math.max(itemValidDays, termPriceInfo.validDays)
                      : itemValidDays;
                }
              });
            }
            // 初始化minRates或者比较找出最小值
            return {
              proDayRate:
                !minRates.proDayRate || Big(itemDayRate || 1).lt(minRates.proDayRate)
                  ? itemDayRate
                  : Big(minRates.proDayRate),
              proApr:
                !minRates.proApr || Big(itemApr || 40).lt(minRates.proApr)
                  ? itemApr
                  : Big(minRates.proApr),
              oApr:
                !minRates.oApr || Big(itemOldApr || 40).lt(minRates.oApr)
                  ? itemOldApr
                  : Big(minRates.oApr),
              validDays:
                !minRates.validDays ||
                (typeof itemValidDays === "number" && itemValidDays > minRates.validDays)
                  ? itemValidDays
                  : minRates.validDays,
            };
          },
          { proDayRate: null, proApr: null, oApr: null, validDays: null },
        );
        this.rateInfos = { proDayRate, proApr, oApr, validDays };

        // 降息场景兜底处理(当前年利率proApr必须小于老年利率oApr)
        this.isRateDecrease = this.isRateDecrease && proApr && Big(proApr).lt(oApr || "32.8");

        // 已获取额度用户的利率文案
        if (proApr !== null && proDayRate !== null) {
          if (this.isRateDecrease) {
            this.formattedRateTextA = this.formatTemplateString(
              this.translationData.formattedRateText1a,
              proApr.toString(),
            );
            this.formattedRateTextC = this.formatTemplateString(
              this.translationData.formattedRateText1c,
              oApr.toString(),
            );
            this.formattedRateTextF = this.formatTemplateString(
              this.translationData.formattedRateText1f,
              Big(proDayRate).mul(100).toString(),
            );
          } else {
            this.formattedRateTextA = this.formatTemplateString(
              this.translationData.formattedRateText1a,
              proApr.toString(),
            );
            this.formattedRateTextD = this.formatTemplateString(
              this.translationData.formattedRateText1d,
              Big(proDayRate).mul(100).toString(),
            );
          }
        }
      } else {
        // 未获取额度用户的利率文案
        const dayRate = this.parseFloatWithPrecision(
          this.creditInfo.dictCreditInfo?.dayRate || "0.01",
        ).toString();
        const apr = this.parseFloatWithPrecision(
          this.creditInfo.dictCreditInfo?.apr || "3.7",
        ).toString();
        this.formattedRateTextA = this.formatTemplateString(
          this.translationData.formattedRateText2,
          apr.toString(),
          Big(dayRate).mul(100).toString(),
        );
        this.formattedRateTextB = "";
        this.formattedRateTextC = "";
        this.formattedRateTextD = "";
      }

      this.aboveButtonText = (() => {
        if (this.isLimitApplyFail || this.isAccessFailed) {
          return this.translationData.notMeetingLimitApplyCondition;
        }
        if (this.isLimitNormalLoanOtherErr) {
          return this.translationData.notMeetingLoanApplyCondition;
        }
        if (this.isLimitNormalInsufficient) {
          return this.formatTemplateString(
            this.translationData.LimitNormalInsufficientCondition,
            amountNumberFormat(Big(this.creditInfo.minLoan).div(100), 0),
          );
        }
        if (this.isLimitNormalLoanNotAvailable) {
          return this.formatTemplateString(
            this.translationData.LimitNotAvailableCondition,
            this.langType === 0 ? this.cpInfo?.supplierName : this.cpInfo?.supplierEnName,
            this.cpInfo.supplierContact,
          );
        }
        return "";
      })();

      this.formattedButtonText = (() => {
        if (this.param.sdkVersionCode >= 90004000 && !this.param.userId) {
          // 未登录
          return this.translationData.formattedUnloginButtonText;
        }
        if (this.isLimitExpire) {
          return this.translationData.formattedButtonText13;
        }
        if (this.isAccessFailed || this.isLimitApplyFail) {
          // 准入失败 或 授信失败
          return this.translationData.formattedButtonText1;
        }
        if (this.isNotAccessOrLogout || this.isUnknownLimitOrInvalid) {
          // 未准入
          // 准入成功+未申请额度
          // 激活失败
          if (
            (this.creditInfo.flowNo && this.realNameInfo?.status === 1) ||
            this.creditInfo.reActiveStatus === 1
          ) {
            return this.translationData.formattedButtonText12;
          }
          return this.translationData.formattedButtonText2;
        }
        if (this.isLimitNormal) {
          // 准入成功+已有额度
          // 还需要实现继续借款/放款中/借款失败
          if (this.isLimitNormalLoanAppling) {
            return this.translationData.formattedButtonText6;
          }
          if (this.isLimitNormalLoanOtherErr) {
            return "";
          }
          if (this.isRepayOverdue) {
            if (this.creditInfo.repayInfo?.repayStatus === 1) {
              return this.translationData.formattedRepayingButtonText;
            }
            return this.translationData.formattedButtonText11;
          }
          return this.translationData.formattedButtonText3;
        }

        if (this.isLimitAppling) {
          // 准入成功+申请中
          return this.translationData.formattedButtonText4;
        }
        return "";
      })();

      this.formattedOverdueInfo = (() => {
        if (this.isRepayOverdue) {
          const penalty = Big(this.creditInfo.overdueInfo?.overduePenalty || 0).div(100);
          return this.formatTemplateString(
            this.translationData.formattedOverdueInfo,
            this.creditInfo.overdueInfo?.overdueOrder <= 1
              ? this.formatTemplateString(
                  this.translationData.formattedOverdueInfoSubject,
                  this.creditInfo.overdueInfo?.overdueOrder,
                  this.creditInfo.overdueInfo?.overdueDays <= 1
                    ? this.formatTemplateString(
                        this.translationData.dayInfo,
                        this.creditInfo.overdueInfo?.overdueDays,
                      )
                    : this.formatTemplateString(
                        this.translationData.dayInfos,
                        this.creditInfo.overdueInfo?.overdueDays,
                      ),
                )
              : this.formatTemplateString(
                  this.translationData.formattedOverdueInfoSubjects,
                  this.creditInfo.overdueInfo?.overdueOrder,
                  this.creditInfo.overdueInfo?.overdueDays <= 1
                    ? this.formatTemplateString(
                        this.translationData.dayInfo,
                        this.creditInfo.overdueInfo?.overdueDays,
                      )
                    : this.formatTemplateString(
                        this.translationData.dayInfos,
                        this.creditInfo.overdueInfo?.overdueDays,
                      ),
                ),
            penalty,
          );
        }
        return "";
      })();

      this.cellTitleWithoutLoan = this.translationData.cellTitleWithoutLoan;

      this.cellTextWithoutLoan = this.translationData.cellTextWithoutLoan;

      this.cellTitleWithLoan = this.translationData.cellTitleWithLoan;

      this.formattedRepaymentAmount = amountNumberFormat(
        (this.repaySupplementaryInfo?.totalDuerepay || Big(0)).div(100).toNumber().toFixed(2),
      );

      this.formattedRepaymentText = this.isRepayOverdue
        ? this.formatTemplateString(
            this.translationData.formattedRepaymentText1,
            this.repaySupplementaryInfo.ordersNumber <= 1
              ? this.formatTemplateString(
                  this.translationData.orderNumInfo,
                  this.repaySupplementaryInfo.ordersNumber,
                )
              : this.formatTemplateString(
                  this.translationData.orderNumInfos,
                  this.repaySupplementaryInfo.ordersNumber,
                ),
            this.creditInfo.overdueInfo.overdueOrder,
          )
        : this.formatTemplateString(
            this.translationData.formattedRepaymentText2,
            this.repaySupplementaryInfo.ordersNumber <= 1
              ? this.formatTemplateString(
                  this.translationData.orderNumInfo,
                  this.repaySupplementaryInfo.ordersNumber,
                )
              : this.formatTemplateString(
                  this.translationData.orderNumInfos,
                  this.repaySupplementaryInfo.ordersNumber,
                ),
          );
      this.increaseAmountReduceInterestText = this.translationData.increaseAmountReduceInterestText;

      // 最后刷新调额调价记录下发所对应弹窗的展示
      if (
        (this.isLimitIncreaseRecord || this.isRateDecreaseRecord) &&
        this.creditInfo.creditChange?.creditChangeRecordId !== this.storeCreditChangeRecordId
      ) {
        this.discountInfoList = [];
        this.discountDialogInfo = "";
        if (this.isLimitIncreaseRecord) {
          const showTag =
            typeof this.creditInfo.creditChange.tempLimitValidDays === "number" &&
            this.creditInfo.creditChange.tempLimitValidDays >= 0;
          let discountValidTimeText = "";
          if (showTag) {
            if (this.creditInfo.creditChange.tempLimitValidDays === 0) {
              discountValidTimeText = this.translationData.discountExpiredTodayText;
            } else {
              discountValidTimeText = this.formatTemplateString(
                this.creditInfo.creditChange.tempLimitValidDays > 1
                  ? this.formatTemplateString(
                      this.translationData.discountValidTimeText,
                      this.translationData.dayInfos,
                    )
                  : this.formatTemplateString(
                      this.translationData.discountValidTimeText,
                      this.translationData.dayInfo,
                    ),
                this.creditInfo.creditChange.tempLimitValidDays,
              );
            }
          }
          this.discountInfoList.push({
            type: "limit",
            showTag,
            validTime: discountValidTimeText,
            title: this.translationData.limitDiscountTitle,
            curvalue: amountNumberFormat(
              Big(this.creditInfo.creditChange.totalAmount || 0).div(100),
              0,
            ),
            oldvalue: amountNumberFormat(
              Big(this.creditInfo.creditChange.oldLimit || 0).div(100),
              0,
            ),
          });
          this.discountDialogInfo = this.formatTemplateString(
            this.translationData.limitDiscountInfo,
            amountNumberFormat(Big(this.creditInfo.remainLimit || 0).div(100), 0),
          );
        }
        if (this.isRateDecreaseRecord) {
          const showTag =
            typeof this.creditInfo.creditChange.tempPriceValidDays === "number" &&
            this.creditInfo.creditChange.tempPriceValidDays >= 0;
          let discountValidTimeText = "";
          if (showTag) {
            if (this.creditInfo.creditChange.tempPriceValidDays === 0) {
              discountValidTimeText = this.translationData.discountExpiredTodayText;
            } else {
              discountValidTimeText = this.formatTemplateString(
                this.creditInfo.creditChange.tempPriceValidDays > 1
                  ? this.formatTemplateString(
                      this.translationData.discountValidTimeText,
                      this.translationData.dayInfos,
                    )
                  : this.formatTemplateString(
                      this.translationData.discountValidTimeText,
                      this.translationData.dayInfo,
                    ),
                this.creditInfo.creditChange.tempPriceValidDays,
              );
            }
          }
          this.discountInfoList.push({
            type: "rate",
            showTag,
            validTime: discountValidTimeText,
            title: this.translationData.rateDiscountTitle,
            curvalue: showTag
              ? `${this.creditInfo.creditChange.tempApr}%`
              : `${this.creditInfo.creditChange.newApr}%`,
            oldvalue: `${this.creditInfo.creditChange.oldApr}%`,
          });
          if (!this.isLimitIncreaseRecord) {
            this.discountDialogInfo = this.formatTemplateString(
              this.translationData.rateDiscountInfo,
              showTag
                ? Big(this.creditInfo.creditChange.tempDayRate)
                    .mul(100)
                    .toString()
                    .replace(/\.?0+$/, "")
                : Big(this.creditInfo.creditChange.newDayRate)
                    .mul(100)
                    .toString()
                    .replace(/\.?0+$/, ""),
            );
          }
        }
        if (this.discountInfoList.some((item) => item.showTag === true)) {
          this.discountDialogInfo = `${this.discountDialogInfo}${this.translationData.validTimeDiscountInfo}`;
        }
        if (this.discountInfoList.length === 2) {
          this.discountDialogTitle = this.translationData.discountDialogTitleLimitRate;
        } else if (this.discountInfoList.length === 1) {
          if (this.discountInfoList[0].type === "limit") {
            if (this.discountInfoList[0].showTag) {
              // 临额
              this.discountDialogTitle = this.translationData.discountDialogTitleTempLimit;
            } else {
              // 固额
              this.discountDialogTitle = this.translationData.discountDialogTitleFixedLimit;
            }
          }
          if (this.discountInfoList[0].type === "rate") {
            if (this.discountInfoList[0].showTag) {
              // 临息
              this.discountDialogTitle = this.translationData.discountDialogTitleTempRate;
            } else {
              // 固息
              this.discountDialogTitle = this.translationData.discountDialogTitleFixedRate;
            }
          }
        }
        this.formattedDiscountButtonText = this.translationData.formattedButtonText3;
      }
    },

    async initialClientSide() {
      this.creditProcessProperty.emptySpaceStyle = {};
      if (this.isUnknownLimitOrInvalid && this.creditProcess?.realInfo) {
        if (this.realNameInfo?.status !== 1) {
          del(`U_CREDIT_PROCESS_V2_${this.param.userId}`);
          this.creditProcess = {};
        } else {
          if (!this.isLimitExpire) {
            this.formattedButtonText = this.translationData.formattedButtonText12;
          }
          // 获取未完成的授信流程需要认证的步骤
          let steps = 0;
          (this.creditProcess.verifyList || []).forEach((item) => {
            if (!this.creditProcess[item]) {
              steps += 1;
            }
          });
          steps = Math.max(1, steps - 1);
          this.guide2ContinueApplyText = this.formatTemplateString(
            steps > 1
              ? this.translationData.guide2ContinueApplyTexts
              : this.translationData.guide2ContinueApplyText,
            steps,
          );
          this.changeCooperativeProductText = this.translationData.changeCooperativeProductText;
          // 计算滚动播放的气泡的高度
          this.creditProcessProperty.shortTextWidth = this.calculateTextWidth(
            this.guide2ContinueApplyText,
          );
          // 短气泡的宽度
          this.creditProcessProperty.shortBubbleWidth = Math.min(
            this.creditProcessProperty.shortTextWidth + 28,
            window.innerWidth - 72,
          );
          this.creditProcessProperty.longTextWidth = this.calculateTextWidth(
            this.changeCooperativeProductText,
          );
          // 长气泡的宽度
          this.creditProcessProperty.longBubbleWidth = Math.min(
            this.creditProcessProperty.longTextWidth + 28,
            window.innerWidth - 72,
          );
          if (this.creditProcessProperty.shortTextWidth > window.innerWidth - 96) {
            const lineNumber = Math.ceil(
              this.creditProcessProperty.shortTextWidth / (window.innerWidth - 96),
            );
            this.creditProcessProperty.shortTextHeight = lineNumber * 16 + 8;
          }
          if (this.creditProcessProperty.longTextWidth > window.innerWidth - 96) {
            const lineNumber = Math.ceil(
              this.creditProcessProperty.longTextWidth / (window.innerWidth - 96),
            );
            this.creditProcessProperty.longTextHeight = lineNumber * 16 + 8;
          }
          this.creditProcessProperty.shortBubbleLeft =
            (window.innerWidth - 72 - this.creditProcessProperty.shortBubbleWidth) / 2;
          this.creditProcessProperty.longBubbleLeft =
            (window.innerWidth - 72 - this.creditProcessProperty.longBubbleWidth) / 2;
          this.creditProcessProperty.emptySpaceHeight = Math.max(
            68,
            this.creditProcessProperty.longTextHeight + 26,
          );
          this.creditProcessProperty.emptySpaceStyle = {
            height: `calc(var(--dp1) * ${Math.max(68, this.creditProcessProperty.longTextHeight + 26)})`,
          };
        }
      } else if (
        !this.isRepayOverdue &&
        !this.isLimitNormalLoanAppling &&
        !this.isLimitNormalLoanOtherErr &&
        !this.isLimitNormalLoanNotAvailable &&
        !this.isLimitNormalZero &&
        !this.isLimitNormalInsufficient &&
        this.isLimitNormalLoanUnsubmitted
      ) {
        this.formattedButtonText = this.translationData.formattedButtonText5;
      }
    },

    calculateTextWidth(text) {
      const span = document.createElement("span");
      span.style.visibility = "hidden";
      span.style.whiteSpace = "nowrap";
      span.style.fontSize = this.creditProcessProperty.fontSize; // 根据实际UI中的字体大小调整
      span.style.fontFamily = "Arial, sans-serif"; // 根据实际UI中的字体调整
      span.innerText = text;
      document.body.appendChild(span);
      const width = span.offsetWidth;
      document.body.removeChild(span);
      return width;
    },

    /**
     *
     * @param {*} updater       updater函数
     * @param {*} expireFuntion 过期函数
     * @param {*} expireTime    过期时间
     * @param {*} expireTimeInLocal 以本地时间为基准的过期时间
     * @param {*} updateTimerCallback 更新定时器的回调
     * @param {*} unit 时间单位，默认是毫秒
     */
    startExpireTimeUpdater(
      updater,
      expireFuntion,
      expireTime,
      expireTimeInLocal,
      updateTimerCallback,
      unit = "mS",
    ) {
      let timeout = null;
      const res = getDiffTime(expireTime, expireTimeInLocal, unit);
      const setNewTimeout = (interval) => {
        timeout = setTimeout(() => {
          const newTimer = this.startExpireTimeUpdater(
            updater,
            expireFuntion,
            expireTime,
            expireTimeInLocal,
            updateTimerCallback,
            unit,
          );
          if (updateTimerCallback) {
            updateTimerCallback(newTimer);
          }
        }, interval - 8);
      };

      if (res.interval === 1) {
        setNewTimeout(100);
      } else if (res.interval === 2) {
        setNewTimeout(1000);
      } else if (res.interval === 3) {
        setNewTimeout(60000);
      } else if (res.interval === 4) {
        setNewTimeout(3600000);
      } else if (res.interval === 5) {
        setNewTimeout(86400000);
      }

      if (res.status === 0) {
        // 状态可用
        expireFuntion();
      } else if (res.status === 1) {
        updater(this.formatTemplateString(this.translationData.formattedButtonText7, res.second));
      } else if (res.status === 2) {
        updater(
          this.formatTemplateString(
            this.translationData.formattedButtonText8,
            res.hour,
            res.minite,
          ),
        );
      } else if (res.status === 3) {
        updater(
          this.formatTemplateString(
            this.translationData.formattedButtonText9,
            this.translationData.month[res.month],
            res.day,
          ),
        );
      } else if (res.status === 4) {
        updater(
          this.formatTemplateStringInorder(
            this.translationData.formattedButtonText10,
            res.year,
            this.translationData.month[res.month],
            res.day,
          ),
        );
      }
      return timeout;
    },

    formatTemplateString(str, ...args) {
      let formatted = str;
      args.forEach((arg) => {
        formatted = formatted.replace("%s", arg);
      });
      return formatted;
    },

    formatTemplateStringInorder(str, ...args) {
      let formatted = str;
      let cnt = 1;
      args.forEach((arg) => {
        formatted = formatted.replace(`%${cnt}$s`, arg);
        cnt += 1;
      });
      return formatted;
    },

    async loadSession() {
      this.getAddShortcutDialogSilent();
      // 清除老版本的授信流程缓存
      del(`U_CREDIT_PROCESS_${this.param.userId}`);
      // 加载新版本的授信流程缓存
      this.creditProcess = getStore(`U_CREDIT_PROCESS_V2_${this.param.userId}`) || {};
      this.loanProcess = getStore(`U_LOAN_PROCESS_${this.param.userId}`) || {};
      this.storeCreditChangeRecordId =
        getStore(`U_CREDITCHANGE_STORE_${this.param.userId}`) || null;
    },

    loadWelcomeDialogSession() {
      if (getStore(`U_NOTFIRSTENTER_STORE_${this.param.userId}`) === null) {
        this.firstEnter = true;
      } else {
        this.firstEnter = false;
      }
    },

    /**
     * @typedef {Object} CreditCheckResult
     * @property {string} nextPage - 跳转页面
     * @property {number} accessResult - 准入结果
     */
    /**
     * 检查授信流程缓存
     * @returns {CreditCheckResult}
     */
    async checkCreditStore(e) {
      if (
        this.isUnknownLimitOrInvalid &&
        (this.creditInfo.flowNo || this.creditInfo.reActiveStatus === 1) &&
        this.realNameInfo.status === 1
      ) {
        const res = await request("/loan/api/bff/rebind", {
          flowNo: this.creditInfo.flowNo,
        });
        if (res.code === 0 && res.data?.accessResult === 0) {
          // 如果用户准入的flowNo发生变化或者supplier发生变化，更新本地授信流程缓存
          if (
            this.creditProcess?.flowNo !== res.data?.flowNo ||
            this.creditProcess?.supplier !== res.data?.supplier
          ) {
            const creditStore = {
              realInfo: "default",
              supplier: res.data?.supplier,
              supplierName: res.data?.supplierName,
              flowNo: res.data?.flowNo,
              verifyList: res.data?.verifyList,
              firstCp: res.data?.firstCp,
            };
            setWithExactExpire(`U_CREDIT_PROCESS_V2_${this.param.userId}`, creditStore, "7D");
          } else {
            delete this.creditProcess.retainUser;
            delete this.creditProcess.FACE_CHECK;
            setWithExactExpire(
              `U_CREDIT_PROCESS_V2_${this.param.userId}`,
              this.creditProcess,
              "7D",
            );
          }
          if (e) {
            report("wallet_page_click", {
              click_name: "apply_credit_click",
              isContinue: true,
            });
          }
          return {
            nextPage: "realname",
          };
        }
        if (res.code === 0 && res.data?.accessResult === 1) {
          if (e) {
            report("wallet_page_click", {
              click_name: "apply_credit_click",
              isContinue: true,
            });
          }
          return {
            nextPage: "accessResult",
            accessResult: 1,
          };
        }
      }
      if (this.isLimitExpire) {
        this.creditProcess.reActive = 1;
        this.creditProcess.realInfo = "default";
        this.creditProcess.verifyList = ["AGREEMENT_CREDIT", "FACE_CHECK"];
        delete this.creditProcess.retainUser;
        setWithExactExpire(`U_CREDIT_PROCESS_V2_${this.param.userId}`, this.creditProcess, "7D");
      } else {
        // 1、如果用户未准入或者查询用户额度信息中没有flowNo，则删除本地授信流程缓存
        // 2、如果重新准入时发现用户未实名，或者调重新准入接口出现异常，则删除本地授信流程缓存，重新授信
        del(`U_CREDIT_PROCESS_V2_${this.param.userId}`);
      }

      if (e) {
        report("wallet_page_click", {
          click_name: "apply_credit_click",
          isContinue: false,
        });
      }
      return {
        nextPage: "realname",
      };
    },

    async loadTranslations() {
      let translationData;
      try {
        if (this.langType === 1) {
          translationData = await import("./res/en_US");
        } else {
          translationData = await import("./res/zh_CN");
        }
      } catch (error) {
        this.errorCode = 431;
      }
      return translationData.default;
    },

    handleSuppliers(suppliers) {
      if (this.langType === 1) {
        return suppliers.map((item) => ({
          ...item,
          translatedName: item.supplierEnName || item.supplierName,
        }));
      }
      return suppliers;
    },
  },
});
