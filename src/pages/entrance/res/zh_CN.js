const resourceZhCN = {
  navigationHeader: "借钱",
  addShortcutDialogText: "将借钱服务添加到桌面？",
  addShortcutToastText: "已为借钱服务创建快捷方式",
  addShortcutFailedToastText: "创建快捷方式失败",
  welcomeDialogTitle: "欢迎使用借钱服务",
  welcomeDialogInfo: "由荣耀合作机构提供服务 安全合规",
  formattedWelcomeButtonText: "知道了",
  sloganInfo1: "最高可借%s万",
  sloganInfo2: "年利率低至%s%",
  sloganInfo3: "%s秒极速审批",
  sloganHeader: "你的生活备用金",
  sloganText1: "额度高",
  sloganText2: "利率低",
  sloganText3: "审批快",
  loanProductOwner: "荣耀",
  loanSupplierText1: "荣耀合作机构提供服务",
  suggestProductTitle: "其他严选推荐",
  disclaimerText:
    "免责声明：荣耀仅提供信贷引流服务，不参与实际借款额度评估、息费定价和放贷资金等金融环节。金融服务均由合作持牌机构提供。",
  noticeText: "请根据个人能力合理贷款，理性消费，避免逾期。",
  customerServiceDialogButtonText: "好的",
  cancelButtonText: "取消",
  contactService: "联系客服",
  addShortButtonText: "添加",
  suggestProductText: "最高可借 | 年化利率%s%起",
  suggestProductButtonText: "立即申请",
  keepSilentCheckboxText: "不再提示",
  supplierInfoDescription: "合作机构",
  supplierInfoClarSt: "荣耀合作机构如下，最终为您提供贷款服务的机构请以审批结果为准。",
  supplierInfoButtonText: "知道了",

  // ------------------------------------------------------------------------------

  formattedOverdueWarning: "暂时无法申请借款，逾期会加收罚息，且将影响个人征信，请尽快还款",
  formattedIntroduction: "了解借钱服务",
  loanLimitTitle1: "最高可借 (元)",
  loanLimitTitle2: "可借额度 (元)",
  loanLimitTitle3: "逾期应还 (元)",
  loanLimitTitle4: "剩余可借额度 (元)",
  loanLimitTitle5: "你可以借 (元)",
  loanLimitTitle6: "逾期应还金额待更新（元）",
  formattedLimitAmount: "",
  formattedOverdueAmount: "",
  notMeetingLimitApplyCondition: "暂不符合额度申请条件",
  notMeetingLoanApplyCondition: "暂不符合申请条件",
  LimitNormalInsufficientCondition: "低于起借金额￥%s，额度暂不可用",
  LimitNotAvailableCondition: "额度暂不可用，详情可咨询%s客服 %s",
  couponText: "你有1张【%s】未使用",
  formattedButtonText1: "不符合额度申请条件",
  formattedButtonText2: "查看额度",
  formattedButtonText3: "去借款",
  formattedButtonText4: "正在获取额度",
  formattedButtonText5: "继续借款",
  formattedButtonText6: "借款审核中",
  formattedButtonText11: "立即还款",
  formattedRepayingButtonText: "还款处理中...",
  formattedButtonText12: "继续申请",
  formattedButtonText13: "激活额度",
  formattedUnloginButtonText: "登录查看额度",
  cellTitleWithoutLoan: "我的借款",
  cellTextWithoutLoan: "暂无借款",
  cellTitleWithLoan: " 剩余应还总额",
  formattedRepaymentAmount: "",

  customerServiceDialogText: "联系%s客服",
  loanSupplierText2: "由%s提供服务",
  formattedRateText1a: "年利率 (单利) %s%起",
  formattedRateText1b: "%s%",
  formattedRateText1c: "%s%",
  formattedRateText1d: "，借1万元用1天只需%s元",
  formattedRateText1f: "借1万元用1天只需%s元",
  formattedRateText2: "年利率 (单利) %s%起，借1万元用1天只需%s元",
  formattedButtonText7: "%s秒后可重新申请",
  formattedButtonText8: "%s小时%s分钟后可重新申请",
  formattedButtonText9: "%s月%s日后可重新申请",
  formattedButtonText10: "%1$s年%2$s月%3$s日后可重新申请",
  formattedOverdueInfo: "%s，罚息￥%s",
  formattedRepaymentText1: "共%s，其中%s笔借款存在逾期",
  formattedRepaymentText2: "共%s",
  formattedOverdueInfoSubject: "有%s笔借款，累计逾期%s",
  formattedOverdueInfoSubjects: "有%s笔借款，累计逾期%s",
  dayInfo: "%s天",
  dayInfos: "%s天",
  orderNumInfo: "%s笔借款",
  orderNumInfos: "%s笔借款",
  couponText1: "七天免息券",
  increaseAmountReduceInterestText: "提额降息",
  discountValidTimeText: "限时%s",
  discountExpiredTodayText: "今日到期",
  limitDiscountTitle: "额度提高至(元)",
  rateDiscountTitle: "年利率降低至",
  limitDiscountInfo: "当前剩余可借额度%s元",
  rateDiscountInfo: "借1万用1天仅%s元",
  validTimeDiscountInfo: "，限时优惠，请尽快使用",
  loanLimitIncreaseTitle: "含临时额度%s元",
  loanLimitIncreaseTitleWithoutAvailableLimit: "含临时额度",
  discountDialogTitleLimitRate: "恭喜您获得专享借款优惠",
  discountDialogTitleTempLimit: "恭喜您获得限时额度提高",
  discountDialogTitleTempRate: "恭喜您获得限时利率优惠",
  discountDialogTitleFixedLimit: "恭喜您获得额度提高",
  discountDialogTitleFixedRate: "恭喜您获得利率优惠",
  rateDiscountText: "限时降息",
  guide2ContinueApplyText: "仅差%s步即可完成申请",
  guide2ContinueApplyTexts: "仅差%s步即可完成申请",
  changeCooperativeProductText: "上次申请额度失败，已为您更换合作机构",
  loadingText: "数据加载中，请稍后重试",
  serverErrorText: ["服务异常，请", "重试"],
  userLogoutText: "用户已注销",
  jumpOutText: "正在跳转到第三方页面",
  reActFailBublTxt: "原额度激活失败，您可申请新额度",
  limitExpireAlert: "您的额度已过期，请重新激活额度",
  limitExpireTagTxt: "已失效",

  supplierInfo: {
    1: "度小满金融",
    2: "360借条",
    3: "星图金融任性贷",
    4: "招联",
    5: "京东金条",
  },

  month: {
    1: "1",
    2: "2",
    3: "3",
    4: "4",
    5: "5",
    6: "6",
    7: "7",
    8: "8",
    9: "9",
    10: "10",
    11: "11",
    12: "12",
  },
};

export default resourceZhCN;
