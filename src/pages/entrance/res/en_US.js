const resourceEnUS = {
  navigationHeader: "Loan",
  addShortcutDialogText: "Add Loan service to the Home screen?",
  addShortcutToastText: "The Loan Services shortcut has been created.",
  addShortcutFailedToastText: "Failed to create shortcut.",
  welcomeDialogTitle: "Welcome to Loan Services",
  welcomeDialogInfo: "Provided by HONOR's trusted partners, secure and compliant.",
  formattedWelcomeButtonText: "Got it",
  sloganInfo1: "You can borrow up to %s0,000 yuan",
  sloganInfo2: "Annual interest rate as low as %s%",
  sloganInfo3: "High-speed approval within %s sec",
  sloganHeader: "Your reserve fund",
  sloganText1: "High quota",
  sloganText2: "Low rate",
  sloganText3: "Fast approval",
  loanProductOwner: "HONOR",
  loanSupplierText1: "Service by HONOR's partners",
  suggestProductTitle: "Other recommendations",
  disclaimerText:
    // eslint-disable-next-line max-len
    "Disclaimer: HONOR only provides credit diversion services and does not participate in financial services such as loan amount assessment, interest pricing, and loan funding. Financial services are provided by licensed cooperative institutions.",
  noticeText:
    "Make reasonable loans based on your personal circumstances to take rational consumption choices and avoid late payments.",
  customerServiceDialogButtonText: "OK",
  cancelButtonText: "Cancel",
  contactService: "Contact",
  addShortButtonText: "Add",
  suggestProductText: "Maximum loan amount | Annual interest rate starting from %s%",
  suggestProductButtonText: "Apply",
  keepSilentCheckboxText: "Do not remind again",
  supplierInfoDescription: "Cooperative institutions",
  supplierInfoClarSt:
    "The following are the HONOR's cooperative institutions that provide loan services, and the specific institutions that have passed the approval and finally provide loans to you.",
  supplierInfoButtonText: "Got it",

  formattedOverdueWarning:
    // eslint-disable-next-line max-len
    "You're currently not eligible to apply for a loan. Defaulting on a loan will charged you a penalty interest and affect your personal credit. Please repay as soon as possible.",
  formattedIntroduction: "Learn more about Loan Services",
  loanLimitTitle1: "Maximum loan amount (Yuan)",
  loanLimitTitle2: "Available loan amount (Yuan)",
  loanLimitTitle3: "Overdue repayment (Yuan)",
  loanLimitTitle4: "Available loan amount (Yuan)",
  loanLimitTitle5: "Your loan limit (Yuan)",
  loanLimitTitle6: "Overdue amount pending update (CNY)",
  formattedLimitAmount: "",
  formattedOverdueAmount: "",

  notMeetingLimitApplyCondition: "You're currently not eligible for loan amount application.",
  notMeetingLoanApplyCondition: "You're currently not eligible for loan application",
  LimitNormalInsufficientCondition:
    "The available loan amount is less than ￥%s, and the amount is temporarily unavailable.",
  LimitNotAvailableCondition:
    "The amount is temporarily unavailable. Contact customer service at %s: %s",
  couponText: "1 coupon 【%s】 available for use",
  formattedButtonText1: "Not eligible for loan amount application",
  formattedButtonText2: "Check loan limit",
  formattedButtonText3: "Loan",
  formattedButtonText4: "Obtaining loan amount …",
  formattedButtonText5: "Continue to loan",
  formattedButtonText6: "Loan review in progress …",
  formattedButtonText11: "Repay",
  formattedRepayingButtonText: "Processing...",
  formattedButtonText12: "Continue to apply",
  formattedButtonText13: "Activate",
  formattedUnloginButtonText: "Log in & check loan limit",
  cellTitleWithoutLoan: "My loans",
  cellTextWithoutLoan: "No loans yet.",
  cellTitleWithLoan: "Repayments due",
  formattedRepaymentAmount: "",

  customerServiceDialogText: "Contact customer service at %s: ",
  loanSupplierText2: "Service by %s",
  formattedRateText1a: "The annual interest rate (simple interest) starts from %s% ",
  formattedRateText1b: "%s% ",
  formattedRateText1c: "%s%",
  formattedRateText1d: ". You're only charged %s yuan for a borrow of 10,000 yuan.",
  formattedRateText1f: "You're only charged %s yuan for a borrow of 10,000 yuan.",
  formattedRateText2:
    "The annual interest rate (simple interest) is as low as %s%. You're only charged %s yuan for a borrow of 10,000 yuan.",
  formattedButtonText7: "Reapply after %s sec",
  formattedButtonText8: "Reapply after %s hr and %s min",
  formattedButtonText9: "Reapply after %s %s",
  formattedButtonText10: "Reapply after %2$s %3$s, %1$s",
  formattedOverdueInfo: "%s subject to a service charge of ￥%s",
  formattedRepaymentText1: "%s, %s overdue",
  formattedRepaymentText2: "%s",
  formattedOverdueInfoSubject: "%s loan overdue for %s is",
  formattedOverdueInfoSubjects: "%s loans overdue for %s are",
  dayInfo: "%s day",
  dayInfos: "%s days",
  orderNumInfo: "%s loan",
  orderNumInfos: "%s loans",
  couponText1: "7-day interest-free coupon",
  increaseAmountReduceInterestText: "Higher limit, lower rate",
  discountValidTimeText: "Limit: %s",
  discountExpiredTodayText: "Expires today",
  limitDiscountTitle: "Limit raised to (RMB)",
  rateDiscountTitle: "APR cut to",
  limitDiscountInfo: "You can now apply for a loan up to %s RMB.",
  rateDiscountInfo: "Daily interest rate drops to %s RMB per 10,000 RMB. ",
  validTimeDiscountInfo: "This limited-time offer expires soon.",
  loanLimitIncreaseTitle: "Temp loan limit (￥%s) included",
  loanLimitIncreaseTitleWithoutAvailableLimit: "Temp loan limit included",
  discountDialogTitleLimitRate: "Congrats! You've got a special offer.",
  discountDialogTitleTempLimit: "Congrats! You've got a limited-time offer.",
  discountDialogTitleTempRate: "Congrats! You've got a limited-time offer.",
  discountDialogTitleFixedLimit: "Congrats! You've got a special offer.",
  discountDialogTitleFixedRate: "Congrats! You've got a special offer.",
  rateDiscountText: "Limited-time offer",
  guide2ContinueApplyText: "Just %s step away",
  guide2ContinueApplyTexts: "Just %s steps away",
  changeCooperativeProductText:
    "Last loan attempt failed. A new partner institution has been assigned.",
  loadingText: "Loading data … Try again later.",
  serverErrorText: ["Service error, ", "try again"],
  userLogoutText: "User was deleted",
  jumpOutText: "Redirecting to a third-party website …",
  reActFailBublTxt: "Unable to activate the original credit limit. You can apply for a new one.",
  limitExpireAlert: "The credit limit has expired. Reactivate it.",
  limitExpireTagTxt: "Expired",

  supplierInfo: {
    1: "Du Xiaoman",
    2: "360 Online Microcredit",
    3: "Star Atlas Finance",
    4: "Merchants Union Consumer Finance",
    5: "JD Jintiao",
  },

  month: {
    1: "Jan.",
    2: "Feb.",
    3: "Mar.",
    4: "Apr.",
    5: "May",
    6: "Jun.",
    7: "Jul.",
    8: "Aug.",
    9: "Sep.",
    10: "Oct.",
    11: "Nov.",
    12: "Dec.",
  },
};

export default resourceEnUS;
