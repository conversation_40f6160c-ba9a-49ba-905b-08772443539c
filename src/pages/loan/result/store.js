import { defineStore } from "pinia";
import { request } from "../../../helpers/utils";

export default defineStore("loan/result", {
  state: () => ({
    // 页面初始数据
    applyRes: {
      applyStatus: 0,
    },
    param: {},
    applyFlag: false,
    errorCode: null,
    errorMessage: "",
    suggestProducts: null,
    curOrderInvalid: false,
    signStatus: 0, // 是否显示代扣资源位
    signResourceInfo: {}, // 代扣资源位信息
    isOnResume: false,
    signUrlValidTime: null,
    timer: null,
  }),
  actions: {
    async queryApplyStatus() {
      const res = await request(
        "/loan/api/loan/queryApplyStatus",
        { applyNo: this.param.applyNo },
        { mock: false },
      );
      if (res.code !== 0) {
        this.errorCode = res.code;
        this.errorMessage = res.message;
        return;
      }
      this.applyRes = res.data;
      if ([3, 4, 5].includes(this.applyRes.applyStatus)) {
        await this.getAdActivity();
      }
    },
    async getResourcePositionInfo() {
      const sdkVersionCode = this.param?.sdkVersionCode;
      if (sdkVersionCode < 90007000) return;
      const res = await request(
        "/loan/api/user/withholdSignUrl",
        { returnUrl: "" },
        { mock: false },
      );
      if (res.code !== 0) {
        return;
      }
      this.signStatus = res.data?.signStatus;
      this.signResourceInfo = res.data?.signResourceInfo;
      this.signUrlValidTime = res.data?.signResourceInfo?.timeOut || 0;
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }

      if (this.signUrlValidTime > 0) {
        this.timer = setTimeout(() => this.getResourcePositionInfo(), this.signUrlValidTime);
      }
    },
    async initial() {
      if (typeof window !== "undefined" && !window.navigator.onLine) {
        this.applyFlag = !window.navigator.onLine;
        if (this.applyFlag) {
          return;
        }
      }
      if (this.param.applyExpired) {
        this.curOrderInvalid = true;
        return;
      }
      if (this.param.applyCode || this.param.applyMessage) {
        return;
      }
      await Promise.all([this.queryApplyStatus(), this.getResourcePositionInfo()]);
    },
    async getAdActivity() {
      const data = await request("/loan/api/config/suggest/list", {});
      this.suggestProducts = data.data;
    },
  },
});
