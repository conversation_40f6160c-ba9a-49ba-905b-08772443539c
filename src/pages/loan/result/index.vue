<script setup>
import { onServerPrefetch, ref, onMounted, computed } from "vue";
import Big from "big.js";
import useStore from "./store";
import { initStore, request, amountNumberFormat } from "../../../helpers/utils";
import {
  goto,
  report,
  hasShortcut,
  updateShortcut,
  enableBackPress,
  regNativeEvent,
  decrypt,
} from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { getStore, setWithExactExpireUnit, del } from "../../../helpers/storage";
import { loanNext } from "../../../helpers/next";
import SuggestProductCard from "../../component/SuggestProductCard.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { urlMap } from "../../../helpers/constants";
import {
  reportLoanApply30SecInReviewFinishClick,
  reportLoanExpiredGuidePageCancel,
  reportLoanExpiredGuidePageContinue,
  reportLoanExpiredGuidePageShow,
} from "./report";
import resourcePosition from "../../../components/resourcePosition.vue";

const { store, data } = initStore(useStore);

onServerPrefetch(store.initial);

const duration = 30; // **倒计时总秒数**
const countdown = ref(duration);
const currentRate = ref(0); // **初始为0，表示圆环起点**
const flag = ref(false);
const ifAddCheck = ref(true);
const showShortCut = ref(false);
const ifCodeError = ref(false);
const codeMessage = ref("");
const resourcePositionRef = ref(null);
const ifFoldScreen = ref(false);
const loanExpiredClick = async (action) => {
  if (action === "confirm") {
    reportLoanExpiredGuidePageContinue();
    const loanStorage = getStore(`U_LOAN_PROCESS_${store.param.userId}`) || {};
    del(`U_LOAN_PROCESS_${store.param.userId}`);
    if (typeof loanStorage.loanInfo === "string") {
      const info = await decrypt(loanStorage.loanInfo);
      if (typeof info === "object") {
        goto(
          `${urlMap.loan_calc}?amount=0&fromPage=loanResult&retryAmount=${info.loanAmount}&loanInfo=${encodeURIComponent(loanStorage.loanInfo)}`,
        );
        return;
      }
    }
    goto(`${urlMap.loan_calc}?amount=0&fromPage=loanResult`);
  }
  if (action === "cancel") {
    reportLoanExpiredGuidePageCancel();
    del(`U_LOAN_PROCESS_${store.param.userId}`);
    goto(urlMap.loan_index, false, true);
  }
};

const showLoanExpiredGuidePage = () => {
  flag.value = true;
  store.curOrderInvalid = true;
  reportLoanExpiredGuidePageShow();
};

const filterFailTitle = computed(() => {
  if (store.applyFlag) {
    return "申请失败";
  }
  if (store.curOrderInvalid) {
    return "订单失效，请重新申请";
  }
  return "暂无法申请借款";
});

const loanAmount = computed(() => {
  const amount = Big(data.value.applyRes.totalAmount || 0)
    .div(100)
    .toString();
  return amountNumberFormat(amount);
});

const firstTermAmount = computed(() => {
  const amount = Big(data.value.applyRes.firstTermAmount || 0)
    .div(100)
    .toString();
  return amountNumberFormat(amount);
});

const applyStatus = computed(
  () =>
    // // 1-审核中，2-成功，3-失败，4-拒绝，5-取消，6-需鉴权（用信补录，补签协议后再提交借款）
    store.applyRes.applyStatus,
);

const verifyList = computed(() => data.value.applyRes.verifyList);

const updateNetwork = () => {
  setNetwork();
};

async function addShortCut() {
  // 添加快捷方式
  if (
    ifAddCheck.value &&
    !showShortCut.value &&
    store.param.versionCode &&
    store.param.versionCode >= 81300350
  ) {
    const ret = await updateShortcut(true);
    if (ret) del(`U_POPUP_STORE_${store.param.userId}`);
  }
}

const toHomeClick = async () => {
  if (applyStatus.value === 1) {
    reportLoanApply30SecInReviewFinishClick();
  }
  if (applyStatus.value === 2) {
    await addShortCut();
    report("wallet_page_click", { click_name: "loan_apply_success_finish_click" });
    setWithExactExpireUnit("U_HOME_HIDE_SHOW_ANIMATION", true, 7, "D");
  }
  if (applyStatus.value === 3) {
    report("wallet_page_click", { click_name: "loan_apply_fail_finish_click" });
  }
  if (applyStatus.value === 4 || data.value.param.applyCode === "4") {
    report("wallet_page_click", { click_name: "loan_apply_rejected_finish_click" });
  }
  if (applyStatus.value === 5) {
    report("wallet_page_click", { click_name: "loan_apply_cancel_finish_click" });
  }
  del("successInfo");
  goto("/wallet-loan-web/pages/index", false, true);
};

const goBack = () => {
  del("successInfo");
  if (store.curOrderInvalid) {
    loanExpiredClick("cancel");
  } else {
    goto("/wallet-loan-web/pages/index", false, true);
  }
};

function filterVerifyList(list) {
  // TODO: 是否是补充协议？？？
  const steps = [];
  const infoFileds = list.filter(
    (item) =>
      item !== "FACE_CHECK" &&
      item !== "AGREEMENT_COMMON" &&
      item !== "AGREEMENT_LOAN" &&
      item !== "SMS",
  );
  if (list.includes("FACE_CHECK")) {
    steps.push("FACE_CHECK");
  }
  if (list.includes("AGREEMENT_COMMON") || list.includes("AGREEMENT_LOAN")) {
    steps.push("AGREEMENT_P");
  }
  if (infoFileds.length) {
    steps.push("INFOS");
  }
  if (list.includes("SMS")) {
    steps.push("SMS");
  }
  return { steps, infoFileds };
}

function getReapplyDate(daysToAdd) {
  const currentDate = new Date();
  const newDate = new Date(currentDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000);

  if (newDate.getFullYear() === currentDate.getFullYear()) {
    return `${newDate.getMonth() + 1}月${newDate.getDate()}日`;
  }
  return `${newDate.getFullYear()}年${newDate.getMonth() + 1}月${newDate.getDate()}日`;
}

const getControlDay = () => {
  const daysToAdd = data.value?.applyInfo?.refuseControlDays || null; // 要推迟的天数
  if (store.curOrderInvalid) {
    return "因订单操作时间过长，已失效，请重新申请。";
  }
  if (ifCodeError.value) {
    return codeMessage.value;
  }
  if (data.value.applyFlag) {
    return "网络未连接，请检查网络设置";
  }
  if (data.value.param.applyMessage) {
    return data.value.param.applyMessage;
  }
  if (!daysToAdd) {
    return "请重试";
  }
  return `${getReapplyDate(daysToAdd)}后可重新申请`;
};

const handleNetwork = () => {
  if (!flag.value || data.value.applyFlag) {
    window.location.reload();
  }
};

regNativeEvent("onBack", () => {
  goBack();
});

regNativeEvent("onResume", () => {
  store.isOnResume = true;
  store.getResourcePositionInfo();
});

const reportEventList = ref([]);

const ifReportCheck = (checkList, param) => {
  return checkList.includes(param);
};

const filterReportEvent = (status) => {
  switch (status) {
    case 1:
      if (!ifReportCheck(reportEventList.value, "loan_apply_30sec_in_review_page")) {
        report("wallet_page_view", { page_name: "loan_apply_30sec_in_review_page" });
        reportEventList.value.push("loan_apply_30sec_in_review_page");
      }
      break;
    case 2:
      if (!ifReportCheck(reportEventList.value, "loan_apply_success_page")) {
        report("wallet_page_view", { page_name: "loan_apply_success_page" });
        reportEventList.value.push("loan_apply_success_page");
      }
      break;
    case 3:
      if (!ifReportCheck(reportEventList.value, "loan_apply_fail_page")) {
        report("wallet_page_view", { page_name: "loan_apply_fail_page" });
        reportEventList.value.push("loan_apply_fail_page");
      }
      break;
    case 4:
      if (!ifReportCheck(reportEventList.value, "loan_apply_rejected_page")) {
        report("wallet_page_view", { page_name: "loan_apply_rejected_page" });
        reportEventList.value.push("loan_apply_rejected_page");
      }
      break;
    case 5:
      if (!ifReportCheck(reportEventList.value, "loan_apply_cancel_page")) {
        report("wallet_page_view", { page_name: "loan_apply_cancel_page" });
        reportEventList.value.push("loan_apply_cancel_page");
      }
      break;
    default:
  }
};

onMounted(async () => {
  ifFoldScreen.value = window.matchMedia("(min-width: 656px)").matches;
  if (applyStatus.value !== 1) {
    filterReportEvent(applyStatus.value);
  }
  showShortCut.value = await hasShortcut();
  enableBackPress(false);
  window.addEventListener("online", handleNetwork);
  if (store.param.applyExpired) {
    showLoanExpiredGuidePage();
    enableBackPress(true);
    return;
  }
  if (data.value.errorCode) {
    flag.value = true;
    ifCodeError.value = true;
    codeMessage.value = data.value.errorMessage;
    enableBackPress(true);
    return;
  }
  ifCodeError.value = false;
  if (data.value.param.applyStatus) {
    flag.value = true;
    const successInfo = getStore("successInfo");
    if (successInfo) {
      data.value.applyRes = successInfo;
      enableBackPress(true);
      return;
    }
    await store.initial();
    enableBackPress(true);
    return;
  }
  if (!window?.navigator?.onLine) {
    data.value.applyFlag = !window.navigator.onLine;
    if (data.value.applyFlag) {
      flag.value = true;
      enableBackPress(true);
      return;
    }
  }
  if (data.value.param.applyCode || data.value.param.applyMessage) {
    flag.value = true;
    enableBackPress(true);
    return;
  }
  const { applyNo, userId } = data.value.param;
  const timer = setInterval(async () => {
    // 倒计时30s，轮询授信申请结果查询接口
    if (countdown.value > 1) {
      if (data.value.errorCode) {
        flag.value = true;
        ifCodeError.value = true;
        codeMessage.value = data.value.errorMessage;
        clearInterval(timer);
        enableBackPress(true);
        return;
      }
      ifCodeError.value = false;
      if (!window?.navigator?.onLine) {
        data.value.applyFlag = !window.navigator.onLine;
        if (data.value.applyFlag) {
          flag.value = true;
          enableBackPress(true);
          clearInterval(timer);
          return;
        }
      }
      countdown.value -= 1;
      currentRate.value = ((duration - countdown.value) / (duration - 1)) * 30;
      enableBackPress(false);
      if (applyStatus.value === 2) {
        // TODO: 成功
        filterReportEvent(2);
        clearInterval(timer);
        flag.value = true;
        enableBackPress(true);
        setWithExactExpireUnit("successInfo", data.value.applyRes, 32, "D");
        del(`U_LOAN_PROCESS_${userId}`);
      } else if (countdown.value % 5 === 0) {
        await store.queryApplyStatus();
        // if (currentRate.value % 5 === 0 ) {
        // 如果授信申请查询状态为1--审核中,继续30s倒计时
        if (applyStatus.value === 6) {
          const { supplier, loanInfo, faceInfo } = getStore(`U_LOAN_PROCESS_${userId}`);
          const { steps, infoFileds } = filterVerifyList(verifyList.value);
          const storageData = {
            supplier,
            applyNo,
            steps,
            infoFileds,
            loanInfo,
            faceInfo,
          };
          clearInterval(timer);
          setWithExactExpireUnit(`U_LOAN_PROCESS_${userId}`, storageData, 4, "D");
          loanNext(userId);
        } else {
          if (applyStatus.value === 4) {
            // TODO: 拒绝
            filterReportEvent(4);
            flag.value = true;
            clearInterval(timer);
            del(`U_LOAN_PROCESS_${userId}`);
          } else if (applyStatus.value === 3) {
            // TODO: 失败
            flag.value = true;
            clearInterval(timer);
            filterReportEvent(3);
            del(`U_LOAN_PROCESS_${userId}`);
          } else if (applyStatus.value === 2) {
            // TODO: 成功
            filterReportEvent(2);
            flag.value = true;
            clearInterval(timer);
            setWithExactExpireUnit("successInfo", data.value.applyRes, 32, "D");
            del(`U_LOAN_PROCESS_${userId}`);
          } else if (applyStatus.value === 5) {
            // TODO: 取消
            filterReportEvent(5);
            flag.value = true;
            clearInterval(timer);
            del(`U_LOAN_PROCESS_${userId}`);
          } else if (applyStatus.value === 0) {
            const loanStorage = getStore(`U_LOAN_PROCESS_${userId}`) || null;
            if (loanStorage) {
              const res = await request("/loan/api/loan/apply", {
                encryptedParams: { applyNo, ...loanStorage.infos },
              });
              if (res.code !== 0) {
                throw new Error(res.message);
              }
              if (loanStorage.infos) {
                delete loanStorage.infos.fileInfos;
              }
              setWithExactExpireUnit(`U_LOAN_PROCESS_${userId}`, loanStorage, 4, "D");
            }
          }
          enableBackPress(true);
        }
      }
    } else if (countdown.value === 1) {
      // 倒计时30s后，如 授信申请查询状态applyStatus依旧为1--审核中,关闭倒计时，展示正在审核页面
      currentRate.value = 30;
      clearInterval(timer);
      await store.initial();
      if (data.value.errorCode) {
        flag.value = true;
        ifCodeError.value = true;
        codeMessage.value = data.value.errorMessage;
        enableBackPress(true);
        return;
      }
      if (applyStatus.value === 1) {
        filterReportEvent(1);
      }
      flag.value = true;
      enableBackPress(true);
    }
  }, 1000);
});
</script>

<template>
  <div class="main">
    <div class="content-top">
      <hnr-nav-bar v-if="flag" transparent="true" class="nav-padding-top" title="借钱">
        <template #left>
          <icsvg-public-back-filled @click="goBack"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <hnr-nav-bar
        v-else
        transparent="true"
        class="nav-padding-top"
        style="margin-left: -40px"
        title="借钱"
      />
    </div>
    <div class="content">
      <div
        v-if="
          !flag &&
          !data.param.applyCode &&
          !data.applyFlag &&
          !data.param.applyStatus &&
          !store.curOrderInvalid
        "
      >
        <div class="circleStyle">
          <hnr-circle
            v-model:current-rate="currentRate"
            :rate="30"
            :text="currentRate"
            :clockwise="true"
            size="64"
          >
            <template #default>
              <div class="circle-text">
                {{ `${countdown}` }}
                <div style="font-size: var(--dp18); margin-top: var(--dp5)">s</div>
              </div>
            </template>
          </hnr-circle>
        </div>
        <div class="textStyle1">正在快速处理您的申请</div>
        <hnr-divider class="divider" line />
        <div :class="store.signStatus !== 0 ? 'boxStyle' : 'boxStyleNormal'">
          <div class="iconStyle">
            <div class="item">
              <img class="icon" src="/public/loan/todo.svg" />
              <div class="textStyle2">您的申请已提交</div>
            </div>
            <div class="verticalLine1"></div>
            <div class="item">
              <img class="icon" src="/public/loan/todo.svg" />
              <div class="textStyle2">平台已接收</div>
            </div>
            <div class="verticalLine1"></div>
            <div class="item">
              <span class="icon-Credit_Result_Examine"></span>
              <div class="textStyle2">正在进行审批</div>
            </div>
            <div class="verticalLine2"></div>
            <div v-if="store.signStatus !== 0" style="display: flex; line-height: 16px">
              <div style="display: flex; flex-direction: column">
                <div class="icon-Credit_Result_Examine"></div>
                <div
                  class="verticalLine2"
                  style="
                    margin: var(--hnr-elements-margin-vertical-S) 15px 0px
                      var(--hnr-elements-margin-vertical-M);
                  "
                  :style="{ height: store.signStatus === 2 ? '103px' : '170px' }"
                ></div>
              </div>
              <div class="resource-container">
                <div class="textStyle2" style="margin-left: 0px">
                  <span v-if="store.signStatus === 1 || store.signStatus === 3" class="desc-icon"
                    >推荐</span
                  >
                  <span v-if="store.signStatus === 1 || store.signStatus === 3"
                    >签约支付宝自动还款，一键解决“忘记还”</span
                  >
                  <span v-if="store.signStatus === 2">已签约支付宝自动还款服务</span>
                </div>
                <resourcePosition
                  ref="resourcePositionRef"
                  :style="{
                    height: store.signStatus === 2 ? '67px' : '130px',
                  }"
                  :sign-status="store.signStatus"
                  :sign-resource-info="store.signResourceInfo"
                  location="loan_result"
                  :is-on-resume="store.isOnResume"
                  :if-fold-screen="ifFoldScreen"
                />
              </div>
            </div>
            <div
              v-if="store.signStatus !== 0"
              class="verticalLine2"
              style="
                height: 8px;
                margin: 0px 15px var(--hnr-elements-margin-vertical-S)
                  var(--hnr-elements-margin-vertical-M);
              "
            ></div>
            <div class="item">
              <span class="icon-Credit_Result_Examine"></span>
              <div class="textStyle2" style="color: var(--hnr-text-color-secondary)">放款</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="flag" style="display: flex; flex-direction: column; height: calc(100vh - 88px)">
        <div style="flex: 1">
          <div v-if="!data.param.applyCode && !data.applyFlag && !ifCodeError">
            <div v-if="data.applyRes.applyStatus === 1">
              <div class="circleStyle">
                <img src="/public/loan/dealing.svg" alt />
              </div>
              <div class="textStyle1">正在为您获取审批结果</div>
              <div class="second-text normal-text">结果将以短信通知，请耐心等待</div>
            </div>
            <div v-if="data.param.applyStatus === 2 || data.applyRes.applyStatus === 2">
              <div class="circleStyle">
                <img src="/public/loan/success.svg" alt />
              </div>
              <div class="textStyle1" style="margin-top: var(--hnr-elements-margin-vertical-L)">
                {{ `借款发放成功` }}
              </div>
              <div v-show="data.applyRes?.totalAmount" class="limit-money">￥{{ loanAmount }}</div>
              <div
                v-show="
                  data.applyRes?.totalAmount &&
                  data.applyRes?.repayDay &&
                  data.applyRes?.firstTermAmount
                "
                class="desc normal-text"
              >
                <span>说明:</span>
                <span>1.借款已发放，实际到账时间取决于收款银行，请注意查收。</span>
                <span>{{
                  `2.每月${data.applyRes.repayDay ? data.applyRes.repayDay.slice(-2) : ""}日还款，首期还款${
                    firstTermAmount
                  }元。每月${
                    data.applyRes.repayDay ? data.applyRes.repayDay.slice(-2) : ""
                  }日将从您绑定的银行卡中自动扣款。`
                }}</span>
              </div>
              <resourcePosition
                v-if="store.signStatus !== 0 && store.signStatus !== 2"
                ref="resourcePositionRef"
                class="resource-position"
                :style="{
                  height: store.signStatus === 2 ? '67px' : '130px',
                }"
                :sign-status="store.signStatus"
                :sign-resource-info="store.signResourceInfo"
                location="loan_result"
                :is-on-resume="store.isOnResume"
                :if-fold-screen="ifFoldScreen"
              />
            </div>
          </div>
          <div
            v-if="
              data.applyRes.applyStatus === 3 ||
              data.applyRes.applyStatus === 4 ||
              data.applyRes.applyStatus === 5 ||
              data.param.applyCode ||
              data.applyFlag ||
              ifCodeError ||
              store.curOrderInvalid
            "
          >
            <div class="circleStyle">
              <img src="/public/loan/fail.svg" alt />
            </div>
            <div class="textStyle1">{{ filterFailTitle }}</div>
            <div class="second-text normal-text">
              {{ getControlDay() }}
            </div>
            <div
              v-if="store.suggestProducts && store.suggestProducts.length > 0"
              class="recommend-container"
            >
              <div class="list-product">可以尝试以下严选借款产品</div>
              <SuggestProductCard
                style="overflow-y: auto"
                :suggest-products="store.suggestProducts"
                location="借款失败"
              />
            </div>
          </div>
        </div>
        <hnr-checkbox
          v-if="
            !showShortCut &&
            store.param.versionCode &&
            store.param.versionCode >= 81300350 &&
            data.applyRes.applyStatus === 2
          "
          v-model="ifAddCheck"
          >添加“借钱服务”快捷方式到桌面</hnr-checkbox
        >
        <div class="footer">
          <hnr-button
            v-if="data.applyFlag"
            standard-width="true"
            class="btn"
            type="primary"
            @click="updateNetwork"
            >设置网络</hnr-button
          >
          <div v-else-if="store.curOrderInvalid">
            <hnr-button class="btn" type="default" @click="loanExpiredClick('cancel')"
              >取消</hnr-button
            >
            <hnr-button
              class="btn"
              style="margin-left: 12px"
              type="primary"
              @click="loanExpiredClick('confirm')"
              >重新申请</hnr-button
            >
          </div>
          <hnr-button
            v-else
            :standard-width="!ifFoldScreen"
            class="btn"
            type="primary"
            @click="toHomeClick"
            >完成</hnr-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style>
:root {
  --dp: 1px;
}
</style>

<style scoped>
@media (min-width: 719px) {
  .resource-position {
    margin: 12px 102px 0 103px !important;
  }

  .btn {
    min-width: calc(100% - 205px) !important;
  }

  .divider {
    height: 0.5px !important;
    width: calc(100% - 205px) !important;
    margin: 24px 103px 24px 102px !important;
  }

  .boxStyle {
    width: calc(100% - 205px) !important;
    padding: 0px 102px 0px 103px !important;
  }

  .desc {
    margin: 40px 102px 0px 103px !important;
  }
}

.circle-text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: var(--dp24);
  font-weight: var(--hnr-font-weight-medium);
}

.divider {
  height: 0.5px;
  width: calc(100% - 48px);
  margin: 24px 24px;
}

.resource-position {
  margin: 12px var(--hnr-max-padding-end) 0 var(--hnr-max-padding-start);
}

.icon-Credit_Result_Examine:before {
  content: "\e90a";
  font-family: "icomoon";
  font-size: var(--dp16);
  color: var(--hnr-color-quaternary);
}

.main {
  background-color: var(--hnr-color-app-bar-background);
  height: 100vh;
}

.content-top {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--hnr-color-background-cardview);
}

.content {
  padding-top: 88px;
}

:deep(.hnr-checkbox) {
  justify-content: center;
  cursor: none !important;
  line-height: 48px;
  height: 48px;
}

:deep(.hnr-checkbox__icon) {
  cursor: none !important;
}

.footer {
  margin-top: auto;
  display: flex;
  justify-content: center;
  margin-bottom: var(--hnr-default-padding-bottom-fixed);
}

.normal-text {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.second-text {
  margin-top: var(--hnr-elements-margin-vertical-M);
  text-align: center;
}

.desc {
  display: flex;
  flex-direction: column;
  text-align: left !important;
  margin: 40px var(--hnr-max-padding-end) 0 var(--hnr-max-padding-start);
  gap: var(--hnr-elements-margin-vertical-M);
}

.circleStyle {
  display: flex;
  justify-content: center;
  margin-top: var(--dp40);
  margin-bottom: 0;
  height: var(--dp64);
}

::v-deep(.hnr-circle__text) {
  margin-top: 1px;
}

::v-deep(.hnr-circle__textsize) {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}

.circleStyle /deep/ .hnr-circle__textUnit {
  color: var(--hnr-text-color-primary) !important;
  font-size: var(--hnr-subtitle-2) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
}

.contentStyle {
  display: flex;
  justify-content: center;
}

.limit {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-L2);
}
.limit-money {
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-6);
  font-weight: var(--hnr-font-weight-medium);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}

.textStyle1 {
  margin-top: var(--hnr-elements-margin-vertical-L2);
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
}

.textStyle2 {
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  display: flex;
  justify-content: flex-start;
}

.resource-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: calc(100vw - 72px);
}

.desc-icon {
  margin-right: var(--hnr-elements-margin-horizontal-M);
  border-radius: var(--hnr-default-corner-radius-xs);
  font-size: var(--hnr-caption);
  color: var(--hnr-color-primary-dark);
  padding: 0px 4px;
  background: var(--hnr-text-color-secondary-activated);
}

.textStyle3 {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  margin-top: 22px;
  display: flex;
  justify-content: flex-start;
}

.textStyle4 {
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--hnr-elements-margin-horizontal-M);
  margin-top: 22px;
  display: flex;
  justify-content: flex-start;
}

.icon {
  width: 1rem;
  height: 1rem;
}

.textStyle {
  line-height: 2rem;
}

.boxStyle {
  padding: 0px var(--hnr-max-padding-start) var(--hnr-max-padding-start)
    var(--hnr-max-padding-start);
}

.boxStyleNormal {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px var(--hnr-max-padding-start) var(--hnr-max-padding-start)
    var(--hnr-max-padding-start);
}

.flowStyle {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: var(--hnr-default-padding-start);
  padding-right: var(--hnr-default-padding-end);
}

.iconStyle {
  display: flex;
  flex-direction: column;
}

.item {
  display: flex;
  line-height: 16px;
  align-items: center;
}

.verticalLine1 {
  /* 竖线 */
  float: left;
  width: 1px;
  height: 1.5rem;
  margin: var(--hnr-elements-margin-vertical-S) var(--hnr-elements-margin-vertical-M);
  background-color: var(--hnr-text-color-primary-activated);
}

.right {
  height: 104px;
}

.verticalLine2 {
  float: left;
  width: 1px;
  height: 1.5rem;
  margin: var(--hnr-elements-margin-vertical-XS) var(--hnr-elements-margin-vertical-M);
  background-color: var(--hnr-color-tertiary);
}

.recommend-container {
  width: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
  height: calc(100vh - 380px);
}

.list-product {
  padding: var(--hnr-index-anchor-padding);
  margin: var(--hnr-elements-margin-vertical-M) 0;
  color: var(--hnr-color-app-bar-title);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
</style>
