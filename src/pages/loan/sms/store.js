import { defineStore } from "pinia";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { request } from "../../../helpers/utils";
import { getStore } from "../../../helpers/storage";

export default defineStore("loan/sms", {
  state: () => ({
    // 页面初始数据
    param: {},
    info: {},
    isSending: false,
    countdown: 60,
    intervalId: null,
    ifApply: false,
    serialNo: "",
  }),
  actions: {
    async loanClose() {
      const storageData = getStore(`U_LOAN_PROCESS_${this.param.userId}`) || {};
      if (storageData && storageData.applyNo) {
        const loanClose = request("/loan/api/loan/close", { applyNo: storageData.applyNo });
        const timeout = new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, 500);
        });
        await Promise.race([loanClose, timeout]);
      }
    },

    async send() {
      const res = await request(
        "/loan/api/sms/v2/sendVerifyCode",
        { encryptedParams: { type: 3 } },
        { mock: false },
      );
      if (res.code !== 0) {
        showToast({
          message: res.message,
          position: "center",
        });
        setTimeout(() => this.operLog(res, 206), 0);
        return;
      }
      this.serialNo = res.data.serialNo;
      setTimeout(() => this.operLog(res, 206), 0);
      if (this.isSending) {
        return;
      }

      this.isSending = true;
      this.countdown = 60;
      // 开始倒计时
      this.intervalId = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown -= 1;
        } else {
          clearInterval(this.intervalId);
          this.isSending = false;
          this.countdown = 60;
        }
      }, 1000);
    },

    async operLog(res, code) {
      const storage = getStore(`U_LOAN_PROCESS_${this.param.userId}`);
      await request("/loan/api/user/operLog", {
        orderNo: storage.applyNo,
        operType: code,
        operResult: res.code === 0 ? 1 : 2,
        supplier: res.supplier || 1,
        operTime: new Date().getTime(),
      });
    },
    setApply(newVal) {
      this.ifApply = newVal;
    },
  },
});
