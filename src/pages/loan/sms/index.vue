<template>
  <div class="container">
    <div ref="navRef" class="content-top">
      <hnr-nav-bar transparent="true" class="nav-padding-top" title="短信验证">
        <template #left>
          <icsvg-public-back-filled @click="returnClick"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
    </div>
    <span class="sms-title">输入短信验证码</span>
    <span class="sms-desc">{{
      data.isSending ? `已发送至手机号${data.info.mobileNo}` : "&nbsp;"
    }}</span>
    <div class="sms-input">
      <hnr-field
        v-model="code"
        type="number"
        :error="ifError"
        :error-message="errorMessage"
        placeholder="请输入验证码"
        @input="
          () => {
            errorMessage = '';
          }
        "
        @update:model-value="codeChange"
      >
        <template #button>
          <hnr-button
            type="text"
            :disabled="data.isSending"
            style="min-width: auto; max-width: auto"
            @click="send"
          >
            <span style="font-size: var(--hnr-body-2)">{{
              data.isSending ? `${data.countdown}s` : "发送验证码"
            }}</span>
          </hnr-button>
        </template>
      </hnr-field>
    </div>
  </div>
</template>
<script setup>
import { ref, onUnmounted, onMounted } from "vue";
import lodash from "lodash";
import useStore from "./store";
import { initStore, request } from "../../../helpers/utils";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { loanNext } from "../../../helpers/next";
import { goto, regNativeEvent } from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { showRetentionDialog } from "../../../helpers/retention-dialog-utils";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);

const code = ref(null);
const ifError = ref(false);
const errorMessage = ref("");
const intervalId = ref(null);
const confirmShow = ref(false);
const reportList = ref([]);

const toHomeClick = () => {
  if (!reportList.value.includes("toHomeClick")) {
    reportList.value.push("toHomeClick");
  }
};

const handleConfirmClick = () => {
  if (!reportList.value.includes("handleConfirmClick")) {
    reportList.value.push("handleConfirmClick");
  }
};

const returnClick = () => {
  if (!reportList.value.includes("returnClick")) {
    reportList.value.push("returnClick");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
};

const loanNextCallBack = () => {
  confirmShow.value = false;
};

const sureClick = async () => {
  // 短信验证码校验
  data.value.ifApply = false;
  const res = await request(
    "/loan/api/sms/v2/verifyCode",
    { encryptedParams: { code: code.value, type: 3, serialNo: data.value.serialNo } },
    { mock: false },
  );
  if (res.code !== 0) {
    ifError.value = true;
    errorMessage.value = res.message;
    store.operLog(res, 207);
    return;
  }
  setTimeout(() => store.operLog(res, 207), 0);
  ifError.value = false;
  errorMessage.value = "";
  const storageData = getStore(`U_LOAN_PROCESS_${data.value.param.userId}`);
  storageData.sms = true;
  setWithExactExpireUnit(`U_LOAN_PROCESS_${data.value.param.userId}`, storageData, 24, "H");
  loanNext(data.value.param.userId, loanNextCallBack());
};

const codeChange = lodash.debounce(() => {
  if (code.value) {
    sureClick();
  }
}, 1000);

const send = () => {
  store.send();
};

const updateNetwork = () => {
  if (window.location.pathname.includes("loan/sms")) {
    setNetwork();
  }
};

function handleNetwork() {
  const loanStorage = getStore(`U_LOAN_PROCESS_${data.value.param.userId}`) || {};
  if (loanStorage?.applyNo) {
    if (window.location.pathname.includes("loan/sms") && data.value.ifApply) {
      data.value.ifApply = false;
      goto(`/wallet-loan-web/pages/loan/result?applyNo=${loanStorage.applyNo}`);
      window.removeEventListener("online", handleNetwork);
    }
  }
}

regNativeEvent("onBack", () => {
  if (!reportList.value.includes("onBack")) {
    reportList.value.push("onBack");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
});

const hidePhoneMiddleFour = (phoneNumber) => {
  if (!phoneNumber) {
    return "";
  }
  if (phoneNumber.length !== 11) {
    throw new Error("电话号码必须是11位");
  }

  // 提取前三位和后四位
  const firstThree = phoneNumber.substring(0, 3);
  const lastFour = phoneNumber.substring(7);

  // 生成4个星号代替中间四位
  const middleStars = "*".repeat(4);

  // 拼接结果
  const maskedPhoneNumber = firstThree + middleStars + lastFour;

  return maskedPhoneNumber;
};

onMounted(async () => {
  const storageData = getStore(`U_LOAN_PROCESS_${store.param.userId}`);
  store.info.mobileNo = hidePhoneMiddleFour(storageData.mobileNo);
  reportList.value = [];
  window.addEventListener("offline", updateNetwork);
  window.addEventListener("online", handleNetwork);
  store.send();
});

onUnmounted(() => {
  window.removeEventListener("online");
  window.removeEventListener("offline");
  // 组件卸载时清除倒计时定时器
  if (intervalId.value) {
    clearInterval(intervalId.value);
  }
});
</script>

<style scoped>
:deep(.hnr-field__error-message) {
  position: absolute;
  margin-top: 85px;
}

:deep(.hnr-field--withMargin) {
  margin: 0 !important;
  width: 100vw;
}

:deep(.hnr-field__input-area:before) {
  height: 0 !important;
}

:deep(.hnr-field__input-area input, .hnr-field__input-area textarea) {
  text-align: left !important;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  text-align: center;
}

.content-top {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--hnr-color-background-cardview);
}

.sms-title {
  padding-top: 88px;
  margin: var(--hnr-elements-margin-vertical-L) auto 4px auto;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}

.sms-desc {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.sms-input {
  margin: 36px 12px auto 12px !important;
  align-items: center;
  height: 36px;
  line-height: 56px;
  display: flex;
  padding: var(--hnr-elements-margin-horizontal-M2);
  background: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-input-box-corner-radius);
}

.sms-input-name {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

.footer {
  max-width: calc(100vw - 48px) !important;
  width: calc(100vw - 48px);
  position: fixed;
  left: 24px;
  bottom: var(--hnr-default-padding-bottom-fixed);
}
</style>
