import { defineStore } from "pinia";

export default defineStore("loan/infoform", {
  state: () => ({
    // 页面初始数据
    inputBoxOffsetWidth: 0,
    inputCompanyBoxOffsetWidth: 0,
    inputCompanyBoxTextOffsetWidth: 80,
    inputBoxTextOffsetWidth: 80,
    loading: false,
    infoFileds: [],
    ocrResultStatus: "未上传",
    submitInfo: {
      applyNo: "", // 借款申请订单号
      faceCmpResult: null, // 活体识别结果
      residentialAddr: "", // 居住地
      // 拼接成居住地字段
      area: "",
      homeAddress: "",
      familyAddr: "", // 家庭地址同居住地址
      marriage: "", // 婚姻状态
      education: "", // 教育程度
      company: "", // 工作单位
      // 拼接成工作单位字段
      workplace: "",
      workAddress: "",
      career: "", // 职业
      income: "", // 收入
      ocrResult: null, // 身份证Ocr数据
      fileInfos: [
        {
          type: "CARD_FRONT_PHOTO",
          value: "",
          identifier: "identifier",
        },
        {
          type: "CARD_BACK_PHOTO",
          value: "",
          identifier: "identifier",
        },
      ], // 文件信息
      relationInfos: [
        {
          // 联系人信息
          relation: "",
          name: "",
          mobileNo: "",
        },
        {
          // 联系人信息
          relation: "",
          name: "",
          mobileNo: "",
        },
      ],
    },
    param: {},
    supplierId: "",
  }),
  actions: {},
  backDisabled: false,
});
