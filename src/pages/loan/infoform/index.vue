<template>
  <div
    ref="containerRef"
    class="container"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
  >
    <div ref="main" style="flex: 1">
      <div class="content-top" style="z-index: 1000">
        <hnr-nav-bar
          transparent="true"
          class="nav-padding-top"
          style="z-index: 1000"
          title="完善资料"
        >
          <template #left>
            <icsvg-public-back-filled @click="returnClick"></icsvg-public-back-filled>
          </template>
        </hnr-nav-bar>
      </div>
      <div
        ref="contentParRef"
        class="content"
        style="height: calc(100vh - 165px); overflow-y: scroll"
      >
        <div ref="contentRef" :style="{ height: contentHeight }">
          <div v-if="infoFileds.length" class="content-tip">
            <span class="icon-confidentiality"></span>
            <span>{{ privacyNotice }}</span>
          </div>
          <hnr-cell-group
            v-if="
              infoFileds.includes('ID_CARD_OCR') ||
              infoFileds.includes('EDUCATION') ||
              infoFileds.includes('RESIDENTIAL_ADDR') ||
              infoFileds.includes('FAMILY_ADDR')
            "
            title="基础信息"
            class="basic"
            style="border-radius: var(--dp12)"
          >
            <div v-if="infoFileds.includes('ID_CARD_OCR')" class="cell-item" @click="cardClick">
              <div class="defined-item">
                <div class="defined-item-title">身份证件</div>
                <div @click="handleReportClick('fileInfos')">
                  <hnr-field
                    v-if="data.ocrResultStatus === '未上传'"
                    :placeholder="data.ocrResultStatus"
                    readonly
                  />
                  <hnr-field v-else v-model="data.ocrResultStatus" readonly />
                </div>
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider
              v-if="
                infoFileds.includes('ID_CARD_OCR') &&
                (infoFileds.includes('MARRIAGE') ||
                  infoFileds.includes('EDUCATION') ||
                  infoFileds.includes('RESIDENTIAL_ADDR') ||
                  infoFileds.includes('FAMILY_ADDR'))
              "
              line
            />
            <div v-if="infoFileds.includes('MARRIAGE')" class="cell-item" @click="marriageClick">
              <div class="defined-item">
                <div class="defined-item-title">婚姻状态</div>
                <hnr-field v-model="data.submitInfo.marriage" readonly placeholder="请选择" />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider
              v-if="
                infoFileds.includes('MARRIAGE') &&
                (infoFileds.includes('EDUCATION') ||
                  infoFileds.includes('RESIDENTIAL_ADDR') ||
                  infoFileds.includes('FAMILY_ADDR'))
              "
              line
            />
            <div v-if="infoFileds.includes('EDUCATION')" class="cell-item" @click="educatClick">
              <div class="defined-item">
                <div class="defined-item-title">学历</div>
                <hnr-field v-model="data.submitInfo.education" readonly placeholder="请选择" />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider
              v-if="
                infoFileds.includes('EDUCATION') &&
                (infoFileds.includes('RESIDENTIAL_ADDR') || infoFileds.includes('FAMILY_ADDR'))
              "
              line
            />
            <div
              v-if="infoFileds.includes('RESIDENTIAL_ADDR')"
              class="cell-item"
              @click="areaClick"
            >
              <div class="defined-item">
                <div class="defined-item-title">居住地</div>
                <hnr-field v-model="data.submitInfo.area" readonly placeholder="省、市、区" />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider
              v-if="infoFileds.includes('RESIDENTIAL_ADDR') && infoFileds.includes('FAMILY_ADDR')"
              line
            />
            <div
              v-if="infoFileds.includes('FAMILY_ADDR')"
              ref="inputBoxLineRef"
              class="cell-item"
              style="display: flex; flex-direction: column"
            >
              <div class="defined-item" style="align-items: flex-start">
                <div ref="inputBoxTextRef" class="defined-item-title">居住地址</div>
                <textarea
                  ref="inputBoxRef"
                  v-model="data.submitInfo.homeAddress"
                  class="inputBox"
                  placeholder="小区、写字楼、门牌号"
                  maxlength="100"
                  @focus="handleFocus"
                  @update:model-value="handleReportClick('familyAddr', 'input')"
                  @input="checkFamilyAddr"
                />
              </div>
              <div v-if="errorFamilyMessages">
                <hnr-divider
                  line
                  class="divider-line"
                  :style="{
                    width: `${data.inputBoxOffsetWidth}px`,
                    marginLeft: `${data.inputBoxTextOffsetWidth}px`,
                  }"
                />
                <div
                  class="field-message"
                  :style="{ marginLeft: `${data.inputBoxTextOffsetWidth}px` }"
                >
                  {{ errorFamilyMessages }}
                </div>
              </div>
            </div>
          </hnr-cell-group>
          <hnr-cell-group
            v-if="infoFileds.includes('COMPANY')"
            inset
            title="工作信息"
            class="basic"
          >
            <div class="cell-item" @click="workplaceClick">
              <div class="defined-item">
                <div class="defined-item-title">工作地</div>
                <hnr-field
                  v-model="data.submitInfo.workplace"
                  readonly
                  placeholder="省、市、区"
                  @click="handleReportClick('company')"
                />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider line />
            <div
              ref="inputCompanyLineRef"
              class="cell-item"
              style="display: flex; flex-direction: column"
            >
              <div class="defined-item" style="align-items: flex-start">
                <div ref="inputCompanyBoxTextRef" class="defined-item-title">详细地址</div>
                <textarea
                  ref="inputCompanyBoxRef"
                  v-model="data.submitInfo.workAddress"
                  class="inputBox"
                  placeholder="小区、写字楼、门牌号"
                  maxlength="100"
                  @focus="handleFocus"
                  @update:model-value="handleReportClick('familyAddr', 'input')"
                  @input="checkCompanyAddr"
                />
              </div>
              <div v-if="errorCompanyMessages">
                <hnr-divider
                  line
                  class="divider-line"
                  :style="{
                    width: `${data.inputCompanyBoxOffsetWidth}px`,
                    marginLeft: `${data.inputCompanyBoxTextOffsetWidth}px`,
                  }"
                />
                <div
                  class="field-message"
                  :style="{ marginLeft: `${data.inputCompanyBoxTextOffsetWidth}px` }"
                >
                  {{ errorCompanyMessages }}
                </div>
              </div>
            </div>
          </hnr-cell-group>
          <hnr-cell-group
            v-if="infoFileds.includes('CAREER_INCOME') || infoFileds.includes('CAREER')"
            inset
            title="职业信息"
            class="basic"
          >
            <div
              v-if="infoFileds.includes('CAREER') || infoFileds.includes('CAREER_INCOME')"
              class="cell-item"
              @click="careerClick"
            >
              <div class="defined-item">
                <div class="defined-item-title">职业</div>
                <hnr-field
                  v-model="data.submitInfo.career"
                  readonly
                  placeholder="请选择"
                  @click="handleReportClick('career')"
                />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
            <hnr-divider v-if="infoFileds.includes('CAREER_INCOME')" line />
            <div v-if="infoFileds.includes('CAREER_INCOME')" class="cell-item" @click="incomeClick">
              <div class="defined-item">
                <div class="defined-item-title">月收入</div>
                <hnr-field
                  v-model="data.submitInfo.income"
                  readonly
                  placeholder="请选择"
                  @click="handleReportClick('income')"
                />
              </div>
              <span class="icon-arrow-right"></span>
            </div>
          </hnr-cell-group>
          <div v-if="infoFileds.includes('BASICINFO_FILL')">
            <div style="margin: 20px 0px 8px 0px; padding-left: 24px">
              <div
                style="
                  color: var(--hnr-text-color-primary);
                  font-weight: var(--hnr-font-weight-medium);
                "
              >
                联系人
              </div>
              <div style="font-size: var(--hnr-body-2); color: var(--hnr-text-color-secondary)">
                <span>您的信息将被严格保密</span>
                <hnr-icon
                  class="tip-icon"
                  name="/wallet-loan-web/pages/loan/about.svg"
                  style="
                    width: var(--hnr-body-2);
                    height: var(--hnr-body-2);
                    vertical-align: sub;
                    --fill-color: var(--hnr-color-secondary) !important;
                  "
                  @click.stop="handleAboutClick"
                />
              </div>
            </div>

            <hnr-cell-group
              v-for="(info, index) in getRelationInfos()"
              :key="index"
              class="basic"
              style="border-radius: var(--dp12)"
              :style="{
                'margin-bottom':
                  getRelationInfos().length > 1 && index === 0 ? 'var(--dp12)' : '0px',
              }"
            >
              <div class="cell-item" @click="relationshipClick(index)">
                <div class="defined-item">
                  <div class="defined-item-title">关系</div>
                  <hnr-field
                    v-model="relationInfos[index].relation"
                    readonly
                    placeholder="请选择"
                  />
                </div>
                <span class="icon-arrow-right"></span>
              </div>
              <hnr-divider line />
              <div class="cell-item">
                <div class="defined-item" style="align-items: flex-start">
                  <div class="defined-item-title">姓名</div>
                  <hnr-field
                    v-model="info.name"
                    :placeholder="`联系人${index + 1}姓名`"
                    @focus="handleFocus"
                    @blur="checkName(index)"
                    @input="inputNameCheck(index)"
                  />
                </div>
              </div>
              <hnr-divider
                line
                :style="{
                  '--border-color': errorNameMessages[index]
                    ? 'var(--hnr-color-error)'
                    : 'var(--hnr-list-divider)',
                }"
              />
              <span v-if="errorNameMessages[index]" class="errorMsg">{{
                errorNameMessages[index]
              }}</span>
              <div class="cell-item">
                <div class="defined-item" style="align-items: flex-start">
                  <div class="defined-item-title">手机号</div>
                  <hnr-field
                    v-model="info.mobileNo"
                    type="number"
                    :placeholder="`联系人${index + 1}手机号`"
                    @blur="checkMobileNo(index)"
                    @input="inputNoCheck(index)"
                    @focus="handleFocus"
                  />
                </div>
              </div>
              <hnr-divider
                v-if="errorMessages[index]"
                line
                :style="{
                  '--border-color': 'var(--hnr-color-error)',
                }"
              />
              <span
                v-if="errorMessages[index]"
                class="errorMsg"
                style="margin-bottom: var(--dp12)"
                >{{ errorMessages[index] }}</span
              >
            </hnr-cell-group>
          </div>
        </div>
      </div>
    </div>
    <no-ssr>
      <hnr-dialog
        v-model:show="status.showAbountDialog"
        :show-confirm-button="false"
        :show-cancel-button="false"
        :lazy-render="false"
        :narrow-padding="true"
        :before-close="() => false"
        style="
          border-radius: var(--dp30) !important;
          background-image: url(/wallet-loan-web/pages/loan/aboutBg.png) !important;
          background-size: 100% 56% !important;
          background-repeat: no-repeat !important;
          background-position: top !important;
        "
      >
        <div
          style="
            height: 100%;
            box-sizing: border-box;
            padding: 0 var(--hnr-elements-margin-horizontal-M2) 0
              var(--hnr-elements-margin-horizontal-M2);
          "
        >
          <div
            style="
              font-size: var(--hnr-headline-7);
              font-weight: var(--hnr-font-weight-medium);
              color: var(--hnr-text-color-primary);
            "
          >
            <div>我们承诺</div>
            <div>您的信息将被严格保密</div>
          </div>
          <div
            style="
              margin: var(--hnr-elements-margin-vertical-L2) 0
                var(--hnr-elements-margin-horizontal-M2);
              font-size: var(--hnr-body-2);
              color: var(--hnr-text-color-secondary);
            "
          >
            对于您的联系人，我们承诺：
          </div>
          <div
            style="
              padding: var(--hnr-elements-margin-vertical-M2)
                var(--hnr-elements-margin-horizontal-M2);
              border-radius: var(--hnr-default-corner-radius-s);
              opacity: 1;
              background: var(--hnr-color-control-normal);
              font-size: var(--hnr-body-3);
            "
          >
            <div
              class="dot"
              style="display: flex; justify-content: space-between; margin-bottom: var(--dp8)"
            >
              <span style="margin-left: var(--dp14); color: var(--hnr-text-color-secondary)"
                >正常还款无逾期</span
              ><span>不会联系</span>
            </div>
            <div class="dot" style="display: flex; justify-content: space-between">
              <span style="margin-left: var(--dp14); color: var(--hnr-text-color-secondary)"
                >出现逾期</span
              ><span>先联系您</span>
            </div>
          </div>
          <div style="text-align: center; margin-top: var(--dp28)">
            <hnr-button type="default" style="width: 100%" @click="handleAboutClose"
              >知道了</hnr-button
            >
          </div>
        </div>
      </hnr-dialog>
    </no-ssr>
    <no-ssr>
      <hnr-dialog
        v-model:show="status.areaShow"
        title="选择城市地区"
        button-direction="row"
        :lazy-render="false"
        :narrow-padding="true"
      >
        <hnr-picker ref="picker" class="myPicker" :columns="cityList" />
        <template #footer>
          <div class="popup-btn">
            <hnr-button type="text" @click="() => (status.areaShow = false)">取消</hnr-button>
            <hnr-button type="text" @click="areaSureClick">确定</hnr-button>
          </div>
        </template>
      </hnr-dialog>
    </no-ssr>
    <no-ssr>
      <hnr-popup
        v-model:show="status.marriageShow"
        v-touch:swipe.bottom="() => (status.marriageShow = false)"
        position="bottom"
        round
        class="rele-popup"
      >
        <div class="rela-pull" @click="() => (status.marriageShow = false)">
          <span class="icon-arrow-pull" teleport="hnr-divider"></span>
        </div>
        <div class="rele-title">请选择您的婚姻状态</div>
        <hnr-list inset class="relationship-radio">
          <div
            v-for="(item, index) in marriageColumns"
            :key="item.value"
            class="relationship"
            @click="checkMarriageItemClick(item)"
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: var(--dp12) 0;
              "
            >
              <span
                style="
                  font-size: calc(var(--hnr-body-1) * var(--hnr-large-rate));
                  color: var(--hnr-text-color-primary);
                  font-weight: var(--hnr-font-weight-medium);
                "
              >
                {{ item.text }}
              </span>
              <hnr-checkbox :name="item.text" :checked="data.submitInfo.marriage === item.text" />
            </div>
            <hnr-divider
              v-if="index !== marriageColumns.length - 1"
              style="margin: 0 !important"
              line
            />
          </div>
        </hnr-list>
      </hnr-popup>
    </no-ssr>
    <no-ssr>
      <hnr-popup
        v-model:show="status.educatShow"
        v-touch:swipe.bottom="() => (status.educatShow = false)"
        position="bottom"
        round
        class="rele-popup"
      >
        <div class="rela-pull" @click="() => (status.educatShow = false)">
          <span class="icon-arrow-pull" teleport="hnr-divider"></span>
        </div>
        <div class="rele-title">请选择您的学历</div>
        <hnr-list inset class="relationship-radio">
          <div
            v-for="(item, index) in educatColumns"
            :key="item.value"
            class="relationship"
            @click="checkEducatItemClick(item)"
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: var(--dp12) 0;
              "
            >
              <span
                style="
                  font-size: calc(var(--hnr-body-1) * var(--hnr-large-rate));
                  color: var(--hnr-text-color-primary);
                  font-weight: var(--hnr-font-weight-medium);
                "
              >
                {{ item.text }}
              </span>
              <hnr-checkbox :name="item.text" :checked="data.submitInfo.education === item.text" />
            </div>
            <hnr-divider
              v-if="index !== educatColumns.length - 1"
              style="margin: 0 !important"
              line
            />
          </div>
        </hnr-list>
      </hnr-popup>
    </no-ssr>
    <no-ssr>
      <hnr-dialog
        v-model:show="status.workplaceShow"
        title="请选择您的城市地区"
        button-direction="row"
        :lazy-render="false"
        :narrow-padding="true"
      >
        <hnr-picker ref="picker" class="myPicker" :columns="cityList" />
        <template #footer>
          <div class="popup-btn">
            <hnr-button type="text" @click="() => (status.workplaceShow = false)">取消</hnr-button>
            <hnr-button type="text" @click="workplaceSureClick">确定</hnr-button>
          </div>
        </template>
      </hnr-dialog>
    </no-ssr>
    <no-ssr>
      <hnr-dialog
        v-model:show="status.careerShow"
        title="请选择您的职业"
        button-direction="row"
        :lazy-render="false"
        :narrow-padding="true"
      >
        <hnr-picker ref="picker" class="careerPicker" :columns="careerColumns" />
        <template #footer>
          <div class="popup-btn">
            <hnr-button type="text" @click="() => (status.careerShow = false)">取消</hnr-button>
            <hnr-button type="text" @click="careerSureClick">确定</hnr-button>
          </div>
        </template>
      </hnr-dialog>
    </no-ssr>
    <no-ssr>
      <hnr-popup
        v-model:show="status.incomeShow"
        v-touch:swipe.bottom="() => (status.incomeShow = false)"
        position="bottom"
        round
        class="rele-popup"
      >
        <div class="rela-pull" @click="() => (status.incomeShow = false)">
          <span class="icon-arrow-pull" teleport="hnr-divider"></span>
        </div>
        <div class="rele-title">请选择您的月收入</div>
        <hnr-list inset class="relationship-radio">
          <div
            v-for="(item, index) in incomeColumns"
            :key="item.text"
            class="relationship"
            @click="checkIncomeItemClick(item)"
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                padding: var(--dp12) 0;
              "
            >
              <span
                style="
                  font-size: calc(var(--hnr-body-1) * var(--hnr-large-rate));
                  color: var(--hnr-text-color-primary);
                  font-weight: var(--hnr-font-weight-medium);
                "
              >
                {{ item.text }}
              </span>
              <hnr-checkbox :name="item.text" :checked="data.submitInfo.income === item.text" />
            </div>
            <hnr-divider
              v-if="index !== incomeColumns.length - 1"
              style="margin: 0 !important"
              line
            />
          </div>
        </hnr-list>
      </hnr-popup>
    </no-ssr>
    <hnr-popup
      v-model:show="status.relationshipShow"
      v-touch:swipe.bottom="() => (status.relationshipShow = false)"
      position="bottom"
      round
      class="rele-popup"
    >
      <div class="rela-pull" @click="() => (status.relationshipShow = false)">
        <span class="icon-arrow-pull" teleport="hnr-divider"></span>
      </div>
      <div class="rele-title">请选择联系人与您的关系</div>
      <hnr-list inset class="relationship-radio">
        <div
          v-for="(item, index) in list"
          :key="item"
          class="relationship"
          @click="checkItemClick(item)"
        >
          <div
            style="
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              padding: var(--dp12) 0;
            "
          >
            <span
              style="
                font-size: calc(var(--hnr-body-1) * var(--hnr-large-rate));
                color: var(--hnr-text-color-primary);
                font-weight: var(--hnr-font-weight-medium);
              "
            >
              {{ item }}
            </span>
            <hnr-checkbox :name="item" :checked="relationInfos[relCurIndex].relation === item" />
          </div>
          <hnr-divider v-if="index !== list.length - 1" style="margin: 0 !important" line />
        </div>
      </hnr-list>
      <!-- <hnr-button class="footer footer-relation" type="primary" @click="relationshipSureClick">确定</hnr-button> -->
    </hnr-popup>
    <!-- :class="infoFileds.length >= 7 ? 'footer-normal' : 'footer-fixed'" -->
    <div style="display: flex; justify-content: center">
      <hnr-button
        v-if="infoFileds.length"
        :disabled="ifDisabled || infoFileds.includes('PWD')"
        class="footer-position"
        type="primary"
        standard-width="true"
        @click="submitClick"
        >提交</hnr-button
      >
    </div>

    <div v-show="data.loading">
      <hnr-dialog
        v-model:show="data.loading"
        title=""
        message=""
        button-direction="row"
        :before-close="disabledOverlayClose"
      >
        <template #footer>
          <div class="loading">
            <div class="loading-text">正在提交</div>
            <div class="loading-logo">
              <hnr-loading />
            </div>
          </div>
        </template>
      </hnr-dialog>
    </div>
  </div>
</template>
<script setup>
import { onServerPrefetch, ref, onMounted, watch, computed, onBeforeUpdate, nextTick } from "vue";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import lodash from "lodash";
import useStore from "./store";
import { initStore, request, userBehaviorLogReporting } from "../../../helpers/utils";
import { getCurrentCpInfo, determineReActiveStatus } from "../../../helpers/configUtils";
import {
  doOCR,
  report,
  regNativeEvent,
  decrypt,
  encrypt,
  setCp,
} from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { loanNext } from "../../../helpers/next";
import { showRetentionDialog } from "../../../helpers/retention-dialog-utils";
import cityList from "../../../helpers/city";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";

const { store, data } = initStore(useStore);
const containerRef = ref(null);
const status = ref({});
const picker = ref(null);
const checkboxRefs = ref([]);
const errorMessages = ref([]);
const errorNameMessages = ref([]);
const errorCompanyMessages = ref("");
const errorFamilyMessages = ref("");
const startX = ref(null);
const checkSubStatus = ref(true);
const relCurIndex = ref(0);
const nameValidate = ref([]);
const noValidate = ref([]);
const curRelaIndex = ref(0);
const checkItem = ref("配偶");
const checkMarriageItem = ref("未婚");
const checkEducatItem = ref("本科");
const checkIncomeItem = ref("1万-2万");
const infoFileds = ref([]);
const contentParRef = ref(null);
const contentRef = ref(null);
const contentHeight = ref("auto");
const reportList = ref([]);
const inputBoxLineRef = ref();
const inputBoxRef = ref();
const inputBoxTextRef = ref();
const inputCompanyLineRef = ref();
const inputCompanyBoxRef = ref(null);
const inputCompanyBoxTextRef = ref(null);
const privacyNotice = ref("");

const list = ["配偶", "父母", "子女", "兄弟姐妹", "其他亲属"];

onBeforeUpdate(() => {
  checkboxRefs.value = [];
});

// 'ID_CARD_OCR',
// 'BASICINFO_FILL',
// 'BASICINFO_FILL2',
// 'RESIDENTIAL_ADDR',
// 'FAMILY_ADDR',
// 'EDUCATION',
// 'COMPANY',
// 'CAREER_INCOME',

const inputReport = lodash.debounce((param) => {
  report("wallet_page_result", { page_name: "loan_info_page", fields: [param] });
}, 1000);

const handleAboutClick = () => {
  if (!reportList.value.includes("aboutClick")) {
    reportList.value.push("aboutClick");
    report("wallet_page_click", { click_name: "loan_info_relation_info" });
  }
  status.value.showAbountDialog = true;
  if (!reportList.value.includes("aboutView")) {
    reportList.value.push("aboutView");
    report("wallet_page_view", { page_name: "loan_info_relation_info" });
  }
};

const handleAboutClose = () => {
  status.value.showAbountDialog = false;
  if (!reportList.value.includes("aboutClose")) {
    reportList.value.push("aboutClose");
    report("wallet_page_click", { click_name: "loan_info_relation_info_close" });
  }
};

const handleReportClick = (param, type) => {
  if (!reportList.value.includes(param)) {
    reportList.value.push(param);
    if (type) {
      inputReport(param);
    } else report("wallet_page_result", { page_name: "loan_info_page", fields: [param] });
  }
};

const disabledOverlayClose = () => false;

const familyValidate = computed(() => {
  return (
    store.submitInfo.homeAddress &&
    store.submitInfo.homeAddress.length >= 4 &&
    store.submitInfo.homeAddress.length <= 100
  );
});

const companyValidate = computed(() => {
  return (
    store.submitInfo.workAddress &&
    store.submitInfo.workAddress.length >= 4 &&
    store.submitInfo.workAddress.length <= 100
  );
});

const validate = computed(
  () =>
    nameValidate.value.every((item) => item === true) &&
    noValidate.value.every((child) => child === true),
);

const relationInfos = ref([
  {
    // 联系人信息
    relation: "",
    name: "",
    mobileNo: "",
  },
  {
    // 联系人信息
    relation: "",
    name: "",
    mobileNo: "",
  },
]);

const areAllPropertiesNotEmpty = (obj) => {
  let bool = true;
  // 检查一个值是否为非空
  function isNotEmpty(value) {
    // 空字符串、null、undefined 以及空数组都被认为是空的
    return (
      value !== null &&
      value !== undefined &&
      value !== "" &&
      !(Array.isArray(value) && value.length === 0)
    );
  }

  // 递归检查对象的所有属性
  function checkProperties(attr) {
    if (typeof attr !== "object" || attr === null) {
      // 如果不是对象或数组，直接检查值
      return isNotEmpty(attr);
    }

    Object.keys(attr).forEach((key) => {
      if (Object.prototype.hasOwnProperty.call(attr, key)) {
        const value = attr[key];
        // 如果属性值为空，返回false
        if (!checkProperties(value)) {
          bool = false;
        }
      }
    });

    // 所有属性都不为空，返回true
    return bool;
  }

  // 调用递归函数并返回结果
  return checkProperties(obj);
};

const getRelationInfos = () => {
  const relaInfos = infoFileds.value.includes("BASICINFO_FILL2")
    ? data.value.submitInfo.relationInfos
    : data.value.submitInfo.relationInfos.slice(0, 1);
  return relaInfos;
};

const ifDisabled = computed(() => {
  return (
    checkSubStatus.value ||
    (infoFileds.value.includes("BASICINFO_FILL") ? !validate.value : false) ||
    (infoFileds.value.includes("FAMILY_ADDR") ? !familyValidate.value : false) ||
    (infoFileds.value.includes("COMPANY") ? !companyValidate.value : false)
  );
});

const filterSubmitKey = (obj, fileds, isCache) => {
  const newObj = {};
  if (fileds.length) {
    if (fileds.includes("ID_CARD_OCR") && !isCache) {
      newObj.fileInfos = obj.fileInfos;
    }
    if (fileds.includes("EDUCATION")) {
      newObj.education = obj.education;
    }
    if (fileds.includes("MARRIAGE")) {
      newObj.marriage = obj.marriage;
    }
    if (fileds.includes("RESIDENTIAL_ADDR")) {
      newObj.residentialAddr = obj.familyAddr;
      newObj.area = obj.area;
      newObj.homeAddress = obj.homeAddress;
    }
    if (fileds.includes("FAMILY_ADDR")) {
      newObj.familyAddr = obj.familyAddr;
    }
    if (fileds.includes("COMPANY")) {
      newObj.company = obj.company;
      newObj.workplace = obj.workplace;
      newObj.workAddress = obj.workAddress;
    }
    if (fileds.includes("CAREER_INCOME")) {
      newObj.income = obj.income;
      newObj.career = obj.career;
    }
    if (fileds.includes("CAREER")) {
      newObj.career = obj.career;
    }
    if (fileds.includes("BASICINFO_FILL")) {
      if (fileds.includes("BASICINFO_FILL2")) {
        newObj.relationInfos = obj.relationInfos;
      } else {
        newObj.relationInfos = obj.relationInfos.slice(0, 1);
      }
    }
  }
  return newObj;
};

watch(
  () => data.value.submitInfo,
  (n) => {
    store.submitInfo.familyAddr = `${n.area}${n.homeAddress}`;
    store.submitInfo.residentialAddr = n.familyAddr;
    store.submitInfo.company = `${n.workplace}${n.workAddress}`;
    let obj = {};
    obj = filterSubmitKey(n, infoFileds.value, false);
    const storage = getStore(`U_LOAN_PROCESS_${store.param.userId}`) || null;
    if (storage) {
      storage.infoCache = { ...storage.infoCache, ...filterSubmitKey(n, infoFileds.value, false) };
      setWithExactExpireUnit(`U_LOAN_PROCESS_${store.param.userId}`, storage, 4, "D");
    }
    if (infoFileds.value.length) {
      checkSubStatus.value = !areAllPropertiesNotEmpty(obj);
    }
  },
  { deep: true },
);

const toHomeClick = () => {
  if (!reportList.value.includes("toHomeClick")) {
    reportList.value.push("toHomeClick");
  }
};

const handleConfirmClick = () => {
  if (!reportList.value.includes("handleConfirmClick")) {
    reportList.value.push("handleConfirmClick");
  }
};

// 手势返回处理
function handleTouchStart(event) {
  startX.value = event.touches[0].clientX;
}
function handleTouchEnd(event) {
  const endX = event.changedTouches[0].clientX;
  const distance = endX - startX.value;

  // 判断是否是从屏幕边缘向右滑动
  if (distance > 50 && startX.value < 50) {
    // 这里的 50 是阈值，可以根据需要调整
    showRetentionDialog({
      text: "仅差一步即可完成借款，确定离开？",
      cancelFunc: toHomeClick,
      confirmFunc: handleConfirmClick,
    });
  }
}

const returnClick = () => {
  if (store.backDisabled) {
    return;
  }
  if (!(reportList.value.includes("returnClick") || reportList.value.includes("onBack"))) {
    reportList.value.push("returnClick");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
};

const checkName = (index) => {
  curRelaIndex.value = index;
};

const inputName = lodash.debounce(async (index) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  const { name } = data.value.submitInfo.relationInfos[index];
  if (name === "") {
    nameValidate.value[index] = true;
    return;
  }
  const len = getRelationInfos()?.length;
  const regex = /[^·\u4e00-\u9fa5]/g;
  if (regex.test(name)) {
    await nextTick();
    errorNameMessages.value[index] = "请输入正确的名字";
    nameValidate.value[index] = false;
  } else if (
    len > 1 &&
    index === len - 1 &&
    name === data.value.submitInfo.relationInfos[index - 1].name
  ) {
    errorNameMessages.value[index] = "该姓名与另一联系人相同，请重新填写";
    nameValidate.value[index] = false;
  } else if (len > 1 && index === 0) {
    if (name === data.value.submitInfo.relationInfos[index + 1].name) {
      errorNameMessages.value[index + 1] = "该姓名与另一联系人相同，请重新填写";
      nameValidate.value[index + 1] = false;
    } else {
      errorNameMessages.value = ["", ""];
      nameValidate.value = [true, true];
    }
  } else {
    await nextTick();
    errorNameMessages.value[index] = "";
    nameValidate.value[index] = true;
  }
}, 800);

const inputNameInit = async (index) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  const { name } = data.value.submitInfo.relationInfos[index];
  if (name === "") {
    errorNameMessages.value[index] = "";
    nameValidate.value[index] = true;
    return;
  }
  const len = getRelationInfos()?.length;
  const regex = /[^·\u4e00-\u9fa5]/g;
  if (regex.test(name)) {
    await nextTick();
    errorNameMessages.value[index] = "请输入正确的名字";
    nameValidate.value[index] = false;
  } else if (
    len > 1 &&
    index === len - 1 &&
    name === data.value.submitInfo.relationInfos[index - 1].name
  ) {
    errorNameMessages.value[index] = "该姓名与另一联系人相同，请重新填写";
    nameValidate.value[index] = false;
  } else if (len > 1 && index === 0) {
    if (name === data.value.submitInfo.relationInfos[index + 1].name) {
      errorNameMessages.value[index + 1] = "该姓名与另一联系人相同，请重新填写";
      nameValidate.value[index + 1] = false;
    } else {
      errorNameMessages.value = ["", ""];
      nameValidate.value = [true, true];
    }
  } else {
    await nextTick();
    errorNameMessages.value[index] = "";
    nameValidate.value[index] = true;
  }
};

const inputNameCheck = (index) => {
  errorNameMessages.value[index] = "";
  inputName(index);
};

const checkMobileNo = (index) => {
  curRelaIndex.value = index;
};

const targetInputTop = ref(null);
const contentOffsetHeight = ref(null);
const targetEl = ref(null);

const getOffsetTop = (element) => {
  if (element && contentRef.value) {
    const elementRect = element.getBoundingClientRect();
    const containerRect = contentRef.value.getBoundingClientRect();
    const offsetTop = elementRect.top - containerRect.top;
    return parseInt(offsetTop, 10);
  }
  return 0;
};

const handleFocus = async (event) => {
  if (event) {
    targetEl.value = event.target;
  }
};

const inputCheck = lodash.debounce(async (index) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  const { mobileNo } = data.value.submitInfo.relationInfos[index];
  if (mobileNo === "") {
    noValidate.value[index] = true;
    return;
  }
  const len = getRelationInfos()?.length;
  const regex = /^1[3-9]\d{9}$/;
  if (mobileNo && !regex.test(mobileNo)) {
    await nextTick();
    errorMessages.value[index] = "请输入正确的手机号";
    noValidate.value[index] = false;
  } else if (
    len > 1 &&
    index === len - 1 &&
    mobileNo === data.value.submitInfo.relationInfos[index - 1].mobileNo
  ) {
    errorMessages.value[index] = "该手机号与另一联系人相同，请重新填写";
    noValidate.value[index] = false;
  } else if (len > 1 && index === 0) {
    if (mobileNo === data.value.submitInfo.relationInfos[index + 1].mobileNo) {
      errorMessages.value[index] = "";
      noValidate.value[index] = true;
      errorMessages.value[index + 1] = "该手机号与另一联系人相同，请重新填写";
      noValidate.value[index + 1] = false;
    } else {
      errorMessages.value = ["", ""];
      noValidate.value = [true, true];
    }
  } else {
    await nextTick();
    errorMessages.value[index] = "";
    noValidate.value[index] = true;
  }
}, 800);

const inputCheckInit = async (index) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  const { mobileNo } = data.value.submitInfo.relationInfos[index];
  if (mobileNo === "") {
    errorMessages.value[index] = "";
    noValidate.value[index] = true;
    return;
  }
  const len = getRelationInfos()?.length;
  const regex = /^1[3-9]\d{9}$/;
  if (mobileNo && !regex.test(mobileNo)) {
    errorMessages.value[index] = "请输入正确的手机号";
    noValidate.value[index] = false;
  } else if (
    len > 1 &&
    index === len - 1 &&
    mobileNo === data.value.submitInfo.relationInfos[index - 1].mobileNo
  ) {
    errorMessages.value[index] = "该手机号与另一联系人相同，请重新填写";
    noValidate.value[index] = false;
  } else if (len > 1 && index === 0) {
    if (mobileNo === data.value.submitInfo.relationInfos[index + 1].mobileNo) {
      errorMessages.value[index] = "";
      noValidate.value[index] = true;
      errorMessages.value[index + 1] = "该手机号与另一联系人相同，请重新填写";
      noValidate.value[index + 1] = false;
    } else {
      errorMessages.value = ["", ""];
      noValidate.value = [true, true];
    }
  } else {
    await nextTick();
    errorMessages.value[index] = "";
    noValidate.value[index] = true;
  }
};

const inputNoCheck = (index) => {
  errorMessages.value[index] = "";
  inputCheck(index);
};

const educatColumns = ref([]);

const marriageColumns = ref([]);

const careerColumns = ref([]);

const incomeColumns = ref([]);

const params = [
  "loan_dxm_credit_career_1",
  "loan_dxm_credit_income_1",
  "loan_verify_education",
  "loan_verify_marriage",
];

const getCareerList = async () => {
  const res = await request("/loan/api/config/getDictMap", { itemNames: params });
  if (res && res.data) {
    careerColumns.value = res.data.loan_dxm_credit_career_1.map((item) => ({
      ...item,
      text: item.name,
    }));
    incomeColumns.value = res.data.loan_dxm_credit_income_1.map((item) => ({
      ...item,
      text: item.name,
    }));
    educatColumns.value = res.data.loan_verify_education.map((item) => ({
      ...item,
      text: item.name,
    }));
    marriageColumns.value = res.data.loan_verify_marriage.map((item) => ({
      ...item,
      text: item.name,
    }));
    store.submitInfo.career =
      store.submitInfo.career || careerColumns.value.filter((item) => item.value === "02")[0]?.text;
    store.submitInfo.income =
      store.submitInfo.income || incomeColumns.value.filter((item) => item.value === "05")[0]?.text;

    const storageData = getStore(`U_LOAN_PROCESS_${store.param.userId}`);
    storageData.dictMap = res.data;
    setWithExactExpireUnit(`U_LOAN_PROCESS_${store.param.userId}`, storageData, 24, "H");
  }
};

const filterInfoFileds = (arr) => {
  let newArr = [];
  arr.forEach((item) => {
    if ("ID_CARD_OCR".includes(item)) {
      newArr = newArr.concat(["fileInfos"]);
    }
    if ("BASICINFO_FILL".includes(item)) {
      newArr = newArr.concat(["relationInfos"]);
    }
    if ("RESIDENTIAL_ADDR".includes(item)) {
      newArr = newArr.concat(["residentialAddr"]);
    }
    if ("FAMILY_ADDR".includes(item)) {
      newArr = newArr.concat(["familyAddr"]);
    }
    if ("MARRIAGE".includes(item)) {
      newArr = newArr.concat(["marriage"]);
    }
    if ("EDUCATION".includes(item)) {
      newArr = newArr.concat(["education"]);
    }
    if ("COMPANY".includes(item)) {
      newArr = newArr.concat(["company"]);
    }
    if ("CAREER_INCOME".includes(item)) {
      newArr = newArr.concat(["career"], ["income"]);
    }
  });
  return newArr.sort((a, b) => {
    return a[0].localeCompare(b[0]);
  });
};

onServerPrefetch(store.initial);

const uploading = ref(false);

const cardClick = async () => {
  if (!uploading.value) {
    uploading.value = true;
    const cpInfo = await getCurrentCpInfo();
    setCp(cpInfo?.supplierId || 0);
    if (store.param.sdkVersionCode >= 90001000) {
      report("wallet_page_click", { click_name: "h5_idcard_ocr_start" });
    }
    const res = await doOCR({
      reActive: determineReActiveStatus(),
      diversionMethod: cpInfo?.diversionMethod,
    });
    if (res) {
      const bool = res.fileInfos.every((info) => info.value && info.identifier);
      if (bool) {
        data.value.submitInfo.ocrResult = res.ocrResult || null;
        data.value.submitInfo.fileInfos = res.fileInfos;
        showToast({
          message: "上传成功",
          position: "bottom",
        });
        data.value.ocrResultStatus = "已上传";
        const storage = getStore(`U_LOAN_PROCESS_${store.param.userId}`);
        storage.infoCache.fileInfos = [...encrypt(res.fileInfos)];
        setWithExactExpireUnit(`U_LOAN_PROCESS_${store.param.userId}`, storage, 24, "H");
      }
      uploading.value = false;
    }
  }
};

const areaClick = () => {
  if (!reportList.value.includes("residentialAddr")) {
    reportList.value.push("residentialAddr");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["residentialAddr"] });
  }
  status.value.areaShow = true;
};

const areaSureClick = () => {
  const options = picker.value.getSelectedOptions();
  data.value.submitInfo.area = Array.from(
    new Set(options.map((option) => option.text.match(/[\u4e00-\u9fa5]/g).join(""))),
  ).join("");
  status.value.areaShow = false;
};

const marriageClick = () => {
  if (!reportList.value.includes("marriage")) {
    reportList.value.push("marriage");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["marriage"] });
  }
  status.value.marriageShow = true;
};

const educatClick = () => {
  if (!reportList.value.includes("education")) {
    reportList.value.push("education");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["education"] });
  }
  status.value.educatShow = true;
};

const workplaceSureClick = () => {
  const options = picker.value.getSelectedOptions();
  data.value.submitInfo.workplace = Array.from(
    new Set(options.map((option) => option.text.match(/[\u4e00-\u9fa5]/g).join(""))),
  ).join("");
  status.value.workplaceShow = false;
};

const workplaceClick = () => {
  status.value.workplaceShow = true;
};

const relationshipClick = (index) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  relCurIndex.value = index;
  status.value.relationshipShow = true;
};

const checkMarriageItemClick = (item) => {
  if (data.value.submitInfo.marriage && checkMarriageItem.value === item.text) {
    data.value.submitInfo.marriage = "";
  } else {
    data.value.submitInfo.marriage = item.text;
  }
  checkMarriageItem.value = item.text;
  status.value.marriageShow = false;
};

const checkEducatItemClick = (item) => {
  if (data.value.submitInfo.education && checkEducatItem.value === item.text) {
    data.value.submitInfo.education = "";
  } else {
    data.value.submitInfo.education = item.text;
  }
  checkEducatItem.value = item.text;
  status.value.educatShow = false;
};

const checkIncomeItemClick = (item) => {
  if (data.value.submitInfo.income && checkIncomeItem.value === item.text) {
    data.value.submitInfo.income = "";
  } else {
    data.value.submitInfo.income = item.text;
  }
  checkIncomeItem.value = item.text;
  status.value.incomeShow = false;
};

const checkItemClick = (item) => {
  if (!reportList.value.includes("relationInfos")) {
    reportList.value.push("relationInfos");
    report("wallet_page_result", { page_name: "loan_info_page", fields: ["relationInfos"] });
  }
  if (relationInfos.value[relCurIndex.value].relation && checkItem.value === item) {
    relationInfos.value[relCurIndex.value].relation = "";
    data.value.submitInfo.relationInfos[relCurIndex.value].relation = "";
  } else {
    relationInfos.value[relCurIndex.value].relation = item;
    data.value.submitInfo.relationInfos[relCurIndex.value].relation =
      relationInfos.value[relCurIndex.value].relation;
  }
  checkItem.value = item;
  status.value.relationshipShow = false;
};

const careerClick = () => {
  status.value.careerShow = true;
};

const careerSureClick = () => {
  const options = picker.value.getSelectedOptions();
  data.value.submitInfo.career = options[0].text;
  status.value.careerShow = false;
};

const incomeClick = () => {
  status.value.incomeShow = true;
};

const loanNextCallBack = () => {
  Object.keys(status.value).forEach((key) => {
    status.value[key] = false;
  });
};

const submitClick = async () => {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }

  if (infoFileds.value.includes("BASICINFO_FILL")) {
    const res = await request("/loan/api/loan/checkRelationInfo", {
      encryptedParams: {
        relationInfos: getRelationInfos(),
      },
    });
    if (res.code === 0) {
      const invalidInfos = res.data.checkResult?.filter((item) => item.result !== 0);
      if (invalidInfos && invalidInfos.length) {
        const len = getRelationInfos()?.length;
        res.data.checkResult.forEach((info, index) => {
          // 联系人与借款人手机号一致校验
          if (info.result === 1) {
            errorMessages.value[index] = info.errorMsg;
            noValidate.value[index] = false;
          }

          // 联系人之间手机号一致校验
          if (info.result === 2) {
            errorMessages.value[len - 1] = res.data.checkResult[index]?.errorMsg || "";
            noValidate.value[len - 1] = false;
          }
          // 联系人之间姓名一致校验
          if (info.result === 3) {
            errorNameMessages.value[len - 1] = res.data.checkResult[index]?.errorMsg || "";
            nameValidate.value[len - 1] = false;
          }
        });
        return;
      }
    } else {
      showToast({
        message: res.message || "服务异常，请稍后重试",
        position: "center",
      });
      return;
    }
  }

  const { submitInfo } = store;
  const submitInfoCopy = JSON.parse(JSON.stringify(submitInfo));
  delete submitInfoCopy.fileInfos;
  submitInfoCopy.relationInfos = submitInfoCopy.relationInfos.slice(0, 1);
  report("wallet_page_click", {
    click_name: "loan_info_submit",
    fields: [...filterInfoFileds(infoFileds.value)],
  });
  const storage = getStore(`U_LOAN_PROCESS_${store.param.userId}`) || {};

  submitInfo.familyAddr = `${submitInfo.area}${submitInfo.homeAddress}`;
  submitInfo.residentialAddr = submitInfo.familyAddr;
  submitInfo.company = `${submitInfo.workplace}${submitInfo.workAddress}`;
  submitInfo.relationInfos = infoFileds.value.includes("BASICINFO_FILL2")
    ? submitInfo.relationInfos
    : submitInfo.relationInfos.slice(0, 1);
  storage.infos = JSON.parse(JSON.stringify(store.submitInfo));
  storage.infos = filterSubmitKey(storage.infos, infoFileds.value, false);
  setWithExactExpireUnit(`U_LOAN_PROCESS_${store.param.userId}`, storage, 24, "H");
  /* 二次缓存处理 */
  if (storage && storage.openId) {
    const openIdInfo = getStore(`U_LOAN_INFO_STORE_${storage.openId}`);
    decrypt(openIdInfo).then((response) => {
      response.loanSubmitInfo = filterSubmitKey(store.submitInfo, infoFileds.value, true);
      encrypt(response).then((res) => {
        setWithExactExpireUnit(`U_LOAN_INFO_STORE_${storage.openId}`, res, 3000, "D");
      });
    });
  }
  store.loading = true;
  store.backDisabled = true;
  setTimeout(() => {
    store.loading = false;
  }, 3000);
  loanNext(store.param.userId, loanNextCallBack()).finally(() => {
    setTimeout(async () => {
      store.loading = false;
      setTimeout(() => {
        store.backDisabled = false;
        return store.backDisabled;
      }, 2000);
    }, 2000);
  });
};

regNativeEvent("onBack", () => {
  if (store.loading) {
    return;
  }

  const dialogStatusOpens = Object.keys(status.value).filter((key) => status.value[key]);

  if (dialogStatusOpens.length) {
    dialogStatusOpens.forEach((key) => {
      status.value[key] = false;
    });
    return;
  }
  if (!(reportList.value.includes("onBack") || reportList.value.includes("returnClick"))) {
    reportList.value.push("onBack");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
});

regNativeEvent("onResume", () => {
  store.loading = false;
  store.backDisabled = false;
});

regNativeEvent("KeyBoardHeight", async (res) => {
  if (res.result !== 0) {
    const offsetTop = getOffsetTop(targetEl.value);
    const keyBoardHeight = parseInt(res.result / 2, 10);
    if (!contentOffsetHeight.value) contentOffsetHeight.value = contentRef.value.offsetHeight;
    // 56是当前元素实际高度
    targetInputTop.value = offsetTop + 56;
    if (
      contentRef.value.offsetHeight > keyBoardHeight &&
      containerRef.value.offsetHeight - 88 - keyBoardHeight < targetInputTop.value
    ) {
      const scrollTop =
        targetInputTop.value - (containerRef.value.offsetHeight - 88 - keyBoardHeight);
      contentHeight.value = `${contentOffsetHeight.value + scrollTop}px`;
      await nextTick();
      if (contentOffsetHeight.value > contentParRef.value.offsetHeight) {
        contentParRef.value.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
      } else {
        contentRef.value.scrollIntoView({ block: "center", behavior: "smooth" });
      }
    } else {
      contentHeight.value = "auto";
    }
  } else {
    contentHeight.value = "auto";
  }
});

const inputFamilyBoxHeight = () => {
  const inputBoxLine = inputBoxLineRef.value;
  const inputBox = inputBoxRef.value;
  const inputBoxText = inputBoxTextRef.value;
  if (inputBoxLine) {
    if (store.submitInfo.homeAddress === "") {
      inputBox.style.height = "30px";
      inputBoxLine.style.height = "48px";
    } else {
      inputBox.style.height = "auto";
    }
    inputBox.style.height = `${inputBox.scrollHeight - 6}px`;
    inputBoxLine.style.height = `${inputBox.scrollHeight + 12}px`;
    data.value.inputBoxOffsetWidth = inputBox.offsetWidth;
    data.value.inputBoxTextOffsetWidth = inputBoxText.offsetWidth + 16 || 80;
  }
};

const inputCompanyBoxHeight = () => {
  const inputCompanyLine = inputCompanyLineRef.value;
  const inputCompanyBox = inputCompanyBoxRef.value;
  const inputCompanyBoxText = inputCompanyBoxTextRef.value;

  if (inputCompanyBox) {
    if (store.submitInfo.workAddress === "") {
      inputCompanyBox.style.height = "30px";
      inputCompanyLine.style.height = "48px";
    } else {
      inputCompanyBox.style.height = "auto";
    }
    inputCompanyLine.style.height = `${inputCompanyBox.scrollHeight + 12}px`;
    inputCompanyBox.style.height = `${inputCompanyBox.scrollHeight - 6}px`;
    data.value.inputCompanyBoxOffsetWidth = inputCompanyBox.offsetWidth;
    data.value.inputCompanyBoxTextOffsetWidth = inputCompanyBoxText.offsetWidth + 16 || 80;
  }
};

const changeFamilyAddr = lodash.debounce(async (event) => {
  const { value } = event.target;
  if (value.length < 4 && value.length !== 0) {
    await nextTick();
    errorFamilyMessages.value = "输入字符小于4";
    familyValidate.value = false;
  } else {
    await nextTick();
    errorFamilyMessages.value = "";
    familyValidate.value = true;
  }
}, 1000);

const checkFamilyAddr = (event) => {
  errorFamilyMessages.value = "";
  inputFamilyBoxHeight();
  changeFamilyAddr(event);
};

const changeCompanyAddr = lodash.debounce(async (event) => {
  const { value } = event.target;
  if (value.length < 4 && value.length !== 0) {
    await nextTick();
    errorCompanyMessages.value = "输入字符小于4";
    companyValidate.value = false;
  } else {
    errorCompanyMessages.value = "";
    companyValidate.value = true;
  }
}, 1000);

const checkCompanyAddr = (event) => {
  errorCompanyMessages.value = "";
  inputCompanyBoxHeight();
  changeCompanyAddr(event);
};

onMounted(async () => {
  inputFamilyBoxHeight();
  inputCompanyBoxHeight();
  reportList.value = [];
  const cpInfoStorage = await getCurrentCpInfo();
  privacyNotice.value = cpInfoStorage?.privacyNotice;
  data.value.supplierId = cpInfoStorage?.supplierId || 0;
  const storageData = getStore(`U_LOAN_PROCESS_${store.param.userId}`) || {};

  setTimeout(() => {
    userBehaviorLogReporting(
      storageData.applyNo || "",
      106,
      1,
      1,
      data.value.supplierId,
      new Date().getTime(),
    );
  }, 0);

  store.ocrResultStatus =
    storageData.infoCache?.fileInfos &&
    storageData.infoCache?.fileInfos.length &&
    storageData.infoCache?.fileInfos.every((file) => file.value !== "")
      ? "已上传"
      : "未上传";
  infoFileds.value = storageData?.infoFileds || infoFileds.value;
  report("wallet_page_view", {
    page_name: "loan_info_page",
    fields: [...filterInfoFileds(infoFileds.value)],
  });
  let loanCache;
  if (storageData) {
    if (storageData.infoCache) {
      loanCache = { loanSubmitInfo: { ...storageData.infoCache } };
    } else {
      loanCache = storageData.openId ? getStore(`U_LOAN_INFO_STORE_${storageData.openId}`) : null;
      loanCache = await decrypt(loanCache);
    }
    if (loanCache) {
      if (loanCache.loanSubmitInfo) {
        if (
          loanCache.loanSubmitInfo.relationInfos &&
          loanCache.loanSubmitInfo.relationInfos.length <= 1
        ) {
          loanCache.loanSubmitInfo.relationInfos = [
            ...loanCache.loanSubmitInfo.relationInfos,
            { relation: "", name: "", mobileNo: "" },
          ];
        }
        store.submitInfo = { ...store.submitInfo, ...loanCache.loanSubmitInfo };
        if (store.submitInfo.relationInfos) {
          store.submitInfo.relationInfos.forEach((item, index) => {
            inputNameInit(index);
            inputCheckInit(index);
          });
        }

        if (store.submitInfo.homeAddress) {
          changeFamilyAddr({ target: { value: store.submitInfo.homeAddress } });
        }

        if (store.submitInfo.workAddress) {
          checkCompanyAddr({ target: { value: store.submitInfo.workAddress } });
        }

        Object.keys(loanCache.loanSubmitInfo).forEach((key) => {
          if (loanCache.loanSubmitInfo[key] !== "" || !loanCache.loanSubmitInfo[key]) {
            if (key === "area") {
              report("wallet_page_result", {
                page_name: "loan_info_page",
                fields: ["residentialAddr"],
              });
            } else if (key === "homeAddress") {
              report("wallet_page_result", { page_name: "loan_info_page", fields: ["familyAddr"] });
            } else if (key === "workplace" || key === "workAddress") {
              report("wallet_page_result", { page_name: "loan_info_page", fields: ["company"] });
            } else report("wallet_page_result", { page_name: "loan_info_page", fields: [key] });
          }
        });
        relationInfos.value = store.submitInfo.relationInfos;
        checkItem.value = store.submitInfo.relationInfos[0].relation;
        checkMarriageItem.value = store.submitInfo.marriage;
        checkEducatItem.value = store.submitInfo.education;
        checkIncomeItem.value = store.submitInfo.income;
      }
    }
  }

  getCareerList();
  status.value = {
    areaShow: false,
    showAbountDialog: false,
    marriageShow: false,
    educatShow: false,
    workplaceShow: false,
    relationshipShow: false,
    careerShow: false,
    incomeShow: false,
  };

  data.value.submitInfo.applyNo = storageData?.applyNo;
});
</script>
<style lang="scss" scoped>
.dot::before {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: var(--hnr-text-color-primary-activated);
  border-radius: 50%;
}

.dot {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

:deep(.hnr-icon svg) {
  margin: 0px 0px var(--dp1) var(--dp6) !important;
  fill-opacity: 0.7 !important;
}

:deep(.hnr-dialog__content--isolated-footer) {
  margin-bottom: 20px !important;
}

.myPicker {
  :deep(.hnr-picker-column__hnr-ellipsis) {
    width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.careerPicker {
  :deep(.hnr-picker-column__hnr-ellipsis) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.popup-btn {
  width: 100%;
  :deep(.hnr-button) {
    min-width: 50% !important;
  }
}
</style>
<style scoped>
@media (prefers-color-scheme: dark) {
  :deep(.rele-popup) {
    background: #2e2e2e !important;
  }
  .icon-arrow-pull:before {
    opacity: 0.2 !important;
  }

  .content-tip {
    background: #112840 !important;
  }
}

:root {
  --border-color: var(--hnr-list-divider) !important;
}

.errorMsg {
  color: var(--hnr-color-error);
  font-size: var(--hnr-caption);
  margin: var(--dp8) 0 0 var(--dp12);
}

:deep(.hnr-divider--line:before) {
  border-color: var(--border-color, var(--hnr-list-divider)) !important;
}

:deep(.hnr-field) {
  min-height: var(--dp48) !important;
  height: var(--dp48) !important;
}

:deep(.hnr-field__body) {
  min-height: var(--dp48) !important;
  height: var(--dp48) !important;
}

:deep(.hnr-divider) {
  width: auto;
  margin: auto 12px;
}

:deep(.hnr-checkbox__icon) {
  cursor: none !important;
}

:deep(.hnr-button--disabled) {
  opacity: 0.38;
}

:deep(.hnr-cell-group--small-margin-top) {
  margin-top: auto !important;
}

:deep(.hnr-cell-group--inset) {
  margin: auto !important;
}

:deep(.hnr-field__error-message) {
  margin-left: -80px;
}

:deep(.hnr-icon svg g) {
  fill: none;
}

:deep(.hnr-field__input-area) {
  width: calc(100vw - 145px) !important;
}

:deep(.hnr-field__input-area:focus-within:before) {
  background-color: none !important;
}

:deep(.hnr-field__input-area:before) {
  height: var(--lineHeight) !important;
  background-color: var(--hnr-color-error);
  left: -80px !important;
  width: calc(100vw - var(--dp48)) !important;
}

:deep(.hnr-cell-group__title) {
  color: var(--hnr-text-color-primary) !important;
  font: var(--hnr-body-1) !important;
  font-weight: var(--hnr-font-weight-medium) !important;
  display: flex !important;
  align-items: center !important;
  height: 48px !important;
  padding-top: 0px !important;
}

:deep(.hnr-field) {
  height: var(--dp48) !important;
}

:deep(.hnr-field--withMargin) {
  margin: 0px !important;
  width: 100% !important;
}

:deep(.hnr-field__control::-webkit-input-placeholder) {
  font-size: var(--hnr-body-2); /* 字体大小 */
}

:deep(.hnr-field__control::-ms-input-placeholder) {
  font-size: var(--hnr-body-2); /* 字体大小 */
}

:deep(.hnr-field__input-area input, .hnr-field__input-area textarea) {
  font-size: var(--hnr-body-2);
}

.inputBox {
  /* width: 100%; */
  width: calc(100vw - 128px);
  /* resize: vertical; */
  margin: var(--dp13) 0 0 0;
  font-size: var(--hnr-body-2);
  font-weight: 400;
  color: var(--hnr-color-primary);
  font-family: inherit;
  border: none;
  outline: none;
  /* padding-left: 12px; */
  word-break: break-all;
  overflow: hidden;
  resize: none;
  padding-left: 0;
  padding-right: 0;
  background: transparent;
}

textarea::placeholder {
  font-size: var(--hnr-body-2);
  font-weight: 400;
  color: var(--hnr-text-color-secondary);
}

.divider-line {
  background: red;
  width: calc(100vw - 140px) !important;
}
.field-message {
  color: var(--hnr-color-error);
  font-size: calc(var(--hnr-body-3) / var(--hnr-large-font-rate));
  padding-top: 10px;
  padding-bottom: var(--hnr-elements-margin-vertical-M2);
}

.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  margin: 14px auto 20px auto;
}

.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp20);
  color: var(--hnr-color-foreground);
  font-size: var(--dp18);
  opacity: 0.15;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content-top {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--hnr-color-background-cardview);
}

.content {
  padding-top: 88px;
}

.content-tip {
  background: #e3f1ff;
  border-radius: var(--hnr-input-box-corner-radius);
  margin: var(--hnr-elements-margin-vertical-M2) var(--hnr-elements-margin-vertical-M2) 0px
    var(--hnr-elements-margin-vertical-M2);
  padding: var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2);
  color: var(--hnr-text-color-primary-activated);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  align-items: center;
}

.icon-confidentiality {
  font-size: 16px;
  margin-right: 6px;
}

.icon-confidentiality:before {
  content: "\e907";
  color: #256fff;
}

.basic {
  display: flex;
  flex-direction: column;
  width: calc(100vw - 24px) !important;
  margin-left: 12px !important;
  color: var(--hnr-text-color-primary) !important;
  font: var(--hnr-body-1) !important;
  font-weight: var(--hnr-font-weight-medium) !important;
}

.cell-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 12px;
}

.defined-item {
  display: flex;
  align-items: center;
  /* gap: var(--hnr-elements-margin-horizontal-L); */
}

.defined-item-title {
  line-height: 48px;
  min-width: 64px;
  margin-right: var(--hnr-elements-margin-horizontal-L) !important;
}

.icon-arrow-right {
  font-size: var(--dp20);
  color: #d5d5d5;
}

.cell-padding {
  padding: 0 12px !important;
}

.footer-fixed {
  position: fixed;
  bottom: var(--hnr-default-padding-bottom-fixed);
}

.footer-relation {
  margin: var(--hnr-elements-margin-vertical-L2) auto var(--hnr-default-padding-bottom-fixed) auto;
}

.footer-normal {
  margin-bottom: var(--hnr-default-padding-bottom-fixed);
}

.footer-position {
  margin-bottom: var(--hnr-default-padding-bottom-fixed);
}

.footer {
  max-width: calc(100vw - 48px) !important;
  width: calc(100vw - 48px);
  margin-left: 24px !important;
  /* margin: var(--hnr-elements-margin-vertical-L)
    var(--hnr-elements-margin-horizontal-L2)
    var(--hnr-default-padding-bottom-fixed)
    var(--hnr-elements-margin-horizontal-L2); */
}

.city-popup {
  margin: auto 12px 16px 12px;
  width: calc(100vw - 24px);
  border-bottom-left-radius: var(--hnr-default-corner-radius-l);
  border-bottom-right-radius: var(--hnr-default-corner-radius-l);
}

.city-title {
  margin: 30px auto 30px 40px;
  font-size: 20px;
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-medium);
}

.city-footer {
  width: calc(100vw - 24px);
  line-height: 68px;
}

.rele-popup {
  min-height: 360px !important;
  background: var(--hnr-color-background-cardview);
}

.rela-pull {
  text-align: center;
  position: sticky;
  top: 0;
}

.rele-title {
  line-height: 56px;
  margin: auto auto auto 24px;
  font-size: 20px;
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-medium);
  position: sticky;
  top: -5px;
}

.relationship-radio {
  margin: 0 !important;
  padding: 0 var(--hnr-elements-margin-horizontal-L2) !important;
  padding-bottom: 12px !important;
  border-radius: 0px !important;
}
.btn {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
  border-right: 1px solid var(--hnr-color-quaternary);
}
</style>
