<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, onServerPrefetch } from "vue";
import lodash from "lodash";
import useStore from "./store";
import {
  initStore,
  request,
  // userBehaviorLogReporting,
  contractClickCallback,
} from "../../../helpers/utils";
import { goto, report, regNativeEvent, enableBackPress } from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import { loanNext } from "../../../helpers/next";
import { showRetentionDialog } from "../../../helpers/retention-dialog-utils";
import SignReadButton from "../../../components/SignReadButton.vue";
import reloadingComponent from "../../../components/reloadingComponent.vue";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { supplierIdMap } from "../../../helpers/constants";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);

const navRef = ref(null);
const navHeight = ref(null);
const bottomBtnRef = ref(null);
const iframeRef = ref(null);
const isShowMore = ref(false);
const confirmShow = ref(false);
const iframeHeight = ref(0);
const contentHeight = ref(0);
const ifReading = ref([]);
const reportList = ref([]);
const supplierId = ref(0);
const isDarkMode = ref(false);

const disabledOverlayClose = () => false;

async function reload() {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }
  if (store.contractFail) {
    store.contractFail = false;
    store.getContractList();
  }
}

const toHomeClick = () => {
  if (!reportList.value.includes("toHomeClick")) {
    reportList.value.push("toHomeClick");
  }
};

const handleConfirmClick = () => {
  if (!reportList.value.includes("handleConfirmClick")) {
    reportList.value.push("handleConfirmClick");
  }
};

const onClickLeft = () => {
  if (!reportList.value.includes("onClickLeft")) {
    reportList.value.push("onClickLeft");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
};

const afterReadingIt = () => {
  const arr = data.value.iframeArr.filter((item) => item.status);
  if (arr.length === data.value.iframeArr.length) {
    data.value.afterReadingItStatus = true;
  }
};

const scrollPosition = ref({ st: 0, ed: 0, top: 0, bottom: 0, init: "top" });
let startY = 0;

const iframeClick = lodash.debounce(
  (index, scrollInit = "top") => {
    if (!data.value.iframeArr[index].status) {
      ifReading.value[index] = true;
    }
    data.value.currentIndex = index;
    data.value.iframeArr[index].then((res) => {
      if (res.contractType === "pdf" || res.contractType === "html") {
        iframeRef.value.removeAttribute("srcdoc");
        iframeRef.value.src = res.contractUrl;
      }
      if (res.contractType === "content") {
        iframeRef.value.removeAttribute("src");
        iframeRef.value.srcdoc = res.contractUrl;
      }
    });
    scrollPosition.value.init = scrollInit;
  },
  1000,
  { leading: true },
);

const handleTouchStart = (event) => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  if (iframeDocument) {
    scrollPosition.value.st =
      iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
  }
  // 记录初始触摸位置
  const touch = event.touches[0];
  startY = touch.clientY;
};

const swipeTop = () => {
  if (
    scrollPosition.value.st === scrollPosition.value.top &&
    scrollPosition.value.ed === scrollPosition.value.top
  ) {
    data.value.iframeArr[data.value.currentIndex].status = true;
    if (data.value.currentIndex - 1 >= 0) {
      iframeClick(data.value.currentIndex - 1, "bottom");
    }
    afterReadingIt();
  }
};

const swipeBottom = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  const scrollHeight =
    iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
  const windowHeight = iframeDocument.defaultView.innerHeight;
  scrollPosition.value.bottom = scrollHeight - windowHeight - 1;
  if (
    scrollPosition.value.st > scrollPosition.value.bottom &&
    scrollPosition.value.ed > scrollPosition.value.bottom
  ) {
    data.value.iframeArr[data.value.currentIndex].status = true;
    if (data.value.currentIndex + 1 < data.value.iframeArr.length) {
      iframeClick(data.value.currentIndex + 1, "top");
    }
    afterReadingIt();
  }
};

const handleScroll = lodash.debounce(
  (event) => {
    // 计算滑动距离
    const touch = event.touches[0];
    const deltaY = touch.clientY - startY;
    const iframeDocument =
      iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
    if (iframeDocument) {
      scrollPosition.value.ed =
        iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
    }

    // 根据滑动距离执行相应的逻辑
    if (deltaY > 30) {
      swipeTop();
    } else if (deltaY < -30) {
      swipeBottom();
    }
  },
  600,
  { leading: true },
);

const handleClick = (event) => {
  const callback = contractClickCallback(event);
  if (callback?.next === "goto") {
    goto(callback.href, true);
  }
};

const handleIframeLoad = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  const scrollHeight =
    iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
  const windowHeight = iframeDocument.defaultView.innerHeight;
  scrollPosition.value = {
    ...scrollPosition.value,
    st: 0,
    ed: 0,
    top: 0,
    bottom: scrollHeight - windowHeight,
  };
  iframeDocument.removeEventListener("touchstart", handleTouchStart);
  iframeDocument.addEventListener("touchstart", handleTouchStart);
  iframeDocument.removeEventListener("touchmove", handleScroll);
  iframeDocument.addEventListener("touchmove", handleScroll);
  iframeDocument.removeEventListener("click", handleClick);
  iframeDocument.addEventListener("click", handleClick);
  navHeight.value = `${navRef.value.clientHeight}px`;
  iframeHeight.value = `${window.innerHeight - navRef.value.clientHeight - bottomBtnRef.value.clientHeight}px`;
  contentHeight.value = `${window.innerHeight - navRef.value.clientHeight}px`;
};

const operLog = async () => {
  const storageData = getStore(`U_LOAN_PROCESS_${data.value.param.userId}`) || {};
  await request("/loan/api/user/operLog", {
    orderNo: storageData?.applyNo || "",
    operType: 203,
    operResult: 1,
    operParams: JSON.stringify(data.value.iframeArr),
    supplier: 1,
    operTime: new Date().getTime(),
  });
};

const loanNextCallBack = () => {
  confirmShow.value = false;
  store.loading = false;
  store.signLoading = false;
};

const agreeClick = async () => {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }
  report("wallet_page_click", { click_name: "loan_agreement_read_click" });
  const loanInfo = getStore(`U_LOAN_PROCESS_${data.value.param.userId}`) || {};
  loanInfo.agreement = true;
  setWithExactExpireUnit(`U_LOAN_PROCESS_${data.value.param.userId}`, loanInfo, 4, "D");
  setTimeout(() => {
    operLog();
  }, 0);
  data.value.loading = true;
  loanNext(data.value.param.userId, loanNextCallBack()).finally(() => {
    setTimeout(async () => {
      data.value.loading = false;
    }, 2000);
  });
};

const getIframeArr = () =>
  isShowMore.value ? data.value.iframeArr : data.value.iframeArr.slice(0, 1);

watch(
  () => store.signInfo,
  (n) => {
    isShowMore.value = !!n.expendAll || isShowMore.value;
  },
  { deep: true },
);

watch(
  () => isShowMore,
  async () => {
    getIframeArr();
    await nextTick();
    iframeHeight.value = `${window.innerHeight - navRef.value.clientHeight - bottomBtnRef.value.clientHeight}px`;
  },
  { deep: true },
);
watch(
  () => data.value.iframeArr,
  (n) => {
    if (n && n.length) {
      if (n === 1) {
        store.iframeArr[0].then(() => {
          store.signLoading = false;
        });
      }
      store.iframeArr.forEach((item, index) => {
        item.then((res) => {
          store.iframeArr[index].contractName = res.contractName;
        });
      });
      iframeClick(0);
    }
  },
);

regNativeEvent("onBack", () => {
  if (store.loading || store.backDisabled) {
    return;
  }
  if (!reportList.value.includes("onBack")) {
    reportList.value.push("onBack");
  }
  showRetentionDialog({
    text: "仅差一步即可完成借款，确定离开？",
    cancelFunc: toHomeClick,
    confirmFunc: handleConfirmClick,
  });
});

regNativeEvent("onResume", async () => {
  store.loading = false;
  window.nextLoadingStatus(false);
  await enableBackPress(true);
});

const changeList = () => {
  isShowMore.value = !isShowMore.value;
};

onMounted(async () => {
  isDarkMode.value = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
  const res = await getCurrentCpInfo();
  supplierId.value = res?.supplierId || supplierId.value;
  reportList.value = [];
  window.addEventListener("online", () => {
    if (window.location.pathname === "/wallet-loan-web/pages/loan/sign") {
      window.location.reload();
    }
  });

  await store.getContractList();
});
onUnmounted(() => {
  window.removeEventListener("online");
});
</script>

<template>
  <div class="page">
    <div ref="navRef" class="content-top">
      <hnr-nav-bar transparent="true" class="nav-padding-top" title="协议签署">
        <template #left>
          <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <div class="content-title">
        {{ `以下协议由${store.supplierName}提供，请仔细阅读：` }}
      </div>
    </div>
    <div class="content" :style="{ height: contentHeight, paddingTop: navHeight }">
      <div class="content-iframe">
        <iframe
          ref="iframeRef"
          frameborder="0"
          style="border-radius: var(--dp12) var(--dp12) 0 0 !important"
          :style="{ background: isDarkMode && supplierId === supplierIdMap.ppd ? 'black' : '' }"
          width="100%"
          :height="iframeHeight"
          @load="handleIframeLoad"
        ></iframe>
      </div>
      <div ref="bottomBtnRef" class="btn-area">
        <div
          :style="{
            overflowY: isShowMore ? 'auto' : 'hidden',
            'max-height': '248px',
          }"
        >
          <!-- 多份协议 -->
          <div
            v-if="data.iframeArr.length > 1"
            class="header item-right light-color"
            @click="changeList"
          >
            <span>{{ `共${data.iframeArr.length}份协议` }}</span>
            <span :class="isShowMore ? 'icon-down' : 'icon-up'"></span>
          </div>
          <!-- 多份协议 -->

          <!-- 单份协议 -->
          <div v-if="data.iframeArr.length === 1" class="header btn-area-fold">
            <div class="item-right light-color">
              <span>{{ `共${data.iframeArr.length}份协议` }}</span>
            </div>
          </div>
          <!-- 单份协议 -->
          <div
            v-for="(item, index) in getIframeArr()"
            :key="index"
            class="btn-area-agreement"
            @click="iframeClick(index)"
          >
            <div>{{ store.contractNameList[index] }}</div>
          </div>
        </div>
        <SignReadButton
          :force-pull-down="data.signInfo.readAll"
          :read-already="data.afterReadingItStatus"
          :mandatory-reading-seconds="data.signInfo.readingSeconds"
          @click="agreeClick"
        />
      </div>
    </div>
    <div v-show="data.loading">
      <hnr-dialog
        v-model:show="data.loading"
        title=""
        message=""
        button-direction="row"
        :before-close="disabledOverlayClose"
      >
        <template #footer>
          <div class="loading">
            <div class="loading-text">正在提交</div>
            <div class="loading-logo">
              <hnr-loading />
            </div>
          </div>
        </template>
      </hnr-dialog>
    </div>
    <div v-show="data.signLoading" class="entrance-loading">
      <reloading-component
        :loading-status="!data.contractFail"
        :error-status="data.contractFail"
        @try-click="reload()"
      />
    </div>
  </div>
</template>

<style scoped>
:deep(.hnr-button--disabled) {
  opacity: 0.38;
}

.entrance-loading {
  margin-top: 133px;
  height: calc(100vh - 133px);
  width: 100%;
}

.signItemActive {
  /* border: 1px solid red;
  border-radius: 4px; */
}

.page {
  background: var(--hnr-color-background-cardview);
  height: 100vh;
}

.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  margin: 14px auto 20px auto;
}

.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

.content-title {
  color: var(--hnr-text-color-secondary);
  padding: 0 24px 8px 24px;
  font-size: 13px;
}

.content-top {
  position: fixed;
  top: 0;
  width: 100%;
  background: var(--hnr-color-background-cardview);
}

.btn-area {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background: var(--hnr-color-background-cardview);
  padding: 16px 24px var(--hnr-default-padding-bottom-fixed) 24px;
  box-sizing: border-box;
}

.btn-area ::-webkit-scrollbar {
  display: none;
}

.btn-area-agreement {
  display: flex;
  width: 70%;
  word-wrap: break-word !important;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  gap: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-primary-activated);
}

.btn-area-fold {
  display: flex;
  justify-content: flex-start;
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  gap: var(--hnr-elements-margin-vertical-S);
}
.btn-area-btn {
  width: 100%;
  max-width: none !important;
  margin-top: 8px;
}

.item-right {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.light-color {
  color: var(--hnr-text-color-secondary);
}

.icon-size {
  width: 12px;
  height: 12px;
  color: var(--hnr-color-secondary);
}
.puList {
  max-height: 248px;
  overflow-y: auto;
}
.puList ::-webkit-scrollbar {
  display: none;
}
.header {
  position: absolute;
  right: 24px;
  color: var(--hnr-text-color-secondary);
  z-index: 1;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.icon-down:before {
  content: "\e900";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.icon-up:before {
  content: "\e901";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.content-iframe {
  margin: 0 var(--dp12);
  border-radius: var(--hnr-default-corner-radius-m);
  background: var(--hnr-color-card-background);
}
</style>
