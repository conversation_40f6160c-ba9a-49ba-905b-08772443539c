import { defineStore } from "pinia";
import Big from "big.js";
import { showDialog, showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { loanNext } from "../../../helpers/next";
import { amountNumberFormat, filterLoanUsage, request } from "../../../helpers/utils";
import {
  getStore,
  removeStoreKeysUnit,
  setWithExactExpire,
  updateStoreUnit,
} from "../../../helpers/storage";
import { decrypt, encrypt, report, toastMessage } from "../../../helpers/native-bridge";
import { setNetwork } from "../../../helpers/network-helper";
import { delRetentionStatusStore } from "../../../helpers/configUtils";
import { getCouponList } from "../../../api/coupon";
import { couponStatus } from "../../../api/typedef/coupon";
import { LOAN_VERIFY_ERROR_CODE_MAP, urlMap } from "../../../helpers/constants";

export default defineStore("loan/calc", {
  state: () => ({
    repayMethods: [
      { title: "等额本息", subTitle: "每期还款金额相同" },
      { title: "等额本金", subTitle: "等额本金" },
      { title: "先息后本", subTitle: "按期支付利息，末期支付所有本金" },
      { title: "等额本息", subTitle: "每期还款金额相同" },
    ],
    param: {},
    maxLoan: 0,
    minLoan: 50000,
    loanPalceHolder: "",
    loanAmountMessage: "",
    isLoanAmountValid: true,
    info: {},
    productInfos: [],
    currentProd: {},
    couponList: [],
    bankInfo: {},
    // loanAgreement: [],
    bankAgreement: [],
    trialInfo: {},
    // readLoanAgreement: false,
    readBankAgreement: false,
    showPage: false,
    pageType: 1,
    bindList: [],
    repayDay: 15,
    repayType: 0,
    applyNo: "",
    allText: "借全部",
    isDiscount: false,
    isShowReadProtocal: false,
    isTryVerifyException: false,
    isNetworkException: false,
    // 试算失败
    trialFail: false,
    trialRetryType: "amountChange",
    displayInfo: {
      pordSummaryInfos: [],
      cellList: [
        {
          isShow: false,
          title: "优惠券",
          value1: "-￥1330.2",
          value2: "日利率0.015%优惠券",
          value3: "最优惠",
          isIcon: true,
          isLoading: false,
        },
        {
          isShow: true,
          title: "年利率",
          value1: null, // 利率
          value2: "提前结清需按未还本金支付违约金",
          value3: null, // 临时利率
          ifShowDesc: false,
          type: "",
          isIcon: true,
          tempPriceValidDays: null,
        },
        // {
        //   isShow: true,
        //   title: "还款方式",
        //   value1: "等额本息",
        //   value2: "每期还款金额相同",
        //   isIcon: true,
        // },
        {
          isShow: true,
          title: "借多久",
          value1: "12期",
          value2: "每月20日还款",
          isIcon: true,
        },
        {
          isShow: true,
          title: "怎么还",
          value0: "等额本息",
          value1: "首期3月20日应还",
          value4: "￥14222",
          isIcon: true,
          isLoading: false,
        },
      ],
    },
    ifAprChange: false,
    ifRepayTypeSwitchAndAprChange: false,
    beforeChangeApr: "",
    afterChangeApr: "",
    isLoading: false,
    supplierInfo: {
      supplierId: 1,
      cnName: "度小满金融",
    },
    supplier: "",
    applyRes: null,
    loadingMessage: "",
    continueButtonDisabled: false,
    creditInfo: null,
    supplierId: "",
    // 银行卡物料弹窗
    ifShowContract: false,
    ifContractLoading: false,
    isShowMore: false,
    interruptLoanInfo: null,
    // 所有优惠券
    allCouponList: [],
    // 中断订单是否有效
    isInterruptLoanInfoValid: true,
    // 用户当前选择的优惠券
    couponInfo: null,
    // 优惠面板展开的优惠项
    collapseActiveItems: [],
    // 重定向路径
    redirectUrl: "",
    // 试算失败错误信息
    trialErrorMessage: "点击重试",
    isFirstRepayLoading: false,
    ifShowReload: false,
  }),

  actions: {
    async initialCreditInfo(info) {
      const creditInfo = info;

      this.creditInfo = creditInfo;
      if (creditInfo?.remainLimit || creditInfo?.maxLoan) {
        // 处理存在临额的情况
        if (creditInfo.tempLimitInfo) {
          creditInfo.creditLimit =
            creditInfo.totalCreditLimit ||
            creditInfo.creditLimit + (creditInfo.tempLimitInfo?.creditLimit || 0); // 总额度（+临额）
          creditInfo.remainLimit =
            creditInfo.totalAvailableLimit ||
            creditInfo.remainLimit + (creditInfo.tempLimitInfo?.availableLimit || 0); // 总可用额度（+临额）
        }
        this.maxLoan = Math.min(
          new Big(creditInfo.maxLoan || 20000000),
          new Big(creditInfo.remainLimit || 20000000),
        ).toFixed(0);
        this.maxLoan = new Big(this.maxLoan).div(10000).round(0, 0).times(10000);
      }
      if (creditInfo?.minLoan) {
        this.minLoan = new Big(creditInfo.minLoan).div(10000).round(0, 0).times(10000).toFixed(0);
      }
      this.loanPalceHolder = `最高可借${amountNumberFormat(Big(this.maxLoan).div(100).toString(), 0)}`;
      this.productInfos = creditInfo?.productInfos || [];
      this.loanAmountMessage = `最低￥${amountNumberFormat(
        Big(this.minLoan).div(100).toString(),
        0,
      )}起借，请输入100的倍数，最高可借￥${amountNumberFormat(Big(this.maxLoan).div(100).toString(), 0)}`;
      // 第一次进入设置为当天的日期
      this.repayDay = new Date().getDate();
      if (this.productInfos.length) {
        let apr = 100;
        let tempApr = 100;
        let tempPriceValidDays;
        this.productInfos.sort((a, b) => a.repayMethod - b.repayMethod);
        this.productInfos = this.productInfos.map((prod) => {
          const item = { ...prod };
          if (prod.tempPriceValidDays !== undefined) {
            tempPriceValidDays =
              typeof prod.tempPriceValidDays === "number" ? prod.tempPriceValidDays : -1;
          }

          if (prod.repayMethod >= 1 && prod.repayMethod <= 3) {
            if (parseFloat(prod.apr) < apr) {
              apr = prod.apr;
            }
            if (prod.tempApr && parseFloat(prod.tempApr) < tempApr) {
              tempApr = prod.tempApr;
            }
          } else if (prod.repayMethod === 4) {
            this.displayInfo.pordSummaryInfos.push({
              type: 2,
              title: "按期还",
              subtitle: this.supplierInfo.fixedTermPenaltyExplanation,
              apr: prod.apr,
              tempApr: prod.tempApr,
              tempPriceValidDays: prod.tempPriceValidDays,
            });
          }
          item.repayMethodTitle = this.repayMethods[prod.repayMethod - 1].title;
          item.repayMethodDisp = this.repayMethods[prod.repayMethod - 1].subTitle;
          item.termNums.sort((a, b) => {
            if (a < b) {
              return 1;
            }
            return -1;
          });

          return item;
        });
        if (apr < 100) {
          if (tempApr < 100)
            this.displayInfo.pordSummaryInfos.unshift({
              type: 1,
              title: "灵活还",
              subtitle: this.supplierInfo.loanBrExplanation,
              apr,
              tempApr,
              tempPriceValidDays,
            });
          else
            this.displayInfo.pordSummaryInfos.unshift({
              type: 1,
              title: "灵活还",
              subtitle: this.supplierInfo.loanBrExplanation,
              apr,
            });
        }
        if (this.displayInfo.pordSummaryInfos.length === 2) {
          if (
            parseFloat(this.displayInfo.pordSummaryInfos[0].apr) <
            parseFloat(this.displayInfo.pordSummaryInfos[1].apr)
          ) {
            this.displayInfo.pordSummaryInfos[0].tag = "便宜";
          } else {
            this.displayInfo.pordSummaryInfos[1].tag = "便宜";
          }

          this.displayInfo.cellList[1].isShow = false;
        }
        this.displayInfo.pordSummaryInfos.sort((a, b) => {
          if (parseFloat(a.apr) > parseFloat(b.apr)) {
            return 1;
          }
          return -1;
        });

        this.repayType = this.displayInfo.pordSummaryInfos[0].type;
      }
    },

    async initialBindList(bindList) {
      this.bindList = (bindList || []).map((item) => {
        const ret = { ...item };
        if (item.bankCardNo && item.bankCardNo.length > 4) {
          ret.bankCardNo = item.bankCardNo.substr(item.bankCardNo.length - 4, 4);
        }
        return ret;
      });
    },

    async initial() {
      const initialIntls = [
        request("/loan/api/credit/info", {}, { mock: false }),
        request("/loan/api/bankcard/list", {}, { mock: false }),
      ];
      if (this.param.applyNo) {
        initialIntls.push(
          request("/loan/api/loan/queryApplyStatus", { applyNo: this.param.applyNo }),
          getCouponList(),
        );
      }
      const [res, bindList, applyRes, couponListRes] = await Promise.all(initialIntls);

      // 拦截不正常的用户状态
      if (res?.data?.status !== 1) {
        this.redirectUrl = urlMap.loan_index;
        return;
      }

      this.allCouponList = couponListRes?.data || [];
      this.maxLoan = new Big(this.param.amount).div(10000).round(0, 0).times(10000);
      this.info.repayMethod = parseInt(this.param.repayMethod, 10);
      this.info.totalTerm = parseInt(this.param.totalTerm, 10);
      this.initialCreditInfo(res.data);
      this.initialBindList(bindList?.data);

      if (applyRes) {
        this.applyRes = applyRes.data;
      }

      if (
        this.param.applyNo &&
        typeof this.applyRes?.applyStatus === "number" &&
        [0, 6].includes(this.applyRes?.applyStatus)
      ) {
        this.info.loanAmount = 0;
      } else if (
        this.param.fromPage === "loanResult" &&
        this.param.retryAmount &&
        this.param.loanInfo
      ) {
        this.info.loanAmount = Big(this.param.retryAmount).div(new Big(100)).toNumber();
      } else {
        this.info.loanAmount = Big(this.maxLoan).div(new Big(100)).toNumber();
      }
    },

    async loanClose() {
      const loanClose = request("/loan/api/loan/close", { applyNo: this.param.applyNo });
      const timeout = new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, 500);
      });
      await Promise.race([loanClose, timeout]);
    },

    async chooseRepayType(val) {
      this.trialRetryType = "changeRepayType";
      this.trialFail = false;
      this.repayType = val;

      if (!this.productInfos.length) {
        this.isLoading = true;
        const initialIntls = [
          request("/loan/api/credit/info", {}, { mock: false }),
          request("/loan/api/bankcard/list", {}, { mock: false }),
        ];
        const [res, bindList] = await Promise.all(initialIntls);
        if (res.data) {
          this.creditInfo = res.data;
          this.initialCreditInfo(res.data);
          this.initialBindList(bindList?.data);
        } else {
          this.trialFail = true;
        }
      }
      if (this.repayType === 2) {
        this.displayInfo.cellList[1].isIcon = false;
      } else {
        this.displayInfo.cellList[1].isIcon = true;
      }
      if (this.repayType === 1) {
        const [prod] = this.productInfos.filter((item) => item.repayMethod !== 4);
        this.currentProd = prod;
      } else {
        const [prod] = this.productInfos.filter((item) => item.repayMethod === 4);
        this.currentProd = prod;
      }
      if (this.currentProd?.repayMethod) {
        this.info.repayMethod = this.currentProd.repayMethod;
        await this.chooseRepayMethod(this.currentProd.repayMethod);
      }
    },

    async chooseRepayMethod(val) {
      this.trialRetryType = "changeRepayMethod";
      this.trialFail = false;
      this.info.repayMethod = parseInt(val, 10);
      this.currentProd = this.productInfos?.find(
        (prod) => prod.repayMethod === this.info.repayMethod,
      );
      if (!this.currentProd.termNums.includes(this.info.totalTerm)) {
        const [totalTerm] = this.currentProd.termNums;
        this.info.totalTerm = totalTerm;
      }
      await this.getTrial({ changeRepayMethod: true });
    },

    async chooseTerm(val) {
      this.info.totalTerm = parseInt(val, 10);
      this.ifContractLoading = true;
      this.isShowMore = false;
      await this.getTrial({ changeRepayTerm: true });
    },

    async checkAmount(val, unit = "yuan") {
      let loanVal;
      if (unit === "yuan") {
        loanVal = new Big(val).times(100);
      } else if (unit === "fen") {
        loanVal = new Big(val);
      }
      if (loanVal.mod(10000).toFixed(0) !== "0") {
        this.isLoanAmountValid = false;
        this.loanAmountMessage = "借款需为100的整数倍";
        return false;
      }
      if (loanVal.lt(this.minLoan)) {
        this.isLoanAmountValid = false;
        this.loanAmountMessage = `单笔借款最低￥${amountNumberFormat(Big(this.minLoan).div(100).toString(), 0)}`;
        return false;
      }
      if (loanVal.gt(this.maxLoan)) {
        this.isLoanAmountValid = false;
        this.loanAmountMessage = `当前最多可借￥${amountNumberFormat(Big(this.maxLoan).div(100).toString(), 0)}`;
        return false;
      }
      this.isLoanAmountValid = true;
      this.loanAmountMessage = `最低￥${amountNumberFormat(
        Big(this.minLoan).div(100).toString(),
        0,
      )}起借，请输入100的倍数，最高可借￥${amountNumberFormat(Big(this.maxLoan).div(100).toString(), 0)}`;
      return true;
    },

    async changeAmount(val) {
      this.trialRetryType = "changeAmount";
      this.trialFail = false;
      if (val) {
        if (await this.checkAmount(val)) {
          await this.chooseRepayType(this.repayType);
          return;
        }
      } else {
        this.isLoanAmountValid = true;
        this.loanAmountMessage = `最低￥${amountNumberFormat(
          Big(this.minLoan).div(100).toString(),
          0,
        )}起借，请输入100的倍数，最高可借￥${amountNumberFormat(Big(this.maxLoan).div(100).toString(), 0)}`;
      }
      this.isLoading = false;
      this.showPage = false;
    },

    changeCoupon(val) {
      this.info.couponNo = val;
      this.setTrialDisplayInfo();
      this.ifContractLoading = true;
      this.isShowMore = false;

      // 如果是优惠券没有携带优惠金额，则假装切换了还款方式，这样优惠券会重新转圈加载，并且需要跳过优惠券拉取逻辑(906奇富新增逻辑)
      if (!this.isCouponWithReductionAmount()) {
        this.getTrial({ changeRepayMethod: true, isSkipCouponPull: true });
      } else {
        this.getTrial({ changeCoupon: true });
      }
    },

    // 优惠券是否携带优惠金额
    isCouponWithReductionAmount() {
      const validCoupons =
        this.couponList?.filter((item) => item.status === 1 && !item.unusableReason) || [];
      return !!validCoupons[0]?.discountAmount;
    },

    async getCouponList(
      loanAmount,
      repayMethod,
      totalTerm,
      changeCoupon = false,
      isSkipCouponPull = false,
    ) {
      if (!changeCoupon && !isSkipCouponPull) {
        const couponList = await request(
          "/loan/api/coupon/loanUsableCounpons",
          {
            loanAmount,
            repayMethod,
            totalTerm,
          },
          { mock: false },
        );
        this.couponList = couponList.data?.filter((item) => item.status === 1) || [];
        // 没有优惠券时不显示优惠券单元格
        if (this.couponList.length === 0) this.displayInfo.cellList[0].isShow = false;
        else this.displayInfo.cellList[0].isShow = false;
        this.hasUsableCoupond = this.couponList.some(
          (coupon) => typeof coupon.unusableReason === "undefined",
        );

        // 如果优惠券没有返回优惠金额，则默认选择最早到期时间的优惠券，并且优惠金额在试算后赋值
        if (!this.isCouponWithReductionAmount()) {
          let latestCoupon = null;
          // 优惠券最早到期时间
          let earliestEndTime = 0;

          this.couponList.forEach((coupon) => {
            if (coupon && !coupon.unusableReason && coupon.endTime) {
              const currentEndTime = new Date(coupon.endTime).getTime();
              if (currentEndTime !== null || currentEndTime <= earliestEndTime) {
                earliestEndTime = currentEndTime;
                latestCoupon = coupon;
              } else {
                console.warn("该优惠券没有到期时间:", coupon);
              }
            }
          });
          if (latestCoupon) {
            this.info.couponNo = latestCoupon.couponNo;
          } else {
            this.info.couponNo = null;
            console.warn("无法找到最早到期时间的优惠券");
          }
        } else {
          let discount = 0;
          this.couponList = this.couponList.map((coupon) => {
            const item = { ...coupon };
            if (coupon.discountAmount > discount) {
              discount = coupon.discountAmount;
            }
            if (coupon.discountAmount > 0) {
              item.title = `-￥${new Big(coupon.discountAmount).div(new Big(100)).toFixed(2)}`;
            }
            return item;
          });
          if (this.hasUsableCoupond) {
            this.couponList = this.couponList.map((coupon) => {
              const item = { ...coupon };
              if (coupon.discountAmount === discount) {
                item.tag = "最优惠";
                this.info.couponNo = coupon.couponNo;
              } else {
                item.tag = "";
              }
              return item;
            });
          } else {
            this.info.couponNo = "";
          }
        }
      }
    },

    async getTrial({
      changeRepayMethod = false,
      changeRepayTerm = false,
      changeCoupon = false,
      isSkipCouponPull = false,
    } = {}) {
      this.trialRetryType = "getTrial";
      const getTrialToken = `trial${Date.now()}`;
      console.log(getTrialToken);
      this.getTrialToken = getTrialToken;
      this.trialFail = false;
      if (this.currentProd) {
        const failout = setTimeout(() => {
          if (getTrialToken === this.getTrialToken) this.trialFail = true;
        }, 30000);
        if (!window?.navigator?.onLine) {
          this.displayInfo.cellList[0].isLoading = false;
          this.displayInfo.cellList[3].isLoading = false;
          this.isLoading = true;
          this.trialFail = true;
          this.showPage = false;
          return;
        }
        report("wallet_page_click", { click_name: "loan_trial_click" });
        this.setTrialDisplayInfo(changeRepayMethod, changeRepayTerm, changeCoupon);
        this.isLoading = !this.showPage;
        this.displayInfo.cellList[0].isLoading = !changeCoupon;
        this.displayInfo.cellList[3].isLoading = true;
        let { repayMethod, totalTerm } = this.info;
        const loanAmount = Big(`${this.info.loanAmount || 0}00`);
        if (!(await this.checkAmount(loanAmount, "fen"))) {
          return;
        }
        if (!repayMethod || !totalTerm) {
          await this.chooseRepayType(this.repayType);
          ({ repayMethod, totalTerm } = this.info);
          // return;
        }

        await this.getCouponList(
          loanAmount,
          repayMethod,
          totalTerm,
          changeCoupon,
          isSkipCouponPull,
        );

        const trialInfo = await request(
          "/loan/api/loan/trial",
          { repayMethod, totalTerm, loanAmount, couponNo: this.info.couponNo || null },
          { mock: false },
        );
        // 如果有新线程先获取到了新数据，则老线程不生效
        if (getTrialToken !== this.getTrialToken) {
          return;
        }
        if (trialInfo?.code !== 0) {
          this.trialErrorMessage = "点击重试";
          this.showPage = false;
          this.isLoading = true;
          this.trialFail = true;

          if (trialInfo?.code === 170909) {
            this.trialErrorMessage = trialInfo.message;
          }

          if (!window?.navigator?.onLine) setNetwork();
          this.displayInfo.cellList[2].isLoading = false;
          this.displayInfo.cellList[3].isLoading = false;
          return;
        }
        if (this.info.repayMethod === 4 && trialInfo?.data?.annualRate) {
          const current = this.displayInfo.pordSummaryInfos?.find((prod) => prod.type === 2);
          if (current) {
            if (!current.tempApr) {
              if (current.apr && current.apr !== trialInfo.data.annualRate) {
                this.beforeChangeApr = `${current.apr}%`;
                this.afterChangeApr = `${trialInfo.data.annualRate}%`;
                this.ifAprChange = true;
                setTimeout(() => {
                  this.ifAprChange = false;
                }, 2350);
                if (changeRepayTerm) {
                  showToast({
                    message: "因切换借款期数，借款利率发生变化",
                    position: "bottom",
                    duration: "long",
                  });
                }
              }

              current.apr = trialInfo.data.annualRate;
            }

            if (
              current.tempApr &&
              trialInfo.data.annualRate &&
              current.tempApr !== trialInfo.data.annualRate
            ) {
              this.beforeChangeApr = `${current.tempApr}%`;
              this.afterChangeApr = `${trialInfo.data.annualRate}%`;
              this.ifAprChange = true;
              setTimeout(() => {
                this.ifAprChange = false;
              }, 2350);
              if (changeRepayTerm) {
                showToast({
                  message: "因切换借款期数，借款利率发生变化",
                  position: "bottom",
                  duration: "long",
                });
              }
            }

            if (trialInfo.data.originalRate) {
              current.apr = trialInfo.data.originalRate;
              current.tempApr = trialInfo.data.annualRate;
            }
          }
        }

        clearTimeout(failout);
        this.trialInfo = trialInfo.data;
        this.ifShowContract = !!this.trialInfo.contract;
        if (this.ifShowContract)
          report("wallet_page_view", {
            page_name: "loan_trial_page_agreement_list",
          });
        this.ifContractLoading = false;
        this.info.firstRepayDate = this.trialInfo.firstRepayDate;
        this.repayDay = /^\d{4}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$/.test(
          this.trialInfo.firstRepayDate,
        )
          ? parseInt(this.trialInfo.firstRepayDate.substring(6), 10)
          : new Date().getDate();
        setTimeout(() => this.operLog(trialInfo), 0);
        this.setTrialDisplayInfo(
          !changeRepayMethod,
          !changeRepayTerm,
          !changeCoupon,
          trialInfo?.data?.annualRate,
          trialInfo?.data?.originalRate,
        );
        this.showPage = true;
      } else {
        showToast("未能获得贷款信息");
      }
    },
    async operLog(res) {
      await request("/loan/api/user/operLog", {
        operType: 201,
        operResult: res?.code === 0 ? 1 : 2,
        supplier: res?.supplier || 1,
        operTime: new Date().getTime(),
      });
    },

    setTrialDisplayInfo(
      changeRepayMethod = true,
      changeRepayTerm = true,
      changeCoupon = true,
      annualRate = null,
      originalRate = null,
    ) {
      if (this.info.repayMethod && (changeRepayMethod || annualRate)) {
        // 还款方式
        const pordSummaryInfo = this.displayInfo.pordSummaryInfos.filter(
          (info) => info.type === this.repayType,
        )[0];
        this.displayInfo.cellList[1].ifShowDesc = pordSummaryInfo?.tempPriceValidDays !== -1;
        this.displayInfo.cellList[1].value1 =
          this.displayInfo.pordSummaryInfos.filter((info) => info.type === this.repayType)[0].apr ||
          annualRate;
        this.displayInfo.cellList[1].value2 = this.displayInfo.pordSummaryInfos.filter(
          (info) => info.type === this.repayType,
        )[0].subtitle;
        this.displayInfo.cellList[1].value3 = this.displayInfo.pordSummaryInfos.filter(
          (info) => info.type === this.repayType,
        )[0]?.tempApr;
        this.displayInfo.cellList[1].tempPriceValidDays = this.creditInfo.productInfos.filter(
          (info) => info.repayMethod === this.info.repayMethod,
        )[0]?.tempPriceValidDays;
        this.displayInfo.cellList[1].type = this.repayType;

        // 907 试算接口新增优惠前利率字段originalRate此处做相应的适配
        if (originalRate) {
          this.displayInfo.cellList[1].value1 = originalRate;
          this.displayInfo.cellList[1].value3 = annualRate;
        }
      }
      if (this.info.totalTerm && changeRepayTerm) {
        // 还款期数
        this.displayInfo.cellList[2].value1 = `${this.info.totalTerm}期`;
        this.displayInfo.cellList[2].value2 = `每月${this.repayDay}日还款`;
      }

      if (changeCoupon) {
        // 优惠券
        const couponInfo = this.couponList?.find((item) => item.couponNo === this.info.couponNo);
        this.couponInfo = couponInfo;
        if (couponInfo) {
          this.displayInfo.cellList[0].value1 = couponInfo.title;
          // 优惠券没有携带优惠金额则使用试算返回的优惠金额
          if (!this.isCouponWithReductionAmount()) {
            this.displayInfo.cellList[0].value1 = `-￥${new Big(this.trialInfo.reductionAmount || 0).div(new Big(100)).toFixed(2)}`;
          }
          this.displayInfo.cellList[0].value2 = couponInfo.couponName;
          this.displayInfo.cellList[0].value3 = couponInfo.tag;
        } else {
          this.displayInfo.cellList[0].value1 = "";
          this.displayInfo.cellList[0].value2 = this.hasUsableCoupond ? "不使用" : "暂无可用";
          this.displayInfo.cellList[0].value3 = "";
        }
      }
      this.isDiscount = (this.trialInfo?.reductionAmount || 0) > 0;
      if (this.trialInfo?.repayPlanTerms) {
        this.displayInfo.cellList[3].value0 =
          this.productInfos.filter((info) => info.repayMethod === this.info.repayMethod)?.[0]
            ?.repayMethodName || "";
        this.displayInfo.cellList[3].value1 = `首期${this.trialInfo.repayPlanTerms[0].shouldRepayDate.replace(
          /(\d{4})(\d{2})(\d{2})/,
          "$2月$3日",
        )} ${this.supplierInfo.accurateLoanTrial && this.supplierInfo?.accurateLoanTrial === 1 ? "预估应还" : "应还"}`;
        this.displayInfo.cellList[3].value4 = `￥${amountNumberFormat(
          Big(this.trialInfo.repayPlanTerms[0].amount || 0)
            .div(100)
            .toNumber(),
        )}`;
      }
      this.isLoading = false;
      this.displayInfo.cellList[0].isLoading = false;
      this.displayInfo.cellList[3].isLoading = false;
    },

    /**
     *
     * @param {*} val
     * @param {*} message
     * @returns true-正常/false-异常
     */
    checkElement(val, message) {
      if (!val) {
        showDialog({ message });
      }
      return val;
    },

    loanNextCallBack() {
      this.displayInfo.showLastLoanInfo = false;
      this.isTryVerifyException = false;
      this.isNetworkException = false;
      this.loadingMessage = "";
      this.isLoading = false;
    },

    /**
     *
     * @param {Object} verifyRes 上一次接口返回结果
     * @param {Object} verifyParams 上一次接口请求参数
     * @param {Function} pollStatus 下一轮轮询方法
     * @param {Number} pollStartTime 整个轮询的开始时间
     */
    async handleVerifyResult(verifyRes, verifyParams, pollStatus, pollStartTime) {
      const { applyNo, applyStatus, verifyList = [] } = verifyRes.data || {};
      const updateVerifyParams = {
        ...verifyParams,
        applyNo,
      };
      const TIMEOUT = 15000;
      if (verifyRes?.code === 0) {
        if (applyStatus === 6) {
          delRetentionStatusStore();
          const storageData = getStore(`U_LOAN_PROCESS_${this.param.userId}`);
          storageData.mobileNo = this.creditInfo.mobileNo;
          setWithExactExpire(`U_LOAN_PROCESS_${this.param.userId}`, storageData, "4D");
          // 如果状态不再是6，停止轮询并处理结果
          console.log("Apply status: ", applyStatus, "需鉴权");
          // 处理状态改变后的逻辑
          const steps = [];
          const infoFileds = verifyList.filter(
            (item) =>
              item !== "FACE_CHECK" &&
              item !== "AGREEMENT_COMMON" &&
              item !== "AGREEMENT_LOAN" &&
              item !== "SMS",
          );
          verifyList.forEach((item) => {
            if (item === "AGREEMENT_COMMON" || item === "AGREEMENT_LOAN") {
              steps.push("AGREEMENT");
            } else if (
              infoFileds.length &&
              item !== "FACE_CHECK" &&
              item !== "AGREEMENT_COMMON" &&
              item !== "AGREEMENT_LOAN" &&
              item !== "SMS"
            ) {
              if (!steps.includes("INFOS")) steps.push("INFOS");
            } else steps.push(item);
          });
          if (this.param.sdkVersionCode < 90002000) {
            if (steps.length) {
              steps.forEach((item, index) => {
                if (item === "INFOS" && steps[index + 1] === "FACE_CHECK") {
                  let temp = null;
                  temp = steps[index];
                  steps[index] = steps[index + 1];
                  steps[index + 1] = temp;
                }
              });
            }
          }
          if (applyNo !== this.applyNo)
            removeStoreKeysUnit(
              `U_LOAN_PROCESS_${this.param.userId}`,
              [
                "applyNo",
                "steps",
                "infoFileds",
                "loanInfo",
                "agreement",
                "infos",
                "sms",
                "cardInfo",
                "faceInfo",
                "isApplied",
              ],
              4,
              "D",
            );
          else
            removeStoreKeysUnit(
              `U_LOAN_PROCESS_${this.param.userId}`,
              ["applyNo", "steps", "infoFileds", "loanInfo", "isApplied"],
              4,
              "D",
            );
          const info = {
            ...this.info,
            loanAmount: verifyParams.loanAmount,
          };
          updateStoreUnit(
            `U_LOAN_PROCESS_${this.param.userId}`,
            {
              supplier: this.supplierInfo,
              applyNo,
              steps,
              infoFileds,
              loanInfo: await encrypt(JSON.stringify(info)),
            },
            4,
            "D",
          );
          try {
            await loanNext(this.param.userId, this.loanNextCallBack());
          } catch (error) {
            if (!window?.navigator?.onLine) {
              /* empty */
            } else {
              this.isTryVerifyException = true;
            }
            console.error(`Loan verify success. Loan next error: (${error})`);
          }
          this.isLoading = false;
        } else if (applyStatus === 1) {
          // 如果状态仍然是1，继续轮询
          if (Date.now() - pollStartTime > TIMEOUT) {
            // 超时后的处理逻辑
            this.isLoading = false;
            this.isTryVerifyException = true;
          } else {
            setTimeout(() => {
              pollStatus(updateVerifyParams, pollStartTime);
            }, 1000); // 1秒后再次轮询
          }
        } else if (applyStatus === 4) {
          console.log("Apply status: ", applyStatus, "拒绝");
          this.isLoading = false;
          updateStoreUnit(
            `U_LOAN_PROCESS_${this.param.userId}`,
            {
              isApplied: true,
              applyStatus: 4,
              applyMessage: verifyRes.data?.refuseMsg,
              applyNo,
              steps: [],
              infoFileds: [],
              loanInfo: await encrypt(JSON.stringify(this.info)),
            },
            4,
            "D",
          );
          loanNext(this.param.userId, this.loanNextCallBack());
        }
      } else {
        // 其它的处理逻辑
        if (verifyRes?.code === LOAN_VERIFY_ERROR_CODE_MAP.TRIAL_EXPIRED) {
          toastMessage("借款试算数据已刷新");
          this.doAmountChange(`${this.info.loanAmount}`);
        } else if (verifyRes?.code === "ERR_NETWORK") {
          this.isNetworkException = true;
        } else {
          this.isTryVerifyException = true;
        }
        console.error(`Loan query verify error: (${verifyRes.message})`);
        this.isLoading = false;
      }
    },

    async submit() {
      const storage = getStore(`U_LOAN_PROCESS_${this.param.userId}`) || null;
      if (storage && storage.openId) {
        const openIdInfo = getStore(`U_LOAN_INFO_STORE_${storage.openId}`);
        console.log("解密前", openIdInfo);
        let loanCache =
          typeof openIdInfo === "string"
            ? await decrypt(getStore(`U_LOAN_INFO_STORE_${storage.openId}`))
            : openIdInfo;
        console.log("解密后", openIdInfo);
        loanCache.loanUse = this.info.loanUse;
        loanCache = await encrypt(loanCache);
        setWithExactExpire(`U_LOAN_INFO_STORE_${storage.openId}`, loanCache, "3000D");
      }
      const { repayMethod, totalTerm, couponNo = "", bankCardId, loanUse } = this.info;
      const cantSubmit =
        !this.checkElement(bankCardId, "请选择收款银行") ||
        !this.checkElement(loanUse, "请选择资金用途");
      if (cantSubmit) return;
      const loanUseCopy = filterLoanUsage(loanUse);
      const loanAmount = parseInt(`${this.info.loanAmount}00`, 10);
      this.isLoading = true;
      this.loadingMessage = "正在提交";
      report("wallet_page_click", {
        click_name: "loan_trial_next_click",
        loan_amount: this.info.loanAmount,
        repay_method: repayMethod,
        repay_term: totalTerm,
        coupon_no: couponNo,
        bankcard: bankCardId,
        loan_use: loanUseCopy,
      });

      const pollStatus = async (verifyParams, pollStartTime) => {
        try {
          if (!window?.navigator?.onLine) throw new Error("ERR_NETWORK");
          const verifyRes = await request(
            "/loan/api/loan/verify",
            {
              encryptedParams: verifyParams,
            },
            { mock: false },
          );
          this.handleVerifyResult(verifyRes, verifyParams, pollStatus, pollStartTime);
        } catch (error) {
          if (!window?.navigator?.onLine) {
            this.isNetworkException = true;
          } else {
            this.isTryVerifyException = true;
          }
          console.error("Polling error:", error);
          this.isLoading = false;
        }
      };

      const verifyParams = {
        repayType: this.repayType,
        repayMethod,
        totalTerm,
        couponNo,
        bankCardId,
        loanUse: loanUseCopy,
        loanAmount,
        sign: this.ifShowContract ? 1 : 0,
        ...(this.applyNo === "" ? null : { applyNo: this.applyNo }),
      };
      const verifyRes = await request(
        "/loan/api/bff/loanVerify",
        {
          encryptedParams: verifyParams,
        },
        { mock: false },
      );

      this.handleVerifyResult(verifyRes, verifyParams, pollStatus, Date.now());
    },

    async chooseBankCard(bankInfo) {
      this.info.bankCardId = bankInfo?.bankCardId;
      this.info.bankCardNo = bankInfo?.bankCardNo;
    },

    async recover() {
      try {
        if (
          typeof this.applyRes?.applyStatus === "number" &&
          [0, 6].includes(this.applyRes?.applyStatus)
        ) {
          const storageData = getStore(`U_LOAN_PROCESS_${this.param.userId}`);
          if (storageData && storageData.loanInfo) {
            storageData.loanInfo = await decrypt(storageData.loanInfo);
            if (typeof storageData.loanInfo === "string") {
              storageData.loanInfo = JSON.parse(storageData.loanInfo);
            }
            if (
              !storageData.loanInfo ||
              parseInt(`${storageData.loanInfo.loanAmount}`, 10) > this.maxLoan
            ) {
              removeStoreKeysUnit(
                `U_LOAN_PROCESS_${this.param.userId}`,
                [
                  "applyNo",
                  "steps",
                  "infoFileds",
                  "loanInfo",
                  "agreement",
                  "infos",
                  "sms",
                  "cardInfo",
                  "faceInfo",
                  "isApplied",
                ],
                4,
                "D",
              );
              return;
            }
            this.applyNo = storageData.applyNo || "";
            this.interruptLoanInfo = storageData.loanInfo;
            const { repayMethod, totalTerm, couponNo } = this.interruptLoanInfo;
            // 检查中断订单的优惠券是否可用
            if (couponNo) {
              const couponInfo = this.allCouponList?.find(
                (item) => item.couponNo === couponNo && item.status === couponStatus.NOT_USED,
              );
              if (!couponInfo) {
                this.applyNo = "";
                this.isInterruptLoanInfoValid = false;
                return;
              }
            }

            this.interruptLoanInfo.loanAmount = Big(this.interruptLoanInfo.loanAmount)
              .div(100)
              .toNumber();
            const loanAmount = parseInt(`${this.interruptLoanInfo.loanAmount}00`, 10);
            this.displayInfo.showLastLoanInfo = true;
            report("wallet_page_view", { page_name: "loan_trial_continue_last_order" });
            this.currentProd = this.productInfos?.find(
              (item) => item.repayMethod === this.interruptLoanInfo.repayMethod,
            );
            this.isFirstRepayLoading = true;
            this.ifShowReload = false;
            const trialInfo = await request(
              "/loan/api/loan/trial",
              { repayMethod, totalTerm, loanAmount, couponNo, applyNo: this.applyNo },
              { mock: false },
            );
            if (trialInfo.code !== 0) {
              this.isFirstRepayLoading = false;
              this.ifShowReload = true;
              return;
            }
            this.isFirstRepayLoading = false;
            this.ifShowReload = false;
            this.trialInfo = trialInfo.data;
            const { shouldRepayDate, termAmount } = this.trialInfo.repayPlanTerms[0];
            const month = parseInt(shouldRepayDate.substr(4, 2), 10);
            const date = parseInt(shouldRepayDate.substr(6, 2), 10);
            const repayAmount = new Big(termAmount).div(new Big(100)).toFixed(2);
            this.trialInfo.repayPlanTerms[0].desc = `${month}月${date}日${this.supplierInfo.accurateLoanTrial && this.supplierInfo?.accurateLoanTrial === 1 ? "预估应还" : "应还"}￥${repayAmount}`;
          }
        } else {
          removeStoreKeysUnit(
            `U_LOAN_PROCESS_${this.param.userId}`,
            [
              "applyNo",
              "steps",
              "infoFileds",
              "loanInfo",
              "agreement",
              "infos",
              "sms",
              "cardInfo",
              "faceInfo",
              "isApplied",
            ],
            4,
            "D",
          );
        }
      } catch (e) {
        delete this.applyNo;
        removeStoreKeysUnit(
          `U_LOAN_PROCESS_${this.param.userId}`,
          [
            "applyNo",
            "steps",
            "infoFileds",
            "loanInfo",
            "agreement",
            "infos",
            "sms",
            "cardInfo",
            "faceInfo",
            "isApplied",
          ],
          4,
          "D",
        );
      }
    },

    async doAmountChange(text) {
      const val = text.replace(/\D/g, "");

      await this.changeAmount(val);
    },
  },
});
