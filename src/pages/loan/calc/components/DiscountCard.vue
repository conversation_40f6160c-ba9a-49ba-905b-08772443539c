<template>
  <div
    v-if="totalDiscountInfo.isShow"
    class="discount-card"
    :style="{
      'padding-bottom':
        store.collapseActiveItems?.indexOf('totalDiscount') !== -1 &&
        !isTotalTemporaryUnavailableAndCouponNotTemporaryUnavailable
          ? 'var(--dp20)'
          : 'var(--dp4)',
    }"
  >
    <hnr-collapse
      v-model="store.collapseActiveItems"
      class="collapse-content"
      @change="collapseChange"
    >
      <hnr-collapse-item :name="discountTypes.TOTAL_DISCOUNT" class="total-discount-item">
        <template #title>
          <img
            style="width: 49px; height: 13px"
            src="/loan/totalDiscountTitleLight.webp"
            alt="总优惠标题"
          />
        </template>
        <template #value>
          <div
            v-show="
              totalDiscountInfo.subDiscountCountNum > 1 && !store.displayInfo.cellList[3].isLoading
            "
            style="display: flex; align-items: center"
          >
            <div class="total-discount-value-normal-color">已享</div>
            <div class="total-discount-value-light-color">
              &nbsp;{{ totalDiscountInfo.subDiscountCountNum }}&nbsp;
            </div>
            <div class="total-discount-value-normal-color">项优惠，共省</div>
            <div class="total-discount-value-light-color">
              ￥{{ new Big(totalDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2) }}
            </div>
          </div>

          <div
            v-show="
              totalDiscountInfo.subDiscountCountNum === 1 &&
              !store.displayInfo.cellList[3].isLoading
            "
            style="display: flex"
          >
            <div class="total-discount-value-normal-color">享限时优惠，省</div>
            <div class="total-discount-value-light-color">
              ￥{{ new Big(totalDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2) }}
            </div>
          </div>

          <div
            v-show="
              totalDiscountInfo.subDiscountCountNum === 0 &&
              !store.displayInfo.cellList[3].isLoading
            "
            style="display: flex"
          >
            <div class="total-discount-value-normal-color">{{ totalDiscountInfo.subTitle }}</div>
          </div>

          <div v-show="store.displayInfo.cellList[3].isLoading">
            <hnr-loading size="small" />
          </div>
        </template>

        <template #right-icon>
          <hnr-icon
            v-show="
              store.displayInfo.cellList[3].isLoading ||
              couponDiscountInfo.isShow ||
              tempDiscountInfo.isShow ||
              scheduleDiscountInfo.isShow ||
              tempAndScheduleDiscountInfo.isShow
            "
            :name="getCollapseIcon('totalDiscount')"
          ></hnr-icon>
        </template>

        <div class="total-discount-content">
          <!--优惠券优惠-->
          <hnr-collapse-item
            v-if="couponDiscountInfo.isShow"
            class="collapse-sub-item"
            :name="discountTypes.COUPON_DISCOUNT"
          >
            <template #title>
              <div class="collapse-item-title">优惠券</div>
            </template>

            <template #value>
              <div v-show="!store.displayInfo.cellList[3].isLoading" style="display: flex">
                <div
                  :class="{
                    'total-discount-value-light-color':
                      couponDiscountInfo.status === discountStatus.USED,
                    'total-discount-value-normal-color':
                      couponDiscountInfo.status !== discountStatus.USED,
                  }"
                  class="collapse-item-value"
                >
                  {{ couponDiscountInfo.subTitle }}
                </div>
              </div>

              <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
            </template>

            <template #right-icon>
              <hnr-icon name="/wallet-loan-web/pages/loan/rightIcon.svg"></hnr-icon>
            </template>
          </hnr-collapse-item>

          <!--限时优惠-->
          <hnr-collapse-item
            v-if="tempDiscountInfo.isShow && !scheduleDiscountInfo.isShow"
            class="collapse-sub-item"
            :name="discountTypes.TEMP_DISCOUNT"
          >
            <template #title>
              <div class="collapse-item-title">限时优惠</div>
            </template>

            <template #value>
              <div v-show="!store.displayInfo.cellList[3].isLoading" style="display: flex">
                <div
                  :class="{
                    'total-discount-value-light-color':
                      tempDiscountInfo.status === discountStatus.USED,
                    'total-discount-value-normal-color':
                      tempDiscountInfo.status !== discountStatus.USED,
                  }"
                  class="collapse-item-value"
                >
                  {{ tempDiscountInfo.subTitle }}
                </div>
              </div>
              <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
            </template>

            <template #right-icon>
              <hnr-icon :name="getCollapseIcon('tempDiscount')"></hnr-icon>
            </template>

            <div
              v-show="
                !store.displayInfo.cellList[3].isLoading &&
                tempDiscountInfo.status === discountStatus.USED
              "
              class="discount-sub-card"
            >
              <div class="discount-sub-header">
                <div class="discount-sub-header-left">
                  <span class="discount-sub-title">限时优惠</span>

                  <div v-if="store.displayInfo.cellList[1].tempPriceValidDays > 2">
                    <span class="discount-sub-tag">{{
                      `${store.displayInfo.cellList[1].tempPriceValidDays}天后限时降息失效`
                    }}</span>
                  </div>
                  <div
                    v-else-if="
                      store.displayInfo.cellList[1].tempPriceValidDays <= 2 &&
                      store.displayInfo.cellList[1].tempPriceValidDays > 0
                    "
                  >
                    <span class="discount-sub-tag">{{
                      `限时降息仅剩${store.displayInfo.cellList[1].tempPriceValidDays}天`
                    }}</span>
                  </div>
                  <div v-else>
                    <span class="discount-sub-tag">限时降息今日到期</span>
                  </div>
                </div>
                <div v-show="!store.displayInfo.cellList[3].isLoading" class="discount-sub-amount">
                  省 ¥
                  {{ new Big(tempDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2) }}
                </div>
                <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
              </div>
              <div class="discount-sub-content">
                <div class="discount-sub-details">
                  优惠金额=优惠前利息({{ store.displayInfo.cellList[1].value1 }}%)-优惠后利息({{
                    store.displayInfo.cellList[1].value3
                  }}%)
                </div>
                <div class="discount-sub-note">实际总利息以还款计划展示为准</div>
              </div>
            </div>
          </hnr-collapse-item>

          <!--按期还优惠-->
          <hnr-collapse-item
            v-if="!tempDiscountInfo.isShow && scheduleDiscountInfo.isShow"
            class="collapse-sub-item"
            :name="discountTypes.SCHEDULE_DISCOUNT"
          >
            <template #title>
              <div class="collapse-item-title">按期还优惠</div>
            </template>

            <template #value>
              <div v-show="!store.displayInfo.cellList[3].isLoading" style="display: flex">
                <div
                  :class="{
                    'total-discount-value-light-color':
                      scheduleDiscountInfo.status === discountStatus.USED,
                    'total-discount-value-normal-color':
                      scheduleDiscountInfo.status !== discountStatus.USED,
                  }"
                  class="collapse-item-value"
                >
                  {{ scheduleDiscountInfo.subTitle }}
                </div>
              </div>
              <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
            </template>

            <template #right-icon>
              <hnr-icon :name="getCollapseIcon('scheduleDiscountInfo')"></hnr-icon>
            </template>

            <div
              v-show="
                !store.displayInfo.cellList[3].isLoading &&
                scheduleDiscountInfo.status === discountStatus.USED
              "
              class="discount-sub-card"
            >
              <div class="discount-sub-header">
                <div class="discount-sub-header-left">
                  <span class="discount-sub-title">按期还优惠</span>
                </div>
                <div v-show="!store.displayInfo.cellList[3].isLoading" class="discount-sub-amount">
                  省 ¥
                  {{
                    new Big(scheduleDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2)
                  }}
                </div>
                <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
              </div>
              <div class="discount-sub-content">
                <div class="discount-sub-details">优惠金额=灵活还总利息-按期还总利息</div>
                <div class="discount-sub-note">实际总利息以还款计划展示为准</div>
              </div>
            </div>
          </hnr-collapse-item>

          <!--限时优惠 + 按期还优惠-->
          <hnr-collapse-item
            v-if="tempAndScheduleDiscountInfo.isShow"
            class="collapse-sub-item"
            :name="discountTypes.TEMP_SCHEDULE_DISCOUNT"
          >
            <template #title>
              <div class="collapse-item-title">限时优惠+按期还优惠</div>
            </template>
            <template #value>
              <div v-show="!store.displayInfo.cellList[3].isLoading" style="display: flex">
                <div
                  :class="{
                    'total-discount-value-light-color':
                      tempAndScheduleDiscountInfo.status === discountStatus.USED,
                    'total-discount-value-normal-color':
                      tempAndScheduleDiscountInfo.status !== discountStatus.USED,
                  }"
                  class="collapse-item-value"
                >
                  {{ tempAndScheduleDiscountInfo.subTitle }}
                </div>
              </div>
              <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
            </template>

            <template #right-icon>
              <hnr-icon :name="getCollapseIcon('tempAndScheduleDiscountInfo')"></hnr-icon>
            </template>

            <div
              v-show="
                !store.displayInfo.cellList[3].isLoading &&
                tempDiscountInfo.status === discountStatus.USED
              "
              class="discount-sub-card"
            >
              <div class="discount-sub-header">
                <div class="discount-sub-header-left">
                  <span class="discount-sub-title">限时优惠</span>

                  <div v-if="store.displayInfo.cellList[1].tempPriceValidDays > 2">
                    <span class="discount-sub-tag">{{
                      `${store.displayInfo.cellList[1].tempPriceValidDays}天后限时降息失效`
                    }}</span>
                  </div>
                  <div
                    v-else-if="
                      store.displayInfo.cellList[1].tempPriceValidDays <= 2 &&
                      store.displayInfo.cellList[1].tempPriceValidDays > 0
                    "
                  >
                    <span class="discount-sub-tag">{{
                      `限时降息仅剩${store.displayInfo.cellList[1].tempPriceValidDays}天`
                    }}</span>
                  </div>
                  <div v-else>
                    <span class="discount-sub-tag">限时降息今日到期</span>
                  </div>
                </div>
                <div v-show="!store.displayInfo.cellList[3].isLoading" class="discount-sub-amount">
                  省 ¥
                  {{ new Big(tempDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2) }}
                </div>
                <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
              </div>
              <div class="discount-sub-content">
                <div class="discount-sub-details">
                  优惠金额=优惠前利息({{ store.displayInfo.cellList[1].value1 }}%)-优惠后利息({{
                    store.displayInfo.cellList[1].value3
                  }}%)
                </div>
                <div class="discount-sub-note">实际总利息以还款计划展示为准</div>
              </div>
            </div>

            <div
              v-show="
                !store.displayInfo.cellList[3].isLoading &&
                scheduleDiscountInfo.status === discountStatus.USED
              "
              class="discount-sub-card"
            >
              <div class="discount-sub-header">
                <div class="discount-sub-header-left">
                  <span class="discount-sub-title">按期还优惠</span>
                </div>
                <div v-show="!store.displayInfo.cellList[3].isLoading" class="discount-sub-amount">
                  省 ¥
                  {{
                    new Big(scheduleDiscountInfo.discountAmount || 0).div(new Big(100)).toFixed(2)
                  }}
                </div>
                <div v-show="store.displayInfo.cellList[3].isLoading" class="dash-dash">--</div>
              </div>
              <div class="discount-sub-content">
                <div class="discount-sub-details">优惠金额=灵活还总利息-按期还总利息</div>
                <div class="discount-sub-note">实际总利息以还款计划展示为准</div>
              </div>
            </div>
          </hnr-collapse-item>
        </div>
      </hnr-collapse-item>
    </hnr-collapse>
  </div>
</template>
<script setup>
import { computed, onMounted, defineEmits, ref } from "vue";
import Big from "big.js";
import { initStore } from "../../../../helpers/utils";
import useStore from "../store";
import { discountStatus, discountTypes } from "../util";

const { store } = initStore(useStore);

function getCollapseIcon(itemName) {
  if (store.collapseActiveItems.includes(itemName)) {
    return "/wallet-loan-web/pages/loan/upIcon.svg";
  }
  return "/wallet-loan-web/pages/loan/downIcon.svg";
}

const emit = defineEmits(["showCoupon"]);
const collapseChange = (activeNames) => {
  if (activeNames[activeNames.length - 1] === "couponDiscount") {
    emit("showCoupon");
  }
};

// 优惠券优惠信息
const couponDiscountInfo = computed(() => {
  function calculateStatus() {
    if (store.couponList?.length === 0) {
      return discountStatus.NO_DISCOUNT;
    }

    if (!store.couponInfo && !store.hasUsableCoupond) {
      return discountStatus.TEMPORARY_UNAVAILABLE;
    }

    if (!store.couponInfo && store.hasUsableCoupond) {
      return discountStatus.NOT_USE;
    }

    return discountStatus.USED;
  }

  // 优惠券优惠金额
  const discountAmount = store.couponInfo?.discountAmount || store.trialInfo?.reductionAmount || 0;

  // 状态
  const status = calculateStatus();

  // 是否展示
  const isShow = status !== discountStatus.NO_DISCOUNT;

  function formatSubtitle() {
    if (status === discountStatus.NO_DISCOUNT) {
      return "";
    }
    if (status === discountStatus.TEMPORARY_UNAVAILABLE) {
      return "优惠暂不可用";
    }
    if (status === discountStatus.NOT_USE) {
      return "不使用";
    }

    return `省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
  }

  // 子标题
  const subTitle = formatSubtitle();

  return {
    discountAmount,
    status,
    isShow,
    subTitle,
  };
});

// 临时降价优惠信息
const tempDiscountInfo = computed(() => {
  // 临时降价优惠金额
  const discountAmount = store.trialInfo?.discountAmountInfo?.tempDiscount || 0;

  function calculateStatus() {
    const hasTempApr =
      store.productInfos?.some((item) => !!item.tempApr) ||
      !!store.trialInfo?.discountAmountInfo?.tempDiscount;

    if (!hasTempApr || store.trialInfo?.discountAmountInfo?.tempDiscount == null) {
      return discountStatus.NO_DISCOUNT;
    }

    if (hasTempApr && !discountAmount) {
      return discountStatus.TEMPORARY_UNAVAILABLE;
    }

    return discountStatus.USED;
  }
  // 状态
  const status = calculateStatus();

  // 是否展示
  const isShow = status === discountStatus.USED;

  function formatSubtitle() {
    if (status === discountStatus.NO_DISCOUNT) {
      return "";
    }
    if (status === discountStatus.TEMPORARY_UNAVAILABLE) {
      return "优惠暂不可用";
    }
    if (status === discountStatus.NOT_USE) {
      return "不使用";
    }

    return `省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
  }

  // 子标题
  const subTitle = formatSubtitle();

  return {
    discountAmount,
    status,
    isShow,
    subTitle,
  };
});

// 按期还优惠信息
const scheduleDiscountInfo = computed(() => {
  // 临时降价优惠金额
  const discountAmount = store.trialInfo?.discountAmountInfo?.scheduleDiscount || 0;

  function calculateStatus() {
    // 是否存在大于一种还款方式并且存在按期还的还款方式
    const hasMoreProductAndScheduleProduct =
      store.displayInfo?.pordSummaryInfos?.length > 1 &&
      store.displayInfo?.pordSummaryInfos?.some((item) => item.type === 2);

    if (
      !hasMoreProductAndScheduleProduct ||
      store.trialInfo?.discountAmountInfo?.scheduleDiscount == null
    ) {
      return discountStatus.NO_DISCOUNT;
    }

    if (hasMoreProductAndScheduleProduct && !discountAmount) {
      return discountStatus.TEMPORARY_UNAVAILABLE;
    }

    return discountStatus.USED;
  }

  // 状态
  const status = calculateStatus();

  // 是否展示
  const isShow = status === discountStatus.USED;

  function formatSubtitle() {
    if (status === discountStatus.NO_DISCOUNT) {
      return "";
    }
    if (status === discountStatus.TEMPORARY_UNAVAILABLE) {
      return "优惠暂不可用";
    }
    if (status === discountStatus.NOT_USE) {
      return "不使用";
    }

    return `省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
  }

  // 子标题
  const subTitle = formatSubtitle();

  return {
    discountAmount,
    status,
    isShow,
    subTitle,
  };
});

// 临时降价 + 按期还 优惠信息
const tempAndScheduleDiscountInfo = computed(() => {
  // 临时降价优惠金额
  const discountAmount =
    (tempDiscountInfo.value.discountAmount || 0) + (scheduleDiscountInfo.value.discountAmount || 0);

  function calculateStatus() {
    if (
      tempDiscountInfo.value.status === discountStatus.NO_DISCOUNT ||
      scheduleDiscountInfo.value.status === discountStatus.NO_DISCOUNT
    ) {
      return discountStatus.NO_DISCOUNT;
    }

    if (!discountAmount) {
      return discountStatus.TEMPORARY_UNAVAILABLE;
    }

    return discountStatus.USED;
  }
  // 状态
  const status = calculateStatus();

  // 是否展示
  const isShow =
    tempDiscountInfo.value.status === discountStatus.USED &&
    scheduleDiscountInfo.value.status === discountStatus.USED;

  function formatSubtitle() {
    if (status === discountStatus.NO_DISCOUNT) {
      return "";
    }
    if (status === discountStatus.TEMPORARY_UNAVAILABLE) {
      return "优惠暂不可用";
    }
    if (status === discountStatus.NOT_USE) {
      return "不使用";
    }

    return `省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
  }

  // 子标题
  const subTitle = formatSubtitle();

  return {
    discountAmount,
    status,
    isShow,
    subTitle,
  };
});

// 总优惠信息
const totalDiscountInfo = computed(() => {
  // 总优惠金额
  const discountAmount =
    (couponDiscountInfo.value.discountAmount || 0) +
    (tempDiscountInfo.value.discountAmount || 0) +
    (scheduleDiscountInfo.value.discountAmount || 0);

  function calculateStatus() {
    const statuses = [
      couponDiscountInfo.value.status,
      tempDiscountInfo.value.status,
      scheduleDiscountInfo.value.status,
    ];

    if (statuses.every((s) => s === discountStatus.NO_DISCOUNT)) {
      return discountStatus.NO_DISCOUNT;
    }

    if (statuses.some((s) => s === discountStatus.USED)) {
      return discountStatus.USED;
    }

    if (statuses.some((s) => s === discountStatus.NOT_USE)) {
      return discountStatus.NOT_USE;
    }

    return discountStatus.TEMPORARY_UNAVAILABLE;
  }

  // 状态
  const status = calculateStatus();

  // 是否展示
  const isShow = status !== discountStatus.NO_DISCOUNT;

  function formatSubtitle() {
    if (status === discountStatus.NO_DISCOUNT) {
      return "";
    }
    if (status === discountStatus.TEMPORARY_UNAVAILABLE) {
      return "优惠暂不可用";
    }
    if (status === discountStatus.NOT_USE) {
      return "不使用";
    }

    // 享受了几项优惠
    const subDiscountCountNum = [
      couponDiscountInfo.value.status,
      tempDiscountInfo.value.status,
      scheduleDiscountInfo.value.status,
    ].filter((s) => s === discountStatus.USED).length;

    if (subDiscountCountNum === 1) {
      return `享限时优惠，省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
    }

    if (subDiscountCountNum > 1) {
      return `已享${subDiscountCountNum}项优惠，共省￥${new Big(discountAmount || 0).div(new Big(100)).toFixed(2)}`;
    }

    if (
      couponDiscountInfo.value.status === discountStatus.TEMPORARY_UNAVAILABLE &&
      tempDiscountInfo.value.status === discountStatus.TEMPORARY_UNAVAILABLE &&
      scheduleDiscountInfo.value.status === discountStatus.TEMPORARY_UNAVAILABLE
    ) {
      return "优惠暂不可用";
    }

    return "不使用";
  }

  // 子标题
  const subTitle = formatSubtitle();

  // 享受了几项优惠
  const subDiscountCountNum = [
    couponDiscountInfo.value.status,
    tempDiscountInfo.value.status,
    scheduleDiscountInfo.value.status,
  ].filter((s) => s === discountStatus.USED).length;

  return {
    subDiscountCountNum,
    discountAmount,
    status,
    isShow,
    subTitle,
  };
});

const totalDiscountTitleSrc = ref();
onMounted(() => {
  const darkModeMediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
  if (darkModeMediaQuery.matches) {
    totalDiscountTitleSrc.value = "/loan/totalDiscountTitleDark.webp";
  } else {
    totalDiscountTitleSrc.value = "/loan/totalDiscountTitleLight.webp";
  }

  darkModeMediaQuery.addEventListener("change", (event) => {
    if (event.matches) {
      totalDiscountTitleSrc.value = "/loan/totalDiscountTitleDark.webp";
    } else {
      totalDiscountTitleSrc.value = "/loan/totalDiscountTitleLight.webp";
    }
  });
});

const isTotalTemporaryUnavailableAndCouponNotTemporaryUnavailable = computed(() => {
  return (
    totalDiscountInfo.value.status === discountStatus.TEMPORARY_UNAVAILABLE &&
    couponDiscountInfo.value.status !== discountStatus.TEMPORARY_UNAVAILABLE
  );
});
</script>
<style scoped>
.discount-card {
  border: 1px solid #ffffff;
  background-image: url("/public/loan/discountCardBack.svg"),
    linear-gradient(to right, #fcecdf, #fff6ef);
  background-position:
    top right,
    0 0;
  background-repeat: no-repeat, no-repeat;
  background-size:
    150px auto,
    cover;
  border-radius: var(--hnr-card-corner-radius);
  margin-top: var(--hnr-elements-margin-vertical-M2);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  width: calc(100% - 24px);
  padding: var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2)
    var(--hnr-elements-margin-vertical-M) var(--hnr-elements-margin-horizontal-M2);
  padding-top: 0;
  padding-bottom: 0;
}
.collapse-content {
  background: transparent !important;
}

.total-discount-value-normal-color {
  color: rgba(0, 0, 0, 0.9);
}

.total-discount-value-light-color {
  color: #fa6b19;
}

.collapse-sub-item {
  min-height: var(--dp32) !important;
}

:deep(.collapse-sub-item .hnr-cell-box) {
  padding: 0 !important;
  min-height: var(--dp32) !important;
}

:deep(.hnr-cell-box) {
  padding: 0 !important;
  margin: 0 !important;
}
:deep(.collapse-sub-item .hnr-cell-box .hnr-cell) {
  min-height: var(--dp32) !important;
}

:deep(.collapse-sub-item .hnr-cell-box .hnr-cell .hnr-cell__left) {
  padding: 0 !important;
  min-height: var(--dp32) !important;
}

:deep(.collapse-sub-item .hnr-cell-box .hnr-cell .hnr-cell__value) {
  padding: 0 !important;
  min-height: var(--dp32) !important;
}

.collapse-item-title {
  font-size: var(--dp14);
  color: rgba(0, 0, 0, 0.6);
}

.collapse-item-value {
  font-size: var(--dp14);
}

.discount-sub-card {
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--hnr-default-corner-radius-s);
  padding: var(--dp8) var(--dp12);
  margin-top: var(--dp8);
}

:deep(.total-discount-item .hnr-cell__left) {
  padding-top: var(--dp20) !important;
  padding-bottom: var(--dp16) !important;
}

:deep(.total-discount-item .hnr-cell__value) {
  padding-top: var(--dp20) !important;
  padding-bottom: var(--dp16) !important;
}
.discount-sub-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--dp4);
}

.discount-sub-header-left {
  display: flex;
  align-items: center;
}

.discount-sub-title {
  font-size: var(--dp12);
  color: rgba(0, 0, 0, 0.6);
}

.discount-sub-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--dp11);
  background-color: #ff6e40;
  color: white;
  font-size: var(--dp10);
  padding: 2px 6px;
  border-radius: var(--hnr-default-corner-radius-xs);
  margin-left: 8px;
}

.discount-sub-content {
}

.discount-sub-details {
  font-size: var(--dp12);
  color: rgba(0, 0, 0, 0.38);
  margin-bottom: 2px;
}

.discount-sub-note {
  font-size: var(--dp10);
  color: rgba(0, 0, 0, 0.38);
}

.discount-sub-amount {
  font-weight: var(--hnr-font-weight-medium);
  color: #fa6b19;
  font-size: var(--dp12);
}

:deep(.hnr-cell-group) {
  border-radius: 0 !important;
}
:deep(.hnr-cell-box) {
  background: transparent !important;
  border-radius: 0 !important;
}

:deep(.hnr-collapse-item__content) {
  background: transparent !important;
  padding: 0 !important;
}
:deep(.hnr-collapse-item .hnr-cell) {
  padding: 0 !important;
}
:deep(.hnr-cell__value) {
  gap: 5px;
}

:deep(.hnr-collapse) {
  margin: 0 !important;
}
.dash-dash {
  color: rgba(0, 0, 0, 0.6);
}

:deep(.total-discount-content .collapse-sub-item) {
  margin-bottom: var(--dp8);
  min-height: 0 !important;
}
:deep(.total-discount-content .collapse-sub-item):last-child {
  margin-bottom: 0;
}
:deep(.total-discount-content .collapse-sub-item .hnr-cell-box) {
  min-height: 0 !important;
}
:deep(.total-discount-content .collapse-sub-item .hnr-cell-box .hnr-cell) {
  min-height: 0 !important;
}
:deep(.total-discount-content .collapse-sub-item .hnr-cell-box .hnr-cell .hnr-cell__left) {
  min-height: 0 !important;
}
:deep(.total-discount-content .collapse-sub-item .hnr-cell-box .hnr-cell .hnr-cell__value) {
  display: flex;
  align-items: center !important;
  min-height: 0 !important;
}

:deep(.hnr-collapse-item__title .hnr-cell__right-icon) {
  width: var(--dp20) !important;
  height: var(--dp20) !important;
}

:deep(.hnr-collapse-item__title .hnr-cell__right-icon svg) {
  width: var(--dp20) !important;
  height: var(--dp20) !important;
}

:deep(.hnr-cell__right-icon) {
  align-items: normal;
}
@media (prefers-color-scheme: dark) {
  .discount-card {
    background-image: url("/public/loan/discountCardBack.svg"),
      linear-gradient(to right, #262320, #272625);
    border: 1px solid #2e2e2e;
  }

  .total-discount-value-normal-color {
    color: #ffffffdb;
  }

  .total-discount-value-light-color {
    color: #f56218;
  }

  .collapse-item-title {
    color: #ffffff99;
  }

  .discount-sub-card {
    background: #ffffff19;
  }

  .discount-sub-title {
    color: #ffffff99;
  }

  .discount-sub-amount {
    color: #f56218;
  }

  .discount-sub-details {
    color: var(--hnr-text-color-tertiary);
  }

  .discount-sub-note {
    color: var(--hnr-text-color-tertiary);
  }

  .dash-dash {
    color: #ffffff99;
  }

  :deep(.hnr-icon svg path) {
    fill: currentColor !important;
    fill-opacity: 1 !important;
  }
}
</style>
