<script setup>
import { nextTick, onMounted, ref, watch, defineProps, defineExpose } from "vue";
import lodash from "lodash";
import { getCurrentCpInfo } from "../../../../helpers/configUtils";
import { report } from "../../../../helpers/native-bridge";
import {
  fetchHtml,
  isPdfUrl,
  isSupportForIframeSrc,
  isUseSrcDirectly,
  showPdfUrl,
} from "../../../../helpers/utils";

const props = defineProps({
  iframeArr: {
    type: Array,
    default: () => [],
  },
  contractUrl: {
    type: String,
    default: "",
  },
  closeContractPopup: {
    type: Function,
    default: () => {},
  },
});

const currentIndex = ref(0);

const supplierName = ref("");
const supplierId = ref(0);
const pageRef = ref(null);
const iframeRef = ref(null);
const navRef = ref(null);
const bottomBtnRef = ref(null);
const iframeHeight = ref(0);
const showIframe = ref(true);

const scrollPosition = ref({ st: 0, ed: 0, top: 0, bottom: 0, init: "top" });
let startY = 0;
const handleTouchStart = (event) => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  if (iframeDocument) {
    scrollPosition.value.st =
      iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
  }
  // 记录初始触摸位置
  const touch = event.touches[0];
  startY = touch.clientY;
};
const handleIframeSrc = async (contractUrl) => {
  if (isPdfUrl(contractUrl || "")) {
    iframeRef.value.removeAttribute("srcdoc");
    iframeRef.value.src = showPdfUrl(contractUrl);
  } else if (isUseSrcDirectly(supplierId.value)) {
    iframeRef.value.removeAttribute("srcdoc");
    iframeRef.value.src = contractUrl;
  } else if (isSupportForIframeSrc(supplierId.value)) {
    const innerUrl = new URL(contractUrl);
    const queryString = innerUrl.search;
    iframeRef.value.removeAttribute("srcdoc");
    iframeRef.value.src = `${window.location.origin}/wallet-loan-web/uri?href=${encodeURIComponent(innerUrl.origin + innerUrl.pathname)}${queryString}&isSupportIframeSrcUrl=${true}`;
  } else {
    iframeRef.value.removeAttribute("src");
    iframeRef.value.srcdoc = await fetchHtml(
      `${window.location.origin}/wallet-loan-web/uri?href=${encodeURIComponent(contractUrl)}`,
    );
  }
  props.iframeArr.forEach((item, index) => {
    if (item.contractUrl === contractUrl) {
      currentIndex.value = index;
    }
  });
};

// 点击协议链接方法,利用iframe的渲染与否来解决iframe.src跳转带来history变化的问题
const iframeClick = (contract, scrollInit = "top") => {
  showIframe.value = false;
  setTimeout(async () => {
    showIframe.value = true;
    await nextTick();
    await handleIframeSrc(contract.contractUrl);
  });
  scrollPosition.value.init = scrollInit;
};
const swipeTop = () => {
  if (
    scrollPosition.value.st === scrollPosition.value.top &&
    scrollPosition.value.ed === scrollPosition.value.top
  ) {
    if (currentIndex.value - 1 >= 0) {
      iframeClick(props.iframeArr[currentIndex.value - 1], "bottom");
    }
  }
};
const swipeBottom = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  const scrollHeight =
    iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
  const windowHeight = iframeDocument.defaultView.innerHeight;
  scrollPosition.value.bottom = scrollHeight - windowHeight - 1;
  if (
    scrollPosition.value.st > scrollPosition.value.bottom &&
    scrollPosition.value.ed > scrollPosition.value.bottom
  ) {
    if (currentIndex.value + 1 < props.iframeArr.length) {
      iframeClick(props.iframeArr[currentIndex.value + 1], "top");
    }
  }
};
const handleScroll = lodash.debounce(
  (event) => {
    // 计算滑动距离
    const touch = event.touches[0];
    const deltaY = touch.clientY - startY;
    const iframeDocument =
      iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
    if (iframeDocument) {
      scrollPosition.value.ed =
        iframeDocument.documentElement?.scrollTop || iframeDocument.body?.scrollTop;
    }

    // 根据滑动距离执行相应的逻辑
    if (deltaY > 30) {
      swipeTop();
    } else if (deltaY < -30) {
      swipeBottom();
    }
  },
  600,
  { leading: true },
);

const handleIframeLoad = async () => {
  if (iframeRef.value) {
    const iframeDocument =
      iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
    const scrollHeight =
      iframeDocument.documentElement.scrollHeight || iframeDocument.body.scrollHeight;
    const windowHeight = iframeDocument.defaultView.innerHeight;
    scrollPosition.value = {
      ...scrollPosition.value,
      st: 0,
      ed: 0,
      top: 0,
      bottom: scrollHeight - windowHeight,
    };
    iframeDocument.removeEventListener("touchstart", handleTouchStart);
    iframeDocument.addEventListener("touchstart", handleTouchStart);
    iframeDocument.removeEventListener("touchmove", handleScroll);
    iframeDocument.addEventListener("touchmove", handleScroll);
  }
};

const show = async () => {
  setTimeout(async () => {
    report("wallet_page_view", { page_name: "loan_trial_popup" });
    await nextTick();
    iframeHeight.value = `${pageRef.value.clientHeight - navRef.value.clientHeight - bottomBtnRef.value.clientHeight - 40}px`;
    await handleIframeSrc(props.contractUrl);
  });
};
const simulateMounted = () => {
  show();
};
const simulateClose = () => {
  iframeRef.value.src = "";
};
defineExpose({
  simulateMounted,
  simulateClose,
});
onMounted(async () => {
  const res = await getCurrentCpInfo();
  supplierName.value = res.supplierName || "三方金融机构";
  supplierId.value = res.supplierId || 0;
  show();
});
const isShowMore = ref(false);
const changeList = async () => {
  isShowMore.value = !isShowMore.value;
  await nextTick();
  iframeHeight.value = `${pageRef.value.clientHeight - navRef.value.clientHeight - bottomBtnRef.value.clientHeight - 40}px`;
};

const getIframeArr = () => {
  return isShowMore.value ? props.iframeArr : props.iframeArr.slice(0, 1);
};
const handleClick = () => {
  report("wallet_page_click", { click_name: "loan_trial_read_click" });
  props.closeContractPopup();
};
watch(
  () => isShowMore,
  async () => {
    await nextTick();
    iframeHeight.value = `${pageRef.value.clientHeight - navRef.value.clientHeight - bottomBtnRef.value.clientHeight - 40}px`;
  },
  { deep: true },
);
</script>

<template>
  <div ref="pageRef" class="page">
    <div ref="navRef" class="content-top">
      <div class="content-title">
        {{ `以下协议由${supplierName}提供，请仔细阅读：` }}
      </div>
    </div>
    <div class="content-iframe">
      <iframe
        v-if="showIframe"
        ref="iframeRef"
        frameborder="0"
        style="border-radius: var(--dp12) var(--dp12) 0 0 !important"
        width="100%"
        :height="iframeHeight"
        @load="handleIframeLoad"
      ></iframe>
    </div>
    <div ref="bottomBtnRef" class="btn-area">
      <div
        :style="{
          overflowY: isShowMore ? 'auto' : 'hidden',
          'max-height': '248px',
        }"
      >
        <div v-if="props.iframeArr.length === 1" class="header btn-area-fold">
          <div class="item-right light-color">
            <span>{{ `共${props.iframeArr.length}份协议` }}</span>
          </div>
        </div>
        <div
          v-if="props.iframeArr.length > 1"
          class="header item-right light-color"
          @click="changeList"
        >
          <span>{{ `共${props.iframeArr.length}份协议` }}</span>
          <span :class="isShowMore ? 'icon-down' : 'icon-up'"></span>
        </div>
        <div
          v-for="(item, index) in getIframeArr()"
          :key="index"
          class="btn-area-agreement"
          @click="iframeClick(item)"
        >
          <div>{{ item.contractName }}</div>
        </div>
      </div>
      <hnr-button type="primary" standard-width="true" class="btn-area-btn" @click="handleClick">
        我已阅读并同意以上协议
      </hnr-button>
    </div>
  </div>
</template>

<style scoped>
:deep(.hnr-button--disabled) {
  opacity: 0.38;
}

.entrance-loading {
  margin-top: 133px;
  height: calc(100vh - 133px);
  width: 100%;
}

.signItemActive {
  /* border: 1px solid red;
  border-radius: 4px; */
}

.page {
  background: var(--hnr-color-background-cardview);
  height: 100%;
}

.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  margin: 14px auto 20px auto;
}

.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

.content-title {
  color: var(--hnr-text-color-secondary);
  padding: 0 24px 8px 24px;
  font-size: 13px;
}

.content-top {
  width: 100%;
  background: var(--hnr-color-background-cardview);
}

.btn-area {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 100;
  background: var(--hnr-color-background-cardview);
  padding: 16px 24px var(--hnr-default-padding-bottom-fixed) 24px;
  box-sizing: border-box;
}

.btn-area ::-webkit-scrollbar {
  display: none;
}

.btn-area-agreement {
  display: flex;
  width: 70%;
  word-wrap: break-word !important;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  gap: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-primary-activated);
}

.btn-area-fold {
  display: flex;
  justify-content: flex-start;
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  gap: var(--hnr-elements-margin-vertical-S);
}
.btn-area-btn {
  width: 100%;
  max-width: none !important;
  margin-top: 8px;
}

.item-right {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.light-color {
  color: var(--hnr-text-color-secondary);
}

.icon-size {
  width: 12px;
  height: 12px;
  color: var(--hnr-color-secondary);
}
.puList {
  max-height: 248px;
  overflow-y: auto;
}
.puList ::-webkit-scrollbar {
  display: none;
}
.header {
  position: absolute;
  right: 24px;
  color: var(--hnr-text-color-secondary);
  z-index: 1;
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.icon-down:before {
  content: "\e900";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.icon-up:before {
  content: "\e901";
  font-family: "icomoon";
  font-size: var(--dp12);
  color: var(--hnr-text-color-secondary);
  margin-left: var(--dp4);
}
.content-iframe {
  margin: 0 var(--dp12);
  border-radius: var(--hnr-default-corner-radius-m);
  background: var(--hnr-color-card-background);
}
@media (prefers-color-scheme: dark) {
  .page {
    background-color: #2e2e2e !important;
    .content-top {
      background-color: #2e2e2e !important;
    }
    .btn-area {
      background-color: #2e2e2e !important;
    }
    .content-iframe {
      background-color: #000000 !important;
    }
  }
}
</style>
