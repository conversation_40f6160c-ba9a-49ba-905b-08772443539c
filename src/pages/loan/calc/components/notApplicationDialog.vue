<script setup>
import lodash from "lodash";
import { defineProps } from "vue";
import { amountNumberFormat, initStore } from "../../../../helpers/utils";
import useStore from "../store";
import { removeStoreKeysUnit } from "../../../../helpers/storage";
import { loanNext } from "../../../../helpers/next";
import { report } from "../../../../helpers/native-bridge";

const { store, data } = initStore(useStore);

const props = defineProps({
  initLoanInfo: { type: Function, default: () => {} },
});

function closeLastLoan() {
  report("wallet_page_click", { click_name: "loan_trial_cancel_last_order_click" });
  removeStoreKeysUnit(
    `U_LOAN_PROCESS_${store.param.userId}`,
    [
      "applyNo",
      "steps",
      "infoFileds",
      "loanInfo",
      "agreement",
      "infos",
      "infoCache",
      "sms",
      "cardInfo",
      "faceInfo",
      "isApplied",
    ],
    4,
    "D",
  );
  store.applyNo = "";
  store.displayInfo.showLastLoanInfo = false;
  props.initLoanInfo(null);
}

const disabledOverlayClose = (action) => {
  switch (action) {
    case "clickOverlay":
      return false;
    default:
      return false;
  }
};

const loanNextCallBack = () => {
  store.displayInfo.showLastLoanInfo = false;
};

const go2BreakPoint = () => {
  report("wallet_page_click", { click_name: "loan_trial_continue_last_order_click" });
  store.continueButtonDisabled = true;
  setTimeout(() => {
    store.continueButtonDisabled = false;
  }, 3000);
  // 每次恢复订单确保清除faceInfo
  removeStoreKeysUnit(`U_LOAN_PROCESS_${store.param.userId}`, ["faceInfo"], 4, "D");
  loanNext(store.param.userId, loanNextCallBack());
};

const go2BreakPointDebounce = lodash.debounce(go2BreakPoint, 1000, { leading: true });
</script>
<template>
  <div>
    <hnr-dialog
      v-model:show="data.displayInfo.showLastLoanInfo"
      :show-confirm-button="false"
      :show-cancel-button="false"
      :lazy-render="false"
      :narrow-padding="true"
      :before-close="disabledOverlayClose"
    >
      <div class="laseJk">
        <div>
          <div class="jkTitle">您有一笔未完成的借款申请</div>
          <div class="jkTitleCon">
            <div class="lastTitleLeft comLastS">借款金额</div>
            <div class="lastTitleRight comLastS">
              ￥{{ amountNumberFormat(data.interruptLoanInfo.loanAmount, 0) }}
            </div>
          </div>
          <div class="jkTitleCon">
            <div class="lastTitleLeft comLastS">借款周期</div>
            <div class="lastTitleRight comLastS">{{ data.interruptLoanInfo.totalTerm }}期</div>
          </div>
          <div class="jkTitleCon" style="margin-bottom: var(--hnr-elements-margin-vertical-M)">
            <div class="lastTitleLeft comLastS">首期应还</div>
            <div
              v-if="!data.isFirstRepayLoading && !data.ifShowReload"
              class="lastTitleRight comLastS"
            >
              {{
                data.trialInfo.repayPlanTerms[0]?.desc?.split("￥")[0] +
                "￥" +
                amountNumberFormat(data.trialInfo.repayPlanTerms[0]?.desc?.split("￥")[1] || 0)
              }}
            </div>
            <hnr-loading
              v-if="data.isFirstRepayLoading"
              style="display: flex; height: var(--hnr-body-1); width: var(--hnr-body-1)"
            />
            <div v-if="data.ifShowReload" class="noCardClass">--</div>
          </div>
        </div>
        <div style="text-align: center">
          <hnr-button
            style="width: 100%"
            class="confirmBtn"
            type="primary"
            :disabled="store.continueButtonDisabled"
            @click="go2BreakPointDebounce"
            >继续申请</hnr-button
          >
          <hnr-button type="default" style="width: 100%" @click="closeLastLoan()"
            >放弃该笔，重新申请</hnr-button
          >
        </div>
      </div>
    </hnr-dialog>
  </div>
</template>
<style scoped>
.noCardClass {
  display: flex;
  align-items: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weigth-regular);
}

.laseJk {
  height: 100%;
  box-sizing: border-box;
  padding: var(--hnr-elements-margin-vertical-L2) var(--hnr-elements-margin-horizontal-L2) 0;
}
.jkTitle {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.jkTitleCon {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.lastTitleLeft {
  color: var(--hnr-text-color-secondary);
}
.lastTitleRight {
  color: var(--hnr-text-color-primary);
}
.comLastS {
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}
.confirmBtn {
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.lastTitleMiddle {
  margin: var(--hnr-elements-margin-vertical-M2) 0;
}

/* 弹出框 */
:deep(.hnr-dialog__content--isolated-title) {
  margin-top: 0;
}
:deep(.hnr-dialog__content--narrow-padding) {
  padding: 0;
}
:deep(.hnr-dialog__content) {
  padding: 0;
}
:deep(.hnr-dialog__content--isolated-footer) {
  margin-bottom: var(--hnr-elements-margin-vertical-M2) !important;
}
</style>
