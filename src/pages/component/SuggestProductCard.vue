<template>
  <div class="recommend-card-container">
    <div v-for="item in sortedSuggestProducts" :key="item.supplierId" class="recommend-card">
      <div class="recommend-card-supplier">
        <img class="recommend-card-supplier-icon" :src="item.logoUrl" />
        <span class="recommend-card-supplier-text">{{
          language.startsWith("en") ? item.translatedName : item.supplierName
        }}</span>
      </div>
      <div class="recommend-card-double-row">
        <div class="recommend-card-double-row-left">
          <span class="recommend-card-double-row-one">{{
            formatTemplateString(suggestProductText, item.apr)
          }}</span>
          <span class="recommend-card-double-row-two">￥{{ formatAmount(item.maxLoan) }}</span>
        </div>
        <div class="recommend-card-double-row-right">
          <hnr-button
            type="default"
            size="mini"
            class="recommand-button"
            @click="click2ProductUrlDebounce(item)"
            @exposure="reportExposure(item)"
            >{{ buttonText }}</hnr-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import lodash from "lodash";
import { DEBOUNCE_OPTIONS, DEBOUNCE_WAIT_MILLISECONDS } from "../../helpers/constants";
import { goto, report } from "../../helpers/native-bridge";
import { setNetwork } from "../../helpers/network-helper";
import { amountNumberFormat } from "../../helpers/utils";

export default {
  props: {
    suggestProducts: {
      type: Array,
      required: true,
    },
    buttonText: {
      type: String,
      required: false,
      default: "立即申请",
    },
    suggestProductText: {
      type: String,
      required: false,
      default: "最高可借 | 年化利率%s%起",
    },
    toastText: {
      type: String,
      required: false,
      default: "正在跳转到第三方页面",
    },
    language: {
      type: String,
      required: false,
      default: "zh-CN",
    },
    defineGoto: {
      type: Function,
      required: false,
      default: null,
    },
    location: {
      type: String,
      required: false,
      default: null,
    },
  },
  data: () => {
    return {
      reportedSupplier: [],
    };
  },
  computed: {
    sortedSuggestProducts() {
      if (this.suggestProducts && this.suggestProducts.length > 0) {
        return this.suggestProducts; // 随机展示
      }
      return [];
    },
  },
  mounted() {
    this.click2ProductUrlDebounce = lodash.debounce(
      this.click2ProductUrl,
      DEBOUNCE_WAIT_MILLISECONDS,
      DEBOUNCE_OPTIONS,
    );
    const observerOptions = { threshold: 1 };
    if (this.location?.includes("失败")) {
      observerOptions.root = document.body;
      observerOptions.rootMargin = "0px 0px -80px 0px";
    } else {
      observerOptions.rootMargin = "0px 0px 0px 0px";
    }
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.dispatchEvent(new Event("exposure"));
        }
      });
    }, observerOptions);
    const interval = setInterval(() => {
      const buttons = this.$el.querySelectorAll(".recommand-button");
      if (buttons.length) {
        buttons.forEach((node) => {
          observer.observe(node);
        });
        clearInterval(interval);
      }
    }, 10);
  },
  methods: {
    click2ProductUrl(item) {
      if (typeof window !== "undefined") {
        if (!window?.navigator?.onLine) {
          setNetwork();
        } else if (typeof this.defineGoto === "function") {
          this.defineGoto(item, true);
        } else {
          goto(item.targetUrl, true);
          window.hnr.ui
            .toast({
              msg: this.toastText,
            })
            .catch(() => {});
          report(
            "wallet_page_click",
            {
              click_name: "enter_recommend_supplier_page_click",
              view_location: this.location,
              supplierName: item.supplierName,
              index: item.sortIndex,
              targetUrl: item.targetUrl,
            },
            -1,
          );
        }
      }
    },
    formatTemplateString(str, ...args) {
      let formatted = str;
      args.forEach((arg) => {
        formatted = formatted.replace("%s", arg);
      });
      return formatted;
    },
    formatAmount(amount) {
      return amountNumberFormat(amount, 0);
    },
    reportExposure(item) {
      if (!this.reportedSupplier[item.supplierName]) {
        this.reportedSupplier[item.supplierName] = 1;
        report(
          "wallet_page_view",
          {
            page_name: "recommend_supplier_view",
            view_location: this.location,
            supplierName: item.supplierName,
            index: item.sortIndex,
            targetUrl: item.targetUrl,
          },
          -1,
        );
      }
    },
  },
};
</script>

<style scoped>
.recommend-card-container {
  display: flex;
  flex-direction: column;
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
}

.recommend-card {
  display: flex;
  flex-direction: column;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  border-radius: var(--hnr-card-border-radius);
  background-color: var(--hnr-card-background);
  box-sizing: border-box;
}

.recommend-card-img {
  width: auto;
}

.recommend-card-supplier {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-left: var(--hnr-elements-margin-horizontal-M2);
}

.recommend-card-supplier-icon {
  width: var(--dp16);
  height: var(--dp16);
  object-fit: contain;
}

.recommend-card-supplier-text {
  margin-left: var(--hnr-elements-margin-horizontal-M);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}

.recommend-card-double-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}

.recommend-card-double-row-left {
  display: flex;
  flex-direction: column;
  width: var(--dp224);
}

.recommend-card-double-row-one {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.recommend-card-double-row-two {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}

.recommend-card-double-row-right {
  margin-left: auto;
  margin-right: 0;
}
</style>
