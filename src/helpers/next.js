import { showConfirmDialog, showToast } from "@hihonor/hnr/dist/hnr.es.min";
import {
  encrypt,
  goto,
  gotoWithOptions,
  liveDetection,
  getLiveImage,
  toastMessage,
  decrypt,
  report,
  setCp,
  closeLiveGuidePage,
  regNativeEvent,
} from "./native-bridge";
import { getStore, setWithExactExpire, del, updateStoreUnit } from "./storage";
import { request } from "./utils";
import { getCurrentCpInfo, determineReActiveStatus } from "./configUtils";
import { getDialogConfig, setStatusStore } from "./retention-dialog-utils";
import { getCurrentStore, LOAN_APPLY_ERROR_CODE_MAP, urlMap } from "./constants";

const TIMEOUT_TIME = 180 * 1000;
let startTime;
let endTime;

const formInfoMap = {};
const filterChar = (str) => {
  switch (str) {
    case "配偶":
      return "SPOUSE";
    case "父母":
      return "PARENT";
    case "子女":
      return "CHILDREN";
    case "兄弟姐妹":
      return "SISTER";
    case "其他亲属":
      return "OTHER";
    default:
      return "";
  }
};

const stepMap = {
  FACE_CHECK: "faceInfo",
  AGREEMENT: "agreement",
  AGREEMENT_P: "agreement",
  INFOS: "infos",
  SMS: "sms",
};

export async function loanNext(userId, loanNextCallBack = () => {}) {
  let loanStorage = getStore(`U_LOAN_PROCESS_${userId}`) || {};
  const sdkVersionCode = getCurrentStore()?.param?.sdkVersionCode;

  if (loanStorage.dictMap) {
    Object.keys(loanStorage.dictMap).forEach((key) => {
      loanStorage.dictMap[key].forEach((item) => {
        formInfoMap[item.name] = item.value;
      });
    });
  }

  if (loanStorage.applyStatus) {
    del(`U_LOAN_PROCESS_${userId}`);
    gotoWithOptions({
      url: `/wallet-loan-web/pages/loan/result?applyCode=${loanStorage.applyStatus}&applyMessage=${loanStorage.applyMessage || ""}`,
      allowBack: false,
    });
    return;
  }

  const { steps, applyNo } = loanStorage;
  const firstStep = steps?.find((step) => {
    if (step === "FACE_CHECK") {
      const faceCacheValid = getStore(`U_LOAN_FACECACHE_${userId}`);
      return !(loanStorage[stepMap[step]] && faceCacheValid);
    }
    return !loanStorage[stepMap[step]];
  });
  let nextStep;
  if (firstStep && steps.indexOf(firstStep) + 1 < steps.length) {
    nextStep = steps[steps.indexOf(firstStep) + 1];
  }
  const isLive = nextStep && nextStep === "FACE_CHECK";
  if (firstStep === "FACE_CHECK") {
    const cpInfo = await getCurrentCpInfo();
    setCp(cpInfo?.supplierId || 0);
    const res = await request("/loan/api/user/v2/getUserRealNameInfo", {});
    let fullName = "***";
    let faceSdk = typeof res?.data?.faceSdk === "number" ? res.data.faceSdk : 1;
    if (res.data?.status === 1) {
      fullName = res.data.realName;
      updateStoreUnit(
        `U_LOAN_PROCESS_${userId}`,
        { realInfo: await encrypt(JSON.stringify(res.data)) },
        4,
        "D",
      );
    } else {
      console.error("缺乏用户实名信息（真实姓名和身份证号），无法进行人脸认证。");
      loanNextCallBack();
      showConfirmDialog({
        message: "缺乏用户实名信息（真实姓名和身份证号），无法进行人脸认证。",
        showCancelButton: false,
        confirmButtonText: "知道了",
      }).then((val) => {
        if (val === "confirm") {
          goto("/wallet-loan-web/pages/index", false, true);
          return true;
        }
        return false;
      });
      return;
    }
    if (sdkVersionCode >= 90001000 && sdkVersionCode < 90006000) {
      faceSdk = 0;
      report("wallet_page_click", {
        click_name: "h5_live_detection_start",
        scenes: 2,
        faceSdk,
      });
    } else if (sdkVersionCode >= 90006000) {
      report("wallet_page_click", {
        click_name: "h5_live_detection_start",
        scenes: 2,
        faceSdk,
      });
    }
    startTime = new Date().getTime();
    // 活体认证接口
    const config = await getDialogConfig("/wallet-loan-web/pages/loan/detection");
    const retentionPicurl = config?.picUrl;
    const faceInfo = await liveDetection({
      fullName,
      scenes: "2",
      faceSdk,
      retentionPicurl,
      reActive: determineReActiveStatus(),
      diversionMethod: cpInfo?.diversionMethod,
    });
    if (faceInfo) {
      loanStorage.faceInfo = true;
      updateStoreUnit(`U_LOAN_PROCESS_${userId}`, { faceInfo: true }, 4, "D");
      setWithExactExpire(`U_LOAN_FACECACHE_${userId}`, 1, "10M");
      await loanNext(userId);
    }
    return;
  }
  if (firstStep === "AGREEMENT") {
    goto(
      `/wallet-loan-web/pages/loan/sign?applyNo=${applyNo}&type=3`,
      false,
      false,
      false,
      false,
      isLive,
    );
  }
  if (firstStep === "AGREEMENT_P") {
    goto(
      `/wallet-loan-web/pages/loan/sign?applyNo=${applyNo}&type=4`,
      false,
      false,
      false,
      false,
      isLive,
    );
  }
  if (firstStep === "INFOS") {
    goto("/wallet-loan-web/pages/loan/infoform", false, false, false, false, isLive);
  }
  if (firstStep === "SMS") {
    goto("/wallet-loan-web/pages/loan/sms", false, false, false, false, isLive);
  }

  loanStorage = getStore(`U_LOAN_PROCESS_${userId}`) || {};
  const applyingStatus = getStore(`U_LOAN_PROCESS_APPLYING_${userId}`);
  if (!firstStep) {
    if (!loanStorage.isApplied && !applyingStatus) {
      setWithExactExpire(`U_LOAN_PROCESS_APPLYING_${userId}`, true, "8S");
      loanStorage.infos = loanStorage.infos || {};
      const { fileInfos = [] } = loanStorage.infos;
      const faceFiles = await getLiveImage();
      loanStorage.infos.fileInfos = [...fileInfos, ...(faceFiles?.fileInfos || [])];
      loanStorage.infos.relationInfos =
        loanStorage.infos.relationInfos &&
        loanStorage.infos.relationInfos.map((item) => ({
          ...item,
          relation: filterChar(item.relation),
        }));
      const resultLength = (faceFiles?.fileInfos || []).map((item) => {
        return {
          type: item?.type,
          length: item?.value?.length,
        };
      });
      report("wallet_page_result", { result_name: "img_result", result_length: resultLength });
      loanStorage.infos.marriage = formInfoMap[loanStorage.infos.marriage];
      loanStorage.infos.education = formInfoMap[loanStorage.infos.education];
      if (window.location.pathname.startsWith(urlMap.loan_sign)) {
        getCurrentStore().setLoading(true);
      }

      endTime = new Date().getTime();
      if (sdkVersionCode >= 90003000 && endTime - startTime > TIMEOUT_TIME) {
        await closeLiveGuidePage();
      }
      report("wallet_page_click", {
        click_name: "loan_order_apply",
      });
      const res = await request("/loan/api/loan/apply", {
        encryptedParams: { applyNo, ...loanStorage.infos },
      });
      if (res.code === 0) {
        if (res.data.status === 4) {
          report("wallet_page_view", { page_name: "loan_apply_rejected_page" });
          del(`U_LOAN_PROCESS_${userId}`);
          gotoWithOptions({
            url: `/wallet-loan-web/pages/loan/result?applyCode=${res.data.status}&applyMessage=${res.data.refuseMsg || ""}`,
            allowBack: false,
          });
        } else {
          const updateInfos = loanStorage.infos;
          if (updateInfos?.fileInfos) {
            delete updateInfos.fileInfos;
          }
          updateStoreUnit(
            `U_LOAN_PROCESS_${userId}`,
            { isApplied: true, infos: updateInfos },
            4,
            "D",
          );
          gotoWithOptions({
            url: `/wallet-loan-web/pages/loan/result?applyNo=${applyNo}`,
            allowBack: false,
          });
        }
      } else if (res.code === LOAN_APPLY_ERROR_CODE_MAP.LOAN_EXPIRED) {
        gotoWithOptions({
          url: "/wallet-loan-web/pages/loan/result?applyExpired=1",
          allowBack: false,
        });
      } else {
        if (sdkVersionCode >= 90003000) {
          await closeLiveGuidePage();
        }
        // 借款提交失败
        showToast({
          message: res.agwError ? "服务异常，请稍后重试" : res.message,
          position: "center",
        });
        if (window.nextLoadingStatus) {
          window.nextLoadingStatus(false);
        }
      }
      del(`U_LOAN_PROCESS_APPLYING_${userId}`);
      if (window.location.pathname.startsWith(urlMap.loan_sign)) {
        getCurrentStore().setLoading(true);
      }
      if (window.location.pathname.startsWith(urlMap.loan_sms)) {
        getCurrentStore().setApply(false);
      }
      setTimeout(async () => {
        await request("/loan/api/user/operLog", {
          orderNo: applyNo,
          operType: 205,
          operResult: res.code === 0 ? 1 : 2,
          supplier: res.supplier || 1,
          operTime: new Date().getTime(),
        });
      }, 0);
    } else if (!applyingStatus) {
      console.log("用信订单重复性提交，跳过处理。");
    } else {
      gotoWithOptions({
        url: `/wallet-loan-web/pages/loan/result?applyNo=${applyNo}`,
        allowBack: false,
      });
    }
  }
}

export async function creditNext(userId, data = {}, creditNextCallBack = () => {}) {
  const creditStore = getStore(`U_CREDIT_PROCESS_V2_${userId}`) || {};
  const sdkVersionCode = getCurrentStore()?.param?.sdkVersionCode;
  if (!creditStore.realInfo) {
    goto("/wallet-loan-web/pages/credit/realname");
    return true;
  }
  const verifyList = creditStore.verifyList || [];
  if (verifyList.length === 0) {
    goto("/wallet-loan-web/pages/credit/realname?fromPage=creditResult");
    return false;
  }

  const firstVerify = verifyList?.find((verify) => {
    if (verify === "FACE_CHECK") {
      const faceCacheValid = getStore(`U_CREDIT_FACECACHE_${userId}`);
      return !(creditStore[verify] && faceCacheValid);
    }
    return !creditStore[verify];
  });

  let nextVerify;
  if (verifyList.indexOf(firstVerify) + 1 < verifyList.length) {
    nextVerify = verifyList[verifyList.indexOf(firstVerify) + 1];
  }
  const isLive = nextVerify && nextVerify === "FACE_CHECK";

  if (firstVerify === "FACE_CHECK") {
    const cpInfo = await getCurrentCpInfo();
    setCp(cpInfo?.supplierId || 0);
    const realNameInfo = await request("/loan/api/user/v2/getUserRealNameInfo", {});
    let fullName = "***";
    let faceSdk = typeof realNameInfo?.data?.faceSdk === "number" ? realNameInfo.data.faceSdk : 1;

    if (realNameInfo?.data?.status === 1) {
      fullName = realNameInfo.data.realName;
      creditStore.realInfo = await encrypt(JSON.stringify(realNameInfo.data));
      setWithExactExpire(`U_CREDIT_PROCESS_V2_${userId}`, creditStore, "7D");
    } else {
      delete creditStore.realInfo;
      setWithExactExpire(`U_CREDIT_PROCESS_V2_${userId}`, creditStore, "7D");
      toastMessage("账号未实名，请重新实名认证。");
      goto("/wallet-loan-web/pages/credit/realname?fromPage=creditResult");
      return true;
    }
    if (window.nextLoadingStatus) {
      setTimeout(() => {
        window.nextLoadingStatus(false);
      }, 3000);
    }
    if (sdkVersionCode >= 90001000 && sdkVersionCode < 90006000) {
      faceSdk = 0;
      report("wallet_page_click", {
        click_name: "h5_live_detection_start",
        scenes: 1,
        faceSdk,
      });
    } else if (sdkVersionCode >= 90006000) {
      report("wallet_page_click", {
        click_name: "h5_live_detection_start",
        scenes: 1,
        faceSdk,
      });
    }

    startTime = new Date().getTime();
    // 活体认证接口(scenes="1"表示授信过程)
    const config = await getDialogConfig("/wallet-loan-web/pages/credit/detection");
    const retentionPicurl = config?.picUrl;
    const faceInfo = await liveDetection({
      fullName,
      scenes: "1",
      faceSdk,
      retentionPicurl,
      reActive: determineReActiveStatus(),
      diversionMethod: cpInfo?.diversionMethod,
    });
    if (faceInfo) {
      creditStore.FACE_CHECK = true;
      setWithExactExpire(`U_CREDIT_PROCESS_V2_${userId}`, creditStore, "7D");
      setWithExactExpire(`U_CREDIT_FACECACHE_${userId}`, 1, "10M");
      const liveImage = await getLiveImage();
      await creditNext(
        userId,
        {
          faceInfo: liveImage,
        },
        creditNextCallBack,
      );
      return true;
    }
    toastMessage("人脸认证异常，请稍后重试。");
    return false;
  }

  if (firstVerify === "AGREEMENT_CREDIT") {
    if (data.fromPage === "creditResult") {
      goto(
        `/wallet-loan-web/pages/credit/sign?fromPage=${data.fromPage}`,
        false,
        false,
        false,
        false,
        isLive,
      );
    } else {
      goto("/wallet-loan-web/pages/credit/sign", false, false, false, false, isLive);
    }
    return true;
  }

  if (firstVerify === "CREDIT_PERSONAL_INFO") {
    if (data.fromPage === "creditResult") {
      goto(
        `/wallet-loan-web/pages/credit/infoform?fromPage=${data.fromPage}`,
        false,
        false,
        false,
        false,
        isLive,
      );
    } else {
      goto("/wallet-loan-web/pages/credit/infoform", false, false, false, false, isLive);
    }
    return true;
  }

  // 如果firstVerify为undefined，则申请授信
  if (!firstVerify) {
    try {
      if (sdkVersionCode < 90003000) {
        if (window.nextLoadingStatus) {
          window.nextLoadingStatus(true);
        }
      }
      endTime = new Date().getTime();
      if (sdkVersionCode >= 90003000 && endTime - startTime > TIMEOUT_TIME) {
        window.RETAIN_NEXT_LOADING_STATUS = true;
        await closeLiveGuidePage();
      }
      // creditStore.info 通过 await encrypt(JSON.stringify(info)) 加密得到的
      let info = await decrypt(creditStore.info || "{}");
      if (typeof info === "string") {
        info = JSON.parse(info);
      }
      const iData = data;
      // 传参中是否存在faceInfo
      if (!iData.faceInfo) {
        iData.faceInfo = await getLiveImage();
      }
      setTimeout(() => {
        report("wallet_page_click", {
          click_name: "credit_info_submit",
          info: { encryptedParams: { ...info, ...iData.faceInfo } },
        });
      }, 0);
      // 调用授信申请接口
      const { reActive = 0 } = creditStore;
      const res = await request("/loan/api/credit/apply", {
        encryptedParams: { ...info, ...iData.faceInfo, reActive },
      });

      if (res.code === 0) {
        // 接口返回正常
        if (res.data?.applyStatus === 2) {
          // 授信申请通过
          del(`U_CREDIT_PROCESS_V2_${userId}`);
          goto(
            `/wallet-loan-web/pages/credit/result?applyNo=${res.data?.applyNo || "OLD_CUSTMER"}
            &message=${res.data?.remainLimit}&apr=${res.data?.apr || ""}&dayRate=${res.data?.dayRate || ""}&reActive=${reActive}`,
          );
        } else if (res.data?.applyStatus === 3) {
          // 授信申请拒绝
          del(`U_CREDIT_PROCESS_V2_${userId}`);
          goto(
            `/wallet-loan-web/pages/credit/result?applyNo=${res.data?.applyNo}&message=${res.data?.refuseMsg}&reActive=${reActive}`,
          );
        } else if (res.data?.applyStatus === 1 && res.data?.supplier) {
          goto(
            `/wallet-loan-web/pages/credit/result?applyNo=${res.data?.applyNo}&applyStatus=${res.data?.applyStatus}&supplier=${res.data?.supplier}&reActive=${reActive}`,
          );
        } else {
          // 授信申请审核中
          goto(
            `/wallet-loan-web/pages/credit/result?applyNo=${res.data?.applyNo}&reActive=${reActive}`,
          );
        }
        creditNextCallBack();
        return true;
      }
      if (res.code !== 1201) {
        if (window.nextLoadingStatus) {
          window.nextLoadingStatus(false);
        }
        // 授信申请提交异常
        if (res.code !== 1401) toastMessage(res.message);
        // apply接口服务异常时关闭活体页
        if (sdkVersionCode >= 90003000) {
          await closeLiveGuidePage();
        }
        return false;
      }
    } catch (error) {
      return false;
    }
  }

  return true;
}

regNativeEvent("retentionPopup", () => {
  if (window.location.pathname.indexOf("/wallet-loan-web/pages/credit") === 0) {
    setStatusStore("/wallet-loan-web/pages/credit/detection");
  } else if (window.location.pathname.indexOf("/wallet-loan-web/pages/loan") === 0) {
    setStatusStore("/wallet-loan-web/pages/loan/detection");
  }
});
