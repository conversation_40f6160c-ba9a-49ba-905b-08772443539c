import Big from "big.js";

/*
 @param key 缓存键值
 @param key 缓存值
 不改变有效期，如果没有则返回false
*/
function setStore(key, value, lifeCycle = "session") {
  const storeProxy = lifeCycle === "local" ? localStorage : sessionStorage;
  if (typeof value === "string") {
    storeProxy.setItem(key, value);
  } else {
    storeProxy.setItem(key, JSON.stringify(value));
  }
  return true;
}

/*
封装store，基于原生store增加过期机制，Key规范未U_表示用户数据 P_产品数据
store有效期单位D 天 H 小时 S秒
*/
/*
 @param key 缓存键值
 @param key 缓存值
 @param until 到期的时间戳，长整型数
*/
const expireReg = /(\d+)(S|D|H|M)/;

/*
 @param key 缓存键值
 @param key 缓存值
 @param duration 有效期时长 数量 1 3
 @param unit 单位 H M D
*/
export function setWithExactExpireUnit(key, value, duration, unit) {
  let durationMillSec = 0;
  if (unit === "S") {
    durationMillSec = parseInt(new Big(duration).times(1000).toFixed(0), 10);
  } else if (unit === "M") {
    durationMillSec = parseInt(new Big(duration).times(60000).toFixed(0), 10);
  } else if (unit === "H") {
    durationMillSec = parseInt(new Big(duration).times(3600000).toFixed(0), 10);
  } else if (unit === "D") {
    durationMillSec = parseInt(new Big(duration).times(86400000).toFixed(0), 10);
  } else {
    return false;
  }
  const now = new Date().getTime();
  const expireTime = parseInt(new Big(now).plus(durationMillSec), 10);
  const storeData = {
    expireTime,
    value,
  };
  setStore(key, storeData, "local");
  return true;
}

export function setWithExactTimeStamp(key, value, until) {
  if (!until) {
    setStore(key, value, "session");
    return true;
  }
  if (!key) {
    return false;
  }
  const now = new Date().getTime();
  const minTime = parseInt(new Big(now).plus(600000).toFixed(0), 10);
  if (until < minTime) {
    // 10分钟内的不予保存
    return false;
  }
  const storeData = {
    expireTime: until,
    value,
  };
  setStore(key, storeData, "local");
  return true;
}

/*
 @param key 缓存键值
 @param key 缓存值
 @param expiration 有效期时长 1D 一天 2H 2小时
*/
export function setWithExactExpire(key, value, expiration = "") {
  if (!key) {
    return false;
  }
  if (!expiration) {
    setStore(key, value, "session");
    return true;
  }
  const matchResult = expiration.match(expireReg);
  if (!matchResult || matchResult.length !== 3) {
    return false;
  }
  return setWithExactExpireUnit(key, value, matchResult[1], matchResult[2]);
}

/*
 @param key 缓存键值
 如果过期或者不存在返回false
*/
export function getStore(key) {
  let val = sessionStorage.getItem(key);
  if (val) {
    try {
      return JSON.parse(val);
    } catch (e) {
      console.error(e);
      return val;
    }
  }
  val = localStorage.getItem(key);
  try {
    val = JSON.parse(val);
    const now = new Date().getTime();
    if (parseInt(val.expireTime, 10) < now) {
      return null;
    }
    return val.value;
  } catch (e) {
    return null;
  }
}

/*
 @param key 缓存键值
*/
export function del(key) {
  sessionStorage.removeItem(key);
  localStorage.removeItem(key);
}

export function updateStoreUnit(key, update, duration, unit) {
  const storageData = getStore(key) || {};
  const updatedData = { ...storageData, ...update };
  setWithExactExpireUnit(key, updatedData, duration, unit);
}

export function removeStoreKeysUnit(key, removeKeys, duration, unit) {
  const storageData = getStore(key) || {};
  removeKeys.forEach((removeKey) => {
    delete storageData[removeKey];
  });
  setWithExactExpireUnit(key, storageData, duration, unit);
}
