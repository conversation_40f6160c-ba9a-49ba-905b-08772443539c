import { goto } from "./native-bridge";

export function getHostByOrigin(origin) {
  if (origin === "https://wallet-web-drcn.cloud.honor.com") {
    return ".itsec";
  }
  return "-sit.test";
}

export async function agreement(url, type, contractName) {
  if (!url) {
    return;
  }
  if (url.includes(".pdf")) {
    goto(
      `/wallet-loan-web/pages/pdfView?contractUrl=${encodeURIComponent(
        url,
      )}&contractName=${contractName}`,
      false,
    );
  } else if (type === "forceDark") {
    goto(url, true, false, true);
  } else {
    goto(url, true);
  }
}

export function getThemeNameByMediaQuery(mediaQuery) {
  if (mediaQuery && mediaQuery.matches) {
    return "themeName=dark";
  }
  return "";
}
