let hnrJsSdk = null;

/**
 * 初始化 HNR JS SDK
 * @param {number} retryTimes - 尝试次数，默认为 3 次
 * @returns {Promise<Object>} 返回初始化后的 SDK 对象
 */
export async function initHnrJsSdk(retryTimes = 3) {
  // 如果 SDK 已经初始化，则直接返回
  if (hnrJsSdk) {
    return hnrJsSdk;
  }

  try {
    // 尝试导入 @hihonor/hnr-js-sdk 模块
    const module = await import("@hihonor/hnr-js-sdk");
    hnrJsSdk = module.default; // 假设默认导出的是 SDK 实例
    if (window.location.host !== "wallet-web-drcn.cloud.honor.com") {
      hnrJsSdk.setDebugMode(true);
    }
    return hnrJsSdk;
  } catch (error) {
    console.error("Failed to load HNR JS SDK:", error);

    // 如果剩余尝试次数为 0，则抛出错误
    if (retryTimes <= 1) {
      throw new Error(
        `Failed to initialize HNR JS SDK after ${3 - retryTimes + 1} attempts: ${error.message}`,
      );
    }

    // 否则递归调用自身并减少尝试次数
    return initHnrJsSdk(retryTimes - 1);
  }
}

export function isInLoanApp() {
  if (typeof navigator === "undefined") {
    return false;
  }
  return /hiHonor-wallet$/i.test(navigator.userAgent);
}
