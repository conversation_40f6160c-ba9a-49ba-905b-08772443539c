export default async function getPageComponentByUrl(url) {
  let ret;
  switch (url) {
    case "/index":
      ret = (
        await Promise.all([
          import("../pages/entrance/index.vue"),
          import("../pages/entrance/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/entrance/index.vue,/pages/component/SuggestProductCard.vue");
      break;
    case "/credit/realname":
      ret = (
        await Promise.all([
          import("../pages/credit/realname/index.vue"),
          import("../pages/credit/realname/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/credit/realname/index.vue");
      break;
    case "/credit/sign":
      ret = (
        await Promise.all([
          import("../pages/credit/sign/index.vue"),
          import("../pages/credit/sign/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/credit/sign/index.vue");
      break;
    case "/credit/result":
      ret = (
        await Promise.all([
          import("../pages/credit/result/index.vue"),
          import("../pages/credit/result/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/credit/result/index.vue");
      break;
    case "/loan/calc":
      ret = (
        await Promise.all([
          import("../pages/loan/calc/index.vue"),
          import("../pages/loan/calc/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/loan/calc/index.vue");
      break;
    case "/loan/sign":
      ret = (
        await Promise.all([
          import("../pages/loan/sign/index.vue"),
          import("../pages/loan/sign/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/loan/sign/index.vue");
      break;
    case "/loan/infoform":
      ret = (
        await Promise.all([
          import("../pages/loan/infoform/index.vue"),
          import("../pages/loan/infoform/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/loan/infoform/index.vue");
      break;
    case "/loan/result":
      ret = (
        await Promise.all([
          import("../pages/loan/result/index.vue"),
          import("../pages/loan/result/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/loan/result/index.vue");
      break;
    case "/my/autheration":
      ret = (
        await Promise.all([
          import("../pages/my/autheration/index.vue"),
          import("../pages/my/autheration/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/autheration/index.vue");
      break;
    case "/my/clearoff":
      ret = (
        await Promise.all([
          import("../pages/my/clearoff/index.vue"),
          import("../pages/my/clearoff/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/clearoff/index.vue");
      break;
    case "/my/clearHistory":
      ret = (
        await Promise.all([
          import("../pages/my/clearHistory/index.vue"),
          import("../pages/my/clearHistory/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/clearHistory/index.vue");
      break;
    case "/my/desktop":
      ret = (
        await Promise.all([
          import("../pages/my/desktop/index.vue"),
          import("../pages/my/desktop/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/desktop/index.vue");
      break;
    case "/my/loanprocess":
      ret = (
        await Promise.all([
          import("../pages/my/loanprocess/index.vue"),
          import("../pages/my/loanprocess/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/loanprocess/index.vue");
      break;
    case "/my/logoff":
      ret = (
        await Promise.all([
          import("../pages/my/logoff/index.vue"),
          import("../pages/my/logoff/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/logoff/index.vue");
      break;
    case "/my/couponlist":
      ret = (
        await Promise.all([
          import("../pages/my/couponlist/index.vue"),
          import("../pages/my/couponlist/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/couponlist/index.vue");
      break;
    case "/my/expand":
      ret = (
        await Promise.all([
          import("../pages/my/expand/index.vue"),
          import("../pages/my/expand/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/expand/index.vue");
      break;
    case "/my/home":
      ret = (
        await Promise.all([import("../pages/my/home/<USER>"), import("../pages/my/home/<USER>")])
      )
        .map((module) => module.default)
        .concat("/pages/my/privacymgr/index.vue");
      break;
    case "/my/privacymgr":
      ret = (
        await Promise.all([
          import("../pages/my/privacymgr/index.vue"),
          import("../pages/my/privacymgr/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/home/<USER>");
      break;
    case "/my/getCoupon":
      ret = (
        await Promise.all([
          import("../pages/my/getCoupon/index.vue"),
          import("../pages/my/getCoupon/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/getCoupon/index.vue");
      break;
    case "/my/modifyPhone":
      ret = (
        await Promise.all([
          import("../pages/my/modifyPhone/index.vue"),
          import("../pages/my/modifyPhone/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/my/modifyPhone/index.vue");
      break;
    case "/pdfView":
      ret = (
        await Promise.all([import("../pages/pdfView/index.vue"), import("../pages/pdfView/store")])
      )
        .map((module) => module.default)
        .concat("/pages/pdfView/index.vue");
      break;
    case "/pdfView2Frame":
      ret = (
        await Promise.all([
          import("../pages/pdfView2Frame/index.vue"),
          import("../pages/pdfView2Frame/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/pdfView2Frame/index.vue");
      break;
    case "/example":
      ret = (
        await Promise.all([import("../pages/example/index.vue"), import("../pages/example/store")])
      )
        .map((module) => module.default)
        .concat("/pages/example/index.vue");
      break;
    case "/loan/sms":
      ret = (
        await Promise.all([
          import("../pages/loan/sms/index.vue"),
          import("../pages/loan/sms/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/loan/sms/index.vue");
      break;
    case "/repayment/list":
      ret = (
        await Promise.all([
          import("../pages/repayment/list/index.vue"),
          import("../pages/repayment/list/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/list/index.vue");
      break;
    case "/repayment/trial":
      ret = (
        await Promise.all([
          import("../pages/repayment/trial/index.vue"),
          import("../pages/repayment/trial/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/trial/index.vue");
      break;
    case "/repayment/settle":
      ret = (
        await Promise.all([
          import("../pages/repayment/settle/index.vue"),
          import("../pages/repayment/settle/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/settle/index.vue");
      break;
    case "/repayment/sms":
      ret = (
        await Promise.all([
          import("../pages/repayment/sms/index.vue"),
          import("../pages/repayment/sms/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/sms/index.vue");
      break;
    case "/repayment/apply":
      ret = (
        await Promise.all([
          import("../pages/repayment/apply/index.vue"),
          import("../pages/repayment/apply/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/apply/index.vue");
      break;
    case "/repayment/detail":
      ret = (
        await Promise.all([
          import("../pages/repayment/detail/index.vue"),
          import("../pages/repayment/detail/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/detail/index.vue");
      break;
    case "/repayment/record":
      ret = (
        await Promise.all([
          import("../pages/repayment/record/index.vue"),
          import("../pages/repayment/record/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/repayment/record/index.vue");
      break;
    case "/hyperPolymerization":
      ret = (
        await Promise.all([
          import("../pages/hyperPolymerization/index.vue"),
          import("../pages/hyperPolymerization/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/hyperPolymerization/index.vue");
      break;
    case "/transfer":
      ret = (
        await Promise.all([
          import("../pages/transfer/index.vue"),
          import("../pages/transfer/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/transfer/index.vue");
      break;
    default:
      ret = (
        await Promise.all([
          import("../pages/notfound/index.vue"),
          import("../pages/notfound/store"),
        ])
      )
        .map((module) => module.default)
        .concat("/pages/notfound/index.vue");
  }
  return ret;
}
