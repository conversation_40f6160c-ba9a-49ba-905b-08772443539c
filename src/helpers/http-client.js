import axios from "axios";
import { DEFAULT_TIMEOUT } from "../main";
import { getCurrentCpInfo, delCurrentCpInfo } from "./configUtils";
import { getStore, updateStoreUnit } from "./storage";
import { getCurrentStore } from "./constants";

const apibase = "/wallet-loan-web/api";

const instance = axios.create({
  baseURL: apibase,
  timeout: DEFAULT_TIMEOUT,
});

const requestHandler = async (config) => {
  let reqConfig = config;
  if (!reqConfig) {
    reqConfig = {};
  }
  if (reqConfig.url === "/loan/api/user/operLog") {
    const cpInfo = await getCurrentCpInfo();
    reqConfig.data.supplier = cpInfo?.supplierId || 0;
  }
  if (reqConfig?.mock) {
    if (!reqConfig.headers) {
      reqConfig.headers = {};
    }
    reqConfig.headers["x-mock"] = 1;
  }
  if (!reqConfig.headers) {
    reqConfig.headers = {};
  }
  reqConfig.headers.startTime = new Date().getTime();
  // 调用bff/loanVerify接口埋入请求时间戳
  if (reqConfig.url === "/loan/api/bff/loanVerify") {
    const cpInfo = await getCurrentCpInfo();
    reqConfig.data = {
      ...reqConfig.data,
      trialValidTime: cpInfo.trialValidTime,
      trialTime: getStore(`U_LOAN_PROCESS_${getCurrentStore().param.userId}`).trialTime,
    };
  }
};

const responseHandler = (response) => {
  const { config = {}, headers = {}, data = {} } = response;
  // 下述接口返回的code为0时，清除当前cp信息缓存
  const delCurrentCpInfoUrls = [
    "/loan/api/user/v2/bind",
    "/loan/api/bff/rebind",
    "/loan/api/user/v2/logoff",
  ];
  if (delCurrentCpInfoUrls.includes(config.url) && data?.code === 0) {
    delCurrentCpInfo();
  }

  // 试算接口调用(请求参数无applyNo)埋入请求时间戳
  if (config.url === "/loan/api/loan/trial" && !JSON.parse(config.data).applyNo) {
    updateStoreUnit(
      `U_LOAN_PROCESS_${getCurrentStore().param.userId}`,
      {
        trialTime: new Date(headers.date).getTime(),
      },
      "24",
      "H",
    );
  }
};

instance.interceptors.request.use(
  async (config) => {
    await requestHandler(config);
    return config;
  },
  (error) =>
    // Do something with request error
    Promise.reject(error),
);

instance.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    // TODO Write to android
    responseHandler(response);
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    return Promise.reject(error);
  },
);

export default instance;
