/**
 * 判断一个值是否可以被视为一个有限的数字。
 * @param {*} value - 需要检查的值。
 * @returns {boolean} 如果值是或可以转换为一个有限数字，则返回 true；否则返回 false。
 */
export function isNumber(value) {
  if (typeof value === "number") {
    return Number.isFinite(value);
  }

  if (typeof value !== "string") {
    return false;
  }

  const str = value.trim();

  if (str === "") {
    return false;
  }

  const num = Number(str);

  return !Number.isNaN(num) && Number.isFinite(num);
}

// 金额格式化，例如 1000000 -> 10,000.00
export function formatCurrencyAmount(num) {
  return (num / 100).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}
