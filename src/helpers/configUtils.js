import { getStore, setWithExactExpire, del, updateStoreUnit } from "./storage";
import { getCurrentStore, STORE_VERSION_CODE, urlMap } from "./constants";
import { getClientConfig, getCpConfig } from "../api/config";
import { getAdActivityListV2 } from "../api/ad";

const popupConfigKeys = ["ACTIVITY_POPUP_CONFIG", "RETENTION_POPUP_CONFIG"];

/**
 * 获取当前用户关联的供应商的配置信息
 * @returns Dict or null
 */
export async function getCurrentCpInfo() {
  if (typeof window !== "undefined") {
    const userId = getCurrentStore()?.param?.userId;
    if (userId) {
      const cpInfo = getStore(`U_CPINFO_${STORE_VERSION_CODE}_${userId}`);
      if (cpInfo) {
        return cpInfo;
      }
    } else {
      return null;
    }
  }
  const res = await getCpConfig();
  if (typeof window !== "undefined") {
    const userId = getCurrentStore()?.param?.userId;
    if (res?.data) {
      setWithExactExpire(`U_CPINFO_${STORE_VERSION_CODE}_${userId}`, res?.data, "1H");
    }
  }
  return res?.data;
}

export function delCurrentCpInfo() {
  del(`U_CPINFO_${STORE_VERSION_CODE}_${getCurrentStore()?.param?.userId}`);
}

/**
 * 获取前端的通用配置信息
 * @returns Dict or null
 */
export async function getClientConfigInfo() {
  if (typeof window !== "undefined") {
    const userId = getCurrentStore()?.param?.userId;
    if (userId) {
      const clientConfigInfo = getStore(`U_CLIENT_CONFIG_INFO_${STORE_VERSION_CODE}_${userId}`);
      if (clientConfigInfo) {
        return clientConfigInfo;
      }
    } else {
      return null;
    }
  }
  const res = await getClientConfig();
  if (typeof window !== "undefined") {
    const userId = getCurrentStore()?.param?.userId;
    if (res?.data) {
      setWithExactExpire(`U_CLIENT_CONFIG_INFO_${STORE_VERSION_CODE}_${userId}`, res?.data, "1H");
    }
  }
  return res?.data;
}

/**
 * 读取出相关的弹窗配置素材
 * @param {Object.<string, AdSpaceDtoV2>} popupConfig
 */
function getRefAdActivities(popupConfig) {
  const refAdActivities = {};
  Object.values(popupConfig).forEach(
    /**
     * @param {AdSpaceDtoV2} resource 资源位信息
     */
    (resource) => {
      const { spaceType, carousel, popupPage, popupMoment, activityList } = resource;
      // 只处理弹窗类资源位
      if (spaceType === 3) {
        const popupPageIdentifierList = popupPage.split("|");
        // 分别处理不同弹窗触发时机
        // 1-页面打开 2-页面关闭
        if (popupMoment === 1) {
          popupPageIdentifierList.forEach(
            /**
             * @param {string} popupPageIdentifier 弹窗页面标识符
             */
            (popupPageIdentifier) => {
              if (
                popupPageIdentifier &&
                Object.prototype.hasOwnProperty.call(urlMap, popupPageIdentifier)
              ) {
                const pageOpenRefAdActivities = refAdActivities.page_open || {};
                pageOpenRefAdActivities[popupPageIdentifier] = {
                  pageUrl: urlMap[popupPageIdentifier],
                  carousel,
                  popupList: (activityList || []).reduce((currentPopupList, activity) => {
                    const curentPopup = {
                      popupId: `popupId_${activity.id}`,
                      picUrl: activity.picUrl,
                      targetUrl: activity.targetUrl,
                      activityName: activity.activityName,
                      policyId: activity.abTestPolicy?.expConfCode,
                    };
                    if (activity.popupTriggers === 1) {
                      curentPopup.popupTriggers = activity.popupTriggers;
                      curentPopup.maxTriggersNum = 1;
                      curentPopup.triggersInterval = 0;
                    } else if (activity.popupTriggers === 2) {
                      curentPopup.popupTriggers = activity.popupTriggers;
                      curentPopup.maxTriggersNum = activity.maxTriggersNum;
                      curentPopup.triggersInterval = activity.triggersInterval;
                    }
                    currentPopupList.push(curentPopup);
                    const pic = new Image();
                    pic.src = activity.picUrl;
                    return currentPopupList;
                  }, []),
                };
                refAdActivities.page_open = pageOpenRefAdActivities;
              }
            },
          );
        } else if (popupMoment === 2) {
          popupPageIdentifierList.forEach(
            /**
             * @param {string} popupPageIdentifier 弹窗页面标识符
             */
            (popupPageIdentifier) => {
              if (
                popupPageIdentifier &&
                Object.prototype.hasOwnProperty.call(urlMap, popupPageIdentifier)
              ) {
                if (Array.isArray(activityList) && activityList.length > 0) {
                  const pageExitRefAdActivities = refAdActivities.page_exit || [];
                  pageExitRefAdActivities.push({
                    pageUrl: urlMap[popupPageIdentifier],
                    activityName: activityList[0].activityName,
                    picUrl: activityList[0].picUrl,
                    policyId: activityList[0].abTestPolicy?.expConfCode,
                    ...(popupPageIdentifier === "credit_realname_activity" && {
                      paramName: "activityCode",
                    }),
                  });
                  const pic = new Image();
                  pic.src = activityList[0].picUrl;
                  refAdActivities.page_exit = pageExitRefAdActivities;
                }
              }
            },
          );
        }
      }
    },
  );
  return refAdActivities;
}

/**
 * 缓存弹窗配置信息
 * @param {BaseResponse<Object.<string, AdSpaceDtoV2>>} adSpace
 */
export function storePopupConfigList(adSpace) {
  let popupConfig;
  if (adSpace?.code === 0) {
    popupConfig = adSpace.data;
  } else if (adSpace?.agwError === 1 && adSpace.agwErrorCode === 404) {
    popupConfig = "Unavailable";
  }
  if (!popupConfig) {
    return;
  }
  if (typeof window !== "undefined") {
    let activityPopupConfigList;
    let retentionPopupConfigList;
    if (popupConfig === "Unavailable") {
      activityPopupConfigList = "Unavailable";
      retentionPopupConfigList = "Unavailable";
    } else if (typeof popupConfig === "object") {
      // 转化接口返回
      const refAdActivities = getRefAdActivities(popupConfig);
      activityPopupConfigList = refAdActivities.page_open;
      retentionPopupConfigList = refAdActivities.page_exit;
    }
    setWithExactExpire(
      `U_ACTIVITY_POPUP_CONFIG_${STORE_VERSION_CODE}`,
      activityPopupConfigList,
      "1H",
    );
    setWithExactExpire(
      `U_RETENTION_POPUP_CONFIG_${STORE_VERSION_CODE}`,
      retentionPopupConfigList,
      "1H",
    );
  }
}

/**
 * 获取弹窗配置信息
 * @returns {Promise<{}>}
 */
export async function getPopupConfigList() {
  if (typeof window !== "undefined") {
    const popupConfig = {};
    const isEmpty = ["ACTIVITY_POPUP_CONFIG"].find((popupConfigKey) => {
      const popupConfigList = getStore(`U_${popupConfigKey}_${STORE_VERSION_CODE}`);
      return !popupConfigList;
    });
    if (isEmpty) {
      const adSpace = await getAdActivityListV2();
      storePopupConfigList(adSpace);
    }
    popupConfigKeys.forEach((popupConfigKey) => {
      const popupConfigList = getStore(`U_${popupConfigKey}_${STORE_VERSION_CODE}`);
      if (popupConfigList) {
        if (popupConfigList === "Unavailable") {
          popupConfig[popupConfigKey] = null;
        } else {
          popupConfig[popupConfigKey] = popupConfigList;
        }
      }
    });
    return popupConfig;
  }
  return {};
}

/**
 * 获取落地弹窗配置信息
 * @returns {Promise<{}>}
 */
export async function getActivityPopupConfigList() {
  return getPopupConfigList().then((res) => res.ACTIVITY_POPUP_CONFIG);
}

/**
 * 获取挽留弹窗配置信息
 * @returns {Promise<{}>}
 */
export async function getRetentionPopupConfigList() {
  return getPopupConfigList().then((res) => res.RETENTION_POPUP_CONFIG);
}

/**
 * 删除弹窗配置信息
 */
export function delPopupConfigList() {
  popupConfigKeys.forEach((popupConfigKey) => {
    del(`U_${popupConfigKey}_${STORE_VERSION_CODE}`);
  });
}

/**
 * 删除挽留弹窗弹窗状态缓存
 */
export function delRetentionStatusStore() {
  const userId = getCurrentStore()?.param?.userId;
  del(`U_RETENTION_STATUS_${userId}`);
}

/**
 * 判断用户reActive状态
 * @returns {number} reActive状态值(0或1)
 */
export function determineReActiveStatus() {
  const { pathname } = window.location;
  const store = getCurrentStore() || {};
  const userId = store?.param?.userId;
  let reActive = 0;
  if (pathname === "/wallet-loan-web/pages/index") {
    const { creditInfo = {} } = store;
    if (creditInfo.status === 3 && creditInfo.cancelStatus === 0) {
      reActive = 1;
    } else {
      reActive = creditInfo.reActiveStatus === 2 ? 1 : 0;
    }
  } else if (pathname.indexOf("/wallet-loan-web/pages/credit") === 0) {
    const creditProcess = getStore(`U_CREDIT_PROCESS_V2_${userId}`) || {};
    reActive = creditProcess.reActive || 0;
    if (pathname === urlMap.credit_result) {
      reActive = parseInt(store.param.reActive || 0, 10);
    }
  } else if (pathname.indexOf("/wallet-loan-web/pages/loan") === 0) {
    if (pathname === "/wallet-loan-web/pages/loan/calc") {
      const { creditInfo = {} } = store;
      reActive = creditInfo.reActiveStatus === 2 ? 1 : 0;
      if (!/[0-9]{19,}/.test(userId)) {
        updateStoreUnit(`U_LOAN_PROCESS_${userId}`, { reActive }, 4, "D");
      }
    } else {
      const loanProcess = getStore(`U_LOAN_PROCESS_${userId}`) || {};
      reActive = loanProcess.reActive;
    }
  }
  return reActive;
}

export async function processEvent(event = {}) {
  const ret = event;
  try {
    const cpInfo = await getCurrentCpInfo();
    ret.diversionMethod = cpInfo?.diversionMethod;
    ret.reActive = determineReActiveStatus();
    return ret;
  } catch (e) {
    console.error(e);
    return ret;
  }
}
