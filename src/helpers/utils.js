import forge from "node-forge";
import parser from "ua-parser-js";
import Big from "big.js";
import { computed, getCurrentInstance } from "vue";
import getMockData from "../../mockUtils";
import { setNetwork } from "./network-helper";
import { del, getStore, setWithExactExpire } from "./storage";
import { getAxiosInstance, getCurrentStore, supplierIdMap } from "./constants";
import { initHnrJsSdk, isInLoanApp } from "./hnr-helper";

export function initStore(useStore) {
  const { proxy } = getCurrentInstance();
  const store = useStore(proxy.$pinia);
  const data = computed(() => store.$state);
  return { store, data };
}

export function generateRandom(type, length) {
  // 将字节序列转换为十六进制字符串
  const hexString = forge.util.bytesToHex(forge.random.getBytesSync(Math.ceil(length / 2)));
  if (type === 0) {
    // 将十六进制字符串转换为数字字符串
    return hexString
      .split("")
      .map((char) => parseInt(char, 16))
      .join("")
      .substring(0, length);
  }
  return hexString;
}

export function sha256Hex(data) {
  return forge.util.bytesToHex(forge.md.sha256.create().update(data, "utf8").digest());
}

export function sha1Hex(data) {
  return forge.util.bytesToHex(forge.md.sha1.create().update(data, "utf8").digest());
}

function getHideStr(needHideStr, startSize, endSize, length) {
  if (needHideStr?.length >= 500) {
    return "ommited...";
  }
  const hideRegBuffer = ["([\\s\\S]{"];
  const replaceSb = ["$1"];
  const mark = length - startSize - endSize;
  for (let i = 0; i < mark; i += 1) {
    replaceSb.push("*");
  }
  hideRegBuffer.push(startSize, "})[\\s\\S]*([\\s\\S]{", endSize, "})");
  replaceSb.push("$2");
  return needHideStr.replace(new RegExp(hideRegBuffer.join(""), "gi"), replaceSb.join(""));
}

export function hideMarkStr(needHideMark, keys, filterKeys, keyRegMap) {
  if (needHideMark) {
    if (keys && keyRegMap) {
      const key = keys.find((item) => needHideMark.toLowerCase().includes(item));
      const filterKey = filterKeys.find((item) => needHideMark.toLowerCase().includes(item));
      if (filterKey) {
        return "";
      }
      if (key) {
        return needHideMark.replace(new RegExp(keyRegMap[key], "gi"), (str) => hideMarkStr(str));
      }
    }
    let startSize;
    let endSize;
    const { length } = needHideMark;
    const isUrl = needHideMark.toLowerCase().indexOf("http") > -1;

    if (!isUrl) {
      if (length > 4) {
        const i = Math.floor(length / 3);
        startSize = i;
        endSize = i;
      } else {
        startSize = 1;
        endSize = 0;
      }
    } else {
      startSize = 5;
      endSize = 3;
    }
    return getHideStr(needHideMark, startSize, endSize, length);
  }
  return "";
}
function getRealIP(req) {
  // 获取客户端ip地址
  return (
    req.headers["x-forwarded-for"] ||
    req.ip ||
    req.connection?.remoteAddress ||
    req.socket?.remoteAddress ||
    req.connection?.socket?.remoteAddress ||
    ""
  );
}
export function buildContextStore(req) {
  const agentInfo = parser(req.headers["user-agent"]);
  const deviceInfo = JSON.parse(req.cookies.wallet_deviceInfo || "{}");
  const userInfo = JSON.parse(req.cookies.wallet_userInfo || "{}");
  const sdkVersionCode = parseInt(req.cookies.sdkVersionCode, 10);
  const xoaid = req.cookies["x-oaid"];
  const { mockFlag } = req.cookies;
  const logContext = {
    version: "V1.0",
    timestamp: new Date().toISOString(),
    type: "svr",
    traceId: `${generateRandom(1, 8)}-${generateRandom(0, 4)}-${generateRandom(0, 4)}-${generateRandom(
      0,
      4,
    )}-${generateRandom(1, 12)}`,
    userId: hideMarkStr(userInfo.userId),
    deviceId: hideMarkStr(userInfo.deviceId),
    deviceModel: deviceInfo.deviceModel,
    versionCode: deviceInfo.versionCode,
    sourceId: getRealIP(req),
    serviceName: "wallet-loan-web",
  };
  if (req.originalUrl.indexOf("?") > -1) {
    logContext.interfaceName = req.originalUrl.substring(0, req.originalUrl.indexOf("?"));
  } else {
    logContext.interfaceName = req.originalUrl;
  }
  return {
    logContext,
    agentInfo,
    userInfo,
    deviceInfo,
    sdkVersionCode,
    "x-oaid": xoaid,
    mockFlag,
  };
}

export function gcmEncrypt(data) {
  const key = forge.random.getBytesSync(32);
  const cipher = forge.cipher.createCipher("AES-GCM", key);
  const iv = forge.random.getBytesSync(16);
  cipher.start({
    iv,
  });
  cipher.update(forge.util.createBuffer(data, "utf8"));
  cipher.finish();
  const ret = cipher.output.data + cipher.mode.tag.data;
  return [ret, key, iv].map((item) => forge.util.bytesToHex(item));
}

const rsaPubKey = forge.pki.publicKeyFromPem(
  // eslint-disable-next-line max-len
  "-----BEGIN PUBLIC KEY-----\nMIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAgTkR114G/DgR9aKApsa5oql9V8euSWxKvS6FOm2qrfRDedSE53/5OkD8UtW0imXJ79M0OjFzBJ/vbWQNotSi2QXYGejoTuOuHUs4lVVhRe41IIWronQQ4ImuRn40M+IGwKhAqGDGuSUZUzzt8oniOKalbvhieNoEH+k69ZC/l842ayIgtvHKgwtFa0BMPAQCfsGfdhSwVfFJ+YwMzzPAQfDFgjKLhf7A0LtnncxCcCCb4J9Y5GXfpduy2WdcI8xxPI3Mt6v8H2NEwvWWMFg+laNAcysuHqQNuW6r+Wu2e1I91rvXGm3vDqka6ynOG3LrNcofbk4zmcDL8p4BM+2UWYcEZi0y1oZB8JiOL38LBOyzrMQ0hrZlmbGP1nY3OjnhcXvdRSUn1yFhyDwcq3oTcesnWqVefyejz+QcZYazqeNerws2mnotXPogvxuae35BqGJdtoEH06Q+pniKpejKW0tqMMLsPInnrPLEJaZEGGXTogM+FI4GVeAqvIS+1lBvAgMBAAE=\n-----END PUBLIC KEY-----",
);
export function rasEncrypt(data) {
  return forge.util.encode64(
    rsaPubKey.encrypt(data, "RSA-OAEP", {
      md: forge.md.sha256.create(),
      mgf1: {
        md: forge.md.sha256.create(),
      },
    }),
  );
}

const sendVerifyCodeObj = {
  url: "/loan/api/sms/v2/sendVerifyCode",
  codeArr: [173402, 173403, 173404, 170009],
};
const verifyCodeObj = {
  url: "/loan/api/sms/v2/verifyCode",
  codeArr: [170002, 170009],
};
const logoffObj = {
  url: "/loan/api/user/v2/logoff",
  codeArr: ["170002", "173701", "173702", "173703", "170009"],
};
const anomalousCode = [500, 501, 502, 503, 504, 505];

export async function request(url, data = {}, options = {}, method = "POST") {
  const httpClient = getAxiosInstance();
  if (typeof window === "undefined") {
    if (options?.mock && process.env.DEF_ENV_TYPE === "development") {
      const res = await getMockData(url);
      return res?.data;
    }
  } else if (!window?.navigator?.onLine) {
    if (options?.isHideOffline) {
      return null;
    }
    setNetwork();
    return null;
  }
  const config = {
    method,
    url,
    data,
    ...options,
  };
  const reqOpt = options;
  if (config?.data?.encryptedParams) {
    if (!reqOpt.headers) {
      reqOpt.headers = {};
    }
    if (!reqOpt.headers["x-wallet-key"]) {
      if (typeof config.data.encryptedParams !== "string") {
        config.data.encryptedParams = JSON.stringify(config.data.encryptedParams);
      }
      const [ret, key, iv] = gcmEncrypt(config.data.encryptedParams);
      config.data.encryptedParams = `${iv}:${ret}`;
      if (!config.headers) {
        config.headers = {};
      }
      config.headers["x-wallet-key"] = rasEncrypt(key);
      reqOpt.headers["x-wallet-key"] = config.headers["x-wallet-key"];
    }
  }
  try {
    const res = await httpClient.request(config);
    if (
      res.data?.code === 1201 &&
      typeof window !== "undefined" &&
      (!options.retryTimes || options.retryTimes < 2)
    ) {
      if (options.retryTimes) {
        reqOpt.retryTimes += 1;
      } else {
        reqOpt.retryTimes = 1;
      }
      if (isInLoanApp()) {
        const hnrJsSdk = await initHnrJsSdk();
        await hnrJsSdk.customApi("refreshToken");
      }
      return new Promise((resolve) => {
        setTimeout(async () => {
          const retryData = await request(url, data, options, method);
          resolve(retryData);
        }, 500);
      });
    }
    if (
      res.data?.message &&
      (/Request failed with status code \d+$/.test(res.data.message) ||
        res.data.message === "Network Error")
    ) {
      res.data.message = "无法连接服务器，请稍后重试！";
    }

    if (anomalousCode.includes(res.data?.code)) {
      res.data.message = "系统异常，请稍后重试";
    }

    if (url === sendVerifyCodeObj.url) {
      if (
        res.data.code === 0 &&
        res.data.data?.status !== 0 &&
        sendVerifyCodeObj.codeArr.includes(res.data.data?.status)
      ) {
        return { code: res.data.data?.status, message: res.data.data?.errorDesc };
      }
    }
    if (url === verifyCodeObj.url) {
      if (
        res.data.code === 0 &&
        res.data.data?.status !== 0 &&
        verifyCodeObj.codeArr.includes(res.data.data?.status)
      ) {
        return { code: res.data.data?.status, message: res.data.data?.errorDesc };
      }
    }
    if (url === logoffObj.url) {
      if (
        res.data.code === 0 &&
        !res.data.data?.logoffResult &&
        logoffObj.codeArr.includes(res.data.data?.logoffErrCode)
      ) {
        return { code: res.data.data?.logoffErrCode, message: res.data.data?.logoffErrDesc };
      }
    }

    return res.data;
  } catch (e) {
    const { code } = e;
    let { message } = e;
    let agwErrorCode;
    if (message) {
      const agwErrorCodeMatch = message.match(/Request failed with status code (\d+)$/);
      if (agwErrorCodeMatch) {
        // 如果匹配成功，提取状态码并赋值给 statusCode，且转化成number
        agwErrorCode = Number(agwErrorCodeMatch[1]);
      }
      if (agwErrorCodeMatch || message === "Network Error") {
        message = "无法连接服务器，请稍后重试！";
      }

      if (code === "ECONNABORTED") {
        message = "请求超时，请检查您的网络或稍后重试";
      }
    }
    return {
      code,
      message,
      status: e.status,
      agwError: 1,
      ...(agwErrorCode !== undefined && { agwErrorCode }),
    };
  }
}

/**
 *
 * @param {*} timestamp 时间戳
 * @param {*} unit      时间戳单位 —— 'S'-秒/'M'-分/'H'-时/'D'-天
 * @returns status —— 0-过期/1-秒/2-时分/3-月日/4-年月日
 *          interval —— 0-过期/1-100毫秒/2-秒/3-分/4-时/5-日
 */
export function getDiffTime(timestamp, localtimestamp, unit = "S") {
  const res = {
    status: 0, // 0-过期/1-秒/2-时分/3-月日/4-年月日
    interval: 0, // 0-过期/1-秒/2-分/3-时/4-日
    year: 0,
    month: 0,
    day: 0,
    hour: 0,
    minite: 0,
    second: 0,
  };
  let unifytimestamp = 0;
  let localunifytimestamp = 0;
  if (unit === "mS") {
    unifytimestamp = timestamp;
    localunifytimestamp = localtimestamp;
  } else if (unit === "S") {
    unifytimestamp = parseInt(new Big(timestamp).times(1000), 10);
    localunifytimestamp = parseInt(new Big(localtimestamp).times(1000), 10);
  } else if (unit === "M") {
    unifytimestamp = parseInt(new Big(timestamp).times(60000), 10);
    localunifytimestamp = parseInt(new Big(localtimestamp).times(60000), 10);
  } else if (unit === "H") {
    unifytimestamp = parseInt(new Big(timestamp).times(3600000), 10);
    localunifytimestamp = parseInt(new Big(localtimestamp).times(3600000), 10);
  } else if (unit === "D") {
    unifytimestamp = parseInt(new Big(timestamp).times(86400000), 10);
    localunifytimestamp = parseInt(new Big(localtimestamp).times(86400000), 10);
  } else {
    return res;
  }

  const localdeadline = new Date(localunifytimestamp);
  const deadline = new Date(unifytimestamp);
  const now = new Date();
  const diff = localdeadline - now;
  // 时间已过期
  if (diff <= 0) {
    return res;
  }
  // 计算差异时间
  const diffSeconds = Math.floor(diff / 1000);
  if (diffSeconds < 2) res.interval = 1;
  if (diffSeconds < 120 && diffSeconds >= 2) res.interval = 2;
  if (diffSeconds < 60) {
    res.status = 1;
    res.second = diffSeconds;
    return res;
  }
  const diffHours = Math.floor(diffSeconds / (60 * 60));
  const diffMinutes = Math.floor((diffSeconds / 60) % 60);
  if (diffHours < 25 && diffSeconds >= 120) res.interval = 3;
  if (diffHours < 24) {
    res.status = 2;
    res.hour = diffHours;
    res.minite = diffMinutes;
    return res;
  }
  // 超过24小时
  if (diffHours < 49 && diffHours >= 25) res.interval = 4;
  if (diffHours >= 49) res.interval = 5;
  const year = deadline.getFullYear();
  const month = deadline.getMonth() + 1; // 月份是从0开始的
  const day = deadline.getDate();
  const currentYear = new Date().getFullYear();
  res.month = month;
  res.day = day;
  if (year === currentYear) {
    // 当年内
    res.status = 3;
    return res;
  }
  // 非当年内
  res.status = 4;
  res.year = year;
  return res;
}

export function amountNumberFormat(amount, minimumFractionDigits = 2) {
  return new Intl.NumberFormat("zh-CN", { minimumFractionDigits }).format(amount);
}

export function filterLoanUsage(str) {
  switch (str) {
    case "个人日常消费":
      return "RCXF";
    case "房屋装修":
      return "ZX";
    case "旅游出行":
      return "LY";
    case "在职深造":
      return "JX";
    case "其他消费":
      return "Others";
    default:
      return "";
  }
}

export function upgradeCreditProcessStore(userId, processType) {
  const creditStore = getStore(`U_CREDIT_PROCESS_${userId}`) || {};
  const creditStoreV2 = getStore(`U_CREDIT_PROCESS_V2_${userId}`) || {};
  if (Object.keys(creditStoreV2).length !== 0) {
    return;
  }
  if (Object.keys(creditStore).length === 0) {
    return;
  }
  const verifyList = ["AGREEMENT_CREDIT", "CREDIT_PERSONAL_INFO", "FACE_CHECK"];
  setWithExactExpire(`U_CREDIT_FACECACHE_${userId}`, 1, "10M");

  creditStoreV2.realInfo = creditStore.realInfo;
  creditStoreV2.FACE_CHECK = creditStore.faceInfo;
  creditStoreV2.signInfo = creditStore.signInfo;
  creditStoreV2.realNameInfoNew = creditStore.realNameInfoNew;
  creditStoreV2.isSmsCode = creditStore.isSmsCode;
  creditStoreV2.isFromSign = creditStore.isFromSign;
  creditStoreV2.supplier = creditStore.supplierId;
  creditStoreV2.verifyList = verifyList;

  let found = false;
  verifyList.forEach((item) => {
    if (item === processType) {
      found = true;
    }
    if (!found) {
      creditStoreV2[item] = true;
    }
  });

  creditStoreV2.firstCp = creditStoreV2.firstCp || true;
  creditStoreV2.supplierName = creditStoreV2.supplierName || "合作机构";
  creditStoreV2.allRead = creditStoreV2.allRead || false;
  setWithExactExpire(`U_CREDIT_PROCESS_V2_${userId}`, creditStoreV2, "7D");
}

export function parseQueryStringToObj(url) {
  const regex = /(\?|&)([^#&=]+)=([^#&=]*)/g;
  let match = regex.exec(url);
  const params = {};
  while (match) {
    const key = decodeURIComponent(match[2].replace(/\+/g, " "));
    const value = decodeURIComponent(match[3].replace(/\+/g, " "));
    if (!Object.prototype.hasOwnProperty.call(params, key)) {
      params[key] = value;
    } else if (Array.isArray(params[key])) {
      params[key].push(value);
    } else {
      params[key] = [params[key], value];
    }
    match = regex.exec(url);
  }
  return params;
}

export function handleHtmlText(htmlText, fullDomain = "") {
  let message = htmlText;
  if (fullDomain) {
    message = message.replace(/<head>/i, `<head><base href="${fullDomain}">`);
  }
  const darkModeStyle = `<meta name="color-scheme" content="dark light">
    <style>span {color: var(--hnr-text-color-primary) !important;}</style>
    <style>ul * {color: var(--hnr-text-color-primary) !important;}</style>
    <style>body {background-color: var(--hnr-color-card-background) !important;}</style>
    <style>body * {background-color: var(--hnr-color-card-background) !important;}</style>
    <style>@media (prefers-color-scheme: dark){img {filter: invert(1) hue-rotate(180deg) !important;}}</style>`;
  // 在协议页，规避三方页面问题插入深色模式适配标签
  if (message?.includes("<head>")) {
    message = message.replace("<head>", `<head>${darkModeStyle}`);
  } else if (message?.includes("<html>")) {
    message = message.replace("<html>", `<html>${darkModeStyle}`);
  } else {
    message = `<meta name="color-scheme" content="dark light">${darkModeStyle}${message}`;
  }
  if (typeof message === "string") message = message.replace(/background:white/g, "");
  return message;
}

export async function fetchHtml(url, fullDomain = "") {
  let message = "";
  const response = await fetch(url);
  const buffer = await response.arrayBuffer();
  message = new TextDecoder("utf-8").decode(buffer);
  if (/charset=("{0,1})(gb2312|GBK)("{0,1})/.test(message)) {
    // 如果检测到 GB2312 编码，尝试使用 TextDecoder 解码
    try {
      const decoder = new TextDecoder("gbk"); // 使用 'gbk' 作为替代
      message = decoder.decode(buffer);
    } catch (error) {
      message = "Not Found";
    }
  }
  message = handleHtmlText(message, fullDomain);
  return message;
}

export function isPdfUrl(url) {
  return /\.pdf(\?.*)?$/i.test(url);
}

export function showPdfUrl(url) {
  return `${window.location.origin}/wallet-loan-web/pages/pdfView2Frame?contractUrl=${encodeURIComponent(url)}`;
}

export function showPdfUrlNew(url, name) {
  return `${window.location.origin}/wallet-loan-web/pages/pdfView?contractUrl=${encodeURIComponent(url)}&contractName=${name}`;
}

/**
 * 判断给定的URL是否指向PDF文件
 * 1. 先检查URL是否以.pdf结尾
 * 2. 如果不是，则发起HEAD请求检查Content-Type
 * @param {string} url - 要检查的URL
 * @returns {Promise<boolean>} 如果是PDF文件返回true，否则返回false
 */
export async function isPdfUrlAsync(url) {
  // 先进行快速的扩展名检查
  const pdfRegex = /\.pdf($|\?|#)/i;
  if (pdfRegex.test(url)) {
    return true;
  }

  try {
    // 发起HEAD请求检查响应头
    const response = await fetch(url, {
      method: "HEAD",
      mode: "cors", // 注意跨域限制
      cache: "no-store",
    });

    // 检查Content-Type
    const contentType = response.headers.get("Content-Type");
    return contentType && contentType.toLowerCase().includes("application/pdf");
  } catch (error) {
    return false; // 网络请求失败时默认返回false
  }
}

/**
 * 用户行为日志上报
 * @param {String} orderNo 订单号，授信和借款使用apply_no，还款使用借款的out_order_no
 * @param {Integer} operType 操作类型，101-实名准入页（含标准流程页面和运营投放页） 102-人脸确认页（授信流程） 103-人脸确认页（借款流程） 104-授信协议页 105-借款协议页 106-借款完善资料页 107-申请借款页 108-借款试算页
 * @param {Integer} eventType 事件类型，1-曝光，2-点击
 * @param {Integer} operResult 操作结果，1-成功，2-失败，3-取消
 * @param {Integer} supplier 服务商，1-度小满，5-京东，6-乐信
 * @param {Date} operTime 操作时间（端侧时间）
 * @returns
 */
export function userBehaviorLogReporting(
  orderNo,
  operType,
  eventType,
  operResult,
  supplier,
  operTime,
) {
  request(
    "/loan/api/user/realTimeOperReport",
    { orderNo, operType, eventType, operResult, supplier, operTime },
    { mock: false },
  );
}

export function storeMarketingTracking(marketingTracking) {
  const userId = getCurrentStore()?.param?.userId;
  // 对接营销中心自动化营销-落地页归因参数写入缓存
  if (marketingTracking) {
    // 获取当前时间
    const now = new Date();
    // 获取当天的23:59:59
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    // 计算时间差（毫秒）
    const timeDifference = new Big(endOfDay.getTime()).minus(now.getTime());
    // 将毫秒转换为秒
    const secondsDifference = new Big(timeDifference).div(1000).round();
    if (userId) {
      setWithExactExpire(
        `U_MARKETING_TRACKING_${userId}`,
        { marketing_tracking: marketingTracking },
        `${secondsDifference}S`,
      );
    } else {
      setWithExactExpire(
        "U_MARKETING_TRACKING_LOGOUT",
        { marketing_tracking: marketingTracking },
        `${secondsDifference}S`,
      );
    }
  } else {
    const marketingTrackingLogout = (getStore("U_MARKETING_TRACKING_LOGOUT") || {})
      ?.marketing_tracking;
    if (marketingTrackingLogout && userId) {
      const now = new Date();
      const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
      const timeDifference = new Big(endOfDay.getTime()).minus(now.getTime());
      const secondsDifference = new Big(timeDifference).div(1000).round();
      setWithExactExpire(
        `U_MARKETING_TRACKING_${userId}`,
        { marketing_tracking: marketingTrackingLogout },
        `${secondsDifference}S`,
      );
      del("U_MARKETING_TRACKING_LOGOUT");
    }
  }
}

export function getAsyncStorage() {
  return global.asyncLocalStorage.getStore();
}

/**
 * 处理合同中的点击事件回调函数。
 *
 * @param {Event} event - 触发回调的点击事件对象。
 */
export function contractClickCallback(event) {
  const tagNameList = ["span", "b", "u", "font"];
  let tarElement = event.target;
  let searchTimes = 5;
  event.preventDefault();
  while (tagNameList.includes(tarElement?.tagName?.toLowerCase()) && searchTimes > 0) {
    tarElement = tarElement.parentElement;
    searchTimes -= 1;
  }
  if (
    tarElement?.tagName?.toLowerCase() === "a" ||
    tarElement?.parentElement?.tagName?.toLowerCase() === "a"
  ) {
    const { textContent } =
      tarElement.tagName.toLowerCase() === "a" ? tarElement : tarElement.parentElement;
    const href = tarElement.getAttribute("href") || tarElement.parentElement.getAttribute("href");
    if (href && !textContent?.includes("www.fenqile.com")) {
      return {
        next: "goto",
        href,
      };
    }
  }
  return null;
}

/**
 * 判断Object是否为空
 * @param {Object} obj
 * @returns {boolean}
 */
export function isEmptyObject(obj) {
  return Object.keys(obj).length === 0;
}

/**
 * 获取当前网络状态
 */
export function getNetworkStatus() {
  return window?.navigator?.onLine;
}

/**
 * 支持iframe的src属性的CP
 * @type {number[]}
 */
const supportIframeSrcSuppliers = [supplierIdMap.qf, supplierIdMap.ppd];

/**
 * 判断是否支持iframe的src属性
 */
export function isSupportForIframeSrc(supplierId) {
  return supportIframeSrcSuppliers.includes(supplierId);
}

export function isProductionEnv() {
  return ["production", "pre"].includes(process.env.DEF_ENV_TYPE);
}

// 分期乐协议中,由于js中的接口存在跨域问题,所以必须直接访问,所以不支持深色模式
export function isUseSrcDirectly(supplierId) {
  return supplierId === supplierIdMap.lx;
}
