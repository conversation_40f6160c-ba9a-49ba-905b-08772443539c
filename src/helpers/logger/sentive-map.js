const regMap = {
  "mobile,phoneNumber,mobileNo":
    "[\\+]?(?<!\\d)(?:(?:1[3456789]\\d{9})|(?:861[356789]\\d{9}))(?!\\d)",
  "smsCode,snsCode": "(?<!\\d)(?:\\d{6})(?!\\d)",
  ctfCode: "(?<!\\d)(?:(?:\\d{15})|(?:\\d{18})|(?:\\d{17}[xX]))(?!(\\d)|[A-Za-z])",
  "userId,userID,userid,userAccountId,openId": "[A-Za-z0-9]{19,}",
  "deviceId,deviceSn,x-snbps-deviceid": "[A-Za-z0-9]{5,64}",
  "orderNo,orderId,snb_order_no,payOrderNo,tradeOrderNo,spOrderNum,snb_ticket,applyNo,outOrderNo,bankCardId,repayOrderId,couponNo,bankCardNo,repayNo":
    "[A-Za-z0-9]{5,32}",
  "fingerPrint,appFingerprint,clientAppHash": "[A-Za-z0-9]{32,}",
  "accessToken,refreshToken,clientSecret,token,access_token,refresh_token,id_token,pushToken,access-token,walletToken":
    "[A-Za-z0-9+-\\/=._]{32,}",
  "sign,signature,Signature-Value,Signature-Type,signType,packageValue,nonceStr,x-sign":
    "[A-Za-z0-9/+=]{10,}",
  "uid,Uid,udid": "[A-Za-z0-9]{1,36}",
  "client_id,appid,cpId": "[0-9]{8,64}",
  Authorization: "[A-Za-z0-9+_ +=]{8,256}",
  "userAccount,account,operatorid,operator,refundAccount": "[A-Za-z0-9+_]{8,64}",
  email: "\\w+([- +.]\\w)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*",
  password: "[A-Za-z0-9]{8,64}",
  customerName: "^[\\u4e00-\\u9fa5=:]+",
  "totalAmount,activationFee,rechargeAmount,order_amount,refundAccount,refund_amt,amount,bankCode,dayLimit,singleLimit,totalAvailableLimit,availableLimit,amount,oldLimit,repayAmount,loanAmount":
    "[0-9]{1,}",
  idNo: "^\\d{17}[\\dX]$",
  "name,realName": "[\\d,\\u4E00-\\u9FA5]{2,5}(?:·[\\u4E00-\\u9FA5]{2,5})*",
  "apr,dateRate,dayRate,oldDayRate,newDayRate,tempDayRate": "([1-9]\\d*\\.?\\d*)|(0\\.\\d*[1-9])",
  "creditLimit,remainLimit,maxLoan,minLoan,principal,interest,overdueAmount,mgmtFee,principalsum":
    "[0-9]+",
  "loanUrl,contractUrl,returnUrl": "[a-zA-Z0-9.]+[:]{0,}[0-9]*[a-zA-Z0-9/-]+",
  "apiLogoUrl,logoUrl,targetUrl":
    "(http[s]{0,1}://)(([A-Za-z0-9-]{2,})(?:.[A-Za-z0-9-]{2,})+)(?=[/])",
  abbreviation: "(?:[\\u4E00-\\u9FA5])*(\\u94F6\\u884C)(?:[\\u4E00-\\u9FA5])*",
  encryptedParams: "[A-Za-z0-9:]{32,}",
  firstRepayDate: "[0-9]{1,}",
  urlref: "(https|http:\\/\\/)?[a-zA-Z0-9.]+[:]{0,}[\\d]{0,}[a-zA-Z0-9\\/\\-]+",
};

const result = {};
Object.keys(regMap).forEach((keys) => {
  keys.split(",").forEach((key) => {
    result[key.toLocaleLowerCase()] = regMap[keys];
  });
});

export default result;
