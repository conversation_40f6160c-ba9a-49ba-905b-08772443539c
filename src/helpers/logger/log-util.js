import log4js from "log4js";
import logConfig from "./log-config";
import keyRegMap from "./sentive-map";
import keyRegArray from "./filter-array";
import { hideMarkStr } from "../utils";

log4js.configure(logConfig);
const cacheRegMap = {};

export function buildLoggerContext(logContext, type) {
  const logger = log4js.getLogger(type);
  Object.keys(logContext).forEach((key) => {
    logger.addContext(key, logContext[key]);
  });
  return logger;
}

function getMapByLogKeys(keys = []) {
  const keyIndex = keys.join("|");
  if (cacheRegMap[keyIndex]) {
    return cacheRegMap[keyIndex];
  }
  const sb = [];
  keys.forEach((item) => {
    sb.push(
      `(["|\\"]{0,}(${item})["|\\"]{0,}(\\s{0,})[:|=](\\s{0,})[\\[|\\"|"]{0,}(${keyRegMap[item]})[\\]|\\"|"]{0,}[\\,|\\&]{0,})|`,
    );
  });
  const pattenMap = new RegExp(sb.join(""), "ig");
  cacheRegMap[keyIndex] = pattenMap;
  return pattenMap;
}

export function hideMarkLog(logStr) {
  try {
    // 1.判断配置文件中是否已经配置需要脱敏字段
    if (logStr) {
      let handleLogStr = logStr;
      const lowerStr = handleLogStr.toLowerCase();
      let keys = [];
      const filterKeys = [];
      Object.keys(keyRegMap).forEach((key) => {
        if (lowerStr.includes(`"${key}"`)) {
          keys.push(key);
        }
      });
      keyRegArray.forEach((key) => {
        if (lowerStr.includes(`"${key}"`)) {
          filterKeys.push(key);
        }
      });
      if (keys.includes("mobileno")) {
        keys = keys.filter((item) => item !== "mobile");
      }
      if (keys.includes("realname")) {
        keys = keys.filter((item) => item !== "name");
      }
      if (keys.length) {
        handleLogStr = handleLogStr.replace(getMapByLogKeys(keys), (str) =>
          hideMarkStr(str, keys, filterKeys, keyRegMap),
        );
      }
      return handleLogStr;
    }
    return "";
  } catch (e) {
    // 如果抛出异常为了不影响流程，直接返回原信息
    return logStr;
  }
}

export function logInterface(costTime, flag, code) {
  const { req, context = {} } = global.asyncLocalStorage.getStore();
  const { logContext } = context;
  const logger = buildLoggerContext(logContext, "interface");
  logger.addContext("responseTime", costTime);
  logger.addContext("flag", flag);
  logger.addContext("responseCode", code);
  if (flag === "T") {
    logger.addContext("responseInfo", "success");
  } else {
    logger.addContext("responseInfo", "fail");
  }
  logger.addContext("requestArgs", hideMarkLog(JSON.stringify(req.query)));
  logger.info("");
}

export function logGeneral(message) {
  const { context } = global.asyncLocalStorage.getStore();
  const { logContext } = context;
  const logger = buildLoggerContext(logContext, "general");
  logger.info(hideMarkLog(JSON.stringify(message)));
}

export function logRun(message, level = "info", needMask = true) {
  const { context } = global.asyncLocalStorage.getStore();
  const { logContext } = context;
  const logger = buildLoggerContext(logContext, level === "error" ? "error" : "run");
  logger.addContext("level", level.toUpperCase());
  if (needMask) {
    logger[level](hideMarkLog(message));
  } else {
    logger[level](message);
  }
}
