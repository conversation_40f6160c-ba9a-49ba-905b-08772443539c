import forge from "node-forge";
import { getCurrentCpInfo, processEvent } from "./configUtils";
import { getStore } from "./storage";
import { setNetwork } from "./network-helper";
import { isInLoanApp, initHnrJsSdk } from "./hnr-helper";
import { domainMap, getCurrentStore } from "./constants";

let runningCount = 0;

function getUrlTurns() {
  const { href } = window.location;
  const url = new URL(href);
  const address = url.origin + url.pathname;
  const queryString = url.search;
  const params = new URLSearchParams(queryString);
  const urlref = params.get("urlref");
  return {
    url: address,
    urlref: urlref || "",
  };
}

function sha1Hex(data) {
  return forge.util.bytesToHex(forge.md.sha1.create().update(data, "utf8").digest());
}

export async function goto(
  url,
  showTitle = false,
  isClearTask = false,
  forceDark = false,
  backMove = false,
  isLive = false,
  allowBack = true,
) {
  document.cookie = `access_fail=0; expires=${new Date(0)}; path=/;`;
  let newUrl = url;
  if (newUrl?.indexOf("/wallet-loan-web/") === 0) {
    const uRL = new URL(`${window.location.origin}${newUrl}`);
    const originURL = new URL(window.location.href);
    uRL.searchParams.append("urlref", `${originURL.origin}${originURL.pathname}`);
    newUrl = uRL.href;
  }
  if (isInLoanApp()) {
    if (!window?.navigator?.onLine) {
      setNetwork();
      return;
    }
    let clearTask = isClearTask;
    let isBack = backMove;
    if (url?.indexOf("/wallet-loan-web/pages/index") === 0) {
      clearTask = true;
      isBack = true;
    }

    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("openWebActivity", {
      url:
        newUrl.indexOf("/wallet-loan-web/") === 0 ? `${window.location.origin}${newUrl}` : newUrl,
      showTitle,
      clearTask,
      forceDark,
      isBack,
      isLive,
      allowBack,
    });
  } else {
    window.location.href =
      newUrl.indexOf("/wallet-loan-web/") === 0 ? `${window.location.origin}${newUrl}` : newUrl;
  }
}

export async function gotoWithOptions(options = {}) {
  const {
    url,
    showTitle = false,
    isClearTask = false,
    forceDark = false,
    backMove = false,
    isLive = false,
    allowBack = true,
  } = options;
  goto(url, showTitle, isClearTask, forceDark, backMove, isLive, allowBack);
}

export async function back() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("back");
  } else {
    window.history.back();
  }
}

export async function closeLiveGuidePage() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("closeLiveGuidePage");
  }
}

/**
 * 上报事件
 * @param {String} eventId
 * @param {Object} event
 * @param {Number} needSupplierId （默认）1=打点需要获取供应商ID -1=打点不需要获取供应商ID
 * @returns
 */
const reportedPages = [];
export async function report(eventId, event, needSupplierId = 1) {
  if (isInLoanApp()) {
    let cpInfo;
    let supplierId;
    if (needSupplierId !== -1) {
      if (event?.supplierId || event?.supplierId === 0) {
        supplierId = event.supplierId;
      } else {
        cpInfo = await getCurrentCpInfo();
        supplierId = cpInfo?.supplierId || 0;
      }
    }
    const clientId = window.location.search
      ?.split("&")
      ?.filter((item) => item.includes("clientId"))[0]
      ?.split("=")[1];
    const hnrJsSdk = await initHnrJsSdk();

    // 曝光和点击事件都上报归因参数
    const { url, urlref } = getUrlTurns();
    let marketingTracking = null;
    const userId = getCurrentStore()?.param?.userId;
    const storageData =
      getStore(userId ? `U_MARKETING_TRACKING_${userId}` : "U_MARKETING_TRACKING_LOGOUT") || {};
    marketingTracking = storageData.marketing_tracking;

    runningCount += 1;
    const domain = window.location.host;
    const eventData = JSON.stringify({
      ...(await processEvent(event)),
      needSupplierId,
      supplierId: needSupplierId !== -1 ? supplierId : 0,
      clientId,
      url,
      urlref,
      ...(marketingTracking && { marketing_tracking: marketingTracking }),
      ...(domainMap[domain] === "production" && { reportedPages }),
    });
    if ((getCurrentStore().param?.sdkVersionCode || 0) < 90005000) {
      hnrJsSdk.customApi("onEvent", { eventId, eventData }).catch(() => {
        // 兼容老版本
        hnrJsSdk.customApi("onWalletLoanEvent", { eventId, eventData });
      });
    } else {
      hnrJsSdk.customApi("onWalletLoanEvent", { eventId, eventData });
    }
    runningCount -= 1;
    if (eventId === "wallet_page_view" && !reportedPages.includes(event.page_name)) {
      reportedPages.push(event.page_name);
    }
  } else {
    console.dir({ eventId, ...(await processEvent(event)) });
  }
}

export async function userImprovementPlan() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("experienceEnable");
    return data;
  }
  return false;
}
export async function setUserImprovementPlan(param) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("setExperience", { enable: param });
    return data;
  }
  return false;
}
export async function sixClickNative(param) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("enableDebug", { enable: param });
    return data;
  }
  return false;
}

/**
 * @param {*} param
 * @returns Boolean
 */
export async function liveDetection(param) {
  if (isInLoanApp() && !getCurrentStore()?.param?.skipLiveDetection) {
    if (runningCount === 0) {
      const hnrJsSdk = await initHnrJsSdk();
      const value = await hnrJsSdk.customApi("liveDetection", param);
      return value?.result;
    }
    return new Promise((resolve) => {
      setTimeout(() => {
        const ret = liveDetection(param);
        resolve(ret);
      }, 100);
    });
  }
  return true;
}

export function setCp(supplierId) {
  if (isInLoanApp()) {
    if (getCurrentStore()?.param?.sdkVersionCode >= 90001000) {
      initHnrJsSdk().then((hnrJsSdk) => {
        hnrJsSdk.customApi("setCp", { cp: supplierId });
      });
    }
  }
}

export async function doOCR(param = {}) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("doOCR", param);
    return {
      fileInfos: [
        {
          type: "CARD_FRONT_PHOTO",
          value: data.faceImage,
          identifier: "identifier",
        },
        {
          type: "CARD_BACK_PHOTO",
          value: data.backImage,
          identifier: "identifier",
        },
      ],
    };
  }
  return {
    fileInfos: [
      {
        type: "CARD_FRONT_PHOTO",
        value: "",
      },
      {
        type: "CARD_BACK_PHOTO",
        value: "",
      },
    ],
  };
}

export async function refreshToken() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    await hnrJsSdk.customApi("refreshToken");
  }
}

export async function encrypt(text) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("encrypt", { content: text });
    return data;
  }
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(text);
    }, 0);
  });
}

export async function decrypt(text) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("decrypt", { content: text });
    return data;
  }
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(text);
    }, 0);
  });
}

/**
 * @param {Boolean} param
 * @returns
 */
export async function updateShortcut(param) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const ret = await hnrJsSdk.customApi("updateShortcut", { show: param });
    return ret;
  }
  return false;
}

/**
 * @returns Boolean
 */
export async function hasShortcut() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const data = await hnrJsSdk.customApi("hasShortcut");
    return data;
  }
  return false;
}

/**
 *
 * @returns
 */
export async function launchSupport() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("launchSupport");
  }
  return false;
}

/**
 *
 * 设置活体信息
 * @returns
 */

export async function setLiveImage(liveData) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("setLiveImage", liveData);
  }
}

/**
 *
 * 获取活体照片
 * @returns String
 */
export async function getLiveImage() {
  if (isInLoanApp() && !getCurrentStore()?.param?.skipLiveDetection) {
    const hnrJsSdk = await initHnrJsSdk();
    const value = await hnrJsSdk.customApi("getLiveImage");
    return {
      fileInfos: [
        {
          identifier: "identifier",
          type: "FRONT_PHOTO",
          value,
        },
      ],
    };
  }
  return {
    fileInfos: [
      {
        identifier: "identifier",
        type: "FRONT_PHOTO",
        value: "FILE_PATH",
      },
    ],
  };
}

/**
 * 清除内存中的活体照片
 * @returns
 */
export async function clearLiveImage() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("clearLiveImage");
  }
  return false;
}
/**
 * 转至拨号键
 * @returns
 */
export async function callPhone(param) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    await hnrJsSdk.customApi("callPhone", { phoneNum: param });
  }
  return false;
}

const REGISTER_EVENTS = [
  "onResume",
  "onNetChange",
  "onBack",
  "onLoanResume",
  "replaceUrl",
  "onKeyboardStateChange",
  "retentionPopup",
  "KeyBoardHeight",
];
const FUNCTION_MAP = {};
if (isInLoanApp()) {
  initHnrJsSdk().then((hnrJsSdk) => {
    hnrJsSdk.others
      .registerListener({
        eventTypes: REGISTER_EVENTS,
        callback: (res) => {
          const eventParam = JSON.parse(res);
          if (FUNCTION_MAP[eventParam.eventType]) {
            FUNCTION_MAP[eventParam.eventType].forEach((func) => {
              func(JSON.parse(res));
            });
            return false;
          }
          return true;
        },
      })
      .catch(() => {});
    window.addEventListener("beforeunload", () => {
      hnrJsSdk.others.unregisterListener({
        eventTypes: REGISTER_EVENTS,
      });
    });
  });
}

/**
 * native事件监听页面
 * 注册方法避免使用async修饰符
 * @returns
 */
export function regNativeEvent(event, func) {
  if (isInLoanApp()) {
    if (!FUNCTION_MAP[event]) {
      FUNCTION_MAP[event] = [];
    }
    FUNCTION_MAP[event].push(func);
  }
}

/**
 * native事件监听页面
 * 注册方法避免使用async修饰符
 * @returns
 */
export function unRegNativeEvent(event, func) {
  if (isInLoanApp()) {
    if (!FUNCTION_MAP[event]) {
      FUNCTION_MAP[event] = [];
    }
    FUNCTION_MAP[event] = FUNCTION_MAP[event].filter((item) => item !== func);
  }
}

export async function hideLoading() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    await hnrJsSdk.customApi("hideLoading");
  }
}
/**
 * 取消/允许手势返回
 * @param {Boolean} enable
 * @returns
 */
export async function enableBackPress(enable) {
  if (isInLoanApp()) {
    runningCount += 1;
    const hnrJsSdk = await initHnrJsSdk();
    await hnrJsSdk.customApi("enableBackPress", { enable });
    runningCount -= 1;
  }
  return false;
}

export function toastMessage(msg) {
  if (isInLoanApp()) {
    window.hnr.ui
      .toast({
        msg,
      })
      .catch(() => {});
  }
}

export async function getUserId() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    const res = await hnrJsSdk.customApi("getUserId");
    if (res?.userId) getCurrentStore().param.userId = sha1Hex(res.userId);
    return res?.userId ? sha1Hex(res.userId) : null;
  }
  return null;
}

export async function doLogin() {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("doLogin");
  }
}

let clickCount = 0;
let timer0 = 0;
let timer6 = 0;
export async function sixClick() {
  clickCount += 1;
  if (clickCount === 1) {
    timer0 = new Date().getTime() / 1000;
  }
  if (clickCount === 6) {
    timer6 = new Date().getTime() / 1000;
    if (timer6 - timer0 <= 3) {
      await sixClickNative(true);
      clickCount = 0;
    } else {
      await sixClickNative(false);
      clickCount = 0;
    }
  }
}

export async function updateTitleBar(visible, title) {
  if (isInLoanApp()) {
    const hnrJsSdk = await initHnrJsSdk();
    hnrJsSdk.customApi("updateTitleBar", { visible, title });
  }
}
