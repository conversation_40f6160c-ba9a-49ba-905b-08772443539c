import { request } from "../helpers/utils";

/**
 * 用户授信信息
 * @typedef {Object} UserCreditInfo
 * @property {number} creditStatus - 授信状态(0-未申请授信，1-审核中，2-通过，3-拒绝)
 * @property {number} supplier - 三方借贷公司supplierId
 */

/**
 * 获取用户授信信息
 * @returns {Promise<Res<UserCreditInfo>>} - 用户授信信息
 */
export async function getUserInfo() {
  return request("/loan/api/user/getUserInfo");
}

/**
 * 上报用户行为记录
 * @param {Object} operData - 用户行为记录数据
 * @returns {Promise<unknown>} - 上报用户行为记录
 */
export async function operLog(operData) {
  return request("/loan/api/user/operLog", operData);
}

/**
 * 获取用户的实名信息。
 *
 * @returns {Promise} 返回一个 Promise 对象，解析为用户的实名信息数据。
 */
export async function getUserRealNameInfo() {
  return request("/loan/api/user/v2/getUserRealNameInfo");
}
