import { request } from "../helpers/utils";

/**
 * 银行卡转账还款
 * @param {GenerateRepayUrlParam} data - 银行卡转账还款入参
 * @returns {Promise<BaseResponse<GenerateRepayUrlDto>>} - 银行卡转账还款返回参数
 */
export async function transferRepay(data) {
  return request("/loan/api/repay/transfer", { encryptedParams: data });
}

/**
 * 还款结果查询
 * @param {string} repayNo - 还款订单号
 * @returns {Promise<BaseResponse<QueryRepayResultResponse>>} - 还款结果查询数据返回参数
 */
export async function repayStatusQuery(repayNo) {
  return request("/loan/api/repay/queryStatus", { repayNo });
}

/**
 * 还款记录查询
 * @param outOrderNo
 * @param repayNo
 * @returns {Promise<BaseResponse<RepayRecordDto[]>>}
 */
export async function repayRecordQuery(outOrderNo, repayNo) {
  return request("/loan/api/repay/record", { outOrderNo, repayNo });
}
