import { request } from "../helpers/utils";

/**
 * 查询借款记录
 * @param {LoanRecordParam} params
 * @returns {Promise<BaseResponse<LoanRecordListDto>>}
 */
export async function getLoanRecord(params) {
  return request("/loan/api/loan/record", params);
}

/**
 * 查询借款订单详情
 * @param {string} outOrderNo
 * @returns {Promise<BaseResponse<LoanRecordDetailDto>>}
 */
export async function getLoanOrderDetail(outOrderNo) {
  return request("/loan/api/loan/detail", { outOrderNo });
}
