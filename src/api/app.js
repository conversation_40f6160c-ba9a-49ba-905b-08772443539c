import { request } from "../helpers/utils";

/**
 * @typedef {Object} ExtSettingParam
 * @property {number} personalRecommendation - 个性化推荐，1-开启，2-关闭 (O)
 * @property {number} marketingNotice - 营销通知，1-开启，2-关闭 (O)
 */

/**
 *
 * @param {number} source - 设置来源 1-钱包 2-小贷
 * @returns {Promise<unknown>} - 查询用户app配置
 */
export async function querySetting(source) {
  return request("/config/api/app/setting/query", { source });
}

/**
 *
 * @param {number} source - 设置来源 1-钱包 2-小贷
 * @param {ExtSettingParam} extSetting - 扩展功能配置
 * @returns {Promise<unknown>} - 保存用户app配置
 */
export async function saveSetting(source, extSetting) {
  return request("/config/api/app/setting/save", {
    source,
    extSetting,
  });
}
