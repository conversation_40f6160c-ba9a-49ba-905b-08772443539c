import { request } from "../helpers/utils";

/**
 * 获取小贷严选推荐配置列表
 * @returns {Promise<BaseResponse<SuggestSupplierDto[]>>} - 小贷严选推荐配置列表
 */
export async function getSuggestList() {
  return request("/loan/api/config/suggest/list", {});
}

/**
 * 获取CP配置信息
 * @returns {Promise<BaseResponse<CpConfigDto>>} - CP配置信息
 */
export async function getCpConfig() {
  return request("/loan/api/config/getCpConfig", {});
}

/**
 * 获取前端通用配置信息
 * @returns {Promise<BaseResponse<ClientConfigDto>>} - 前端通用配置信息
 */
export async function getClientConfig() {
  return request("/loan/api/config/getClientConfig", {});
}
