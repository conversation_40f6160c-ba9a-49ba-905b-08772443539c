/**
 * @typedef LoanRecordParam
 * @property {number} [status] - 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败
 * @property {number} [currentPage] - 当前页码，不传则全量返回
 * @property {number} [pageSize] - 每页条数
 */

/**
 * 贷款记录列表 DTO，包含分页信息和记录列表
 * @typedef {Object} LoanRecordListDto
 * @property {number} totalNum - 记录总条数
 * @property {Array<LoanRecordDto>} records - 记录列表
 * @property {number} [currentPage] - 当前页码（可选）
 * @property {number} [pageSize] - 每页条数（可选）
 * @property {number} totalPage - 总页数
 * @property {number} totalCount - 记录总数
 */

/**
 * 单条贷款记录 DTO
 * @typedef {Object} LoanRecordDto
 * @property {string} outOrderNo - 渠道方借款订单号，借款失败可不返回
 * @property {string} applyNo - 荣耀侧借款申请订单号
 * @property {string} applyDate - 借款申请日期，yyyyMMdd
 * @property {number} applyTime - 借款申请时间，毫秒
 * @property {number} loanAmount - 借款金额，单位：分
 * @property {number} status - 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败
 * @property {number} totalTerm - 借款期数
 * @property {number} repayTermNum - 已还款期数
 * @property {number} currentTerm - 当前期次
 * @property {number} payableAmount - 剩余应还金额，单位：分
 * @property {string} loanSource - 借款来源
 * @property {number} clearTime - 结清时间(未结清：0)
 * @property {number} overdueDays - 借据逾期天数
 * @property {number} overdueAmount - 借据逾期未还金额
 */

/**
 * 借款详情DTO
 * @typedef {Object} LoanRecordDetailDto
 * @property {string} applyDate 借款申请日期，yyyyMMdd
 * @property {number} applyTime 借款申请时间戳
 * @property {string} effectiveDate 借款起息日
 * @property {number} loanAmount 借款金额，单位：分
 * @property {number} status 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败
 * @property {number} totalTerm 借款期数
 * @property {number} repayMethod 还款方式 1-等额本息，2-等额本金，3-先息后本，4-锁期
 * @property {string} repayMethodName 还款方式展示名称
 * @property {string} outOrderNo DXM借款订单号
 * @property {number} clearTime 结清时间(未结清：0)
 * @property {number} paidAmount 已还总金额，单位：分
 * @property {number} paidPrinAmount 已还本金总额，单位：分
 * @property {number} paidInterAmount 已还利息总额，单位：分
 * @property {number} paidFeeAmount 已还服务费总额，单位：分
 * @property {number} paidPenalty 已还罚息，单位：分
 * @property {string} bindCardNo 收款卡号
 * @property {string} bindBankCode 收款卡发卡行code
 * @property {string} bindBankName 收款卡发卡行名称
 * @property {string} couponNo 优惠券id
 * @property {string} dayRate 实际日利率
 * @property {string} apr 年利率
 * @property {number} reductionAmount 优惠金额，单位：分
 * @property {number} prePenalty 提前还款违约金，单位：分
 * @property {Array<RepayPlanTermDto>} repayPlanTerms 还款计划列表
 * @property {Array<ContractDto>} contractList 已签署借款协议列表
 * @property {string} institutionNames 机构名称（出资方）
 * @property {number} supplier 服务商，1-度小满
 * @property {string} reductionAmountDesc 优惠金额返现文案
 */

/**
 * 还款计划期DTO
 * @typedef {Object} RepayPlanTermDto
 * @property {number} termNo 期次
 * @property {string} shouldRepayDate 应还款日期，yyyyMMdd
 * @property {number} termStatus 当期状态 1-已结清，2-待还款，3-部分已还，4-逾期，5-宽限期
 * @property {number} repayCategory 还款类型，1-主动还款，2-银行卡代扣
 * @property {number} termAmount 本期应还总额,单位：分
 * @property {number} termPrincipal 本期应还本金，单位：分
 * @property {number} termInterest 本期应还利息，单位：分
 * @property {number} termFee 本期应还服务费
 * @property {number} termReductionAmount 本期优惠券减免金额，单位：分
 * @property {number} amount 应还金额
 * @property {number} termPenalty 本期应还罚息
 * @property {number} termOverdueFee 本期应还逾期费
 * @property {number} termViolateFee 本期应还违约金
 * @property {number} termMgntFee 本期应还账务管理费
 * @property {number} termServiceFee 本期应还服务费
 * @property {number} termCharges 本期应还费用，单位：分
 * @property {number} termPrinPenalty 本期应还本金罚息，单位：分
 * @property {number} termInterPenalty 本期应还利息罚息，单位：分
 * @property {string} termInterAndFee 本期息费，单位：分
 * @property {number} paidTime 本期实际还款日
 * @property {number} paidTermAmount 本期实还总额，单位：分
 * @property {number} paidTermPrincipal 本期实还本金，单位：分
 * @property {number} paidTermInterest 本期实还利息，单位：分
 * @property {number} paidTermCharges 本期实还费用，单位：分
 * @property {number} paidTermReductionAmount 本期实还优惠券额，单位：分
 * @property {number} paidTermPrinPenalty 本期实还本金罚息，单位：分
 * @property {number} paidTermInterPenalty 本期实还利息罚息，单位：分
 * @property {number} paidTermOverdueFee 本期实还逾期费，单位：分
 * @property {number} paidTermFee 本期实还服务费，单位：分
 * @property {number} paidTermServiceFee 本期实还服务费，单位：分
 * @property {number} paidTermMgntFee 本期实还账务管理费，单位：分
 * @property {number} paidTermViolateFee 本期实还违约金，单位：分
 * @property {number} payableTermAmount 剩余应还还款金额，单位：分
 * @property {number} payableTermPrincipal 剩余应还还款本金，单位：分
 * @property {number} payableTermInterest 剩余应还还款利息，单位：分
 * @property {number} payableTermCharges 剩余应还还款费用，单位：分
 * @property {number} payableTermPrinPenalty 剩余应还本金罚息，单位：分
 * @property {number} payableTermInterPenalty 剩余应还利息罚息，单位：分
 * @property {number} payableTermOverdueFee 剩余应还逾期费，单位：分
 * @property {number} payableTermFee 剩余应还服务费，单位：分
 * @property {number} payableTermServiceFee 剩余应还服务费，单位：分
 * @property {number} payableTermMgntFee 剩余应还账务管理费，单位：分
 * @property {number} payableTermViolateFee 剩余应还违约金，单位：分
 * @property {number} payableInterAndFee 剩余应还息费，单位：分
 * @property {number} overdueDays 逾期天数
 * @property {number} overdueAmt 逾期金额，单位：分
 * @property {boolean} preRepay 是否提前还款
 * @property {boolean} overdue 是否逾期
 * @property {number} earlyRepayDays 提前还款天数
 * @property {number} paidTermPenalty 本期实还罚息
 * @property {number} payableTermPenalty 剩余应还罚息
 * @property {number} overdueAmount 逾期金额
 * @property {number} loanOverDueDays 逾期天数，兼容度小满
 * @property {number} termTempDiscount 本期限时降价优惠金额，单位：分
 * @property {number} termScheduleDiscount 本期按期还优惠金额，单位：分
 * @property {number} termTotalDiscount 本期总优惠金额，单位：分
 * @property {string} reductionAmountDesc 优惠金额文案
 */

/**
 * 协议信息DTO
 * @typedef {Object} ContractDto
 * @property {number} defaultChecked 是否默认勾选， 1：勾选   0：不勾选
 * @property {string} contractId 协议ID
 * @property {string} contractName 合同名称
 * @property {string} contractUrl 合同地址
 */
