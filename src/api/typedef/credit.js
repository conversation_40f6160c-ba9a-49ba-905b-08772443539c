/**
 * @typedef {Object} CreditInfo
 * @property {number} accessResult - 准入状态，0-未准入，1-准入成功，2-拒绝，3-注销 (M)
 * @property {number} [supplier] - 服务商，1-度小满，5-京东，6-乐信 (O)
 * @property {string} [supplierName] - 服务商名称，例：度小满 (O)
 * @property {string} [supplierLogo] - 服务商Logo (O)
 * @property {string} [openId] - 准入成功返回openId (O)
 * @property {string} [flowNo] - 流水号，多CP场景使用，如果返回需要作为准入接口的入参 (O)
 * @property {string} [mobileNo] - 手机号，准入成功时返回 (O)
 * @property {number} status - 额度状态，0-未获取额度，1-正常，2-审核中，3-失效，4-拒绝 (M)
 * @property {number} [creditLimit] - 授信总额度，单位分 (O)
 * @property {number} [limitType] - 额度类型，1-循环额度 (O)
 * @property {number} [remainLimit] - 可用额度，单位分 (O)
 * @property {string} [limitExpireDate] - 额度失效日期，格式yyyyMMdd (O)
 * @property {number} [repayDay] - 还款日 (O)
 * @property {number} [limitUseErrStatus] - 额度使用状态，1-存在放款中借款，2-存在逾期订单不可借款，3-暂时无法支用，4-其他原因 (O)
 * @property {string} [limitUseErrDesc] - 额度使用描述 (O)
 * @property {number} [greyExpireTime] - 额度管控截止时间，时间戳(秒) (O)
 * @property {number} [totalAvailableLimit] - 总可用额度（临额+固额总和），单位分 (O)
 * @property {number} [totalCreditLimit] - 总授信额度（临额+固额总和），单位分 (O)
 * @property {number} [maxLoan] - 单笔借款最大金额，单位分 (O)
 * @property {number} [minLoan] - 单笔借款最小金额，单位分 (O)
 * @property {boolean} [canCreditChange] - 是否可提额 (O)
 * @property {Array<ProductInfoDto>} [productInfos] - 产品信息 (O)
 * @property {RepayInfoDto} [repayInfo] - 用户待还款信息 (O)
 * @property {TempLimitInfo} [tempLimitInfo] - 临额相关信息 (O)
 * @property {CreditChangeDto} [creditChange] - 调额调价信息 (O)
 * @property {OverdueInfoDto} [overdueInfo] - 逾期信息 (O)
 * @property {DictCreditInfoDto} [dictCreditInfo] - 字典配置额度信息（loan_home_page_redit_info） (O)
 * @property {number} [refuseControlDays] - 授信拒绝管控期，单位：天 (O)
 * @property {Date} [applyTime] - 授信申请时间 (O)
 * @property {number} [greyExpireSeconds] - 额度管控截止时间倒计时，单位：秒 (O)
 * @property {number} [refuseControlSeconds] - 授信拒绝或者全部准入失败时管控期倒计时，单位：秒 (O)
 */
