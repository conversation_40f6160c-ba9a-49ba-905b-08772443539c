/**
 * 生成还款链接入参
 * @typedef {Object} GenerateRepayUrlParam
 * @property {RepayOrder[]} repayList - 还款列表
 * @property {number} totalAmount - 还款总金额
 * @property {number} repayType - 还款类型，1-正常还款，2-提前还款
 */

/**
 * 还款订单
 * @typedef {Object} RepayOrder
 * @property {number} payAmount - 还款金额
 * @property {number} [couponNo] - 优惠券id
 * @property {number} [reductionAmount] - 优惠金额
 * @property {RepayTerm[]} repayTerms - 还款期次列表
 */

/**
 * 还款期次
 * @typedef {Object} RepayTerm
 * @property {number} term - 期次
 * @property {number} amount - 还款金额
 */

/**
 * 生成还款链接数据传输对象
 * @typedef {Object} GenerateRepayUrlDto
 * @property {string} repayNo - 还款订单号
 * @property {string} [repayUrl] - 跳转链接
 */

/**
 * 还款结果查询数据传输对象
 * @typedef {Object} QueryRepayResultResponse
 * @property {number} repayStatus - 还款状态，1-还款中，2-还款成功，3-部分还款成功，4-还款失败
 * @property {string} [repayResult] - 返回失败原因
 * @property {number} [supprtReoffer] - 还款成功是否支持reoffer
 */

export const repayStatusMap = {
  Repaying: 1,
  Success: 2,
  PartialSuccess: 3,
  Failed: 4,
};

/**
 * 还款记录数据传输对象
 * @typedef {Object} RepayDto
 * @property {string} outOrderNo - 三方借款订单号
 * @property {number} repayAmount - 还款金额，单位：分
 * @property {string} repayTime - 实际还款时间，格式：yyyy-MM-dd HH:mm:ss
 * @property {number} repayStatus - 还款状态，1-还款中，2-还款成功，3-部分还款成功，4-还款失败
 * @property {string} [repayResult] - 返回失败原因
 */
