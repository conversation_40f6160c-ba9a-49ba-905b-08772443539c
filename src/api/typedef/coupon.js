/**
 * @typedef Coupon
 * @property {string} couponNo - 优惠券id
 * @property {number} couponType - 券类型 券类型，1: 放款券; 2: 还款券
 * @property {number} status - 券状态。
 *   1-未使用：对应度小满未使用（2）；
 *   2-已使用：包含度小满已绑定（7）、使用中（8）、已使用（3）、已冻结（4）；
 *   3-已过期：对应度小满已过期（6），已回收（10）和已失效（11）两个状态不返回给用户。
 * @property {number} startTime - 券有效期开始时间 - Unix timestamp in milliseconds
 * @property {number} endTime - 券有效期结束时间 - Unix timestamp in milliseconds
 * @property {number} discountAmount - 优惠金额，单位分
 */

// 优惠券状态
export const couponStatus = {
  NOT_USED: 1, // 未使用
  USED: 2, // 已使用
  OUT_TIME: 3, // 已过期
};
