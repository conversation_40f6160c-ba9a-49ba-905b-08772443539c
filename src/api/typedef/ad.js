/**
 * 广告活动数据传输对象
 * @typedef {Object} AdActivityDto
 * @property {number} id - 主键
 * @property {string} spaceCode - 运营位编码
 * @property {string} activityName - 活动名称
 * @property {string} picUrl - 图片地址
 * @property {string} targetUrl - 跳转链接地址
 * @property {Date} startTime - 开始时间
 * @property {Date} endTime - 结束时间
 * @property {Date} updateTime - 更新时间
 */

/**
 * 策略实体
 * @typedef {Object} PolicyConciseDto
 * @property {string} expConfCode - 命中策略id
 * @property {string} expConfName - 命中策略名称
 * @property {string[]} events - 策略对应的埋点事件列表
 */

/**
 * 资源位素材数据传输对象
 * @typedef {Object} AdActivityDtoV2
 * @property {number} id - 主键
 * @property {string} spaceCode - 运营位编码
 * @property {string} [activityCode] - 素材id
 * @property {string} activityName - 素材名称
 * @property {Date} [startTime] - 活动开始时间
 * @property {Date} [endTime] - 活动结束时间
 * @property {string} [picUrl] - 图片地址
 * @property {string} [txt] - 文本素材
 * @property {string} [tagContent] - 文本标签内容
 * @property {number} [tagEffect] - 标签展示效果，1-点击失效，2-固定时间
 * @property {Date} [tagShowStartTime] - 标签展示开始时间
 * @property {Date} [tagShowEndTime] - 标签展示结束时间
 * @property {number} linkType - 跳转类型，1-deeplink，3-H5，4-快应用
 * @property {string} targetUrl - 跳转链接地址
 * @property {number} [cardGroupType] - 关联卡片类型：0-交通卡，1-门禁卡，2-蓝牙车钥匙，3-银行卡，4-NFC车钥匙，5-NFC银行卡，6-乘车码，7-医保码，8-支付宝乘车码，10-校园卡
 * @property {!number} thirdProvider - 是否外部服务，0-否，1-是
 * @property {string} [thirdAppPackagename] - 外部App包名
 * @property {number} [supportAppVersion] - 支持的钱包app版本
 * @property {number} [supportHnidVersion] - 支持的账号app版本
 * @property {number} [popupTriggers] - 弹窗次数选项，1-固定1次，2-自定义配置(弹窗X次，每次间隔X天)
 * @property {number} [maxTriggersNum] - 最多触发次数（popupTriggers = 2-自定义配置时）
 * @property {number} [triggersInterval] - 触发间隔天数（popupTriggers = 2-自定义配置时）
 * @property {PolicyConciseDto} [abTestPolicy] - AB实验命中策略id
 */

/**
 * 资源位数据传输对象
 * @typedef {Object} AdSpaceDtoV2
 * @property {string} spaceCode - 资源位ID
 * @property {string} spaceName - 资源位名称
 * @property {number} spaceType - 资源类型，1-列表类，2-金刚位，3-弹窗类
 * @property {number} carousel - 是否支持轮播，0-否，1-是
 * @property {string} [resourceType] - 资源位元素，逗号分隔，pic-图片，txt-文本
 * @property {number} [txtLength] - 文本字符限制
 * @property {number} [tagLength] - 文字标签字符限制
 * @property {string} [popupPage] - 弹窗触发页面，|表示或，*表示且
 * @property {number} [popupMoment] - 弹窗触发时机，1-页面打开，2-页面退出
 * @property {number} [popupFrequency] - 退出触发页面时，弹窗弹出的频率，1-每次都弹，2-只弹1次
 * @property {AdActivityDtoV2[]} activityList - 素材列表
 */
