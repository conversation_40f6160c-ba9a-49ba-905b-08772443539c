import { request } from "../helpers/utils";

export async function sendVerifyCode(newMobileNo) {
  return request(
    "/loan/api/sms/v2/sendVerifyCode",
    { encryptedParams: { type: 6, mobileNo: newMobileNo } },
    { mock: false },
  );
}

export async function modifyPhone(newMobileNo, smsCode) {
  return request("/loan/api/sms/modifyPhone", {
    encryptedParams: { mobileNo: newMobileNo, smsCode },
  });
}
