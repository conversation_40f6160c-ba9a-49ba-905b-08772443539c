body {
  background: var(--hnr-color-background-cardview);
  margin: 0;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    --hnr-color-button-normal: #3c393c !important;
  }
}

@font-face {
  /* 1.注意font-family:icomoon */
  font-family: "icomoon";
  /* 2.注意路径! */
  src: url("/public/icon/fonts/icomoon.eot?7kkyc2");
  src:
    url("/public/icon/fonts/icomoon.eot?7kkyc2#iefix") format("embedded-opentype"),
    url("/public/icon/fonts/icomoon.ttf?7kkyc2") format("truetype"),
    url("/public/icon/fonts/icomoon.woff?7kkyc2") format("woff"),
    url("/public/icon/fonts/icomoon.svg?7kkyc2#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
.nav-padding-top {
  padding-top: 32px;
}

.nav-padding-top .hnr-nav-bar__center {
  width: calc(100% - 84px) !important;
  margin: 0px 44px 0px 40px !important;
}

.nav-padding-top .hnr-nav-bar__content {
  transition: none;
}