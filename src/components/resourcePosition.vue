<template>
  <div
    class="container"
    :style="{
      flexDirection: ifChangeLayout ? 'row' : 'column',
      paddingTop: props.signStatus === 2 ? '16px' : '20px',
      paddingBottom: ifChangeLayout ? '24px' : '16px',
    }"
  >
    <div class="content">
      <img
        v-if="ifShowImage"
        class="left"
        :style="{ paddingTop: props.signStatus === 2 ? '7px' : '0px' }"
        :src="props.signResourceInfo.icon"
        @error="handleImageError"
      />
      <div v-else class="left"></div>
      <div class="right" :style="{ paddingTop: props.signStatus === 2 ? '0px' : '2px' }">
        <span class="title">{{ props.signResourceInfo.title }}</span>
        <span class="desc">{{ props.signResourceInfo.description }}</span>
        <span v-if="ifChangeLayout" class="footer-desc" style="margin-top: 0px !important"
          >点击立即开通即表示同意<a @click="handleContractClick"
            >《{{ props.signResourceInfo.contractName }}》</a
          ></span
        >
      </div>
    </div>
    <div v-if="props.signStatus === 1 || props.signStatus === 3" class="footer">
      <div style="display: flex; justify-content: flex-end; padding: 0px 24px">
        <div class="card-coupon-bubble">
          <span class="card-coupon-bubble-text">{{ props.signResourceInfo.bubbleText }}</span>
        </div>
      </div>
      <hnr-button
        v-if="ifChangeLayout"
        type="primary"
        style="width: 144px; border-radius: var(--hnr-default-corner-radius-l) !important"
      >
        {{ props.signResourceInfo.button }}
      </hnr-button>

      <hnr-button v-else class="btn" type="primary" @click="handleSignClick">{{
        props.signResourceInfo.button
      }}</hnr-button>
      <span v-if="!ifChangeLayout" class="footer-desc"
        >点击立即开通即表示同意<a @click="handleContractClick"
          >《{{ props.signResourceInfo.contractName }}》</a
        ></span
      >
    </div>
  </div>
</template>
<script setup>
import { defineProps, onMounted, ref } from "vue";
import { goto, report } from "../helpers/native-bridge";
import { setNetwork } from "../helpers/network-helper";
import { isPdfUrlAsync, showPdfUrlNew } from "../helpers/utils";

const props = defineProps({
  ifFoldScreen: {
    type: Boolean,
    default: false,
  },
  isOnResume: {
    type: Boolean,
    default: false,
  },
  location: {
    type: String,
    default: "",
  },
  signStatus: {
    type: Number,
    default: 0,
  },
  signResourceInfo: {
    type: Object,
    default: () => {},
  },
});

const reportSignStatusLists = [];
const ifChangeLayout = ref(false);
const ifShowImage = ref(true);

const handleImageError = () => {
  ifShowImage.value = false;
};

const handleSignClick = () => {
  if (!window?.navigator?.onLine) {
    setNetwork();
    return;
  }
  if (!reportSignStatusLists.includes("alipay_sign_entrace")) {
    reportSignStatusLists.push("alipay_sign_entrace");
    report("wallet_page_click", {
      page_name: "alipay_sign_entrace",
      pageLocation: props.location,
    });
  }
  window.location.href = props.signResourceInfo.signUrl;
};

const handleContractClick = async () => {
  if (!reportSignStatusLists.includes("alipay_sign_protocol")) {
    reportSignStatusLists.push("alipay_sign_protocol");
    report("wallet_page_click", {
      page_name: "alipay_sign_protocol",
      pageLocation: props.location,
    });
  }
  const cUrl = new URL(props.signResourceInfo.contractUrl);
  const isPdfUrl = await isPdfUrlAsync(cUrl);
  if (isPdfUrl) {
    goto(
      showPdfUrlNew(props.signResourceInfo.contractUrl, props.signResourceInfo.contractName),
      false,
    );
  } else goto(props.signResourceInfo.contractUrl, true);
};

onMounted(() => {
  if (props.ifFoldScreen && props.location === "repayment_list") {
    ifChangeLayout.value = true;
  }
  if (props.signStatus !== 0 && !props.isOnResume) {
    report("wallet_page_view", {
      page_name: "alipay_sign_entrace",
      signStatus: props.signStatus,
      pageLocation: props.location,
    });
  }
});
</script>

<style lang="scss">
.left {
  width: 56px;
  height: 56px;

  img {
    width: 56px;
    height: 56px;
  }
}
</style>

<style scoped>
@media (min-width: 719px) {
  .btn {
    min-width: calc(100% - 192px) !important;
  }
}
@media (prefers-color-scheme: dark) {
  .container {
    background: #2d3238 !important;
  }

  .title {
    color: rgba(255, 255, 255, 0.86) !important;
  }

  .desc {
    color: rgba(255, 255, 255, 0.6) !important;
  }

  .footer-desc {
    color: rgba(255, 255, 255, 0.6) !important;
  }
}

a {
  text-decoration: none;
  color: var(--hnr-text-color-secondary-activated) !important;
}

.hnr-button {
  font-size: var(--hnr-body-3);
  font-family: var(--hnr-font-weight-medium);
}

.hnr-MagicOs-9 .hnr-button--larger {
  height: var(--dp28) !important;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 20px 12px 16px;
  background: #e4ebf8;
  border-radius: var(--hnr-default-corner-radius-m);
}

.content {
  flex: 1;
  display: flex;
  gap: 16px;
}

.right {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.title {
  font-size: 15px;
  color: var(--hnr-text-color-primary);
  font-weight: bold;
}

.desc {
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  color: var(--hnr-text-color-secondary);
}

.footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card-coupon-bubble {
  widows: auto !important;
  display: flex;
  align-items: center;
  align-self: center;
  position: relative;
  height: var(--dp12);
  background-color: #fa782d;
  /* background-color: #ffffff;  */
  border-radius: var(--hnr-default-corner-radius-m);
}

.btn {
  border-radius: var(--hnr-default-corner-radius-l) !important;
  min-width: calc(100% - 30px) !important;
}

.card-coupon-bubble::after {
  content: "";
  position: absolute;
  left: 50%; /* 将三角形定位到气泡底部的中间 */
  bottom: calc(0px - var(--dp2)); /* 将三角形移动到气泡外部，负值等于三角形的高度 */
  margin-left: calc(0px - var(--dp3)); /* 三角形宽度的一半，用于居中对齐 */
  border-width: var(--dp3) var(--dp4) 0 var(--dp4); /* 上 左 下 右 */
  border-style: solid;
  border-color: #fa782d transparent transparent transparent;
}

.card-coupon-bubble-text {
  color: var(--hnr-color-primary-dark);
  font-size: 9px;
  font-weight: var(--hnr-font-weight-regular);
  padding: var(--hnr-elements-margin-vertical-XS) var(--hnr-elements-margin-vertical-S);
}

.footer-desc {
  margin-top: 8px;
  font-size: 9px;
  font-weight: var(--hnr-font-weight-regular);
  color: var(--hnr-text-color-secondary);
}
</style>
