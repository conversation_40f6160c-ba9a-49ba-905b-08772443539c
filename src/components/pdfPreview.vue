<script setup>
import { defineProps, defineEmits, ref, watch, onMounted } from "vue";
import VueOfficePdf from "@vue-office/pdf/lib/v3/vue-office-pdf";
import Viewport from "./Viewport.vue";

const props = defineProps({
  isShowContractDetailPopup: { type: Boolean, default: false },
  contractUrl: { type: String, default: "" },
  pdfKey: { type: Number, default: 1 },
});
const emit = defineEmits(["close"]);
const closeFunc = () => {
  emit("close");
};
const isContractDetailPopup = ref(false);
const contentNode = ref();
const containerHeight = ref("10000vh");

const renderedHandler = () => {
  console.log("渲染完成");
  setTimeout(() => {
    if (contentNode.value?.wrapperRef) {
      containerHeight.value = `${contentNode.value?.wrapperRef?.offsetHeight}px`;
    }
  }, 100);
};
const errorHandler = () => {
  console.log("渲染失败");
};
const viewHeight = ref(0);
const viewWidth = ref(0);
const darkMode = ref(false);
onMounted(() => {
  darkMode.value = window.matchMedia("(prefers-color-scheme: dark)").matches;
  viewHeight.value = window.innerHeight - 97 - 108;
  viewWidth.value = window.innerWidth;
});

watch(
  () => props.isShowContractDetailPopup,
  (newValue) => {
    isContractDetailPopup.value = newValue;
  },
  { immediate: true },
);
</script>
<template>
  <hnr-popup
    v-model:show="isContractDetailPopup"
    :before-close="() => true"
    round
    position="bottom"
    class="contract-detail-popup"
  >
    <div class="pull-icon-box">
      <span class="icon-arrow-pull" teleport="hnr-divider" @click="closeFunc"></span>
    </div>
    <div class="contract-con">
      <main>
        <Viewport
          :key="props.pdfKey"
          :view-height="viewHeight"
          :view-width="viewWidth"
          :dark-mode="darkMode"
        >
          <template #default="scope">
            <vue-office-pdf
              ref="contentNode"
              :src="props.contractUrl"
              :style="{ transform: scope.transform, height: containerHeight }"
              @rendered="renderedHandler"
              @error="errorHandler"
            />
          </template>
        </Viewport>
      </main>
    </div>
    <div class="close-btn-box">
      <hnr-button type="primary" standard-width="true" class="close-btn" @click="closeFunc()"
        >关闭</hnr-button
      >
    </div>
  </hnr-popup>
</template>
<style scoped>
.contract-detail-popup {
  height: calc(100vh - 97px);
  display: flex;
  flex-direction: column;
  z-index: 1100 !important;
  user-select: none;
}
.contract-con {
  flex: 1;
  overflow: auto;
}
.pull-icon-box {
  text-align: center;
  padding-bottom: var(--dp10);
}
.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp20);
  color: #d5d5d5;
}
main {
  /* padding: 15px; */
}
.pull-icon {
  width: var(--dp32);
  height: var(--dp18);
}
.close-btn-box {
  padding: 0 var(--hnr-max-padding-start) 0 var(--hnr-max-padding-end);
  text-align: center;
}
.close-btn {
  margin: var(--hnr-elements-margin-vertical-L) 0 var(--hnr-default-padding-bottom-fixed);
}
:deep(.hnr-button--primary) {
  max-width: 100% !important;
}
:deep(.hnr-overlay) {
  z-index: 1100 !important;
}
.contract-content-iframe {
  height: calc(100vh - 240px);
  border-radius: 10px;
}
@media (prefers-color-scheme: dark) {
  :deep(.hnr-popup) {
    background: #2e2e2e;
  }
  .icon-arrow-pull:before {
    color: #ffffff33;
  }
}
:deep(.vue-office-pdf-wrapper) {
  padding: 0 !important;
  background: #ffffff !important;
}
</style>
