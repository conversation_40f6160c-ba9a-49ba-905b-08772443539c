<script setup>
import { defineProps, defineEmits, ref, watch, computed, onMounted } from "vue";
import { getCurrentCpInfo } from "../helpers/configUtils";
import { supplierIdMap } from "../helpers/constants";

const props = defineProps({
  isShowContractDetailPopup: { type: Boolean, default: false },
  contractContent: { type: String, default: "" },
  contractHtml: { type: String, default: "" },
});
const emit = defineEmits(["close"]);
const closeFunc = () => {
  emit("close");
  return true;
};
const isContractDetailPopup = ref(false);
const iframeRef = ref(null);
const isDarkMode = ref(false);
const supplierId = ref(0);

watch(
  () => props.isShowContractDetailPopup,
  (newValue) => {
    isContractDetailPopup.value = newValue;
  },
  { immediate: true },
);

const filterHtml = computed(() => {
  if (props.contractHtml) {
    return props.contractHtml;
  }
  if (props.contractContent.includes("<html>")) {
    return props.contractContent;
  }
  return null;
});

onMounted(async () => {
  isDarkMode.value = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
  const res = await getCurrentCpInfo();
  supplierId.value = res?.supplierId || supplierId.value;
});
</script>
<template>
  <hnr-popup
    v-model:show="isContractDetailPopup"
    :before-close="closeFunc"
    round
    position="bottom"
    class="contract-detail-popup"
  >
    <div class="pull-icon-box">
      <span class="icon-arrow-pull" teleport="hnr-divider" @click="closeFunc"></span>
    </div>
    <div class="contract-con">
      <main>
        <iframe
          v-if="isContractDetailPopup"
          ref="iframeRef"
          class="contract-content-iframe"
          :style="{ background: isDarkMode && supplierId === supplierIdMap.ppd ? 'black' : '' }"
          :src="!props.contractContent.includes('<html>') ? props.contractContent : null"
          :srcdoc="filterHtml"
          frameborder="0"
          scrolling="yes"
          width="100%"
          loading="lazy"
        ></iframe>
      </main>
    </div>
    <div class="close-btn-box">
      <hnr-button type="primary" standard-width="true" class="close-btn" @click="closeFunc()"
        >关闭</hnr-button
      >
    </div>
  </hnr-popup>
</template>
<style scoped>
.contract-detail-popup {
  max-height: calc(100vh - 97px);
  display: flex;
  flex-direction: column;
  z-index: 1100 !important;
  user-select: none;
}
.contract-con {
  flex: 1;
  overflow: auto;
}
.pull-icon-box {
  text-align: center;
  padding-bottom: var(--dp10);
}
.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp20);
  color: #d5d5d5;
}
main {
  padding: 15px;
  padding-bottom: 0;
}
.pull-icon {
  width: var(--dp32);
  height: var(--dp18);
}
.close-btn-box {
  padding: 0 var(--hnr-max-padding-start) 0 var(--hnr-max-padding-end);
  text-align: center;
}
.close-btn {
  margin: var(--hnr-elements-margin-vertical-L) 0 var(--hnr-default-padding-bottom-fixed);
}
:deep(.hnr-button--primary) {
  max-width: 100% !important;
}
:deep(.hnr-overlay) {
  z-index: 1100 !important;
}
.contract-content-iframe {
  height: calc(100vh - 240px);
  border-radius: 10px;
}
@media (prefers-color-scheme: dark) {
  :deep(.hnr-popup) {
    background: #2e2e2e;
  }
  .icon-arrow-pull:before {
    color: #ffffff33;
  }
}
</style>
