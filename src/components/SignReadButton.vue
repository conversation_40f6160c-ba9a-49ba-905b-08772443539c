<template>
  <div class="btn-line">
    <hnr-button
      v-if="(!theFourthType && time === 0) || (theFourthType && time === 0 && PullDownComplete)"
      type="primary"
      standard-width="true"
      class="btn-area-btn"
      loading-text="加载中..."
      :loading="nextLoading"
      :disabled="(isPullDown === 1 && !PullDownComplete) || nextLoading"
      :style="isPullDown === 1 && !PullDownComplete ? { opacity: '0.38' } : ''"
      @click="onClick"
      >{{
        isPullDown === 1 && !PullDownComplete ? "请滑动阅读至底部" : "我已阅读并同意以上协议"
      }}</hnr-button
    >
    <hnr-button
      v-if="time > 0 && isPullDown === 0 && !theFourthType"
      type="primary"
      standard-width="true"
      class="btn-area-btn"
      disabled
      style="opacity: 0.38"
      >{{ `请阅读以上协议（${time}s)` }}</hnr-button
    >
    <hnr-button
      v-if="theFourthType && (time > 0 || !PullDownComplete)"
      type="primary"
      standard-width="true"
      class="btn-area-btn"
      disabled
      style="opacity: 0.38"
      >请滑动阅读至底部{{ time > 0 ? `（${time}s)` : "" }}</hnr-button
    >
  </div>
</template>
<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from "vue";

const props = defineProps({
  forcePullDown: {
    type: Number,
  },
  readAlready: {
    type: Boolean,
  },
  mandatoryReadingSeconds: {
    type: Number,
  },
});

const isPullDown = ref(null);
const PullDownComplete = ref(null);
const time = ref(null);
const theFourthType = ref(null);
const nextLoading = ref(false);

// 定义emit事件
const emits = defineEmits(["click"]);

// 点击图片时触发的方法
const onClick = () => {
  if (window.navigator.onLine) {
    nextLoading.value = true;
  }
  emits("click");
};

function extractBracketedContent(str) {
  const startIndex = str.indexOf("{");
  if (startIndex === -1) {
    return "{}";
  }
  const endIndex = str.indexOf("}", startIndex);
  if (endIndex === -1) {
    return "{}";
  }
  return str.substring(startIndex, endIndex + 1);
}

watch(
  () => props.mandatoryReadingSeconds,
  () => {
    const str = extractBracketedContent(window.name);
    const obj = JSON.parse(str);
    time.value = obj.readingSeconds || props.mandatoryReadingSeconds;
    isPullDown.value = props.forcePullDown;
    if (props.mandatoryReadingSeconds > 0 && props.forcePullDown === 1) {
      theFourthType.value = true;
    }
    if (props.mandatoryReadingSeconds > 0) {
      const intervalId = setInterval(() => {
        if (time.value > 0) {
          time.value -= 1;
          obj.readingSeconds = time.value;
        } else {
          clearInterval(intervalId);
          obj.readingSeconds = 0;
        }
        window.name = JSON.stringify(obj);
      }, 1000);
    }
  },
  {
    deep: true,
  },
);
watch(
  () => props.readAlready,
  () => {
    PullDownComplete.value = props.readAlready;
  },
  {
    deep: true,
  },
);
onMounted(() => {
  window.nextLoadingStatus = function nextLoadingStatus(val) {
    nextLoading.value = val;
  };
});
</script>
<style scoped>
.btn-line {
  text-align: center;
}
.btn-area-btn {
  /* width: 100%; */
  margin-top: 8px;
  /* max-width: none !important; */
}
</style>
