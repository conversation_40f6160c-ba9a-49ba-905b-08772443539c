<template>
  <div :style="{ 'height: 64px': isLoanPage }">
    <no-ssr>
      <div
        v-show="selectedcard"
        :class="{ 'plan-item': true, 'plan-item-loan': isLoanPage }"
        @click="closeadd"
      >
        <div
          :class="{ 'plan-item-left': true, 'plan-item-left-loan': isLoanPage }"
          style="width: calc(100% - 16px)"
        >
          <div
            class="plan-item-left-top"
            :style="{ 'align-items': subheading ? 'none' : 'center' }"
          >
            <span class="plan-item-left-top-left" style="width: 30%; min-width: 70px !important">
              {{ label }}
            </span>
            <div class="plan-item-left-top-right">
              <div :class="subheading ? 'plan-item-right-top' : 'plan-item-right-top1'">
                {{ selectedcard?.bankName }}({{ selectedcard?.bankCardNo }})
              </div>
            </div>
          </div>
          <div v-show="subheading" class="plan-item-left-bottom">
            <div v-if="isReservePhoneError" style="color: #ff0000">请更换银行卡还款</div>
            <div v-else>
              <span>{{ `${selectedcard?.singleLimit}${selectedcard?.dotString}` }}</span>
              <span style="white-space: nowrap">{{ `${selectedcard?.dayLimit}` }}</span>
            </div>
          </div>
        </div>
        <div class="plan-item-right-top1" style="width: 16px">
          <span class="icon-right"></span>
        </div>
      </div>

      <div
        v-show="!selectedcard"
        :class="{ 'plan-item': true, 'plan-item-loan': isLoanPage }"
        @click="closeadd"
      >
        <div
          :class="{ 'plan-item-left': true, 'plan-item-left-loan': isLoanPage }"
          style="width: 100%"
        >
          <div
            class="plan-item-left-top"
            :style="{ 'align-items': subheading ? 'none' : 'center' }"
          >
            <span class="plan-item-left-top-left" style="width: 30%; min-width: 70px !important">
              {{ label }}
            </span>
            <div class="plan-item-right">
              <hnr-button
                v-if="loadreq"
                loading
                type="text"
                style="display: flex; justify-content: flex-end; min-width: var(--dp40)"
              />
              <div v-else>
                <div v-if="noClick" class="noCardClass">未添加<span class="icon-right"></span></div>
                <div v-if="!noClick" class="noCardClass" @click.stop="clickAgain">点击重试</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- // 用户银行卡列表 -->
      <hnr-popup
        v-model:show="showdialog"
        v-touch:swipe.bottom="bankListSwipe"
        position="bottom"
        style="
          border-radius: var(--hnr-default-corner-radius-l) var(--hnr-default-corner-radius-l) 0 0;
        "
        class="bank-list-scroll-top"
      >
        <div class="dialogbg">
          <div class="pull-icon-box">
            <span class="icon-arrow-pull" teleport="hnr-divider" @click="back"></span>
          </div>
          <div class="topleft">{{ title }}</div>
          <div
            ref="bankListScrollItem"
            class="bank-list-scroll bank-list-scroll-item"
            @touchstart="bankListTouchStart"
            @touchend="bankListTouchEnd"
          >
            <hnr-radio-group v-model="selectedCardId" class="bank-list-scroll-item">
              <hnr-cell-group class="scroll-container1 bank-list-scroll-item" :with-margin="false">
                <hnr-cell
                  v-for="(i, index) in cards"
                  :key="i.bankCardId"
                  style="min-height: 64px"
                  :title="i.bankName + '(' + i.bankCardNo + ')'"
                  center
                  :border="index !== cards.length - 1"
                  clickable
                  class="bank-list-scroll-item"
                  @click="confirm(i)"
                >
                  <template #value>
                    <hnr-radio :name="i.bankCardId" />
                  </template>
                  <template #label>
                    <span class="selftcell-label">{{ i.singleLimit }}{{ i.dotString }}</span
                    ><span class="selftcell-label">{{ i.dayLimit }}</span>
                  </template>
                </hnr-cell>
                <hnr-cell class="bank-list-scroll-item" @click="openaddfirst">
                  <template #prefix>
                    <img :src="addbankicon" alt class="imgs bank-list-scroll-item" />
                  </template>
                  <template #title>
                    <div>添加新银行卡</div>
                  </template>
                </hnr-cell>
                <div v-if="showBankTransferCard" style="margin-top: 12px">
                  <div class="other-methods-title">其他方式</div>
                  <hnr-cell title="银行APP转账" @click="handleBankAppTransfer">
                    <template #value>
                      <span class="icon-right"></span>
                    </template>

                    <template #label>
                      <span>无额度限制，推荐大额还款时使用</span>
                    </template>
                  </hnr-cell>
                </div>
              </hnr-cell-group>
            </hnr-radio-group>
          </div>
          <div v-if="false" class="dialogfooter">
            <hnr-button type="primary" style="max-width: 100%; width: 100%" @click="confirm"
              >确定</hnr-button
            >
          </div>
        </div>
      </hnr-popup>
      <!-- 添加银行卡页面 -->
      <hnr-popup
        v-model:show="showaddcard"
        style="max-width: 100%; z-index: 1001"
        :overlay="false"
        position="bottom"
        class="myPopup"
      >
        <hnr-nav-bar class="nav-padding-top" transparent="true" title="添加银行卡">
          <template #left>
            <icsvg-public-back-filled @click="backOne"></icsvg-public-back-filled>
          </template>
          <template #right>
            <hnr-button
              style="color: var(--hnr-text-color-primary-activated)"
              size="mini"
              type="text"
              @click="opensupport"
              >查看支持银行</hnr-button
            >
          </template>
        </hnr-nav-bar>
        <div>
          <div
            style="
              width: calc(
                100vw - var(--hnr-elements-margin-vertical-M2) -
                  var(--hnr-elements-margin-vertical-M2)
              );
              margin: 0 auto;
              margin-bottom: var(--hnr-elements-margin-vertical-L);
              margin-top: var(--hnr-elements-margin-vertical-M2);
            "
          >
            <hnr-card type="empty" style="width: 100%">
              <div
                style="
                  display: flex;
                  padding: var(--hnr-elements-margin-vertical-M)
                    var(--hnr-elements-margin-horizontal-M2);
                "
              >
                <div
                  style="width: var(--dp16); margin-right: 6px; display: flex; align-items: center"
                >
                  <img src="/secure.svg" />
                </div>
                <div
                  style="
                    font-weight: var(--hnr-font-weight-regular);
                    font-size: var(--hnr-body-3);
                    color: var(--hnr-text-color-primary-activated);
                  "
                >
                  您的银行卡号、银行预留手机号及验证码将用于绑定添加银行卡，我们将严格遵守相关法律法规保护您的个人信息。
                </div>
              </div>
            </hnr-card>
          </div>
          <div>
            <hnr-field
              ref="selfbankno"
              v-model="bankInfo.bankCardNo"
              type="number"
              class="addYhkInput"
              :formatter="checkstrlong"
              border
              placeholder="本人银行卡号"
              @update:model-value="cardinput"
              @blur="cardBlur"
              @input="cardInputChange"
            >
              <template v-if="bankCardNoGuide" #button>
                <hnr-button size="mini" type="text" @click="handleShowPopupClick">
                  查卡号
                </hnr-button>
              </template>
            </hnr-field>
            <div class="errmsg">{{ cardErrorMessage }}</div>
            <hnr-field
              v-model="bankInfo.mobileNo"
              :type="ifChangeInputType ? 'number' : 'text'"
              clearable
              clear-icon="/wallet-loan-web/pages/loan/close_doublelayer_filled.svg"
              class="addYhkInput"
              :formatter="checkNumber"
              border
              placeholder="银行预留手机号"
              @update:model-value="phoneinput"
              @blur="numberblur"
            />
            <div v-if="ifShowMsg" class="toastmsg">输入银行预留手机号</div>
            <div class="errmsg">{{ phoneErrorMessage }}</div>
            <hnr-field
              ref="Captcha"
              v-model="bankInfo.smsCode"
              type="number"
              :formatter="checkNumber"
              class="addYhkInput"
              border
              placeholder="验证码"
              @update:model-value="authinput"
              @blur="codeBlur"
            >
              <template #button>
                <hnr-button
                  :disabled="
                    countdown > 0 ||
                    !(
                      bankInfo.bankCardNo &&
                      bankInfo.mobileNo &&
                      !(cardErrorMessage || phoneErrorMessage)
                    ) ||
                    messageDisabled
                  "
                  size="mini"
                  type="text"
                  @click="startCountdown"
                >
                  {{ countdown ? `${countdown}s` : "获取验证码" }}
                </hnr-button>
              </template>
            </hnr-field>
            <div class="errmsg">{{ authErrorMessage }}</div>
          </div>
          <div v-show="showbutton" class="addYhkBtm">
            <div
              v-show="isShowContract"
              style="
                display: flex;
                margin: 0 var(--hnr-elements-margin-horizontal-L2);
                align-items: center;
              "
            >
              <hnr-checkbox v-model="ischoose" style="padding-right: var(--dp8)"> </hnr-checkbox>
              <div>
                <span class="addYhkAggre">已阅读并同意</span>
                <span
                  v-for="(item, index) in contract.contractList"
                  :key="item.contractName"
                  style="
                    color: var(--hnr-text-color-primary-activated);
                    height: var(--dp24);
                    line-height: var(--dp24);
                    font-size: var(--hnr-body-3);
                  "
                  @click="getContractDetail(item)"
                >
                  {{ item.contractName
                  }}<span v-if="index < contract.contractList.length - 1">、</span>
                </span>
              </div>
            </div>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                width: calc(100vw - 48px);
              "
            >
              <hnr-button
                :disabled="
                  bankInfo.smsCode === '' ||
                  bankInfo.mobileNo === '' ||
                  bankInfo.bankCardNo === '' ||
                  nextDisable
                "
                type="primary"
                class="content-bottom-btn-next"
                :loading="loading"
                loading-text="加载中..."
                @click="verifySms()"
                >下一步</hnr-button
              >
            </div>
          </div>
        </div>
      </hnr-popup>
      <!-- 协议半弹窗 -->
      <hnr-dialog
        v-model:show="ifPressNextStep"
        class="contractDialog"
        title="请阅读并同意以下条款"
        show-filter="true"
        :before-close="() => false"
        style="z-index: 9998"
      >
        <span>
          <a
            v-for="(item, index) in contract.contractList"
            :key="item.id"
            style="font-size: var(--hnr-body-1)"
            @click="getContractDetail(item)"
          >
            {{ `${item.contractName}${index < contract.contractList.length - 1 ? "、" : ""}` }}
          </a>
        </span>
        <span
          style="
            margin-top: var(--dp8);
            font-size: var(--hnr-body-1);
            color: var(--hnr-color-primary);
          "
          >点击“同意并继续”即代表您同意协议内容并提交申请。</span
        >
        <template #footer>
          <div style="width: 100%; display: flex; gap: var(--dp12)">
            <hnr-button
              type="default"
              style="min-width: calc(50% - var(--dp6))"
              :disabled="isLoading"
              @click="handleCancelClick"
              >取消</hnr-button
            >
            <hnr-button
              :loading="isLoading"
              type="primary"
              style="min-width: calc(50% - var(--dp6))"
              @click="verifySms(true)"
              >同意并继续</hnr-button
            >
          </div>
        </template>
      </hnr-dialog>
      <!-- 查看卡号页 -->
      <cardNoPopup
        :bank-card-no-guide="bankCardNoGuide"
        :if-show-card-no-popup="ifShowCardNoPopup"
        @update:visible="handleCardNoPopupBack"
      />
      <!-- 查看支持的银行 -->
      <hnr-popup
        v-model:show="showsupport"
        position="bottom"
        class="myPopupnot"
        style="z-index: 9999"
      >
        <hnr-sticky>
          <hnr-nav-bar class="nav-padding-top" transparent="true" title="支持银行">
            <template #left>
              <icsvg-public-back-filled @click="backOne"></icsvg-public-back-filled>
            </template>
          </hnr-nav-bar>
          <div v-if="!supportLoadingStatus && !supportErrorStatus" class="bankContentTop">
            <hnr-row justify="space-between" style="width: 100%">
              <!-- <hnr-col span="6">
                <span>银行</span>
              </hnr-col>
              <hnr-col span="9">
                <span>单笔限额</span>
              </hnr-col>
              <hnr-col span="9">
                <span>每日限额</span>
              </hnr-col> -->
              <div style="flex: 1; margin-right: var(--hnr-elements-margin-horizontal-M2)">
                银行
              </div>
              <div
                v-if="ifShowSingleLimit"
                style="flex: 1; margin-right: var(--hnr-elements-margin-horizontal-M2)"
              >
                单笔限额
              </div>
              <div v-if="ifShowDayLimit" style="flex: 1">每日限额</div>
            </hnr-row>
          </div>
        </hnr-sticky>
        <hnr-list v-if="bankList.length > 0" class="scroll-container">
          <div
            v-for="(i, index) in bankList"
            :key="index"
            style="display: flex; flex-direction: column"
          >
            <div class="list-item">
              <hnr-row justify="flex-start" style="width: 100%">
                <div
                  style="
                    flex: 1;
                    display: flex;
                    align-items: center;
                    word-break: break-all;
                    padding: 12px 0;
                    margin-right: var(--hnr-elements-margin-horizontal-M2);
                  "
                >
                  {{ i.bankName }}
                </div>
                <div
                  v-if="ifShowSingleLimit"
                  style="
                    flex: 1;
                    display: flex;
                    align-items: center;
                    word-break: break-all;
                    padding: 12px 0;
                    margin-right: var(--hnr-elements-margin-horizontal-M2);
                  "
                >
                  {{ addCommas(i.singleLimit) }}
                </div>
                <div
                  v-if="ifShowDayLimit"
                  style="
                    flex: 1;
                    display: flex;
                    align-items: center;
                    word-break: break-all;
                    padding: 12px 0;
                  "
                >
                  <span>{{ addCommas(i.dayLimit) }}</span>
                </div>
              </hnr-row>
            </div>
            <hnr-divider class="supportdivider" line />
          </div>
        </hnr-list>
        <!-- 支持的银行搜索为空 -->
        <div v-else class="notSreachContent">
          <reloading-component
            :loading-status="supportLoadingStatus"
            :error-status="supportErrorStatus"
            @try-click="trySupportClick"
          />
          <img
            v-show="!supportLoadingStatus && !supportErrorStatus"
            :src="useloadicon"
            alt=""
            style="margin-bottom: var(--hnr-elements-margin-vertical-M2)"
          />
          <div v-show="!supportLoadingStatus && !supportErrorStatus" class="notSearch">
            无相关搜索结果
          </div>
        </div>
      </hnr-popup>
      <!-- 添加银行卡结果页面 -->
      <hnr-popup v-model:show="showresult" position="bottom" class="myPopup">
        <hnr-nav-bar transparent="true" class="nav-padding-top">
          <template #left>
            <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
          </template>
        </hnr-nav-bar>
        <div class="loading">
          <div class="pubimg">
            <img class="icon" :src="resultWeb.code === 0 ? successicon : failicon" />
          </div>
          <div class="suchead1">{{ resultWeb.code === 0 ? "添加成功" : "添加失败" }}</div>
          <div class="suchead2">{{ messageInfoNew }}</div>
          <div class="footer">
            <hnr-button type="primary" class="content-bottom-btn" @click="onClickLeft"
              >完成
            </hnr-button>
          </div>
        </div>
      </hnr-popup>
      <!-- 协议签署页面 -->
      <!-- <contract-detail-popup
        :is-show-contract-detail-popup="contract.isShowContractDetailPopup"
        :contract-name="contract.contractName"
        :contract-content="contract.contractContent"
        :contract-error-status="contractErrorStatus"
        :contract-loading-status="contractLoadingStatus"
        @close="closecontract"
        @try="selectContract"
      /> -->
      <contract-detail-popup
        v-show="contract.isShowContractDetailPopup"
        style="z-index: 9999 !important"
        :is-show-contract-detail-popup="contract.isShowContractDetailPopup"
        :contract-content="contract.contractUrl"
        :contract-html="contract.contractHtml"
        @close="closecontract"
      />
      <hnr-dialog
        v-model:show="isShowErrorDialog"
        style="z-index: 9999"
        button-direction="row"
        :lazy-render="false"
        show-cancel-button
        cancel-button-text="取消"
        show-confirm-button
        confirm-button-type="primary"
        :confirm-button-text="isOnline ? '重试' : '设置网络'"
        cancel-button-type="text"
        @confirm="handleClickConfirm"
        @cancel="handleClickCancel"
      >
        <template #title> 添加失败 </template>
        <template #default>{{
          isServerError ? "无法连接服务器，请重试。" : "网络未连接，请检查网络设置。"
        }}</template>
      </hnr-dialog>
    </no-ssr>
  </div>
</template>
<script setup>
import { ref, onMounted, nextTick, computed, watch, defineProps, defineEmits } from "vue";
import lodash from "lodash";
import Big from "big.js";
import { showToast } from "@hihonor/hnr/dist/hnr.es.min";
import { getClientConfigInfo, getCurrentCpInfo } from "../../helpers/configUtils";
import { request, amountNumberFormat, handleHtmlText, fetchHtml } from "../../helpers/utils";
import { back, regNativeEvent, report } from "../../helpers/native-bridge";
import { setNetwork } from "../../helpers/network-helper";
import successicon from "/GeneralStatus_GreenSuccess.svg";
import failicon from "/GeneralStatus_RedFailed.svg";
import addbankicon from "/addBank.svg";
import useloadicon from "/repayment/useLoad.svg";
import reloadingComponent from "../reloadingComponent.vue";
import IcsvgPublicBackFilled from "../svg/icsvgPublicBackFilled.vue";
import cardNoPopup from "./cardNoPopup.vue";
import { getCurrentStore, supplierIdMap } from "../../helpers/constants";
import {
  reportCheckSupportBankClick,
  reportCheckCardNoClick,
  reportBankTransferClick,
  reportBankTransferView,
} from "./report";
import ContractDetailPopup from "../ContractDetailPopup.vue";

const ifShowCardNoPopup = ref(false);
const countdown = ref(0);
// 页面字段
const bankInfo = ref({
  bankCardNo: "",
  mobileNo: "",
  smsCode: "",
});
// 添加银行卡
const showaddcard = ref(false);
const bankCardNoGuide = ref(null);
const mobileNo = ref(null);
const ifShowMsg = ref(false);
const ifChangeInputType = ref(false);

// 支持银行页
const showsupport = ref(false);
const checkinfo = ref(null);
// 是否选中同意
// 解决上浮问题
const showbutton = ref(true);
const ischoose = ref(false);
// 展示结果页
const showresult = ref(false);
// 支持行列表
const bankList = ref([]);
// 支持银行搜索内容
const showdialog = ref(false);
// todos 选中卡信息，初始化后默认第一张
const selectedcard = ref(null);
// 选中卡银行id
const selectedid = ref(0);
// 选中卡银行id
const selectedCardId = ref(0);
// 手机校验
const phoneErrorMessage = ref("");
// 卡号校验
const cardErrorMessage = ref("");
// 验证码校验
const authErrorMessage = ref("");
// 通讯加载中
const loadreq = ref(true);
const selfbankno = ref(null);
const Captcha = ref(null);
const cards = ref(null);
const noClick = ref(true);
const messageInfoNew = ref("");
const resultWeb = ref({});
const arrt = ref([]);
const resultFlag = ref(false); // 到达结果页标志
const messageDisabled = ref(false); // 获取验证码点击
const loading = ref(false);
const supplierId = ref(0);
const ifPressNextStep = ref(false);
const isLoading = ref(false);

const ifShowSingleLimit = computed(() => {
  const len = bankList.value.filter((item) => !item.singleLimit)?.length;
  return !(len === bankList.value.length);
});

const ifShowDayLimit = computed(() => {
  const len = bankList.value.filter((item) => !item.dayLimit)?.length;
  return !(len === bankList.value.length);
});

const nextDisable = computed(
  () =>
    cardErrorMessage.value !== "" ||
    phoneErrorMessage.value !== "" ||
    authErrorMessage.value !== "",
);
// 支持银行加载状态
const supportLoadingStatus = ref(false);
const supportErrorStatus = ref(false);
const contractLoadingStatus = ref(false);
const contractErrorStatus = ref(true);
// 支持银行点击重试
const errorCode = ref(null);

const props = defineProps({
  label: {
    type: String,
    default: "银行",
  },
  title: {
    type: String,
    default: "我的银行卡",
  },
  subheading: {
    type: Boolean,
    default: true,
  },
  applyNo: {
    type: String,
    default: "",
  },
  agree: {
    type: String,
    default: "false",
  },
  isLoanPage: {
    type: Boolean,
    required: false,
    default: false,
  },
  isReservePhoneError: {
    type: Boolean,
    default: false,
  },
  showBankTransferCard: {
    type: Boolean,
    default: false,
  },
});

const trySupportClick = async () => {
  supportErrorStatus.value = false;
  supportLoadingStatus.value = true;
  const result = await request(
    "/loan/api/bankcard/supportList",
    { outOrderNo: props.applyNo },
    { mock: false },
  );
  if (result.code === 0) {
    bankList.value = result.data || [];
    bankList.value.forEach((item) => {
      Object.assign(item, {
        singleLimit:
          typeof item.singleLimit === "number" && item.singleLimit !== -1
            ? ` ¥ ${Big(item.singleLimit || 0).div(100)}`
            : item.singleLimit,
        dayLimit:
          typeof item.dayLimit === "number" && item.dayLimit !== -1
            ? ` ¥ ${Big(item.dayLimit || 0).div(100)}`
            : item.dayLimit,
      });
    });
    supportLoadingStatus.value = false;
    supportErrorStatus.value = false;
  } else {
    supportLoadingStatus.value = false;
    supportErrorStatus.value = true;
  }
};
const inputReportStatus = {
  bankCardNo: false,
  mobileNo: false,
  smsCode: false,
};

const $emit = defineEmits([
  "confirm",
  "bankinfo",
  "emitReport",
  "showToast",
  "goToTransferRepayPage",
]);

const contract = ref({
  contractName: "",
  contractContent: "",
  isShowContractDetailPopup: false,
  contractUrl: "",
  contractHtml: "",
});

// 是否展示协议签署组件
const isShowContract = computed(() => {
  return !!contract.value.contractList?.length;
});

// 支持银行卡页千分位格式化
const addCommas = (num) => {
  if (num === -1) {
    return "无限额";
  }

  if (!num) {
    return "--";
  }
  return ` ¥ ${(num.substring(2) / 1).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
};
const closeContractPopup = () => {
  back();
};

const handleSingleLimitStr = (item, param) => {
  if (typeof item[param] === "number" && item[param] !== -1) {
    return `单笔限额 ¥ ${amountNumberFormat(Big(item[param] || 0).div(100), 0)}`;
  }
  if (item[param] === -1) {
    return "单笔无限额";
  }
  return "";
};

const handleDayLimitStr = (item, param) => {
  if (typeof item[param] === "number" && item[param] !== -1) {
    return `每日限额 ¥ ${amountNumberFormat(Big(item[param] || 0).div(100), 0)}`;
  }
  if (item[param] === -1) {
    return "每日无限额";
  }
  return "";
};

const updateCards = (v) => {
  cards.value = v;
  cards.value.forEach((item) => {
    Object.assign(item, {
      singleLimit: handleSingleLimitStr(item, "singleLimit"),
      dotString: item.singleLimit && item.dayLimit ? "，" : "",
      dayLimit: handleDayLimitStr(item, "dayLimit"),
    });
  });
  if (getCurrentStore()?.info?.bankCardId) {
    cards.value?.forEach((card, idx) => {
      if (card.bankCardId === getCurrentStore().info.bankCardId) {
        selectedid.value = idx;
      }
    });
  }
  selectedcard.value = cards.value?.[selectedid.value];
  selectedCardId.value = selectedcard.value?.bankCardId;
  $emit("bankinfo", selectedcard.value);
};
const reqData = async () => {
  // result[0]:dxm银行卡支持清单
  // result[1]:用户银行卡清单
  // result[2]:用户信息查询，获取手机号用
  loadreq.value = true;
  const reqList = [
    request("/loan/api/bankcard/supportList", { outOrderNo: props.applyNo }, { mock: false }),
    // request('/loan/api/user/getUserRealNameInfo', {}, { mock: false }),
  ];
  if (!cards.value || errorCode.value) {
    reqList.push(
      request("/loan/api/bankcard/list", { outOrderNo: props.applyNo }, { mock: false }),
    );
  } else {
    updateCards(cards.value);
  }
  const result = await Promise.all(reqList);
  loadreq.value = false;
  if ((!cards.value || errorCode.value) && result?.[1]?.code === 0) {
    updateCards(result?.[1]?.data || []);
  } else if (!cards.value && result?.[1]?.code !== 0) {
    noClick.value = false;
  }
  if (result[0].code === 0) {
    bankList.value = result[0].data;
    bankList.value.forEach((item) => {
      Object.assign(item, {
        singleLimit:
          typeof item.singleLimit === "number" && item.singleLimit !== -1
            ? ` ¥ ${Big(item.singleLimit || 0).div(100)}`
            : item.singleLimit,
        dayLimit:
          typeof item.dayLimit === "number" && item.dayLimit !== -1
            ? ` ¥ ${Big(item.dayLimit || 0).div(100)}`
            : item.dayLimit,
      });
    });
  } else {
    noClick.value = false;
    supportLoadingStatus.value = false;
    supportErrorStatus.value = true;
  }

  if (result[1].code === 0) {
    noClick.value = true;
  }
  ischoose.value = props.agree !== "false";
  // 弹出软键盘就隐藏下方按钮
  const originalheight = window.innerHeight;
  window.addEventListener(
    "resize",
    () => {
      const resizeheight = window.innerHeight;
      if (resizeheight - 0 < originalheight - 0) {
        showbutton.value = false;
      } else {
        showbutton.value = true;
      }
    },
    true,
  );
  ref(true);
  window.addEventListener("hashchange", () => {
    arrt.value.push(window.location.hash.substring(1));
    showaddcard.value = false;
    showsupport.value = false;
    showdialog.value = false;
    showresult.value = false;
    ifShowCardNoPopup.value = false;
    if (window.location.hash) {
      switch (window.location.hash.substring(1)) {
        case "add":
          showaddcard.value = !resultFlag.value;
          showdialog.value = false;
          setTimeout(() => {
            selfbankno.value.focus();
          }, 600);
          break;
        case "support":
          showaddcard.value = true;
          showsupport.value = true;
          break;
        case "home":
          resultFlag.value = false; // 清除到达结果页标志
          if (noClick.value) showdialog.value = true;
          break;
        case "result":
          showresult.value = true;
          resultFlag.value = true; // 到达了结果页
          break;
        case "cardNo":
          showaddcard.value = true;
          ifShowCardNoPopup.value = true;
          break;
        default:
      }
    }
  });
};
// 关闭半弹窗时退出home的哈希
watch(showdialog, (newVal, oldVal) => {
  if (
    !(showaddcard.value || showresult.value || showsupport.value || ifShowCardNoPopup.value) &&
    !newVal &&
    oldVal &&
    window.location.hash &&
    window.location.hash.substring(1)
  ) {
    back();
  }
});
const clickAgain = async () => {
  await reqData();
};

const selectContract = async () => {
  // 查询协议
  contractErrorStatus.value = false;
  contractLoadingStatus.value = true;
  const resp = await request("/loan/api/contract/v2/list", { type: 2 }, { mock: false });
  if (resp?.code === 0 && resp?.data?.contractList?.length > 0) {
    contract.value.contractList = resp.data.contractList;
    contractErrorStatus.value = false;
    contractLoadingStatus.value = false;
  } else {
    contractLoadingStatus.value = false;
    contractErrorStatus.value = true;
  }
};

const getMobileNo = async () => {
  const store = getCurrentStore();
  if (store?.creditInfo?.mobileNo) {
    mobileNo.value = store.creditInfo.mobileNo;
  } else {
    const res = await request("/loan/api/credit/info", {}, { mock: false });
    if (res.code !== 0) {
      return;
    }
    mobileNo.value = res.data?.mobileNo;
  }

  if (mobileNo.value) {
    ifShowMsg.value = true;
    ifChangeInputType.value = false;
  }
  if (!mobileNo.value) ifChangeInputType.value = true;
  bankInfo.value.mobileNo = mobileNo.value;
};

onMounted(async () => {
  const supplierInfo = await getCurrentCpInfo();
  supplierId.value = supplierInfo?.supplierId || supplierId.value;
  const res = await getClientConfigInfo();
  if (res && res.bankCardNoGuide) bankCardNoGuide.value = res.bankCardNoGuide;
  cards.value = props.initialCards;
  // 查询协议
  selectContract();
  // 初始化数据
  reqData();
});

const handleCardNoPopupBack = (value) => {
  if (value !== undefined) {
    ifShowCardNoPopup.value = value;
    return;
  }
  if (window.location.hash === "#cardNo") {
    back();
  }
};

const backOne = () => {
  if (window.location.hash === "#add") {
    report("wallet_page_click", {
      click_name: "loan_add_bankcard_back",
    });
  }
  back();
};

const backMore = (n = 2) => {
  let num = 0;
  const timer = setInterval(() => {
    if (num < n) {
      num += 1;
      back();
    } else {
      num = 0;
      clearInterval(timer); // 清除定时器
    }
  });
};
async function getContractDetail(contractItem) {
  const selected = contractItem;
  contract.value.contractHtml = "";
  contract.value.contractUrl = "";
  if (!contractItem) {
    ifPressNextStep.value = true;
    report("wallet_page_view", { page_name: "agree_bind_card_popup" });
    return;
  }
  if (selected?.contractContent) {
    contract.value.contractHtml = handleHtmlText(selected.contractContent);
    contract.value.contractUrl = "";
  } else if (selected?.contractUrl) {
    if (supplierId.value !== supplierIdMap.ppd) {
      contract.value.contractHtml = await fetchHtml(
        `${window.location.origin}/wallet-loan-web/uri?href=${selected.contractUrl}`,
      );
      contract.value.contractUrl = "";
    } else {
      contract.value.contractUrl = selected.contractUrl;
      contract.value.contractHtml = "";
    }
  }
  contract.value.isShowContractDetailPopup = true;
}

function closecontract() {
  contract.value.isShowContractDetailPopup = false;
  contract.value.contractUrl = "";
  contract.value.contractHtml = "";
}

regNativeEvent("onBack", () => {
  if (ifPressNextStep.value || contract.value.isShowContractDetailPopup) {
    if (contract.value.isShowContractDetailPopup) {
      closecontract();
      return;
    }
    if (ifPressNextStep.value) {
      ifPressNextStep.value = false;
    }
    return;
  }
  if (window.location.hash === "#add") {
    report("wallet_page_click", {
      click_name: "loan_add_bankcard_back",
    });
  }
  // 从结果页返回时,判断add页加载了几次
  if (window.location.hash.substring(1) === "result") {
    if (Number(window.history.length) > 4) {
      backMore(Number(window.history.length) - 2);
    } else {
      backMore();
    }
    countdown.value = 0;
  } else if (
    window.location.pathname === "/wallet-loan-web/pages/loan/calc" &&
    !window.location.hash
  ) {
    $emit("showToast");
  } else {
    back();
  }
});

// 卡号输入
function cardinput() {
  cardErrorMessage.value = "";
  // 输入卡号时 情况报错
  authErrorMessage.value = "";
  if (!phoneErrorMessage.value && bankInfo.value?.mobileNo && !countdown.value) {
    messageDisabled.value = false;
  }
}

function cardInputChange(event) {
  const { value } = event.target;
  const str = value.replace(/\s+/g, "");
  const reg = /^[0-9]{9,19}$/;
  if (reg.test(str) && countdown.value > 0) {
    countdown.value = 0;
    bankInfo.value.smsCode = "";
  }
}

const handleShowPopupClick = () => {
  reportCheckCardNoClick();
  window.location.hash = "cardNo";
};

// 手机号输入
function phoneinput(value) {
  const currentValue = value;
  const len = mobileNo.value?.length || 0;
  if (!ifChangeInputType.value && currentValue.length < len) {
    bankInfo.value.mobileNo = "";
    ifChangeInputType.value = true;
  }
  ifShowMsg.value = value === mobileNo.value;
  phoneErrorMessage.value = "";
  authErrorMessage.value = "";
  if (!cardErrorMessage.value && bankInfo.value?.bankCardNo && !countdown.value) {
    messageDisabled.value = false;
  }
}
// 验证码输入
function authinput() {
  cardErrorMessage.value = "";
  authErrorMessage.value = "";
}

function opensupport() {
  reportCheckSupportBankClick();
  window.location.hash = "support";
}
const closeadd = lodash.debounce(
  () => {
    if (props.showBankTransferCard) {
      reportBankTransferView();
    }
    if (noClick.value) showdialog.value = true;
    window.location.hash = "home";
  },
  1000,
  { leading: true },
);
function openaddfirst() {
  // 获取准入手机号
  getMobileNo();
  bankInfo.value.bankCardNo = "";
  bankInfo.value.smsCode = "";
  checkinfo.value = "";
  cardErrorMessage.value = "";
  phoneErrorMessage.value = "";
  authErrorMessage.value = "";
  window.location.hash = "add";
  Object.keys(inputReportStatus).forEach((key) => {
    inputReportStatus[key] = false;
  });
  // 添加银行卡页曝光
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "loan_add_bankcard_page",
    });
  });
}
// 发送验证码
async function bindBankcard() {
  const param = {
    bankCardNo: bankInfo?.value.bankCardNo.replace(/\s+/g, ""),
  };
  if (!ifShowMsg.value) param.mobileNo = bankInfo?.value.mobileNo.replace(/\s+/g, "");
  const resp = await request(
    "/loan/api/bankcard/bind",
    {
      encryptedParams: {
        ...param,
      },
    },
    { mock: false },
  );

  setTimeout(async () => {
    await request(
      "/loan/api/user/operLog",
      {
        orderNo: props.applyNo,
        operType: 401,
        operResult: resp?.data?.verifyResult === 0 ? 1 : 2,
        supplier: 1,
        operTime: new Date().getTime(),
      },
      { isHideOffline: true },
    );
  }, 0);
  checkinfo.value = resp;
  if (resp.code !== 0) {
    authErrorMessage.value = resp.message || "请求失败,请检查银行卡号或手机号。";
    messageDisabled.value = true;
    return resp;
  }
  if (resp.code === 0 && resp?.data?.verifyResult !== 0) {
    authErrorMessage.value = resp.data?.verifyMsg
      ? resp.data?.verifyMsg
      : "请求失败,请检查银行卡号或手机号。";
    messageDisabled.value = true;
    return resp;
  }
  showToast({
    message: "验证码已发送",
    position: "bottom",
  });
  Captcha.value.focus();
  return resp;
}

// // 输入内容四位空一格
const checkstrlong = (value) => {
  let str = value;
  str = str.replace(/[-]|[.]/g, "").replace(/(\d{4})(?=\d)/g, "$1 ");
  return str;
};
// // 输入内容四位空一格
const checkNumber = (value) => {
  let str = value;
  str = str.replace(/[-]|[.]/g, "");
  return str;
};
// 加卡完成
async function onClickLeft() {
  if (errorCode.value) {
    showdialog.value = false;
    $emit("bankinfo", selectedcard.value ? selectedcard.value : {});
    if (Number(window.history.length) > 4) {
      backMore(Number(window.history.length) - 2);
    } else {
      backMore();
    }
    return;
  }
  // 到达结果页清除协议勾选
  ischoose.value = false;
  selectedcard.value = cards.value?.[0];
  selectedid.value = 0;
  selectedCardId.value = selectedcard.value?.bankCardId;
  $emit("bankinfo", selectedcard.value ? selectedcard.value : {});
  bankInfo.value.bankCardNo = "";
  bankInfo.value.mobileNo = "";
  bankInfo.value.smsCode = "";
  countdown.value = 0;
  if (Number(window.history.length) > 4) {
    backMore(Number(window.history.length) - 2);
  } else {
    backMore();
  }
}
const isOnline = ref(true);
const isShowErrorDialog = ref(false);
const isServerError = ref(true);

const handleCancelClick = () => {
  ifPressNextStep.value = false;
  report("wallet_page_click", { click_name: "agree_bind_card_popup_cancel" });
};

// 下一步按钮
async function verifySms(ifConfirm = false) {
  if (!checkinfo.value) {
    showToast({
      message: "请点击获取验证码。",
      position: "bottom",
    });
    return;
  }
  if (checkinfo.value?.data?.verifyResult !== 0) {
    showToast({
      message: checkinfo.value?.data?.verifyMsg
        ? `${checkinfo.value?.data?.verifyMsg}`
        : "请求失败,请检查银行卡号或手机号。",
      position: "bottom",
    });
    return;
  }
  if (ifConfirm) {
    ischoose.value = true;
    report("wallet_page_click", { click_name: "agree_bind_card_popup_confirm" });
  }
  if (!ischoose.value && isShowContract.value) {
    // 点击下一步提交(未勾选协议)
    setTimeout(() => {
      report("wallet_page_click", {
        click_name: "loan_add_bankcard_click",
        isCheckContract: false,
      });
    });
    getContractDetail();
    return;
  }
  // 点击下一步提交(已勾选协议)
  setTimeout(() => {
    report("wallet_page_click", {
      click_name: "loan_add_bankcard_click",
      isCheckContract: isShowContract.value ? true : null,
    });
  });
  isOnline.value = window.navigator.onLine;
  if (!window.navigator.onLine) {
    isShowErrorDialog.value = true;
    isServerError.value = false;
  } else {
    loading.value = true;
    isLoading.value = true;
    const timeout = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: -1,
          result: "Timeout",
        });
      }, 10000);
    });
    const value = await Promise.race([
      request(
        "/loan/api/bankcard/bindSmsVerify",
        {
          encryptedParams: {
            bankCardId: checkinfo.value?.data?.bankCardId,
            smsCode: bankInfo?.value.smsCode,
          },
        },
        { mock: false },
      ),
      timeout,
    ]);
    if (value.code === -1) {
      loading.value = false;
      isLoading.value = false;
      ifPressNextStep.value = false;
      return;
    }
    resultWeb.value = value;
    if (value?.code !== 0) {
      isShowErrorDialog.value = true;
      isServerError.value = true;
    } else {
      isShowErrorDialog.value = false;
      isServerError.value = false;
      if (value.data.verifyResult === 0) {
        const listRes = await request(
          "/loan/api/bankcard/list",
          { outOrderNo: props.applyNo },
          { mock: false },
        );
        if (listRes.code !== 0) {
          selectedcard.value = null;
          noClick.value = false;
          errorCode.value = listRes.code;
          onClickLeft();
          return;
        }
        if (listRes.code === 0 && listRes.data) {
          errorCode.value = null;
          cards.value = listRes.data;
          cards.value.forEach((item) => {
            Object.assign(item, {
              singleLimit: handleSingleLimitStr(item, "singleLimit"),
              dotString: item.singleLimit && item.dayLimit ? "，" : "",
              dayLimit: handleDayLimitStr(item, "dayLimit"),
            });
          });
          if (cards.value?.length) {
            for (let i = 0; i < cards.value.length; i += 1) {
              if (checkinfo.value?.data?.bankCardNo === cards.value[i].bankCardNo) {
                selectedcard.value = cards.value[i];
                selectedid.value = i;
                selectedCardId.value = cards.value[i].bankCardId;
                messageInfoNew.value = `${cards.value[i].bankName} ( ${cards.value[i].bankCardNo} )`;
                break;
              }
            }
          }
        }
        setTimeout(async () => {
          await request("/loan/api/user/operLog", {
            orderNo: props.applyNo,
            operType: 402,
            operResult: value.code === 0 ? 1 : 2,
            supplier: 1,
            operTime: new Date().getTime(),
          });
        }, 0);
        window.location.hash = "result";
        setTimeout(() => {
          report("wallet_page_result", {
            result_name: "loan_add_bankcard_success",
          });
        });
      } else {
        setTimeout(() => {
          report("wallet_page_result", {
            result_name: "loan_add_bankcard_failed",
          });
        });
        showToast({
          message: value?.data?.verifyMsg,
          position: "bottom",
        });
      }
    }
    isLoading.value = false;
    ifPressNextStep.value = false;
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
}

function handleClickConfirm() {
  if (isOnline.value) {
    verifySms();
  } else {
    setNetwork().then(() => {
      isOnline.value = window.navigator.onLine;
    });
  }
}
function handleClickCancel() {
  isShowErrorDialog.value = false;
}

const cardBlur = () => {
  const str = bankInfo.value.bankCardNo;
  const reg = /^[0-9]{9,19}$/;
  // 需要nextTick
  if (!reg.test(str)) {
    nextTick(() => {
      cardErrorMessage.value = "请输入正确的银行卡号";
    });
  }
  if (bankInfo.value.bankCardNo && !inputReportStatus.bankCardNo) {
    inputReportStatus.bankCardNo = true;
    report("wallet_page_result", {
      page_name: "page_name: loan_add_bankcard_page",
      fileds: "[bankCardNo]",
    });
  }
};

const checkPhoneNum = computed(() => {
  if (!ifShowMsg.value) {
    if (
      !/^1[3-9]\d{9}$/.test(bankInfo.value.mobileNo) &&
      mobileNo.value !== bankInfo.value.mobileNo
    )
      return true;
  } else if (mobileNo.value !== bankInfo.value.mobileNo) {
    return true;
  }
  return false;
});

const numberblur = () => {
  if (
    (bankInfo.value.mobileNo.length > 0 && bankInfo.value.mobileNo.length !== 11) ||
    checkPhoneNum.value
  ) {
    phoneErrorMessage.value = "请输入正确的预留手机号";
    ifShowMsg.value = false;
  }
  if (bankInfo.value.mobileNo && !inputReportStatus.mobileNo) {
    inputReportStatus.mobileNo = true;
    report("wallet_page_result", {
      page_name: "page_name: loan_add_bankcard_page",
      fileds: "[mobineNo]",
    });
  }
};

const codeBlur = () => {
  // 四位数字的正则表达式
  const fourDigitsRegex = /^\d{4}$/;
  // 六位数字的正则表达式
  const sixDigitsRegex = /^\d{6}$/;

  if (
    fourDigitsRegex.test(bankInfo.value.smsCode) ||
    sixDigitsRegex.test(bankInfo.value.smsCode) ||
    bankInfo.value.smsCode === ""
  ) {
    authErrorMessage.value = "";
  } else {
    authErrorMessage.value = "请输入正确的验证码";
  }
  if (bankInfo.value.smsCode && !inputReportStatus.smsCode) {
    inputReportStatus.smsCode = true;
    report("wallet_page_result", {
      page_name: "page_name: loan_add_bankcard_page",
      fileds: "[smsCode]",
    });
  }
};
// 倒计时
const startCountdown = lodash.debounce(
  async () => {
    Captcha.value.blur();
    if (bankInfo.value.bankCardNo === "") {
      cardErrorMessage.value = "请输入银行卡号";
      return;
    }
    if (bankInfo.value.mobileNo === "") {
      phoneErrorMessage.value = "请输入预留手机号";
      return;
    }
    if (countdown.value > 0) {
      return;
    }
    if (phoneErrorMessage.value || cardErrorMessage.value) {
      authErrorMessage.value = "请求失败,请检查银行卡号或手机号。";
      return;
    }
    const resbindBankcard = await bindBankcard();
    if (resbindBankcard?.code !== 0) {
      return;
    }
    if (resbindBankcard?.code === 0 && resbindBankcard?.data?.verifyResult !== 0) {
      authErrorMessage.value =
        resbindBankcard.data?.verifyMsg || "请求失败,请检查银行卡号或手机号。";
      return;
    }
    // 双击时，上面的countdown.value > 0没生效。？
    if (countdown.value) {
      return;
    }
    countdown.value = 59; // 倒计时60秒
    const intervalId = setInterval(() => {
      if (countdown.value > 0) {
        countdown.value -= 1;
      } else {
        clearInterval(intervalId);
      }
    }, 1000);
  },
  1000,
  { leading: true },
);
function confirm(card) {
  selectedid.value = (cards.value || []).indexOf(card);
  selectedcard.value = card;
  selectedCardId.value = card.bankCardId;
  $emit("bankinfo", card);
  $emit("emitReport");
  back();
}

const bankListScrollPosition = ref({ st: 0, ed: 0 });
const bankListScrollItem = ref();

const bankListTouchStart = () => {
  const bankListScrollElement = bankListScrollItem.value;
  if (bankListScrollElement) {
    bankListScrollPosition.value.st = bankListScrollElement.scrollTop;
  }
};
const bankListTouchEnd = () => {
  const bankListScrollElement = bankListScrollItem.value;
  if (bankListScrollElement) {
    bankListScrollPosition.value.ed = bankListScrollElement.scrollTop;
  }
};
const bankListSwipe = (dir, e) => {
  if (!showdialog.value) return;
  let element = e.target;
  if (element) {
    if (!element.className.toString().includes("hnr-overlay")) {
      while (element && !element.className.toString().includes("bank-list-scroll-top")) {
        if (element.className.toString().includes("bank-list-scroll-item")) {
          if (bankListScrollPosition.value.st === 0 && bankListScrollPosition.value.ed === 0) {
            closeContractPopup();
          }
          return;
        }
        element = element.parentElement;
      }
    }
  }
  // TODO: 需要执行的方法
  closeContractPopup();
};

const handleBankAppTransfer = lodash.debounce(
  () => {
    reportBankTransferClick();
    $emit("go-to-transfer-repay-page");
  },
  2000,
  { leading: true, trailing: false },
);
</script>
<style scoped>
:deep(.hnr-dialog__message) {
  display: flex;
  flex-direction: column;
}

:deep(.hnr-dialog__footer) {
  padding: var(--dp10) var(--dp20) var(--dp20) !important;
}

a {
  text-decoration: none !important;
  font-size: var(--hnr-body-3);
  font-weight: normal;
  line-height: normal;
  color: var(--hnr-text-color-secondary-activated);
}

.icon-down:before {
  content: "\e900";
  font-family: "icomoon";
  font-size: var(--dp16);
  color: var(--hnr-text-color-secondary);
}

.fold {
  color: var(--hnr-text-color-secondary-activated);
  width: calc(100vw - 80px);
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  height: 42px;
}

.icon-up:before {
  content: "\e901";
  font-family: "icomoon";
  font-size: var(--dp16);
  color: var(--hnr-text-color-secondary);
}

.inner-container {
  position: relative;
  overflow: hidden;
}

.expand {
  height: auto;
  max-height: 158px;
  overflow-y: auto;
}

.hnr-cell-box {
  padding: 0;
  margin: 0 !important;
  width: 100% !important;
  background-color: unset !important;
}

.addYhkAggre {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.rightvalue {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  flex-direction: column;
  margin-left: var(--hnr-elements-margin-horizontal-M);
}

.right1 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-bottom: var(--hnr-elements-margin-vertical-S);
}

.right2 {
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}

.rightall {
  display: flex;
  flex: 2;
  align-items: center;
}

.imgs {
  height: var(--dp20);
  margin-top: 2px;
  vertical-align: bottom;
}

.rightimgs {
  height: var(--dp24);
  vertical-align: top;
}

.content-bottom-btn-next {
  width: calc(
    100% - 12px - var(--hnr-elements-margin-horizontal-M2) - 12px -
      var(--hnr-elements-margin-horizontal-M2)
  );
  max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 0 0;
}

.content-bottom-btn {
  width: calc(
    100% - 24px - var(--hnr-elements-margin-horizontal-L2) - 24px -
      var(--hnr-elements-margin-horizontal-L2)
  );
  max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}

.addYhkF {
  display: flex;
  justify-content: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
  padding-top: var(--hnr-elements-margin-vertical-L);
  margin: 0 var(--hnr-max-padding-end) var(--hnr-elements-margin-vertical-S)
    var(--hnr-max-padding-start);
}

.addYhkSe {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  justify-content: center;
  margin-bottom: var(--dp36);
}

.addYhkInput {
  border-radius: var(--hnr-default-corner-radius-m);
  margin-bottom: var(--hnr-elements-margin-vertical-M2) !important;
  background-color: var(--hnr-color-list-card-background) !important;
  padding: 0 var(--hnr-elements-margin-vertical-M2);
  box-sizing: border-box;
}

:deep(.hnr-field__body--border .hnr-field__input-area) {
  background-color: var(--hnr-color-list-card-background);
}

:deep(.hnr-radio__label) {
  margin-left: var(--hnr-elements-margin-horizontal-M);
}

:deep(.hnr-cell--center .hnr-cell__left) {
  padding: 12px 0 !important;
  max-width: calc(100% - 40px) !important;
  /* max-width: 80vw !important; */
}

.scroll-container {
  display: flex;
  flex-direction: column;
  /* width: 100%; */
  height: calc(
    100vh - 32px - var(--hnr-nav-bar-height) - var(--hnr-elements-margin-horizontal-M2) -
      var(--dp40) - var(--hnr-default-padding-bottom-fixed)
  );
  overflow-y: auto;
  /* width: 90%; */
  margin: 0 var(--hnr-elements-margin-horizontal-M2);
  border-bottom-left-radius: var(--hnr-default-corner-radius-m);
  border-bottom-right-radius: var(--hnr-default-corner-radius-m);
}

.notSreachContent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}

:deep(.hnr-cell__label) {
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  /* margin-bottom: var(--hnr-elements-margin-vertical-L); */
}

.bank-list-scroll {
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
  overflow-y: auto;
  border-radius: var(--hnr-list-card-corner-radius);
  max-height: calc(
    100vh - var(--hnr-nav-bar-height) - var(--dp8) - 150px - var(--hnr-elements-margin-vertical-L2)
  );
}

.scroll-container1 {
  display: flex;
  flex-direction: column;
  padding-left: var(--dp24) !important;
  padding-right: var(--dp24) !important;
}

:deep(.hnr-cell-group) {
  background-color: unset !important;
  /* max-height: calc(100vh - var(--hnr-nav-bar-height) - var(--dp8)) !important; */
}

:deep(.hnr-cell-group .hnr-cell-box:first-child) {
  padding-top: 0 !important;
}

.addYhkBtm {
  width: 100%;
  position: fixed;
  bottom: var(--hnr-default-padding-bottom-fixed);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.topleft {
  margin: 0 var(--dp24);
  height: var(--dp56);
  text-align: var(--hnr-nav-bar-text-align);
  color: var(--hnr-color-app-bar-title);
  font-weight: var(--hnr-font-weight-medium);
  font-size: calc(var(--hnr-nav-bar-title-font-size) / var(--hnr-large-font-rate));
  display: flex;
  align-items: center;
}

.supportdivider {
  padding: 0 var(--dp12);
  width: auto;
}

:deep(.hnr-divider) {
  border-style: none !important;
}

.errmsg {
  color: var(--hnr-color-error);
  font-size: var(--hnr-caption);
  text-align: left;
  margin: var(--hnr-elements-margin-vertical-M) 0 var(--hnr-elements-margin-vertical-M2) var(--dp24);
}

.toastmsg {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-caption);
  text-align: left;
  margin: 8px auto auto var(--dp24);
}

.img {
  margin-right: var(--hnr-elements-margin-vertical-S);
  width: 16px;
}

:deep(.hnr-cell__title) {
  /* display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
  display: flex;
  justify-content: center;
  flex-direction: column;
}

:deep(.hnr-cell__prefix) {
  padding-right: var(--hnr-elements-margin-horizontal-M);
}

.notSearch {
  margin-top: var(--hnr-elements-margin-vertical-M);
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.loading {
  display: contents;
  align-items: center;
  justify-content: center;
}

.pubimg {
  display: flex;
  justify-content: center;
  padding-top: var(--hnr-elements-margin-vertical-XXL);
  padding-bottom: var(--hnr-elements-margin-vertical-XL);
}

.suchead1 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  margin-left: var(--hnr-max-padding-start);
  margin-right: var(--hnr-max-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L2);
  padding-top: var(--magic_dimens_element_vertical_large);
  padding-bottom: var(--hnr-elements-margin-vertical-M);
  display: flex;
  justify-content: center;
}

.dialogbg {
  display: flex;
  flex-direction: column;
  min-height: 360px;
  max-height: calc(100vh - var(--hnr-nav-bar-height) - var(--dp8));
  background-color: var(--hnr-color-background-cardview) !important;
}

.suchead2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  padding-bottom: var(--dp64);
  display: flex;
  justify-content: center;
}

.myPopup {
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
  background-color: var(--hnr-color-background-cardview);
}

.myPopupnot {
  background-color: var(--hnr-color-app-bar-background);
  height: 100%;
}

.pull-icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
  height: var(--dp24);
}

.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp18);
  color: #000000;
  opacity: 0.2;
}

.bankContentTop {
  border-top-left-radius: var(--hnr-default-corner-radius-m);
  border-top-right-radius: var(--hnr-default-corner-radius-m);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--hnr-elements-margin-horizontal-M2);
  margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2);
  padding-left: var(--hnr-elements-margin-horizontal-M2);
  padding-right: var(--hnr-elements-margin-horizontal-M2);
  min-height: var(--dp40) !important;
  height: var(--dp40) !important;
  background-color: var(--hnr-color-control-normal-2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.list-item {
  /* height: var(--dp48); */
  background-color: var(--hnr-color-card-background);
  /* line-height: var(--dp48); */
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* margin-left: var(--hnr-elements-margin-horizontal-M2);
  margin-right: var(--hnr-elements-margin-horizontal-M2); */
  padding-left: var(--hnr-elements-margin-horizontal-M2);
  padding-right: var(--hnr-elements-margin-horizontal-M2);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.hnr-list:last-child .list-item {
  /* border-bottom-left-radius: var(--hnr-default-corner-radius-m);
  border-bottom-right-radius: var(--hnr-default-corner-radius-m); */
}

.dialogfooter {
  position: fixed;
  padding-left: var(--dp24);
  padding-right: var(--dp24);
  padding-top: var(--hnr-elements-margin-vertical-L2);
  bottom: var(--hnr-default-padding-bottom-fixed);
  width: calc(100vw - var(--dp24) - var(--dp24));
}

.hnr-row--basic {
  margin: 0;
}

.contt {
  /* margin:0 var(--dp24) calc(var(--hnr-elements-margin-vertical-L2) + var(--hnr-default-padding-bottom-fixed) + var(--hnr-button-height)) var(--dp24); */
  margin: 0 var(--dp24) 0 var(--dp24);
}

.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  z-index: 100;
  /* background: var(--hnr-color-background-cardview); */
}

.plan-item {
  display: flex;
  align-items: center;
  width: 100%;
  /* border-bottom: var(--dp1) solid #E7E7E7; */
  padding: var(--hnr-elements-margin-vertical-M2) 0;
}

.plan-item-loan {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  /* border-bottom: var(--dp1) solid #E7E7E7; */
  height: 100%;
  padding: 0;
}

.plan-item-left {
  align-items: self-start;
}

.plan-item-left-loan {
  display: flex;
  align-items: center;
  height: 100%;
  vertical-align: middle;
}

.plan-item-left-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}

.plan-item-left-top-left {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

/* .plan-item-left-top-right {
  padding-left: var(--hnr-elements-margin-horizontal-S);
} */

.plan-item-left-bottom {
  text-align: end;
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-S);
}

:deep(.hnr-nav-bar__right) {
  margin-right: var(--dp8);
}

:deep(.hnr-field--withMargin) {
  width: calc(
    100vw - var(--hnr-elements-margin-vertical-M2) - var(--hnr-elements-margin-vertical-M2)
  ) !important;
  padding: 0;
  margin: 0 var(--hnr-elements-margin-vertical-M2) !important;
}

:deep(.hnr-field__body--border .hnr-field__input-area) {
  padding: 0 var(--dp12);
}

:deep(.hnr-button__text) {
  font-size: var(--hnr-body-2);
}

.plan-item-right {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.plan-item-right-top1 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-align: end;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-left: var(--dp3);
}

.plan-item-right-top {
  display: flex;
  justify-content: flex-end;
  text-align: end;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

:deep(.hnr-field__control) {
  font-size: calc(var(--hnr-body-1) / var(--hnr-large-font-rate)) !important;
}

:deep(.hnr-field__control::-webkit-input-placeholder) {
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-font-weight-regular);
}

:deep(.hnr-cell__prefix--title) {
  color: #256fff !important;
}

.selftcell-label {
  margin-top: var(--dp2) !important;
  color: var(--hnr-text-color-tertiary) !important;
  font-size: var(--hnr-body-3) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
  /* margin-bottom: var(--hnr-elements-margin-vertical-L) !important; */
  word-break: break-all !important;
}

.icon-right:before {
  display: inline-block;
  content: "\e928";
  font-family: "icomoon";
  font-size: var(--dp24);
  margin-left: var(--hnr-elements-margin-horizontal-S);
  color: var(--hnr-color-quaternary);
}

/* 还款银行卡的按钮展示不全 */
:deep(.hnr-cell__value) {
  margin: 0 !important;
  /* width: 20% !important; */
  padding: 0 !important;
}

.noCardClass {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weigth-regular);
  margin-left: var(--dp3);
}

.hnr-checkbox {
  -webkit-tap-highlight-color: transparent;
  align-items: flex-start !important;
}

:deep(.hnr-popup--center) {
  /* max-width: 100% !important; */
}

:deep(.hnr-card) {
  background-color: #e3f1ff;
}

@media (prefers-color-scheme: dark) {
  :deep(.hnr-card) {
    background-color: #112840;
  }

  .icon-arrow-pull:before {
    color: #ffffff;
    opacity: 0.2;
  }

  .dialogbg {
    background-color: #2e2e2e !important;
  }
}

.other-methods-title {
  padding-top: 12px;
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}

.transfer-label {
  font-size: 12px;
  color: var(--hnr-text-color-secondary);
}
</style>
