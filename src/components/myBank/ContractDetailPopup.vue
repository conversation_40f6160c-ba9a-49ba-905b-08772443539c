<script setup>
import { defineProps, defineEmits, ref, watch } from "vue";
import reloadingComponent from "../reloadingComponent.vue";
import { contractClickCallback } from "../../helpers/utils";
import { goto } from "../../helpers/native-bridge";

const props = defineProps({
  isShowContractDetailPopup: { type: Boolean, default: false },
  contractContent: { type: String, default: "" },
  contractHtml: { type: String, default: "" },
  contractLoadingStatus: { type: Boolean, default: false },
  contractErrorStatus: { type: Boolean, default: true },
});
const emit = defineEmits(["close", "try"]);
const closeFunc = () => {
  emit("close");
};
const trySupportClick = () => {
  emit("try");
};
const isContractDetailPopup = ref(false);
const iframeRef = ref(null);

watch(
  () => props.isShowContractDetailPopup,
  (newValue) => {
    isContractDetailPopup.value = newValue;
  },
  { immediate: true },
);

const handleClick = (event) => {
  const callback = contractClickCallback(event);
  if (callback?.next === "goto") {
    goto(callback.href, true);
  }
};

const handleIframeLoad = () => {
  const iframeDocument = iframeRef.value.contentDocument || iframeRef.value.contentWindow.document;
  iframeDocument.addEventListener("click", handleClick);
};
</script>
<template>
  <hnr-popup
    v-model:show="isContractDetailPopup"
    :before-close="() => true"
    round
    position="bottom"
    class="contract-detail-popup"
  >
    <div class="pull-icon-box">
      <span class="icon-arrow-pull" teleport="hnr-divider" @click="closeFunc"></span>
    </div>
    <div class="contract-con">
      <template v-if="props.contractContent">
        <main>
          <iframe
            ref="iframeRef"
            class="contract-content-iframe"
            :srcdoc="props.contractContent"
            frameborder="0"
            scrolling="yes"
            width="100%"
            loading="lazy"
            @load="handleIframeLoad"
          ></iframe>
        </main>
      </template>
      <template v-else>
        <div style="height: 50vh">
          <reloading-component
            :loading-status="props.contractLoadingStatus"
            :error-status="props.contractErrorStatus"
            @try-click="trySupportClick"
          />
        </div>
      </template>
    </div>
    <div class="close-btn-box">
      <hnr-button type="primary" standard-width="true" class="close-btn" @click="closeFunc()"
        >关闭</hnr-button
      >
    </div>
  </hnr-popup>
</template>
<style scoped>
.contract-detail-popup {
  max-height: calc(100vh - 97px);
  display: flex;
  flex-direction: column;
  z-index: 1100 !important;
  user-select: none;
}
.contract-con {
  flex: 1;
  overflow: auto;
}
.pull-icon-box {
  text-align: center;
  padding-bottom: var(--dp10);
}
.icon-arrow-pull:before {
  content: "\e902";
  font-family: "icomoon";
  font-size: var(--dp20);
  color: #d5d5d5;
}
main {
  padding: 15px;
  padding-bottom: 0;
}
.pull-icon {
  width: var(--dp32);
  height: var(--dp18);
}
.close-btn-box {
  padding: 0 var(--hnr-max-padding-start) 0 var(--hnr-max-padding-end);
  text-align: center;
}
.close-btn {
  margin: var(--hnr-elements-margin-vertical-L) 0 var(--hnr-default-padding-bottom-fixed);
}
:deep(.hnr-button--primary) {
  max-width: 100% !important;
}
:deep(.hnr-overlay) {
  z-index: 1100 !important;
}
.contract-content-iframe {
  height: calc(100vh - 240px);
  border-radius: 10px;
}
@media (prefers-color-scheme: dark) {
  :deep(.hnr-popup) {
    background: #2e2e2e;
  }
  .icon-arrow-pull:before {
    color: #ffffff33;
  }
}
</style>
