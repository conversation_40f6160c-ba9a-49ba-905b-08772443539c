import childProcess from "child_process";

const NODE_ENV = process.env.ENV_TYPE;
childProcess.exec(
  `npm run ${NODE_ENV || "fat"}`,
  {
    encoding: "gbk",
    maxBuffer: 1024 * 1024 * 20,
  },
  (error, stdout, stderr) => {
    if (error) {
      console.error(`command exec error: ${error}`);
      return;
    }
    console.log(`stdout: ${stdout}`);
    console.error(`stderr: ${stderr}`);
  },
);
