// app/layout.tsx
"use client";

import * as React from "react";
import { Fragment, useEffect, useState } from "react"; // <--- 引入 Fragment
import "@/styles/globals.css";
import clsx from "clsx";
import { <PERSON><PERSON>, ToastProvider } from "@heroui/react";
import { useRouter } from "next/navigation";
import {
  ThemeProvider as NextThemesProvider,
  ThemeProviderProps,
} from "next-themes";
import { HeroUIProvider } from "@heroui/system";

import SimpleLoginPrompt from "../components/login/SimpleLoginPrompt";

export interface ProvidersProps {
  children: React.ReactNode;
  themeProps?: ThemeProviderProps;
}

export function Providers({ children, themeProps }: ProvidersProps) {
  const router = useRouter();

  return (
    <HeroUIProvider navigate={router.push}>
      <NextThemesProvider {...themeProps}>{children}</NextThemesProvider>
    </HeroUIProvider>
  );
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loggedInUser = JSON.parse(
      localStorage.getItem("user-storage") || "{}",
    )?.state?.currentUser;

    if (loggedInUser) {
      setIsLoggedIn(true);
    }
    setIsLoading(false);
  }, []);

  const handleLoginSuccess = (): void => {
    const chooseSystem = JSON.parse(
      localStorage.getItem("user-storage") || "{}",
    )?.state?.chooseSystem;

    setIsLoggedIn(true);
    if (chooseSystem === "0") {
      router.push("/pages/scene/create");
    }
    if (chooseSystem === "1") {
      router.push("/pages/cp/loan-user/my");
    }

    if (chooseSystem === "2") {
      router.push("/pages/daily-inspection");
    }

    if (chooseSystem === "3") {
      router.push("/pages/file-change");
    }
  };

  return (
    <html suppressHydrationWarning lang="en">
      <head />
      <body
        className={clsx("min-h-screen bg-background font-sans antialiased")}
      >
        {isLoading ? (
          <Providers themeProps={{ attribute: "class", defaultTheme: "light" }}>
            <div
              key="loading-state"
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100vh",
              }}
            >
              <Spinner size="lg" />
            </div>
          </Providers>
        ) : !isLoggedIn ? (
          <Providers themeProps={{ attribute: "class", defaultTheme: "light" }}>
            <SimpleLoginPrompt
              key="login-prompt"
              onLoginSuccess={handleLoginSuccess}
            />
          </Providers>
        ) : (
          <Fragment key="app-layout">
            <ToastProvider placement={"top-center"} />
            <Providers
              themeProps={{ attribute: "class", defaultTheme: "light" }}
            >
              {children}
            </Providers>
          </Fragment>
        )}
      </body>
    </html>
  );
}
