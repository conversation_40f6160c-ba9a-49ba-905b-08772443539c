"use client";

import React, { useEffect, useCallback } from "react";
import {
  Accordion,
  AccordionItem,
  Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
  Chip,
  Spinner,
  Button,
  addToast,
  Card, CardBody,
} from "@heroui/react";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { Icon } from "@iconify/react";

import { LoanOrder } from "@/types";
import { batchUpdateLoanUser, getAllLoanUsers, getMyLoanUsers, updateLoanUser } from "@/actions/cp/loanUserActions";
import { LoanUser } from "@/types/cp/loanUser";
import { RightIcon } from "@/components/icons";
import { useUserStore } from "@/store/user";
import { getUserById, updateUser } from "@/actions/userActions";
import { creditApplyStatusNameMap, CreditInfo, CreditApplyStatusEnum } from "@/types/cp/creditInfo";

const columns = [
  { name: "姓名", uid: "realName" },
  { name: "身份证", uid: "ctfCode" },
  { name: "手机号", uid: "mobileNo" },
  { name: "openId", uid: "userId" },
  { name: "授信状态", uid: "creditStatus" },
  { name: "CP", uid: "cp" },
  { name: "备注", uid: "remarks" },
  { name: "创建时间", uid: "createTime" },
  { name: "操作", uid: "actions" },
];

export default function Page(props: {
  className: string;
  loanOrders?: LoanOrder[];
  setLoanOrders: (loanOrders: LoanOrder[]) => void;
}) {
  const [myLoanUsers, setMyLoanUsers] = React.useState<LoanUser[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const router = useRouter();
  const [dbUser, setDbUser] = React.useState<User | null>(null);
  const currentUser = useUserStore((state) => state.currentUser);
  const [autoCollectLoanUserRules, setAutoCollectLoanUserRules] = React.useState("");

  // 创建一个防抖的更新函数
  const debouncedUpdateUser = useCallback(
    debounce(async (updatedUser: LoanUser) => {
      await updateLoanUser(updatedUser);
    }, 500),
    []
  );

  const initial = async () => {
    if (!currentUser?.id) return;
    
    setIsLoading(true);
    try {
      const [allLoanUsers, dbUserTemp] = await Promise.all([getAllLoanUsers(), getUserById(currentUser.id)]);
      if (dbUserTemp) {
        setDbUser(dbUserTemp);
        const autoCollectLoanUserRules = dbUserTemp?.autoCollectLoanUserRules || "";

        const myLoanUsersTemp: LoanUser[] = [];

        if (autoCollectLoanUserRules) {
          allLoanUsers.forEach(user => {
            const relatedUserIds = user.relatedUserIds as unknown as number[];
            if (user.realName?.includes(autoCollectLoanUserRules) || relatedUserIds?.includes(dbUserTemp.id)) {
              myLoanUsersTemp.push(user);
            }
          });
        }

        setMyLoanUsers(myLoanUsersTemp);
      }
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
   initial();
  }, [autoCollectLoanUserRules]);

  const getCreditStatusColor = (status: number | undefined) => {
    switch (status) {
      case CreditApplyStatusEnum.PENDING:
        return "warning";
      case CreditApplyStatusEnum.PASSED:
        return "success";
      case CreditApplyStatusEnum.REJECTED:
        return "danger";
      default:
        return "default";
    }
  };

  const renderCell = React.useCallback((user: any, columnKey: any) => {
    const cellValue = user[columnKey];

    switch (columnKey) {
      case "createTime":
        // 将 BigInt 转换为字符串，然后转换为数字
        return dayjs(Number(cellValue.toString())).format(
          "YYYY-MM-DD HH:mm:ss",
        );
      case "creditStatus":
        const creditStatus = user.creditInfo?.status?.applyStatus;
        const statusText = creditStatus ? creditApplyStatusNameMap.get(creditStatus) || "未知" : "未授信";
        return (
          <Chip
            color={getCreditStatusColor(creditStatus)}
            variant="flat"
          >
            {statusText}
          </Chip>
        );
      case "remarks":
        return (
          <Input
            value={user.remark || ""}
            className="w-full"
            onValueChange={(value) => {
              const updatedUser = { ...user, remark: value };
              // 立即更新本地状态
              setMyLoanUsers(prevUsers => 
                prevUsers.map(u => u.id === user.id ? updatedUser : u)
              );
              // 延迟更新数据库
              debouncedUpdateUser(updatedUser);
            }}
          />
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip content="进入">
              <span
                className="text-lg text-default-400 cursor-pointer active:opacity-50"
                onClick={() => {
                  if (user.cp === "mock") {
                    router.push(`/pages/cp/loan-user/${user.id}`);
                  }else {
                    router.push(`/pages/cp/loan-user/${user.id}/other-cps`);
                  }
                }}
              >
                <RightIcon />
              </span>
            </Tooltip>
          </div>
        );
      case "userId":
        return (
          <Tooltip content={cellValue}>
            <div className="truncate max-w-[100px] cursor-pointer">
              {cellValue}
            </div>
          </Tooltip>
        );
      default:
        return cellValue;
    }
  }, [debouncedUpdateUser]);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>我的准入账号</div>
      <div className={"flex items-center gap-4"}>
        <Card>
          <CardBody>
            <div className="flex items-center gap-2">
              当姓名包含
              <Input defaultValue={dbUser?.autoCollectLoanUserRules || ""} className={"w-24 mx-2"}
                     onValueChange={async (value) => {
                       if (!dbUser) return;
                       dbUser.autoCollectLoanUserRules = value;
                     }}></Input>
              时，自动从所有准入账号中捞取
              <Button
                size={"sm"}
                color={"primary"}
                onPress={async () => {
                  if (!dbUser) return;
                  updateUser(dbUser as User);
                  setAutoCollectLoanUserRules(dbUser.autoCollectLoanUserRules || "");
                  addToast({
                    title: "更新成功",
                    color: "success",
                  });
                }}
              >
                保存
              </Button>
            </div>
          </CardBody>
        </Card>
        <Button
          isIconOnly
          variant="light"
          onPress={() => {
            if (isLoading) return;
            initial();
          }}
          isDisabled={isLoading}
        >
          <Icon icon="mdi:refresh" width="24" height="24" />
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {isLoading ? (
        <div className="flex justify-center items-center h-[200px]">
          <Spinner size="lg" />
        </div>
      ) : (
        <Table className={props.className} topContent={topContent}>
          <TableHeader columns={columns}>
            {(column) => (
              <TableColumn
                key={column.uid}
                align={column.uid === "actions" ? "center" : "start"}
                className={column.uid === "userId" ? "w-[100px]" : ""}
              >
                {column.name}
              </TableColumn>
            )}
          </TableHeader>
          <TableBody items={myLoanUsers}>
            {(user) => (
              <TableRow key={user.id}>
                {(columnKey) => (
                  <TableCell className={columnKey === "userId" ? "w-[100px]" : ""}>
                    {renderCell(user, columnKey)}
                  </TableCell>
                )}
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </>
  );
}
