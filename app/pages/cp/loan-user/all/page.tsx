"use client";

import React, { useEffect } from "react";
import {
  Accordion, AccordionItem,
  Card, Chip, Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from "@heroui/react";
import { useRouter } from "next/navigation";
import dayjs from "dayjs";

import { LoanOrder } from "@/types";
import { getAllLoanUsers, updateLoanUser } from "@/actions/cp/loanUserActions";
import { LoanUser } from "@/types/cp/loanUser";
import { RightIcon } from "@/components/icons";
import { Icon } from "@iconify/react";
import { useUserStore } from "@/store/user";
import { CreditApplyStatusEnum, creditApplyStatusNameMap } from "@/types/cp/creditInfo";

const columns = [
  { name: "姓名", uid: "realName" },
  { name: "身份证", uid: "ctfCode" },
  { name: "手机号", uid: "mobileNo" },
  { name: "userId", uid: "userId" },
  { name: "授信状态", uid: "creditStatus" },
  { name: "CP", uid: "cp" },
  { name: "创建时间", uid: "createTime" },
  { name: "操作", uid: "actions" },
];

export default function Page(props: {
  className: string;
  loanOrders?: LoanOrder[];
  setLoanOrders: (loanOrders: LoanOrder[]) => void;
}) {
  const [allLoanUsers, setAllLoanUsers] = React.useState<LoanUser[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const router = useRouter();
  const currentUser = useUserStore((state) => state.currentUser);

  useEffect(() => {
    initial();
  }, []);

  const initial = async () => {
    setIsLoading(true);
    getAllLoanUsers().then((res: LoanUser[]) => {
      res.forEach(user => {
        if (!currentUser || !currentUser.id) {
          return;
        }
        user.isFavorite = user.relatedUserIds?.includes(currentUser.id);
      });
      setAllLoanUsers(res);
    }).finally(() => {
      setIsLoading(false);
    });
  }

  const getCreditStatusColor = (status: number | undefined) => {
    switch (status) {
      case CreditApplyStatusEnum.PENDING:
        return "warning";
      case CreditApplyStatusEnum.PASSED:
        return "success";
      case CreditApplyStatusEnum.REJECTED:
        return "danger";
      default:
        return "default";
    }
  };

  const renderCell = React.useCallback((user: any, columnKey: any) => {
    const cellValue = user[columnKey];

    switch (columnKey) {
      case "createTime":
        // 将 BigInt 转换为字符串，然后转换为数字
        return dayjs(Number(cellValue.toString())).format(
          "YYYY-MM-DD HH:mm:ss",
        );

      case "creditStatus":
        const creditStatus = user.creditInfo?.status?.applyStatus;
        const statusText = creditStatus ? creditApplyStatusNameMap.get(creditStatus) || "未知" : "未授信";
        return (
          <Chip
            color={getCreditStatusColor(creditStatus)}
            variant="flat"
          >
            {statusText}
          </Chip>
        );
      case "actions":
        return (
          <div className="relative flex  items-center gap-2">
            <Tooltip content={user.isFavorite ? "取消收藏" : "收藏"}>
              <span
                className="text-lg text-default-400 cursor-pointer active:opacity-50"
                onClick={async () => {
                  const updatedUsers = allLoanUsers.map((u) => {
                    if (u.id === user.id) {
                      if (u.isFavorite) {
                        u.relatedUserIds = u.relatedUserIds?.filter((id) => id !== currentUser?.id);
                      } else {
                        u.relatedUserIds?.push(currentUser?.id);
                      }
                      u.isFavorite = !u.isFavorite;
                    }
                    return u;
                  });
                  
                  // 先更新本地状态
                  setAllLoanUsers(updatedUsers);
                  
                  // 更新服务器数据
                  const result = await updateLoanUser(user);
                  if (!('error' in result)) {
                    // 更新成功后重新获取数据
                    const freshData = await getAllLoanUsers();
                    freshData.forEach(user => {
                      if (!currentUser || !currentUser.id) return;
                      user.isFavorite = user.relatedUserIds?.includes(currentUser.id);
                    });
                    setAllLoanUsers(freshData);
                  }
                }}>
                  <Icon
                    icon={user.isFavorite ? "solar:star-bold" : "solar:star-linear"}
                    className={user.isFavorite ? "text-yellow-500" : ""}
                    width={20}
                  />
              </span>
            </Tooltip>
            <Tooltip content="进入">
              <span
                className="text-lg text-default-400 cursor-pointer active:opacity-50"
                onClick={() => {
                  if (user.cp === "mock") {
                    router.push(`/pages/cp/loan-user/${user.id}`);
                  }else {
                    router.push(`/pages/cp/loan-user/${user.id}/other-cps`);
                  }
                }}
              >
                <RightIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, [allLoanUsers]);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>所有准入账号</div>
      <Button
        isIconOnly
        variant="light"
        onPress={() => {
          if (isLoading) return;
          initial();
        }}
        isDisabled={isLoading}
      >
        <Icon icon="mdi:refresh" width="24" height="24" />
      </Button>
    </div>
  );

  return (
    <>
      {isLoading ? (
        <div className="flex justify-center items-center h-[200px]">
          <Spinner size="lg" />
        </div>
      ) : (
        <Table className={props.className} topContent={topContent}>
          <TableHeader columns={columns}>
            {(column) => (
              <TableColumn
                key={column.uid}
                align={"start"}
              >
                {column.name}
              </TableColumn>
            )}
          </TableHeader>
          <TableBody items={allLoanUsers}>
            {(user) => (
              <TableRow key={user.id}>
                {(columnKey) => (
                  <TableCell>{renderCell(user, columnKey)}</TableCell>
                )}
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </>
  );
}
