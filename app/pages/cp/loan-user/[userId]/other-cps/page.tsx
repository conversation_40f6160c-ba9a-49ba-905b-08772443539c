"use client";

import {
  Accordion, AccordionItem, addToast, Button, Card, CardBody,
  Checkbox, CheckboxGroup,
  Chip, Divider, Input, Modal, ModalBody, ModalContent, Modal<PERSON>ooter,
  ModalHeader, NumberInput, Select, SelectItem, Spinner, Tab, Table, TableBody, TableCell,
  TableColumn, TableHeader, TableRow, Tabs } from "@heroui/react";
import { Icon } from "@iconify/react";
import {
  InterfaceLog, limitStatusNameMap,
  LoanUser,
  loanUserStatusNameMap,
} from "@/types/cp/loanUser";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/cjs/styles/prism";
import { Link } from "@heroui/link";
import { getLoanUserById, updateLoanUser } from "@/actions/cp/loanUserActions";
import React, { useEffect, useState } from "react";
import { getUserInterfaceLogs } from "@/actions/cp/interfaceLogAction";
import { useParams } from "next/navigation";
import {
  creditInfoQuery,
} from "@/actions/cp/otherCps/creditInfoQuery";
import { QF_TARGET_URL } from "@/utils/thirdUrls";
import ProductInfoTable from "@/components/cp/productInfo/productInfoTable";
import { LoanApplyStatusEnum } from "@/types/cp/loanInfo";
import { CreditInfo, CreditStatusEnum } from "@/types/cp/creditInfo";
import { interfaceContainer } from "@/app/cp/interfaceContainer";

export default function Page() {
  const [loanUser, setLoanUser] = useState<LoanUser>();
  const [logs, setLogs] = useState<InterfaceLog[]>([]);
  const { userId } = useParams();
  const [isLoanUserLoading, setIsLoanUserLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isInterfaceLoading, setIsInterfaceLoading] = useState(false);
  const [creditInfo, setCreditInfo] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      setIsLoanUserLoading(true);
      const loanUser = await getLoanUserById(parseInt(userId as string));

      const creditInfo = await creditInfoQuery(loanUser?.userId || "", QF_TARGET_URL);
      setCreditInfo(creditInfo);
      setLoanUser(loanUser);

      const userCreditInfo = loanUser?.creditInfo as unknown as CreditInfo;
      if (creditInfo?.status === 1) {
        userCreditInfo.status.applyStatus = LoanApplyStatusEnum.SUCCESS;
        userCreditInfo.status.creditStatus = CreditStatusEnum.NORMAL;
      }else if (creditInfo?.status === 2) {
        userCreditInfo.status.applyStatus = LoanApplyStatusEnum.PENDING;
        userCreditInfo.status.creditStatus = CreditStatusEnum.PENDING;
      }else if (creditInfo?.status === 3) {
        userCreditInfo.status.applyStatus = LoanApplyStatusEnum.SUCCESS;
        userCreditInfo.status.creditStatus = CreditStatusEnum.EXPIRED;
      }else if (creditInfo?.status === 4) {
        userCreditInfo.status.applyStatus = LoanApplyStatusEnum.FAILED;
        userCreditInfo.status.creditStatus = CreditStatusEnum.REJECTED;
      }

      updateLoanUser(loanUser);
      setIsLoanUserLoading(false);
    };

    fetchData();
  }, [refreshTrigger]);


  const refreshInterfaceLog = async () => {
    if (!loanUser?.id) {
      return;
    }
    setIsInterfaceLoading(true);
    try {
      const logs = await getUserInterfaceLogs(loanUser?.id);
      setLogs(logs);
    } finally {
      setIsInterfaceLoading(false);
    }
  };

  const tabSelectionChange = async (key: React.Key) => {
    if (key === "interfaceLog") {
      await refreshInterfaceLog();
    }
  };

  return (<>
    <Tabs
      aria-label="Options"
      isVertical={true}
      size={"lg"}
      onSelectionChange={tabSelectionChange}

    >
      <Tab key="credit" className={"w-full"} title="授信">
        <Card>
          <CardBody>
            <div className="flex justify-between items-center mb-4">
              <h4>用户状态</h4>
              <Button
                isIconOnly
                variant="light"
                onPress={() => setRefreshTrigger((prev) => prev + 1)}
              >
                <Icon icon="mdi:refresh" width="24" height="24" />
              </Button>
            </div>
            {isLoanUserLoading ? (
              <div className="flex justify-center items-center h-40">
                <Spinner size="lg" color="primary" />
              </div>
            ) :  (
              <>
                <div className="grid grid-cols-4 gap-4 mb-4">
                  <Input
                    isDisabled
                    label="授信状态"
                    value={
                      loanUserStatusNameMap[
                        loanUser?.creditInfo?.status?.applyStatus
                      ]
                    }
                  />

                  <Input
                    isDisabled
                    label="额度状态"
                    value={
                      limitStatusNameMap[creditInfo.status] || "未知"
                    }
                  />

                  <Input
                    isDisabled
                    label="授信额度"
                    value={creditInfo.creditLimit}
                  />

                  <Input
                    isDisabled
                    label="可用额度"
                    value={creditInfo.remainLimit}
                  />
                </div>

                <ProductInfoTable
                  productInfos={creditInfo.productInfos}
                  setProductInfos={setCreditInfo}
                />
              </>
            )}
          </CardBody>
        </Card>
      </Tab>

      <Tab key="loan" className={"w-full"} title="用信&还款">
        <Card>
          <CardBody>

          </CardBody>
        </Card>
      </Tab>

      <Tab key="coupon" className={"w-full"} title="优惠券">
        <Card>
          <CardBody>

          </CardBody>
        </Card>
      </Tab>

      <Tab key="bankCard" className={"w-full"} title="银行卡">
        <Card>
          <CardBody>

          </CardBody>
        </Card>
      </Tab>

      <Tab key="clearOff" className={"w-full"} title="结清证明">
        <Card>
          <CardBody>

          </CardBody>
        </Card>
      </Tab>

      <Tab key="interfaceLog" className={"w-full"} title="接口日志">
        <Card>
          <CardBody>
            <div className="flex justify-between items-center mb-4">
              <h4>接口日志</h4>
              <Button
                isIconOnly
                variant="light"
                onPress={refreshInterfaceLog}
              >
                <Icon icon="mdi:refresh" width="24" height="24" />
              </Button>
            </div>
            {isInterfaceLoading ? (
              <div className="flex justify-center items-center h-40">
                <Spinner size="lg" color="primary" />
              </div>
            ) : logs.length === 0 ? (
              <div className="flex justify-center items-center h-40 text-gray-500">
                暂无接口日志数据
              </div>
            ) : (
              <Accordion variant="shadow">
                {logs.map((log) => (
                  <AccordionItem
                    key={log.id}
                    aria-label={log.interfaceName}
                    title={
                      <div className="flex justify-between items-center">
                        <span>
                          接口名称：{log.interfaceName}
                        </span>
                        <span>
                          请求时间： {log.createTime.toLocaleString()}
                        </span>
                      </div>
                    }
                  >
                    <div className="grid grid-cols-2 gap-4">
                      <Card>
                        <CardBody>
                          <div className="mb-2">
                              <span className="font-bold text-lg">
                                请求数据
                              </span>
                          </div>
                          <pre className="p-4 rounded-lg overflow-auto max-h-[500px]">
                              <SyntaxHighlighter
                                language="json"
                                style={oneDark}
                              >
                                {JSON.stringify(
                                  JSON.parse(log.requestData),
                                  null,
                                  2,
                                )}
                              </SyntaxHighlighter>
                            </pre>
                        </CardBody>
                      </Card>
                      <Card>
                        <CardBody>
                          <div className="mb-2">
                              <span className="font-bold text-lg">
                                响应数据
                              </span>
                          </div>
                          <pre className="p-4 rounded-lg overflow-auto max-h-[500px]">
                              <SyntaxHighlighter
                                language="json"
                                style={oneDark}
                              >
                                {JSON.stringify(
                                  JSON.parse(log.responseData),
                                  null,
                                  2,
                                )}
                              </SyntaxHighlighter>
                            </pre>
                        </CardBody>
                      </Card>
                    </div>
                    <div className={"flex items-center mt-4 gap-4"}>
                      <div className="text-sm text-gray-500">
                        创建时间: {log.createTime.toLocaleString()}
                      </div>
                      <Link
                        size={"sm"}
                        href={
                          interfaceContainer[log.interfaceName as keyof typeof interfaceContainer]?.docUrl
                        }
                      >
                        前往接口文档
                      </Link>
                    </div>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </CardBody>
        </Card>
      </Tab>
    </Tabs>
  </>
  );
}
