"use client";
import {
  Dropdown,
  DropdownI<PERSON>,
  DropdownMenu,
  Dropdown<PERSON><PERSON>ger, <PERSON><PERSON>, <PERSON>overContent, <PERSON>overTrigger,
  User, <PERSON>dal, <PERSON>dalContent, ModalHeader, ModalBody, Button, Tabs, Tab, Card, CardBody, CardFooter
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";

import React, { useEffect, useState } from "react";

import useSceneStore from "@/store/scene";
import { useUserStore } from "@/store/user";
import { usePathname, useRouter } from "next/navigation";
import Sidebar from "@/app/pages/cp/Sidebar";
import { getMockCpQrCode } from "@/actions/cp/getMockCpQrCodeAction";


export const items: SidebarItem[] = [
  {
    key: "myLoanUsers",
    href: "/pages/cp/loan-user/my",
    icon: "solar:home-2-linear",
    title: "我的准入账号",
  },
  {
    key: "allLoanUsers",
    href: "/pages/cp/loan-user/all",
    icon: "solar:users-group-rounded-linear",
    title: "所有准入账号",
  },
  {
    key: "yzm",
    href: "/pages/cp/small-tools/yzm",
    icon: "mdi:lock",
    title: "验证码",
  },
  {
    key: "sfz",
    href: "/pages/cp/small-tools/sfz",
    icon: "solar:card-outline",
    title: "身份证银行卡号生成器",
  },
  {
    key: "qr-code",
    href: "/pages/cp/small-tools/qr-code",
    icon: "mdi:qrcode",
    title: "二维码生成器",
  },
];

export const sectionItems: SidebarItem[] = [
  {
    key: "准入账号",
    title: "准入账号",
    items: [
      {
        key: "myLoanUsers",
        href: "/pages/cp/loan-user/my",
        icon: "solar:home-2-linear",
        title: "我的准入账号",
      },
      {
        key: "allLoanUsers",
        href: "/pages/cp/loan-user/all",
        icon: "solar:users-group-rounded-linear",
        title: "所有准入账号",
      },
    ],
  },
  {
    key: "常用工具",
    title: "常用工具",
    items: [
      {
        key: "yzm",
        href: "/pages/cp/small-tools/yzm",
        icon: "mdi:lock",
        title: "验证码",
      },
      {
        key: "sfz",
        href: "/pages/cp/small-tools/sfz",
        icon: "solar:card-outline",
        title: "身份证银行卡号生成器",
      },
      {
        key: "qr-code",
        href: "/pages/cp/small-tools/qr-code",
        icon: "mdi:qrcode",
        title: "二维码生成器",
      },
    ],
  },
];
export enum SidebarItemType {
  Nest = "nest",
}

export type SidebarItem = {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  type?: SidebarItemType.Nest;
  startContent?: React.ReactNode;
  endContent?: React.ReactNode;
  items?: SidebarItem[];
  className?: string;
};

const systemDescriptions = {
  "0": "用于云测环境的模拟系统，支持完整的业务流程测试和验证",
  "1": "用于三方对接的模拟系统，支持与外部系统的集成测试",
  "2": "用于日常巡检接口报错数据分析和已处理记录管理",
  "3": "配置变更文件追踪"
};

const systems = [
  { key: "0", label: "云测Mock系统", url: "https://mock1.example.com", category: "mock", tutorialUrl: "https://mock1.example.com/tutorial" },
  { key: "1", label: "三方对接Mock系统", url: "https://mock2.example.com", category: "mock", tutorialUrl: "https://mock2.example.com/tutorial" },
  { key: "2", label: "巡检工具", url: "https://inspection.example.com", category: "inspection", tutorialUrl: "https://inspection.example.com/tutorial" },
  { key: "3", label: "配置变更", url: "https://filechange.example.com", category: "fileChange", tutorialUrl: "https://filechange.example.com/tutorial" },
];

export default function CpSidebar() {
  const { theme, setTheme } = useTheme();
  useSceneStore((state) => state.setScenes);
  const logout = useUserStore((state) => state.logoutUser);
  const user = useUserStore((state) => state.currentUser);
  const pathname = usePathname();
  const defaultSelectedKey = items.find(item => item.href === pathname)?.key || "myLoanUsers";
  const setCurrentSystem = useUserStore((state) => state.setCurrentSystem);
  const [username, setUsername] = useState<string>("");
  const router = useRouter();
  const [showSystemModal, setShowSystemModal] = React.useState(false);
  const [mockCpQrCode, setMockCpQrCode] = useState<string>("https://content-test-drcn.hihonorcdn.com/honorWallet/c607a59dedcb38e10e15332c6650279d_20250724150214.png");

  // 退出登录的处理
  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  // 跳转使用教程
  const handleTutorial = (): void => {
    window.open("https://elink.e.hihonor.com/wiki/S0o0wKcP4iyyJCk8R2llIGkkrxc", "_blank");
  };


  const handleSystemChange = async (systemKey: string) => {
    setCurrentSystem(systemKey);
    if (systemKey === "0") {
      router.push("/pages/scene/create");
    }
    if (systemKey === "1") {
      router.push("/pages/cp/loan-user/my");
    }

    if (systemKey === "2") {
      router.push("/pages/daily-inspection");
    }

    if (systemKey === "3") {
      router.push("/pages/file-change");
    }
  };

  useEffect(() => {
    (async () => {
      const qrCode = await getMockCpQrCode();
      setMockCpQrCode(qrCode!);
    })();
  })

  return (
    <div className="flex flex-col items-center w-56 min-h-[calc(100vh-2rem)] my-4 ml-2 bg-gray-400/30 dark:bg-gray-800/30 backdrop-blur-md rounded-2xl border border-divider shadow-lg h-full">
      <div className="flex items-center w-full py-2 px-2">
        <Dropdown placement="bottom-start">
          <DropdownTrigger>
            <User
              as="button"
              className="rounded-none flex flex-row justify-normal flex-1 bg-transparent transition-transform"
              name={user?.username}
            />
          </DropdownTrigger>
          <DropdownMenu aria-label="User Actions" variant="flat">
            <DropdownItem key="logout" onPress={handleLogout}>
              退出
            </DropdownItem>
            <DropdownItem key="tutorial" onPress={handleTutorial}>
              使用教程
            </DropdownItem>
            <DropdownItem key="switch-system" onPress={() => setShowSystemModal(true)}>
              切换系统
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Icon
          icon={theme === 'dark' ? "solar:sun-linear" : "solar:moon-linear"}
          className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors mr-2"
          width={20}
          height={20}
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        />
        <Icon
          icon="solar:logout-2-linear"
          className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors"
          width={20}
          height={20}
          onClick={handleLogout}
        />
      </div>

      <div className="flex-grow w-full flex flex-col">
        <Sidebar defaultSelectedKey={defaultSelectedKey} isCompact={false} items={sectionItems} />
      </div>

      <div className="flex flex-col items-center mb-2">
        <img  src={mockCpQrCode} alt="二维码" className="w-32 h-32 object-contain rounded-lg shadow" />
        <span className="text-xs text-gray-500 mt-2">账号中心扫码授信</span>
      </div>

      {showSystemModal && (
        <Modal isOpen={showSystemModal} onClose={() => setShowSystemModal(false)} placement="top-center">
          <ModalContent>
            <ModalHeader className="flex items-center justify-between">
              <span>切换系统</span>
            </ModalHeader>
            <ModalBody>
              <Tabs defaultSelectedKey="mock" className="w-full">
                <Tab key="mock" title="Mock工具">
                  <div className="grid grid-cols-2 gap-4">
                    {systems.filter(system => system.category === 'mock').map((system) => (
                      <Card
                        key={system.key}
                        className="cursor-pointer transition-all"
                      >
                        <CardBody>
                          <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                          <p className="mt-2 text-sm text-foreground-500">
                            {systemDescriptions[system.key as keyof typeof systemDescriptions]}
                          </p>
                        </CardBody>
                        <CardFooter className="flex flex-col gap-2">
                          <Button
                            className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                            onPress={() => handleSystemChange(system.key)}
                            color="primary"
                            variant="light"
                          >
                            进入系统
                          </Button>
                          {system.tutorialUrl && (
                            <Button
                              className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                              onPress={() => { window.open(system.tutorialUrl, '_blank'); }}
                              variant="light"
                            >
                              使用教程
                            </Button>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </Tab>
                <Tab key="inspection" title="巡检工具">
                  <div className="grid grid-cols-2 gap-4">
                    {systems.filter(system => system.category === 'inspection').map((system) => (
                      <Card
                        key={system.key}
                        className="cursor-pointer transition-all"
                      >
                        <CardBody>
                          <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                          <p className="mt-2 text-sm text-foreground-500">
                            {systemDescriptions[system.key as keyof typeof systemDescriptions]}
                          </p>
                        </CardBody>
                        <CardFooter className="flex flex-col gap-2">
                          <Button
                            className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                            onPress={() => handleSystemChange(system.key)}
                            color="primary"
                            variant="light"
                          >
                            进入系统
                          </Button>
                          {system.tutorialUrl && (
                            <Button
                              className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                              onPress={() => { window.open(system.tutorialUrl, '_blank'); }}
                              variant="light"
                            >
                              使用教程
                            </Button>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </Tab>
                <Tab key="fileChange" title="配置变更">
                  <div className="grid grid-cols-2 gap-4">
                    {systems.filter(system => system.category === 'fileChange').map((system) => (
                      <Card
                        key={system.key}
                        className="cursor-pointer transition-all"
                      >
                        <CardBody>
                          <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                          <p className="mt-2 text-sm text-foreground-500">
                            {systemDescriptions[system.key as keyof typeof systemDescriptions]}
                          </p>
                        </CardBody>
                        <CardFooter className="flex flex-col gap-2">
                          <Button
                            className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                            onPress={() => handleSystemChange(system.key)}
                            color="primary"
                            variant="light"
                          >
                            进入系统
                          </Button>
                          {system.tutorialUrl && (
                            <Button
                              className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                              onPress={() => { window.open(system.tutorialUrl, '_blank'); }}
                              variant="light"
                            >
                              使用教程
                            </Button>
                          )}
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                </Tab>
              </Tabs>
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </div>
  );
}
