"use client";

import { useUserStore } from "@/store/user";
import { useEffect, useState } from "react";
import { reportTrackEvent } from "@/actions/cp/eventTrackingAction";
import { EventTrackingNameTypeEnum } from "@/types/cp/eventTracking";
import styles from "../yzm/loading.module.css";

export default function Page() {
  const user = useUserStore((state) => state.currentUser);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    reportTrackEvent(user?.id || 0, user?.username || "", EventTrackingNameTypeEnum.SMALL_TOOLS_SFZ);
  }, []);

  return (
    <div className={styles.container} style={{ height: '100vh' }}>
      {isLoading && <div className={styles.spinner} />}
      <iframe
        src="https://www.tl.beer/randbankcard.html"
        className="w-full h-full rounded-xl"
        title="身份证&银行卡生成器"
        allowFullScreen
        onLoad={() => setIsLoading(false)}
      />
    </div>
  );
}
