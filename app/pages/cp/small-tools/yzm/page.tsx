"use client"

import React, { useEffect, useState } from "react";
import { useUserStore } from "@/store/user";
import { reportTrackEvent } from "@/actions/cp/eventTrackingAction";
import { EventTrackingNameTypeEnum } from "@/types/cp/eventTracking";
import styles from "./loading.module.css";

export default function Page() {
  const user = useUserStore((state) => state.currentUser);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    reportTrackEvent(user?.id || 0, user?.username || "", EventTrackingNameTypeEnum.SMALL_TOOLS_YZM);
  }, []);

  return (
    <div className={styles.container}>
      {isLoading && <div className={styles.spinner} />}
      <iframe
        src="https://hnid-test-drcn.cloud.hihonor.com/authcode/"
        className="w-full h-full rounded-xl"
        title="验证码"
        allowFullScreen
        onLoad={() => setIsLoading(false)}
      />
    </div>
  );
}