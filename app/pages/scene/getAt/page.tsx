"use client";
import {
  Button,
  NumberInput,
  Select,
  SelectItem,
  Input,
  Spacer,
  Tooltip, DatePicker, addToast,
} from "@heroui/react";
import { createScene, getAtByCode, getScenesByUserId } from "@/actions/sceneActions";
import React, { useEffect, useState } from "react";
import LoanOrderTable from "../../../../components/loanOrder/LoanOrderTable";
import { state } from "sucrase/dist/types/parser/traverser/base";
import { Scene } from "@/types";

let result: "";

export default function SceneCreatePage() {
  const [code, setCode] = useState("");
  const [result, setResult] = useState("");
  // let code: "";

  const submit = async () => {
    // 直接调用 Server Action
    const response = await getAtByCode(code);

    // 检查 Server Action 的返回结果
    if (response) {
      setResult(response);
      addToast({
        title: "获取成功",
        color: "success"
      });
    }
  };

  const handleCopyAT = () => {
    navigator.clipboard.writeText(result);
    alert('已复制到剪贴板');
  };

  const handleCopyInspect = () => {
    const dataStr = `wallet_deviceInfo
    {"versionCode":100020901,"packageName":"com.hihonor.id","deviceModel":"MAA-AN10","appFingerprint":"EDE02A47F935EC8990EF9CD1DEE9D0E9353ED612DAD85900966F79A972EBE071","country":"CN","language":"zh-CN"}

    wallet_userInfo
    {"userId":"6160086500000582164","accessToken":"${result}","deviceId":"2519DCCDCEFC79D1C0F9B63405CB630ED6AA5F00D624F85E0B7E71A72052BFB2","cid":"honorwallet","x-sub-cid":"","x-loc":""}

    sdkVersionCode
    90007000`;

    navigator.clipboard.writeText(dataStr);
    alert('已复制到剪贴板');
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText("https://hnid-test-drcn.cloud.honor.com/CAS/portal/loginAuth.html?validated=true&themeName=blue&service=https%3A%2F%2Fhnoauth-login-test-drcn.cloud.honor.com%2Foauth2%2Fv3%2Flogin%3Faccess_type%3Doffline%26appBrand%3DHONOR%26client_id%3D220619456%26code_challenge_method%3DS256%26display%3Dpage%26flowID%3D17db0531-ea9f-4471-accf-24282ccf3e6b%26h%3D1747895292.5460%26include_granted_scopes%3Dtrue%26lang%3Dzh-cn%26nonce%3Dabcd4321%26prompt%3Dlogin%26redirect_uri%3Dhonorid%253A%252F%252Fredirect_url%26response_type%3Dcode%26scope%3Dopenid%2Bprofile%26state%3Dabcd4321%26v%3D633f69522442ae31196dc04db1ca8720d4867b899ca41bb09d3753ce73e53162&loginChannel=90000300&reqClientType=90&lang=zh-cn&clientID=220619456");
    alert('已复制获取Code链接到剪贴板');
  };

  return (
    <div className="px-4 py-2 w-full h-full">
      <div className="flex flex-row gap-2 items-center">
        <div className="text-2xl font-bold">根据Code获取AT</div>
      </div>

      <Spacer y={4} />

      <button onClick={handleCopyCode} className="p-2 bg-blue-500 text-white rounded">
        获取Code链接
      </button>

      <br />
      <br />
      <br />

      <Input
        isRequired
        className={"mb-4"}
        label="Code"
        labelPlacement="outside"
        name="Code"
        placeholder="请输入Code"
        onValueChange={(value) => setCode(value)}
      />

      <br />
      <br />

      <button
        className="p-2 bg-blue-500 text-white rounded"
        onClick={submit}
      >
        获取AT
      </button>

      <br />
      <br />
      <br />

      <textarea
        readOnly
        placeholder="AT值是"
        value={result}
        className="w-full p-2 border rounded mb-4"
      />

      <br />
      <br />
      <br />
      <button onClick={handleCopyAT} className="p-2 bg-blue-500 text-white rounded">
        复制AT
      </button>


      <br />
      <br />
      <br />
      <button onClick={handleCopyInspect} className="p-2 bg-blue-500 text-white rounded">
        复制Inspect
      </button>
    </div>
  );
}
