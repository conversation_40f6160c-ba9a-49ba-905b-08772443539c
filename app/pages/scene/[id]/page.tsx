"use client";
import { Tab, Tabs } from "@heroui/tabs";
import { Card, CardBody } from "@heroui/card";
import {
  addToast,
  Button,
  DatePicker,
  Input,
  NumberInput,
  Select,
  SelectItem,
} from "@heroui/react";
import { useParams } from "next/navigation";
import React, { useEffect } from "react";
import { parseDate } from "@internationalized/date";

import LoanOrderTable from "../../../../components/loanOrder/LoanOrderTable";
import ProductInfoTable from "../../../../components/productInfo/productInfoTable";

import useSceneStore from "@/store/scene";
import {
  creditStatuses,
  discountSupportStatuses,
  limitUseErrStatuses,
  modifyPhone,
  reofferSupportStatuses,
  repayResultStatuses,
  sendSmsScene,
  signStatuses,
} from "@/types";
import CouponTable from "@/components/coupon/CouponTable";
import { getSceneById, updateScene } from "@/actions/sceneActions";

export default function SceneDetailPage() {
  const { id } = useParams();

  const scene = useSceneStore((state) => state.currentScene);
  const setCurrentScene = useSceneStore((state) => state.setCurrentScene);

  const setProductInfos = useSceneStore(
    (state) => state.setCurrentSceneProductInfos,
  );

  const setLoanOrders = useSceneStore(
    (state) => state.setCurrentSceneLoanOrders,
  );

  const setCoupons = useSceneStore((state) => state.setCurrentSceneCoupons);

  const submit = () => {
    updateScene(scene);
    addToast({
      title: "保存成功",
      color: "success",
    });
  };

  useEffect(() => {
    getSceneById(parseInt(id)).then((data) => {
      setCurrentScene(data);
    });
  }, []);

  return (
    <div className="px-4 py-10 w-full h-full">
      <div className="flex flex-row gap-4">
        <Input
          isRequired
          className={"mb-4"}
          disabled={true}
          label="场景id"
          labelPlacement="outside"
          name="场景id"
          value={scene.id}
        />

        <Input
          isRequired
          className={"mb-4"}
          label="场景名称"
          labelPlacement="outside"
          name="场景名称"
          value={scene.name}
          onValueChange={(value) => setCurrentScene({ ...scene, name: value })}
        />
      </div>

      <Tabs aria-label="Options" isVertical={true} size={"lg"}>
        <Tab key="授信" className={"w-full"} title="授信">
          <Card>
            <CardBody className="grid grid-cols-5 gap-4">
              <Select
                className="max-w-xs"
                label="授信状态"
                selectedKeys={[scene.status?.toString()]}
                value={scene.status}
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    status: parseInt(key.anchorKey as string),
                  })
                }
              >
                {creditStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Select
                className="max-w-xs"
                label="额度不可用原因"
                selectedKeys={[scene.limitUseErrStatus?.toString()]}
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    limitUseErrStatus: parseInt(key.anchorKey as string),
                  })
                }
              >
                {limitUseErrStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              {scene.status === 1 ? (
                <>
                  <NumberInput
                    className="max-w-xs"
                    label="授信总额度"
                    minValue={10000}
                    step={10000}
                    value={scene.creditLimit}
                    onValueChange={(value) =>
                      setCurrentScene({ ...scene, creditLimit: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="可用额度"
                    minValue={10000}
                    step={10000}
                    value={scene.remainLimit}
                    onValueChange={(value) =>
                      setCurrentScene({ ...scene, remainLimit: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="单笔最小可借金额"
                    minValue={10000}
                    step={10000}
                    value={scene.minLoan}
                    onValueChange={(value) =>
                      setCurrentScene({ ...scene, minLoan: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="每月还款日"
                    maxValue={28}
                    minValue={1}
                    step={1}
                    value={scene.repayDay}
                    onValueChange={(value) =>
                      setCurrentScene({ ...scene, repayDay: value })
                    }
                  />
                  <ProductInfoTable
                    productInfos={scene.productInfos}
                    setProductInfos={setProductInfos}
                  />
                </>
              ) : (
                <></>
              )}
            </CardBody>
          </Card>
        </Tab>
        <Tab key="借款&还款" className={"w-full"} title="借款&还款">
          <div className={"flex gap-4 mb-4"}>
            <Select
              className="max-w-xs"
              defaultSelectedKeys={[
                scene.discountAmountInfo?.isSupport?.toString(),
              ]}
              label="是否支持优惠金额功能"
              onSelectionChange={(key) =>
                setCurrentScene({
                  ...scene,
                  discountAmountInfo: {
                    ...scene.discountAmountInfo,
                    isSupport: parseInt(key.anchorKey as string),
                  },
                })
              }
            >
              {discountSupportStatuses.map((status) => (
                <SelectItem key={status.key}>{status.label}</SelectItem>
              ))}
            </Select>

            <Select
              className="max-w-xs"
              defaultSelectedKeys={[scene.repayResultStatus?.toString()]}
              label="还款结果"
              onSelectionChange={(key) =>
                setCurrentScene({
                  ...scene,
                  repayResultStatus: parseInt(key.anchorKey as string),
                })
              }
            >
              {repayResultStatuses.map((status) => (
                <SelectItem key={status.key}>{status.label}</SelectItem>
              ))}
            </Select>
          </div>
          <Card>
            <CardBody>
              <LoanOrderTable
                loanOrders={scene.loanOrders}
                setLoanOrders={setLoanOrders}
              />
            </CardBody>
          </Card>
        </Tab>
        <Tab key="优惠券" className={"w-full"} title="优惠券">
          <Card>
            <CardBody>
              <CouponTable
                coupons={scene.coupons}
                setCoupons={setCoupons}
              />
            </CardBody>
          </Card>
        </Tab>

        <Tab key="Reoffer" className={"w-full"} title="Reoffer">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[
                  scene.reofferInfo?.supprtReoffer?.toString(),
                ]}
                label="是否支持Reoffer"
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    reofferInfo: {
                      ...scene.reofferInfo,
                      supprtReoffer: parseInt(key.anchorKey as string),
                    },
                  })
                }
              >
                {reofferSupportStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="签约" className={"w-full"} title="签约">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[scene.signInfo?.signStatus?.toString()]}
                label="签约状态"
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    signInfo: {
                      ...scene.signInfo,
                      signStatus: parseInt(key.anchorKey as string),
                    },
                  })
                }
              >
                {signStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Input
                isRequired
                className={"mb-4"}
                defaultValue={scene.signInfo?.signUrl}
                label="支付宝代扣签约链接"
                labelPlacement="outside"
                name="支付宝代扣签约链接"
                placeholder="请输入支付宝代扣签约链接"
                onValueChange={(value) =>
                  setCurrentScene({
                    ...scene,
                    signInfo: {
                      ...scene.signInfo,
                      signUrl: value,
                    },
                  })
                }
              />

              <DatePicker
                className={"w-48 mb-4"}
                defaultValue={parseDate(
                  scene.signInfo?.signUrlValidTime?.split(" ")[0] ||
                    "2025-05-08",
                )}
                label="链接有有效截止时间"
                onChange={(value) => {
                  setCurrentScene({
                    ...scene,
                    signInfo: {
                      ...scene.signInfo,
                      signUrlValidTime: value.toString() + " 00:00:0",
                    },
                  });
                }}
              />

              <Input
                isRequired
                className={"mb-4"}
                defaultValue={scene.signInfo?.contractUrl}
                label="协议地址"
                labelPlacement="outside"
                name="协议地址"
                placeholder="请输入协议地址"
                onValueChange={(value) =>
                  setCurrentScene({
                    ...scene,
                    signInfo: {
                      ...scene.signInfo,
                      contractUrl: value,
                    },
                  })
                }
              />

              <Input
                isRequired
                className={"mb-4"}
                defaultValue={scene.signInfo?.contractName}
                label="协议名称"
                labelPlacement="outside"
                name="协议名称"
                placeholder="请输入协议名称"
                onValueChange={(value) =>
                  setCurrentScene({
                    ...scene,
                    signInfo: {
                      ...scene.signInfo,
                      contractName: value,
                    },
                  })
                }
              />
            </CardBody>
          </Card>
        </Tab>
        <Tab key="短信 & 改号" className={"w-full"} title="短信 & 改号">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[scene.smsInfo?.status?.toString()]}
                label="发送验证码返回"
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    smsInfo: {
                      ...scene.smsInfo,
                      status: parseInt(key.anchorKey as string),
                      errorDesc: sendSmsScene.find(
                        (item) =>
                          item.key === parseInt(key.anchorKey as string),
                      )?.label,
                    },
                  })
                }
              >
                {sendSmsScene.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[scene.modifyPhone?.status?.toString()]}
                label="修改手机号返回"
                onSelectionChange={(key) =>
                  setCurrentScene({
                    ...scene,
                    modifyPhone: {
                      ...scene.modifyPhone,
                      status: key.anchorKey as string,
                      errorDesc: modifyPhone.find(
                        (item) =>
                          item.key === parseInt(key.anchorKey as string),
                      )?.label,
                    },
                  })
                }
              >
                {modifyPhone.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>

      <Button
        className={"fixed bottom-10 right-10"}
        color="primary"
        size={"lg"}
        type="submit"
        onPress={submit}
      >
        保存
      </Button>
    </div>
  );
}
