"use client";

import { Tab, Tabs } from "@heroui/tabs";
import { Card, CardBody } from "@heroui/card";
import {
  addToast,
  Button,
  DatePicker,
  Input,
  NumberInput,
  Select,
  SelectItem,
  Spacer,
  Tooltip,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";

import ProductInfoTable from "../../../../components/productInfo/productInfoTable";
import LoanOrderTable from "../../../../components/loanOrder/LoanOrderTable";

import {
  creditStatuses,
  discountSupportStatuses,
  limitUseErrStatuses,
  modifyPhone,
  reofferSupportStatuses,
  repayResultStatuses,
  RepayResultStatuses,
  sendSmsScene,
  signStatuses,
} from "@/types";
import useSceneStore from "@/store/scene";
import CouponTable from "@/components/coupon/CouponTable";
import { useUserStore } from "@/store/user";
import { createScene } from "@/actions/sceneActions";

export default function SceneCreatePage() {
  const user = useUserStore((state) => state.currentUser);
  const sceneDto = useSceneStore((state) => state.SceneDto);
  const setSceneDto = useSceneStore((state) => state.setSceneDto);
  const setSceneDtoProductInfos = useSceneStore(
    (state) => state.setSceneDtoProductInfos,
  );
  const setSceneDtoLoanOrders = useSceneStore(
    (state) => state.setSceneDtoLoanOrders,
  );

  const setSceneDtoCoupons = useSceneStore((state) => state.setSceneDtoCoupons);

  const submit = async () => {
    sceneDto.userId = user?.id;
    // 直接调用 Server Action
    const result = await createScene(sceneDto);

    // 检查 Server Action 的返回结果
    if ("error" in result) {
    } else {
      addToast({
        title: "创建成功",
        color: "success",
      });
      window.location.reload();
    }
  };

  return (
    <div className="px-4 py-2 w-full h-full">
      <div className="flex flex-row gap-2 items-center">
        <div className="text-2xl font-bold">创建场景</div>
        <Tooltip
          content={
            <div className="w-36 px-1 py-2">
              <div className="text-small font-bold">场景使用说明</div>
              <div className="text-tiny">
                系统中的每个场景（如“度小满已授信”）都对应一个用户状态。通过将目标场景的
                ID 配置到
                mockflag，开发者或测试人员可以轻松地将自己的账号模拟成所需的状态，以方便进行调试或测试。
              </div>
            </div>
          }
        >
          <Icon icon="mdi:question-mark-circle" />
        </Tooltip>
      </div>

      <Spacer y={4} />
      <Input
        isRequired
        className={"mb-4"}
        label="场景名称"
        labelPlacement="outside"
        name="场景名称"
        placeholder="请输入场景名称"
        onValueChange={(value) => setSceneDto({ ...sceneDto, name: value })}
      />

      <Tabs aria-label="Options" isVertical={true} size={"lg"}>
        {/*<Tab key="准入" className="w-full" title="准入">*/}
        {/*  <Card>*/}
        {/*    <CardBody className="flex flex-row gap-4">*/}
        {/*      <Select*/}
        {/*        className="max-w-xs"*/}
        {/*        defaultSelectedKeys={[sceneDto.accessStatus?.toString()]}*/}
        {/*        label="选择准入状态"*/}
        {/*        onSelectionChange={(key) =>*/}
        {/*          setSceneDto({*/}
        {/*            ...sceneDto,*/}
        {/*            accessStatus: parseInt(key.anchorKey as string),*/}
        {/*          })*/}
        {/*        }*/}
        {/*      >*/}
        {/*        {accessStatuses.map((accessStatus) => (*/}
        {/*          <SelectItem key={accessStatus.key}>*/}
        {/*            {accessStatus.label}*/}
        {/*          </SelectItem>*/}
        {/*        ))}*/}
        {/*      </Select>*/}

        {/*      {sceneDto.accessStatus === 1 ? (*/}
        {/*        <Select*/}
        {/*          className="max-w-xs"*/}
        {/*          defaultSelectedKeys={[sceneDto.supplier?.toString()]}*/}
        {/*          label="选择准入CP"*/}
        {/*          onSelectionChange={(key) =>*/}
        {/*            setSceneDto({*/}
        {/*              ...sceneDto,*/}
        {/*              supplier: parseInt(key.anchorKey as string),*/}
        {/*            })*/}
        {/*          }*/}
        {/*        >*/}
        {/*          {suppliers.map((supplier) => (*/}
        {/*            <SelectItem key={supplier.key}>{supplier.label}</SelectItem>*/}
        {/*          ))}*/}
        {/*        </Select>*/}
        {/*      ) : (*/}
        {/*        <></>*/}
        {/*      )}*/}
        {/*    </CardBody>*/}
        {/*  </Card>*/}
        {/*</Tab>*/}
        <Tab key="授信" className={"w-full"} title="授信">
          <Card>
            <CardBody className="grid grid-cols-5 gap-4">
              <Select
                className="max-w-xs"
                defaultSelectedKeys={[sceneDto.status?.toString()]}
                label="授信状态"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    status: parseInt(key.anchorKey as string),
                  })
                }
              >
                {creditStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Select
                className="max-w-xs"
                defaultSelectedKeys={[sceneDto.limitUseErrStatus?.toString()]}
                label="额度不可用原因"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    limitUseErrStatus: parseInt(key.anchorKey as string),
                  })
                }
              >
                {limitUseErrStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              {sceneDto.status === 1 ? (
                <>
                  <NumberInput
                    className="max-w-xs"
                    label="授信总额度"
                    minValue={10000}
                    step={10000}
                    value={sceneDto.creditLimit}
                    onValueChange={(value) =>
                      setSceneDto({ ...sceneDto, creditLimit: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="可用额度"
                    minValue={10000}
                    step={10000}
                    value={sceneDto.remainLimit}
                    onValueChange={(value) =>
                      setSceneDto({ ...sceneDto, remainLimit: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="单笔最小可借金额"
                    minValue={10000}
                    step={10000}
                    value={sceneDto.minLoan}
                    onValueChange={(value) =>
                      setSceneDto({ ...sceneDto, minLoan: value })
                    }
                  />
                  <NumberInput
                    className="max-w-xs"
                    label="每月还款日"
                    maxValue={28}
                    minValue={1}
                    step={1}
                    value={sceneDto.repayDay}
                    onValueChange={(value) =>
                      setSceneDto({ ...sceneDto, repayDay: value })
                    }
                  />
                  <ProductInfoTable
                    productInfos={sceneDto.productInfos}
                    setProductInfos={setSceneDtoProductInfos}
                  />
                </>
              ) : (
                <></>
              )}
            </CardBody>
          </Card>
        </Tab>
        <Tab key="借款&还款" className={"w-full"} title="借款&还款">
          <div className={"flex gap-4 mb-4"}>
            <Select
              className="max-w-xs"
              defaultSelectedKeys={[
                sceneDto.discountAmountInfo?.isSupport?.toString(),
              ]}
              label="是否支持优惠金额功能"
              onSelectionChange={(key) =>
                setSceneDto({
                  ...sceneDto,
                  discountAmountInfo: {
                    ...sceneDto.discountAmountInfo,
                    isSupport: parseInt(key.anchorKey as string),
                  },
                })
              }
            >
              {discountSupportStatuses.map((status) => (
                <SelectItem key={status.key}>{status.label}</SelectItem>
              ))}
            </Select>

            <Select
              className="max-w-xs"
              defaultSelectedKeys={[RepayResultStatuses.SUCCESS.toString()]}
              label="还款结果"
              onSelectionChange={(key) =>
                setSceneDto({
                  ...sceneDto,
                  repayResultStatus: parseInt(key.anchorKey as string),
                })
              }
            >
              {repayResultStatuses.map((status) => (
                <SelectItem key={status.key}>{status.label}</SelectItem>
              ))}
            </Select>
          </div>
          <Card>
            <CardBody className="grid grid-cols-5 gap-4">
              <LoanOrderTable
                loanOrders={sceneDto.loanOrders}
                setLoanOrders={setSceneDtoLoanOrders}
              />
            </CardBody>
          </Card>
        </Tab>
        <Tab key="优惠券" className={"w-full"} title="优惠券">
          <Card>
            <CardBody>
              <CouponTable
                coupons={sceneDto.coupons}
                setCoupons={setSceneDtoCoupons}
              />
            </CardBody>
          </Card>
        </Tab>

        <Tab key="Reoffer" className={"w-full"} title="Reoffer">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[
                  sceneDto.reofferInfo.supprtReoffer?.toString(),
                ]}
                label="是否支持Reoffer"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    reofferInfo: {
                      ...sceneDto.reofferInfo,
                      supprtReoffer: parseInt(key.anchorKey as string),
                    },
                  })
                }
              >
                {reofferSupportStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="签约" className={"w-full"} title="签约">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[sceneDto.signInfo.signStatus?.toString()]}
                label="签约状态"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    signInfo: {
                      ...sceneDto.signInfo,
                      signStatus: parseInt(key.anchorKey as string),
                    },
                  })
                }
              >
                {signStatuses.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Input
                isRequired
                className={"mb-4"}
                label="支付宝代扣签约链接"
                labelPlacement="outside"
                name="支付宝代扣签约链接"
                placeholder="请输入支付宝代扣签约链接"
                onValueChange={(value) =>
                  setSceneDto({
                    ...sceneDto,
                    signInfo: {
                      ...sceneDto.signInfo,
                      signUrl: value,
                    },
                  })
                }
              />

              <DatePicker
                className={"w-48 mb-4"}
                label="链接有有效截止时间"
                onChange={(value) => {
                  setSceneDto({
                    ...sceneDto,
                    signInfo: {
                      ...sceneDto.signInfo,
                      signUrlValidTime: value.toString() + " 00:00:0",
                    },
                  });
                }}
              />

              <Input
                isRequired
                className={"mb-4"}
                label="协议地址"
                labelPlacement="outside"
                name="协议地址"
                placeholder="请输入协议地址"
                onValueChange={(value) =>
                  setSceneDto({
                    ...sceneDto,
                    signInfo: {
                      ...sceneDto.signInfo,
                      contractUrl: value,
                    },
                  })
                }
              />

              <Input
                isRequired
                className={"mb-4"}
                label="协议名称"
                labelPlacement="outside"
                name="协议名称"
                placeholder="请输入协议名称"
                onValueChange={(value) =>
                  setSceneDto({
                    ...sceneDto,
                    signInfo: {
                      ...sceneDto.signInfo,
                      contractName: value,
                    },
                  })
                }
              />
            </CardBody>
          </Card>
        </Tab>
        <Tab key="短信 & 改号" className={"w-full"} title="短信 & 改号">
          <Card>
            <CardBody>
              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[sceneDto.smsInfo?.status?.toString()]}
                label="发送验证码返回"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    smsInfo: {
                      ...sceneDto.smsInfo,
                      status: parseInt(key.anchorKey as string),
                      errorDesc: sendSmsScene.find(
                        (item) =>
                          item.key === parseInt(key.anchorKey as string),
                      )?.label,
                    },
                  })
                }
              >
                {sendSmsScene.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>

              <Select
                className="max-w-xs mb-4"
                defaultSelectedKeys={[sceneDto.modifyPhone?.status?.toString()]}
                label="修改手机号返回"
                onSelectionChange={(key) =>
                  setSceneDto({
                    ...sceneDto,
                    modifyPhone: {
                      ...sceneDto.modifyPhone,
                      status: key.anchorKey as string,
                      errorDesc: modifyPhone.find(
                        (item) => item.key === parseInt(key.anchorKey as strin),
                      )?.labl,
                    },
                  })
                }
              >
                {modifyPhone.map((status) => (
                  <SelectItem key={status.key}>{status.label}</SelectItem>
                ))}
              </Select>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>

      <Button
        className={"fixed bottom-10 right-10"}
        color="primary"
        size={"lg"}
        type="submit"
        onPress={submit}
      >
        创建
      </Button>
    </div>
  );
}
