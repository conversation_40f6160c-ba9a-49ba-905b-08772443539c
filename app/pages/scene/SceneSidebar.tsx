"use client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import {
  addToast,
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Listbox,
  ListboxItem,
  ListboxSection,
  User,
  Popover, PopoverTrigger, PopoverContent 
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";

import useSceneStore from "@/store/scene";
import { useUserStore } from "@/store/user";
import { deleteSceneById, getScenesByUserId } from "@/actions/sceneActions";
import { delay } from "@/utils/timeUtils";

export const ListboxWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="w-full max-w-[260px] px-1 py-2 rounded-small">
    {children}
  </div>
);

export default function SceneSidebar() {
  const router = useRouter();
  const { theme, setTheme } = useTheme();
  const scenes = useSceneStore((state) => state.scenes);
  const setScenes = useSceneStore((state) => state.setScenes);
  const logout = useUserStore((state) => state.logoutUser);
  const user = useUserStore((state) => state.currentUser);

  useEffect(() => {
    const userId = user?.id;

    if (!userId) {
      setScenes([]);

      return;
    }

    fetchScenes();
  }, []);

  const fetchScenes = async () => {
    await delay(500);

    try {
      const result = await getScenesByUserId(user?.id);

      if ("error" in result) {
        setScenes([]); // 清空数据
      } else {
        setScenes(result);
      }
    } catch (err) {
      console.error("Error calling getScenesByUserId action:", err);
      setScenes([]); // 清空数据
    }
  };

  // 退出登录的处理
  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  const handleTutorial = (): void => {
    window.open("https://elink.e.hihonor.com/wiki/Au9Uw33bTiK2Kakg0azlpeuArwe", "_blank");
  };

  function RecentSceneDropdown(props: { sceneId: number }) {
    function deleteScene() {
      deleteSceneById(props.sceneId).then(() => {
        addToast({
          title: "删除成功",
          color: "success",
        });
        fetchScenes();
      });
    }

    return (
      <Dropdown>
        <DropdownTrigger>
          <Icon
            className="text-default-500 opacity-0 group-hover:opacity-100"
            icon="solar:menu-dots-bold"
            width={24}
          />
        </DropdownTrigger>
        <DropdownMenu
          aria-label="Dropdown menu with icons"
          className="py-2"
          variant="faded"
        >
          <DropdownItem
            key="delete"
            className="text-danger-500 data-[hover=true]:text-danger-500"
            color="danger"
            startContent={
              <Icon
                className="text-danger-500"
                height={20}
                icon="solar:trash-bin-minimalistic-linear"
                width={20}
              />
            }
            onPress={deleteScene}
          >
            删除
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    );
  }

  return (
    <div className="flex flex-col items-center w-56 min-h-screen bg-gray-100/50 dark:bg-content1/10 backdrop-blur-md border-r border-divider">
      <div className="flex items-center w-full py-2 mb-8 px-2">
        <Dropdown placement="bottom-start">
          <DropdownTrigger>
            <User
              as="button"
              className="rounded-none flex flex-row justify-normal flex-1 bg-transparent transition-transform"
              name={user?.username}
            />
          </DropdownTrigger>
          <DropdownMenu aria-label="User Actions" variant="flat">
            <DropdownItem key="logout" onPress={handleLogout}>
              退出
            </DropdownItem>
            <DropdownItem key="tutorial" onPress={handleTutorial}>
              使用教程
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Icon
          icon={theme === 'dark' ? "solar:sun-linear" : "solar:moon-linear"}
          className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors mr-2"
          width={20}
          height={20}
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        />
        <Icon
          icon="solar:logout-2-linear"
          className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors"
          width={20}
          height={20}
          onClick={handleLogout}
        />
      </div>

      <div className="w-full flex flex-col items-center">
        <Button
          className="w-3/4 mb-4"
          color={"primary"}
          radius="full"
          onPress={() => router.push("/pages/scene/create")}
        >
          创建新的场景
        </Button>

        <Button
          className="w-3/4"
          color={"primary"}
          radius="full"
          onPress={() => router.push("/pages/scene/getAt")}
        >
          获取AT
        </Button>

        <ListboxWrapper>
          <Listbox aria-label="Listbox menu with sections" variant="flat">
            <ListboxSection
              classNames={{
                base: "py-0",
                heading: "py-0 pl-[10px] text-small text-foreground-500",
              }}
              title="最近场景"
            >
              {scenes.map((scene) => (
                <ListboxItem
                  key={scene.id}
                  className="h-[44px] px-[12px] py-[10px] text-foreground-500 hover:text-foreground"
                  endContent={<RecentSceneDropdown sceneId={scene.id} />}
                  onPress={() => router.push(`/pages/scene/${scene.id}`)}
                >
                  {scene.name}
                </ListboxItem>
              ))}
            </ListboxSection>
          </Listbox>
        </ListboxWrapper>
      </div>
    </div>
  );
}
