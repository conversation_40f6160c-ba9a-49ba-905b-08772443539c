'use client';

import React, { useState, useEffect, useCallback, useRef, type ChangeEvent, type FC } from 'react';
import { DiffEditor } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';

// 导入所有需要的模块
import {
  uploadZipAndCreateSession,
  updateFileLines,
  getSessionById,
  getAllSessions,
  deleteSession,
  renameSession,
  exportSession,
  getSessionBasicInfo,
  getSessionFiles,
  getFileModificationHistory,
  updateSessionOrder,
} from "@/actions/fileChange/fileChange";
import { buildFileTree, type FileSystemNode, type FileInfoNode, type DirectoryInfoNode } from '@/utils/file-compare';
import { TreeView } from '@/components/treeView/TreeView';
import { BlameGutter } from '@/components/blameGutter/BlameGutter';
import FileHistory from '@/components/fileHistory/FileHistory';
import {
  Upload,
  FileText,
  Moon,
  Sun,
  Alert<PERSON>riangle,
  PanelLeftClose,
  PanelRightOpen,
  Loader2,
  Download,
  LogOut,
  History,
  Wifi,
  WifiOff
} from "lucide-react";
import { addToast, Tooltip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Button, RadioGroup, Radio } from "@heroui/react";
import { useUserStore } from "@/store/user";
import { DeleteIcon, EditIcon } from "@heroui/shared-icons";
import { delay } from "@/utils/timeUtils";
import { debounce } from "lodash";
import Editor, { loader } from '@monaco-editor/react';
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {CSS} from '@dnd-kit/utilities';
import type { DragEndEvent } from '@dnd-kit/core';

// 导入协作编辑相关组件
import { useCollaboration } from '@/hooks/useCollaboration';
import { OnlineUsers } from '@/components/collaboration/OnlineUsers';
import { CursorIndicator } from '@/components/collaboration/CursorIndicator';
import { CollaborationNotification } from '@/components/collaboration/CollaborationNotification';
import { CollaborationSettings } from '@/components/collaboration/CollaborationSettings';


// 为会话列表项定义类型
interface SessionListItem {
  id: string;
  name: string;
  creatorName: string | null;
  createdAt: Date;
}

// 为可读性，定义 SelectedFile 类型
interface SelectedFile extends FileInfoNode {}

const HomePage: FC = () => {
  // 原有状态
  const userName = useUserStore((state) => state.currentUser)?.username;
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [editorContent, setEditorContent] = useState<string | undefined>(undefined);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [fileTree, setFileTree] = useState<FileSystemNode | null>(null);
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [theme, setTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null;
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      return savedTheme || (prefersDark ? 'dark' : 'light');
    }
    return 'light';
  });
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [sessions, setSessions] = useState<SessionListItem[]>([]);
  const [isSessionListLoading, setIsSessionListLoading] = useState(true);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingSessionName, setEditingSessionName] = useState<string>('');
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState<boolean>(false);
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [exportType, setExportType] = useState<'all' | 'modified'>('all');
  const logout = useUserStore((state) => state.logoutUser);

  // 协作编辑相关状态
  const [isCollaborationEnabled, setIsCollaborationEnabled] = useState(true);
  const [notificationSensitivity, setNotificationSensitivity] = useState(50); // 默认中等敏感度
  
  // 用于防抖通知的ref
  const lastNotificationTimeRef = useRef<number>(0);
  
  // 用于防抖发送文件变更的ref
  const debouncedSendFileChangeRef = useRef<ReturnType<typeof debounce> | null>(null);

  const editorRef = useRef<monaco.editor.IStandaloneDiffEditor | null>(null);
  const gutterRef = useRef<HTMLDivElement | null>(null);
  const scrollListenerRef = useRef<monaco.IDisposable | null>(null);

  // 协作编辑hook
  const {
    isConnected,
    onlineUsers,
    cursors,
    selectFile,
    sendFileChange,
    sendCursorChange,
    disconnect
  } = useCollaboration({
    sessionId: currentSessionId || '',
    username: userName || '',
    fileId: selectedFile?.id,
    onFileChange: (change) => {
      // 处理其他用户的文件变更
      if (change.userId !== 'current-user' && selectedFile?.id === change.fileId) {
        // 只有当内容真正发生变化时才更新和显示通知
        if (change.content !== editorContent) {
          setEditorContent(change.content);
          
          // 根据通知敏感度决定是否显示通知
          if (notificationSensitivity > 0) {
            const now = Date.now();
            const minInterval = notificationSensitivity <= 25 ? 5000 : // 低敏感度：5秒
                              notificationSensitivity <= 50 ? 2000 : // 中等敏感度：2秒
                              notificationSensitivity <= 75 ? 1000 : // 高敏感度：1秒
                              500; // 最高敏感度：0.5秒
            
            if (now - lastNotificationTimeRef.current > minInterval) {
              addToast({
                title: "文件已更新",
                description: `${change.username} 修改了文件`,
                color: "primary",
              });
              lastNotificationTimeRef.current = now;
            }
          }
        }
      }
    },
    onCursorChange: (cursor) => {
      // 处理其他用户的光标变更
      console.log('其他用户光标变更:', cursor);
    },
    onUsersUpdate: (users) => {
      console.log('在线用户更新:', users);
    },
    onUserJoined: (user) => {
      addToast({
        title: "用户加入",
        description: `${user.username} 加入了编辑`,
        color: "success",
      });
    },
    onUserLeft: (user) => {
      addToast({
        title: "用户离开",
        description: `${user.username} 离开了编辑`,
        color: "warning",
      });
    }
  });

  loader.config({
    paths: {
      vs: 'https://content-test-drcn.hihonorcdn.com/honorWallet/vs',
    },
  });

  // dnd-kit sensors 必须在组件顶层调用，不能放在 JSX 里
  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
  );

  // 初始化防抖函数
  useEffect(() => {
    debouncedSendFileChangeRef.current = debounce((fileId: string, content: string) => {
      if (isCollaborationEnabled && isConnected) {
        sendFileChange(fileId, content);
      }
    }, 500); // 500ms 防抖

    return () => {
      debouncedSendFileChangeRef.current?.cancel();
    };
  }, [isCollaborationEnabled, isConnected, sendFileChange]);


  // 原有的 fetchSessionData 函数
  const fetchSessionData = useCallback(async (sessionId: string, keepSelectedPath?: string) => {
    setIsLoading(true);
    setError(null);
    try {
      // 首先获取会话基本信息
      const sessionInfo = await getSessionBasicInfo(sessionId);
      if (!sessionInfo) {
        throw new Error('会话不存在');
      }

      // 创建基础文件树结构（不包含文件内容）
      const basicTree: DirectoryInfoNode = {
        name: sessionInfo.name,
        type: 'directory',
        path: '',
        children: []
      };

      setFileTree(basicTree);

      // 分页加载文件
      const loadFiles = async (page: number = 1, pageSize: number = 50) => {
        const filesResult = await getSessionFiles(sessionId, {
          page,
          pageSize,
          includeLines: true
        });

        if (filesResult) {
          // 构建完整的文件树
          // 将 Prisma 返回的文件数据转换为 DbFile 格式
          const dbFiles = filesResult.files.map(file => ({
            id: file.id,
            path: file.path,
            originalContent: file.originalContent,
            lines: (file as any).lines || []
          }));
          const fullTree = buildFileTree(sessionInfo.name, dbFiles);
          setFileTree(fullTree);

          // 如果还有更多文件，继续加载
          if (page < filesResult.totalPages) {
            // 使用 setTimeout 避免阻塞UI
            setTimeout(() => {
              loadFiles(page + 1, pageSize);
            }, 100);
          } else {
            // 所有文件加载完成，处理文件选择
            if (keepSelectedPath) {
              const findAndSelectFile = (node: FileSystemNode) => {
                if (node.type === 'file' && node.path === keepSelectedPath) {
                  handleFileSelect(node);
                } else if (node.type === 'directory') {
                  node.children.forEach(findAndSelectFile);
                }
              };
              findAndSelectFile(fullTree);
            }
          }
        }
      };

      // 开始加载文件
      await loadFiles();

    } catch (e: any) {
      setError(`加载会话失败: ${e.message}`);
      setFileTree(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 原有的初始化会话列表
  useEffect(() => {
    (async () => {
      await delay(500);
      setIsSessionListLoading(true);
      const sessionList = await getAllSessions();
      setSessions(sessionList);
      setIsSessionListLoading(false);
    })();
  }, []);

  // 修改后的防抖保存函数
  const debouncedSave = useCallback(
    debounce(async (fileId: string, content: string, user: string, sessionId: string, filePath: string) => {
      setIsSaving(true);
      setError(null);

      const result = await updateFileLines(fileId, content, user);
      setIsSaving(false); // 无论成功失败，都结束保存状态

      if (result.success) {
        // 保存成功后，仅在后台更新文件树用于显示 'modified' 状态，
        // 不要重新选择文件，也不要触碰 editorContent。
        // 我们可以创建一个新的函数来处理这个后台更新。
        updateFileTreeAfterSave(sessionId, filePath);
      } else {
        addToast({ title: "保存失败", color: "danger" });
        setError('自动保存失败: ' + result.error);
      }
    }, 1000),
    // 注意：这里移除了 fetchSessionData 的依赖，因为它会导致循环。
    // 我们会用一个更安全的方式来调用它。
    []
  );

  // 新增一个专门用于后台更新的函数
  const updateFileTreeAfterSave = useCallback(async (sessionId: string, updatedFilePath: string) => {
    try {
      const sessionInfo = await getSessionBasicInfo(sessionId);
      if (!sessionInfo) return;

      const filesResult = await getSessionFiles(sessionId, {
        page: 1,
        pageSize: 1000, // 获取所有文件
        includeLines: true
      });

      if (filesResult) {
        // 将 Prisma 返回的文件数据转换为 DbFile 格式
        const dbFiles = filesResult.files.map(file => ({
          id: file.id,
          path: file.path,
          originalContent: file.originalContent,
          lines: (file as any).lines || []
        }));
        const newTree = buildFileTree(sessionInfo.name, dbFiles);
        // 直接更新树，不触发任何文件选择操作
        setFileTree(newTree);
      }
    } catch (e) {
      console.error("后台更新文件树失败:", e);
    }
  }, []);


  // 监听编辑器内容变化，自动保存
  useEffect(() => {
    if (!selectedFile || !editorContent || !userName) return;

    if (userName) {
      debouncedSave(
        selectedFile.id,
        editorContent,
        userName,
        currentSessionId || '',
        selectedFile.path
      );

      // 发送协作编辑变更 - 添加防抖，避免频繁发送
      if (isCollaborationEnabled && isConnected) {
        debouncedSendFileChangeRef.current?.(selectedFile.id, editorContent);
      }
    }

    return () => {
      debouncedSave.cancel();
    };
  }, [editorContent, selectedFile, userName, currentSessionId, debouncedSave, isCollaborationEnabled, isConnected]); // 移除 sendFileChange 依赖

  // 主题切换逻辑
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
    localStorage.setItem('theme', theme);
  }, [theme]);
  // 原有的处理函数
  const toggleTheme = () => setTheme(prev => (prev === 'light' ? 'dark' : 'light'));
  const toggleSidebar = () => setIsSidebarOpen(prev => !prev);

  const handleSessionSelect = async (sessionId: string) => {
    if (currentSessionId === sessionId) return;

    await fetchSessionData(sessionId);
    setCurrentSessionId(sessionId);
  };

  // 新增这个 useEffect 来同步 selectedFile 和 fileTree
  useEffect(() => {
    // 如果没有文件树或没有选中的文件，则什么都不做
    if (!fileTree || !selectedFile?.id) {
      return;
    }

    // 在新的 fileTree 中查找与当前 selectedFile.id 匹配的节点
    const findFileInTree = (node: FileSystemNode, fileId: string): FileInfoNode | null => {
      if (node.type === 'file' && node.id === fileId) {
        return node;
      }
      if (node.type === 'directory') {
        for (const child of node.children) {
          const found = findFileInTree(child, fileId);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };

    const updatedFileNode = findFileInTree(fileTree, selectedFile.id);

    // 如果找到了更新后的节点，并且它的内容确实发生了变化，就更新 selectedFile
    // （检查内容变化可以避免不必要的重渲染，虽然这里直接设置也没问题）
    if (updatedFileNode && updatedFileNode.content !== selectedFile.content) {
      setSelectedFile(updatedFileNode);
    }

  }, [fileTree, selectedFile?.id]); // 依赖 fileTree 和 selectedFile.id

  // 其他原有处理函数保持不变...
  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!userName?.trim()) {
      setError('请输入您的名字或昵称，用于记录修改信息。');
      alert('请输入您的名字后再上传。');
      return;
    }

    setIsLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('creatorName', userName);

    const result = await uploadZipAndCreateSession(formData);

    if (result.success && result.sessionId) {
      const sessionList = await getAllSessions();
      setSessions(sessionList);

      addToast({
        title: "上传成功",
        color: "success",
      });
    } else {
      setError(`上传失败: ${result.error}`);
      addToast({
        title: "上传失败",
        color: "danger",
      });
    }

    setIsLoading(false);
    event.target.value = '';
  };

  const handleFileSelect = (file: FileInfoNode) => {
    setEditorContent(undefined);
    setSelectedFile(null);

    setTimeout(() => {
      setSelectedFile(file);
      setEditorContent(file.content);
      
      // 通知其他用户选择了新文件
      if (isCollaborationEnabled && isConnected) {
        selectFile(file.id, file.content);
      }
    }, 0);
  };

  // 修改后的编辑器挂载处理
  const handleDiffEditorMount = (editor: monaco.editor.IStandaloneDiffEditor, monacoInstance: any) => {
    editorRef.current = editor;
    const modifiedEditor = editor.getModifiedEditor();

    // 清理之前的内容变更监听器
    if ((modifiedEditor as any)._onDidChangeModelContentListener) {
      (modifiedEditor as any)._onDidChangeModelContentListener.dispose();
    }

    // 内容变更监听
    (modifiedEditor as any)._onDidChangeModelContentListener = modifiedEditor.onDidChangeModelContent(() => {
      const value = modifiedEditor.getValue();
      setEditorContent(value);
    });

    // **** MODIFICATION START ****
    // 3. 同步滚动逻辑

    // a. 如果存在旧的滚动监听器，先将其清理掉
    if (scrollListenerRef.current) {
      scrollListenerRef.current.dispose();
    }

    // b. 为编辑器的滚动事件添加新的监听器
    scrollListenerRef.current = modifiedEditor.onDidScrollChange((e) => {
      // 当编辑器滚动时，e.scrollTop 会给出当前的垂直滚动像素值
      // 检查 gutterRef 是否已附加到 DOM
      if (gutterRef.current) {
        // 将编辑器的滚动位置同步赋值给 BlameGutter 的 div
        gutterRef.current.scrollTop = e.scrollTop;
      }
    });

    // c. 初始同步：确保编辑器加载时就对齐
    if (gutterRef.current) {
      gutterRef.current.scrollTop = modifiedEditor.getScrollTop();
    }

    // 添加光标位置监听
    modifiedEditor.onDidChangeCursorPosition((e) => {
      if (isCollaborationEnabled && isConnected && selectedFile) {
        // 确保光标位置是有效的数字
        const line = typeof e.position.lineNumber === 'number' ? e.position.lineNumber - 1 : 0;
        const column = typeof e.position.column === 'number' ? e.position.column - 1 : 0;
        
        sendCursorChange(selectedFile.id, {
          line,
          column
        });
      }
    });
    // **** MODIFICATION END ****
  };

  // 其他原有函数保持不变...
  const handleDeleteSession = async (sessionId: string) => {
    if (!confirm('确定要删除这个会话吗？此操作不可撤销。')) {
      return;
    }

    const result = await deleteSession(sessionId);
    if (result.success) {
      addToast({
        title: "删除成功",
        color: "success",
      });

      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
        setFileTree(null);
        setSelectedFile(null);
        setEditorContent(undefined);
      }

      const sessionList = await getAllSessions();
      setSessions(sessionList);
    } else {
      addToast({
        title: "删除失败",
        color: "danger",
      });
    }
  };

  const handleRenameSession = async (sessionId: string, newName: string) => {
    if (!newName.trim()) {
      addToast({
        title: "会话名称不能为空",
        color: "warning",
      });
      return;
    }

    const result = await renameSession(sessionId, newName);
    if (result.success) {
      addToast({
        title: "重命名成功",
        color: "success",
      });

      const sessionList = await getAllSessions();
      setSessions(sessionList);
      setEditingSessionId(null);
    } else {
      addToast({
        title: "重命名失败",
        color: "danger",
      });
    }
  };

  const startEditing = (sessionId: string, currentName: string) => {
    setEditingSessionId(sessionId);
    setEditingSessionName(currentName);
  };

  const cancelEditing = () => {
    setEditingSessionId(null);
    setEditingSessionName('');
  };

  const confirmRename = (sessionId: string) => {
    handleRenameSession(sessionId, editingSessionName);
  };

  const handleExportSession = () => {
    if (!currentSessionId) {
      addToast({ title: "请先选择一个会话", color: "warning" });
      return;
    }
    setIsExportDialogOpen(true);
  };

  const confirmExport = async () => {
    setIsExporting(true);
    setIsExportDialogOpen(false);
    try {
      if (!currentSessionId) throw new Error('No session id');
      const result = await exportSession(currentSessionId, { exportType }); // 传递类型
      if (result.success && result.zipBuffer && result.fileName) {
        const blob = new Blob([new Uint8Array(result.zipBuffer)], {
          type: 'application/zip'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        const stats = (result as any).stats;
        if (stats) {
          addToast({
            title: `导出成功 - ${stats.modifiedFiles}/${stats.totalFiles} 个文件`,
            color: "success",
          });
        } else {
          addToast({
            title: "导出成功",
            color: "success",
          });
        }
      } else {
        addToast({
          title: "导出失败",
          color: "danger",
        });
      }
    } catch (error) {
      addToast({
        title: "导出失败",
        color: "danger",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const getModifiedFiles = (node: FileSystemNode): string[] => {
    const modifiedFiles: string[] = [];

    const traverse = (currentNode: FileSystemNode) => {
      if (currentNode.type === 'file' && currentNode.isModified) {
        modifiedFiles.push(currentNode.path);
      } else if (currentNode.type === 'directory') {
        currentNode.children.forEach(traverse);
      }
    };

    traverse(node);
    return modifiedFiles;
  };

  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  // 单独的SortableItem组件
  function SortableItem({id, children}: {id: string, children: React.ReactNode}) {
    const {attributes, listeners, setNodeRef, transform, transition, isDragging} = useSortable({id});
    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      zIndex: isDragging ? 10 : undefined,
      opacity: isDragging ? 0.6 : 1,
      boxShadow: isDragging ? '0 2px 8px rgba(0,0,0,0.15)' : undefined,
    };
    return (
      <li ref={setNodeRef} style={style} {...attributes} {...listeners}>
        {children}
      </li>
    );
  }

  return (
    <main className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
      <header className="flex items-center gap-6 px-4 md:px-6 py-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm flex-shrink-0">
        <div className="flex items-center gap-3">
          <button onClick={toggleSidebar} className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700" aria-label="Toggle sidebar">
            {isSidebarOpen ? <PanelLeftClose size={20} /> : <PanelRightOpen size={20} />}
          </button>
          <h1 className="text-lg md:text-xl font-semibold dark:text-gray-100 whitespace-nowrap">配置变更文件追踪</h1>
        </div>

        <div className="ml-auto flex items-center gap-2 md:gap-4">
          {/* 协作编辑状态指示器 */}
          {currentSessionId && (
            <div className="flex items-center gap-2">
              <Tooltip content={isCollaborationEnabled ? "协作编辑已启用" : "协作编辑已禁用"}>
                <button
                  onClick={() => setIsCollaborationEnabled(!isCollaborationEnabled)}
                  className={`p-2 rounded-full transition-colors ${
                    isCollaborationEnabled && isConnected
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-gray-400 dark:text-gray-500'
                  }`}
                  aria-label="Toggle collaboration"
                >
                  {isCollaborationEnabled && isConnected ? <Wifi size={18} /> : <WifiOff size={18} />}
                </button>
              </Tooltip>
              
              {/* 在线用户显示 */}
              {isCollaborationEnabled && isConnected && (
                <OnlineUsers users={onlineUsers} currentUser={userName} />
              )}
            </div>
          )}

          <button
            onClick={handleExportSession}
            disabled={!currentSessionId || isExporting}
            className={`flex items-center gap-2 px-4 py-2 text-white font-medium rounded-lg transition-colors ${
              !currentSessionId || isExporting
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700'
            }`}
            aria-label="Export current session" 
          >
            {isExporting ? <Loader2 size={18} className="animate-spin" /> : <Download size={18} />}
            <span className="hidden md:inline">{isExporting ? '导出中...' : '导出ZIP'}</span>
          </button>
          <label className={`flex items-center gap-2 px-4 py-2 text-white font-medium rounded-lg cursor-pointer transition-colors ${isLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}>
            {isLoading ? <Loader2 size={18} className="animate-spin" /> : <Upload size={18} />}
            <span className="hidden md:inline">{isLoading ? '处理中...' : '上传ZIP'}</span>
            <input type="file" accept=".zip" onChange={handleFileChange} disabled={isLoading} className="hidden" />
          </label>
          <button onClick={toggleTheme} className="p-2 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700" aria-label="Toggle theme">
            {theme === 'light' ? <Moon size={20} /> : <Sun size={20} />}
          </button>
          <LogOut
            className={"cursor-pointer"}
            width={20}
            height={20}
            onClick={handleLogout}
          />
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        <aside className={`transition-all duration-300 ease-in-out border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-hidden ${
          isSidebarOpen ? 'w-[300px] md:w-[350px]' : 'w-0'
        }`}>
          <div className="flex flex-col h-full">
            {/* 会话列表部分 */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h3 className="text-lg font-semibold mb-2">历史版本列表</h3>
              {isSessionListLoading ? (
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Loader2 size={16} className="animate-spin" /> 加载中...
                </div>
              ) : sessions.length > 0 ? (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={({active, over}: DragEndEvent) => {
                    if (active.id !== over?.id) {
                      const oldIndex = sessions.findIndex(s => s.id === active.id);
                      const newIndex = sessions.findIndex(s => s.id === over?.id);
                      const newSessions = arrayMove(sessions, oldIndex, newIndex);
                      setSessions(newSessions);
                      
                      // 保存顺序到后端
                      updateSessionOrder(
                        newSessions.map((s, idx) => ({ id: s.id, order: idx }))
                      ).then(result => {
                        if (!result.success) {
                          console.error('保存会话顺序失败:', result.error);
                          addToast({
                            title: "保存顺序失败",
                            color: "danger",
                          });
                        }
                      });
                    }
                  }}
                >
                  <SortableContext items={sessions.map(s => s.id)} strategy={verticalListSortingStrategy}>
                    <ul className="space-y-1 max-h-48 overflow-y-auto">
                      {sessions.map(session => (
                        <SortableItem key={session.id} id={session.id}>
                          <div className={`flex items-center justify-between p-2 rounded-md transition-colors text-sm group ${
                            currentSessionId === session.id
                              ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                          }`}>
                            <div className="flex-1 min-w-0">
                              {editingSessionId === session.id ? (
                                <div className="space-y-1">
                                  <input
                                    type="text"
                                    value={editingSessionName}
                                    onChange={(e) => setEditingSessionName(e.target.value)}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        confirmRename(session.id);
                                      } else if (e.key === 'Escape') {
                                        cancelEditing();
                                      }
                                    }}
                                    className="w-full px-2 py-1 text-sm border rounded bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                                    autoFocus
                                  />
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() => confirmRename(session.id)}
                                      className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                                    >
                                      确认
                                    </button>
                                    <button
                                      onClick={cancelEditing}
                                      className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                                    >
                                      取消
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <button
                                  onClick={() => handleSessionSelect(session.id)}
                                  className="w-full text-left"
                                >
                                  <p className="font-semibold truncate">{session.name}</p>
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    {new Date(session.createdAt).toLocaleString('zh-CN', {
                                      year: 'numeric',
                                      month: '2-digit',
                                      day: '2-digit',
                                      hour: '2-digit',
                                      minute: '2-digit',
                                      second: '2-digit',
                                      hour12: false
                                    })} by {session.creatorName}
                                  </p>
                                </button>
                              )}
                            </div>
                            {editingSessionId !== session.id && (
                              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditing(session.id, session.name);
                                  }}
                                  className="p-1 rounded text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-all"
                                  title="重命名会话"
                                >
                                  <Tooltip color="primary" content="编辑">
                                    <span className="text-lg text-primary cursor-pointer active:opacity-50">
                                      <EditIcon />
                                    </span>
                                  </Tooltip>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteSession(session.id);
                                  }}
                                  className="p-1 rounded text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 transition-all"
                                  title="删除会话"
                                >
                                  <Tooltip color="danger" content="删除">
                                    <span className="text-lg text-danger cursor-pointer active:opacity-50">
                                      <DeleteIcon />
                                    </span>
                                  </Tooltip>
                                </button>
                              </div>
                            )}
                          </div>
                        </SortableItem>
                      ))}
                    </ul>
                  </SortableContext>
                </DndContext>
              ) : (
                <p className="text-sm text-gray-500">没有找到历史会话。</p>
              )}
            </div>

            {/* 文件树部分 */}
            <div className="flex-grow overflow-y-auto">
              {currentSessionId && isLoading && !fileTree ? (
                <div className="p-4 text-center">正在加载文件树...</div>
              ) : fileTree ? (
                <TreeView
                  root={fileTree}
                  onFileSelect={handleFileSelect}
                  modifiedFiles={fileTree ? getModifiedFiles(fileTree) : []}
                />
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  {error ? (
                    <div className="text-red-500">
                      <AlertTriangle size={32} className="mx-auto mb-2" />
                      <p>{error}</p>
                    </div>
                  ) : (
                    <p>请从上方选择一个会话以查看文件。</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </aside>

        <section className="flex-1 flex flex-col p-4 md:p-6 min-w-0">
          {selectedFile ? (
            <div className="flex flex-col h-full min-h-0">
              <div className="flex items-center justify-between pb-3 mb-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <div className="flex items-center min-w-0">
                  <FileText size={20} className="mr-3 text-gray-500 dark:text-gray-400 flex-shrink-0" />
                  <h2 className="text-lg font-mono font-medium text-gray-700 dark:text-gray-300 truncate">{selectedFile.path}</h2>
                </div>
                <div className="flex items-center gap-4">
                  {/* 协作编辑设置 */}
                  <CollaborationSettings
                    isCollaborationEnabled={isCollaborationEnabled}
                    onToggleCollaboration={setIsCollaborationEnabled}
                    notificationSensitivity={notificationSensitivity}
                    onNotificationSensitivityChange={setNotificationSensitivity}
                  />
                  
                  {/* 历史记录按钮 */}
                  <button
                    onClick={() => setIsHistoryOpen(true)}
                    className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    title="查看修改历史"
                  >
                    <History size={16} />
                    <span className="hidden sm:inline">历史记录</span>
                  </button>
                  
                  {/* 光标指示器 - 只在有多个用户在同一文件时显示 */}
                  {isCollaborationEnabled && isConnected && cursors.length > 0 && (
                    <CursorIndicator 
                      cursors={cursors} 
                      currentUser={userName} 
                      position="inline" 
                      currentFileId={selectedFile?.id}
                    />
                  )}
                  

                  {/*{isSaving && <div className="flex items-center gap-2 text-sm text-gray-500"><Loader2 size={16} className="animate-spin" /> saving...</div>}*/}
                </div>
              </div>
              <div className="flex-1 flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden relative">
                <BlameGutter lines={selectedFile.lines} ref={gutterRef} />
                <div className="flex-1">
                  <DiffEditor
                    key={selectedFile?.id}
                    height="100%"
                    language="yaml"
                    original={selectedFile.originalContent}
                    modified={editorContent}
                    onMount={handleDiffEditorMount}
                    theme={theme === 'light' ? 'vs-light' : 'vs-dark'}
                    options={{
                      renderSideBySide: true,
                      readOnly: false,
                      minimap: { enabled: false },
                      wordWrap: 'off',
                    }}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 dark:text-gray-400">
              <FileText size={48} className="mb-4 text-gray-400" />
              <p className="font-semibold">请从左侧选择一个文件进行查看和编辑</p>
              <p className="text-sm">修改将自动保存并记录修改人</p>
            </div>
          )}
        </section>
      </div>
      
      {/* 文件历史记录弹窗 */}
      <FileHistory
        fileId={selectedFile?.id ?? ''}
        isOpen={isHistoryOpen}
        onClose={() => setIsHistoryOpen(false)}
      />
      {/* 使用 Heroui 组件的导出类型选择弹窗 */}
      <Modal isOpen={isExportDialogOpen} onClose={() => setIsExportDialogOpen(false)} placement="center">
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader>请选择导出类型</ModalHeader>
              <ModalBody>
                <RadioGroup
                  label="导出内容"
                  value={exportType}
                  onValueChange={val => setExportType(val as 'all' | 'modified')}
                  orientation="vertical"
                >
                  <Radio value="all">全部文件</Radio>
                  <Radio value="modified">仅导出已修改文件</Radio>
                </RadioGroup>
              </ModalBody>
              <ModalFooter>
                <Button variant="flat" color="secondary" onPress={onClose}>
                  取消
                </Button>
                <Button color="primary" onPress={confirmExport} isLoading={isExporting}>
                  {isExporting ? "导出中..." : "确认导出"}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* 协作编辑通知组件 */}
      <CollaborationNotification />
    </main>
  );
};

export default HomePage;
