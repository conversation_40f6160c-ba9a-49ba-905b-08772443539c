'use client';

import { addToast, But<PERSON>, Select, SelectItem, toast } from "@heroui/react";
import { Table, TableHeader, TableBody, TableColumn, TableRow, TableCell, Tooltip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input, DatePicker, DateRangePicker } from "@heroui/react";
import { useEffect, useState, useCallback } from "react";
import {
  uploadInspectionData,
  saveInspectionData,
  getInspectionHistory, updateInspectionData, deleteInspectionData, fetchInspectionData
} from "@/actions/dailyInspection/dailyInspectionActions";
import dayjs from "dayjs";
import { InspectionData } from "@/types/dailyInspection/dailyInspection";
import { useUserStore } from "@/store/user";
import { Icon } from "@iconify/react";
import { DeleteIcon } from "@heroui/shared-icons";
import { useTheme } from "next-themes";
import { today, getLocalTimeZone } from "@internationalized/date";
import {I18nProvider} from "@react-aria/i18n";
import { LineChart, Line, ResponsiveContainer, Tooltip as RechartsTooltip } from 'recharts';

export default function Page() {
  const { theme, setTheme } = useTheme();
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<InspectionData[]>([]);
  const [historyData, setHistoryData] = useState<InspectionData[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<InspectionData | null>(null);
  const [processInfo, setProcessInfo] = useState({
    processedBy: '',
    processReason: '',
    processMeasures: ''
  });
  const logout = useUserStore((state) => state.logoutUser);
  const [effectiveDate, setEffectiveDate] = useState<Date>(new Date());
  const [showExistingData, setShowExistingData] = useState(true);
  const [apiLoading, setApiLoading] = useState(false);
  const [dateRange, setDateRange] = useState<{
    start: any;
    end: any;
  }>({
    start: today(getLocalTimeZone()).subtract({ days: 1 }), // 默认查询最近24小时
    end: today(getLocalTimeZone())
  });
  const [trendMap, setTrendMap] = useState<Record<string, number[]>>({});

  // 处理日期范围变化
  const handleDateRangeChange = (value: any) => {
    if (value && value.start && value.end) {
      setDateRange(value);
    }
  };

  // 加载历史数据
  useEffect(() => {
    const loadHistoryData = async () => {
      try {
        const result = await getInspectionHistory();
        if ('error' in result) {
          throw new Error(result.error);
        }

        setHistoryData(result.data);
      } catch (error) {
        console.error('Failed to load history data:', error);
        addToast({
          title: "加载历史数据失败",
          color: "danger",
        });
      }
    };

    loadHistoryData();
  }, []);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      
      try {
        setLoading(true);
        const formData = new FormData();
        formData.append('file', selectedFile);

        const result = await uploadInspectionData(formData);

        console.log("result: ", result);
        if ('error' in result) {
          throw new Error(result.error);
        }

        // 确保数据被正确设置
        const parsedData = result.data;
        setData(parsedData);
        
        // 上传成功后自动进行对比
        compareWithHistory(parsedData);
      } catch (error) {
        console.error('Upload error:', error);
        addToast({
          title: "文件上传失败",
          color: "danger",
        });
        // 清空数据
        setData([]);
      } finally {
        setLoading(false);
      }
    }
  };

  const fetch7DayTrends = async (baseStart: any, baseEnd: any) => {
    const trends: Record<string, number[]> = {};
    const allKeys = new Set<string>();
    const dayjsBase = dayjs(baseEnd).subtract(1, 'day');
    const dailyData: Record<string, number>[] = [];
    for (let i = 6; i >= 0; i--) {
      const day = dayjsBase.subtract(i, 'day');
      const start = day.startOf('day').unix();
      const end = day.endOf('day').unix();
      const result = await fetchInspectionData(start, end);
      const dayMap: Record<string, number> = {};
      if (result.success && Array.isArray(result.data)) {
        result.data.forEach((item: any) => {
          const key = `${item['出错接口']}|${item['错误码']}|${item['失败原因']}|${item[' 原始错误码']}|${item['原始错误原因']}`;
          dayMap[key] = Number(item['次数']) || 0;
          allKeys.add(key);
        });
      }
      dailyData.push(dayMap);
    }
    // 补0逻辑
    allKeys.forEach(key => {
      trends[key] = dailyData.map(dayMap => dayMap[key] ?? 0);
    });
    setTrendMap(trends);
  };

  const handleApiDataFetch = async () => {
    try {
      setApiLoading(true);
      const startDate = dateRange.start.toDate ? dateRange.start.toDate(getLocalTimeZone()) : new Date(dateRange.start);
      const endDate = dateRange.end.toDate ? dateRange.end.toDate(getLocalTimeZone()) : new Date(dateRange.end);
      const startTimestamp = Math.floor(startDate.getTime() / 1000);
      const endTimestamp = Math.floor(endDate.getTime() / 1000);
      // 先查主数据
      const result = await fetchInspectionData(startTimestamp, endTimestamp);
      if (!result.success) {
        throw new Error(result.error);
      }
      if (!result.data || !Array.isArray(result.data)) {
        throw new Error('Invalid response format');
      }
      const convertedData: InspectionData[] = result.data.map((item: any, index: number) => ({
        traceId: item.traceId?.[0] || `api-${Date.now()}-${index}`,
        interface: item['出错接口'] || '',
        errorCode: item['错误码'] || '',
        errorMessage: item['失败原因'] || '',
        originalErrorCode: item[' 原始错误码'] || '',
        originalErrorMessage: item['原始错误原因'] || '',
        count: Number(item['次数']) || 0,
        uniqueCount: Number(item['去重后的次数']) || 0,
        isNew: true,
        processedBy: '',
        processReason: '',
        processMeasures: '',
        createdAt: new Date()
      }));
      const sortedData = convertedData.sort((a, b) => b.uniqueCount - a.uniqueCount);
      // 先查7天趋势，保证 trendMap 已经准备好
      await fetch7DayTrends(startDate, endDate);
      setData(sortedData);
      compareWithHistory(sortedData);
    } catch (error) {
      console.error('API fetch error:', error);
      addToast({
        title: "接口数据获取失败",
        description: error instanceof Error ? error.message : "未知错误",
        color: "danger",
      });
    } finally {
      setApiLoading(false);
    }
  };

  const compareWithHistory = (parsedData: InspectionData[]) => {
    if (!parsedData.length) return;

    // 重置所有数据的 isNew 标记
    const resetData = parsedData.map(item => ({
      ...item,
      isNew: false
    }));

    // 找出新增的错误
    const newData = resetData.filter(current => {
      return !historyData.some(history => 
        history.interface === current.interface &&
        history.errorCode === current.errorCode &&
        history.errorMessage === current.errorMessage &&
        history.originalErrorCode === current.originalErrorCode &&
        history.originalErrorMessage === current.originalErrorMessage
      );
    });

    // 更新数据，标记新增项
    const updatedData = resetData.map(item => ({
      ...item,
      isNew: newData.some(newItem => 
        newItem.interface === item.interface &&
        newItem.errorCode === item.errorCode &&
        newItem.errorMessage === item.errorMessage &&
        newItem.originalErrorCode === item.originalErrorCode &&
        newItem.originalErrorMessage === item.originalErrorMessage
      )
    }));

    setData(updatedData);

    // 显示对比结果
    const totalCount = updatedData.length;
    const newCount = newData.length;
    const existingCount = totalCount - newCount;

    addToast({
      title: `对比结果: 总共 ${totalCount} 条，新增 ${newCount} 条，已存在 ${existingCount} 条`,
      color: "success",
    });
  };

  const handleProcess = (item: InspectionData) => {
    setCurrentItem(item);
    setProcessInfo({
      processedBy: '',
      processReason: '',
      processMeasures: ''
    });
    setIsModalOpen(true);
  };

  const handleProcessSubmit = async () => {
    if (!currentItem) return;
    
    if (!processInfo.processedBy) {
      addToast(
        {
          title: "请填写巡检人",
          color: "danger",
        }
      )
      return;
    }

    const updatedItem = {
      id: crypto.randomUUID(),
      ...currentItem,
      processedBy: processInfo.processedBy,
      processReason: processInfo.processReason || '',
      processMeasures: processInfo.processMeasures || '',
      createdAt: new Date()
    };

    // 保存到数据库
    const result = await saveInspectionData(updatedItem);
    if ('error' in result) {
      addToast(
        {
          title: "保存失败",
          color: "danger",
        }
      )
      return;
    }

    // 更新当前数据
    const updatedData = data.map(item => 
      item.traceId === currentItem.traceId ? updatedItem : item
    );
    setData(updatedData);

    // 添加到历史记录
    setHistoryData([updatedItem, ...historyData]);
    
    addToast(
      {
        title: "保存成功",
        color: "success",
      }
    )
    setIsModalOpen(false);
  };

  const columns = [
    { uid: 'interface', name: '接口' },
    { uid: 'errorCode', name: '错误码' },
    { uid: 'errorMessage', name: '失败原因' },
    { uid: 'originalErrorCode', name: '原始错误码' },
    { uid: 'originalErrorMessage', name: '原始错误原因' },
    { uid: 'count', name: '次数' },
    { uid: 'uniqueCount', name: '去重次数' },
    { uid: 'trend', name: '次数趋势' },
    { uid: 'traceId', name: 'TraceId' },
    { uid: 'isNew', name: '是否新增' },
    { uid: 'actions', name: '操作' }
  ];

  const historyColumns = [
    { uid: 'interface', name: '接口' },
    { uid: 'errorCode', name: '错误码' },
    { uid: 'errorMessage', name: '失败原因' },
    { uid: 'originalErrorCode', name: '原始错误码' },
    { uid: 'originalErrorMessage', name: '原始错误原因' },
    { uid: 'processedBy', name: '巡检人' },
    { uid: 'processReason', name: '报错原因' },
    { uid: 'processMeasures', name: '处理措施' },
    { uid: 'createdAt', name: '处理时间' },
    { uid: 'delead-actions', name: '操作' }
  ];

  const handleDelete = async (id: string) => {
      const result = await deleteInspectionData(id);
      if ('error' in result) {
        addToast({
          title: "删除失败",
          color: "danger",
        });
      }

      setHistoryData(historyData.filter(item => item.id !== id));
      addToast({
        title: "删除成功",
        color: "success",
      });
  };

  const renderCell = useCallback((item: InspectionData, columnKey: string) => {
    const cellValue = item[columnKey as keyof InspectionData];

    switch (columnKey) {
      case 'actions':
        if (!item.isNew) {
          return null;
        }
        return (
          <div className="relative flex items-center gap-2">
            <Button
              size="sm"
              color={item.processedBy ? "success" : "primary"}
              onPress={() => handleProcess(item)}
              isDisabled={!!item.processedBy}
            >
              {item.processedBy ? '已处理' : '标记处理'}
            </Button>
          </div>
        );
        case 'delead-actions':
        return (
          <Tooltip color="danger" content="删除记录">
            <span
              className="text-lg text-danger cursor-pointer active:opacity-50"
              onClick={() => item.id && handleDelete(item.id)}
            >
              <DeleteIcon />
            </span>
          </Tooltip>
        );
      case 'isNew':
        return (
          <span className={`px-2 py-1 rounded-full text-xs ${item.isNew ? 'bg-green-900 text-green-200' : 'bg-gray-700 text-gray-300'}`}>
            {item.isNew ? '是' : '否'}
          </span>
        );
      case 'createdAt':
        return (
          <span>
            {cellValue ? dayjs(cellValue as Date).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </span>
        );
      case 'processedBy':
        return (
          <Input
            size="sm"
            defaultValue={item.processedBy || ''}
            onChange={async (e) => {
              const updatedItem = { ...item, processedBy: e.target.value };
              const updatedData = data.map(d =>
                d.traceId === item.traceId ? updatedItem : d
              );
              setData(updatedData);
              updateInspectionData(updatedItem);
            }}
          />
        );
      case 'processReason':
        return (
          <Input
            size="sm"
            defaultValue={item.processReason || ''}
            onChange={async (e) => {
              const updatedItem = { ...item, processReason: e.target.value };
              const updatedData = data.map(d =>
                d.traceId === item.traceId ? updatedItem : d
              );
              setData(updatedData);
              updateInspectionData(updatedItem);
            }}
          />
        );
      case 'processMeasures':
        return (
          <Input
            size="sm"
            defaultValue={item.processMeasures || ''}
            onChange={async (e) => {
              const updatedItem = { ...item, processMeasures: e.target.value };
              const updatedData = data.map(d =>
                d.traceId === item.traceId ? updatedItem : d
              );
              setData(updatedData);
              updateInspectionData(updatedItem);
            }}
          />
        );
      case 'trend': {
        const key = `${item.interface}|${item.errorCode}|${item.errorMessage}|${item.originalErrorCode}|${item.originalErrorMessage}`;
        const trend = trendMap[key] || [];
        if (!trend.length) return <span>-</span>;
        // 判断是否激增
        const isSuddenIncrease = (() => {
          if (trend.length < 2) return false;
          const last = trend[trend.length - 1];
          const prevAvg = trend.slice(0, -1).reduce((a, b) => a + b, 0) / (trend.length - 1);
          return (prevAvg > 0 && last > prevAvg * 2) || (prevAvg === 0 && last > 0);
        })();
        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
            <ResponsiveContainer width={80} height={24}>
              <LineChart data={trend.map((v: number, i: number) => ({ x: i + 1, y: v }))} margin={{ top: 4, right: 4, left: 4, bottom: 4 }}>
                <Line type="monotone" dataKey="y" stroke="#8884d8" strokeWidth={2} dot={false} />
                <RechartsTooltip
                  contentStyle={{
                    background: "rgba(30,30,30,0.9)",
                    border: "none",
                    borderRadius: 6,
                    padding: "2px 8px",
                    color: "#fff",
                    fontSize: 12,
                    boxShadow: "0 2px 8px rgba(0,0,0,0.15)"
                  }}
                  wrapperStyle={{ outline: "none" }}
                  formatter={(value: any) => [value, '次数']}
                  labelFormatter={i => `第${i + 1}天`}
                />
              </LineChart>
            </ResponsiveContainer>
            {isSuddenIncrease && (
              <span title="次数激增">
                <Icon icon="mdi:alert" color="red" width={16} height={16} />
              </span>
            )}
          </div>
        );
      }
      default:
        return cellValue ? String(cellValue) : '-';
    }
  }, [data, trendMap]);

  const handleEffectiveDateChange = (date: Date) => {
    // 如果选择的日期大于已选择的日期，则不更新
    if (date > effectiveDate) {
      addToast({
        title: "不能选择比当前日期更晚的日期",
        color: "danger",
      });
      return;
    }
    setEffectiveDate(date);
  };

  return (
    <div className="h-screen w-screen overflow-hidden bg-background">
      <div className="h-full w-full p-6 overflow-auto">
        <div className="bg-content1/10 backdrop-blur-md rounded-lg shadow-lg p-6 border border-divider">
          <div className="flex justify-between items-center mb-6">
            <Button
              color={"primary"}
              onPress={() => {
                logout();
                window.location.reload();
              }}
              startContent={<Icon icon="mdi:arrow-left" className="text-lg" />}
            >
              退出
            </Button>
            <h2 className="text-2xl font-bold text-foreground">日常巡检数据分析</h2>
            <div className="flex items-center gap-2">
              <Icon
                icon={theme === 'dark' ? "solar:sun-linear" : "solar:moon-linear"}
                className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors"
                width={24}
                height={24}
                onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
              />
              <div className="w-8"></div> {/* 为了保持标题居中 */}
            </div>
          </div>
          
          <div className="space-y-6">
            <div className="bg-content2/50 rounded-lg p-6 border border-divider">
              <h3 className="text-lg font-medium text-foreground mb-4">数据获取方式</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* CSV文件上传 */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Icon icon="mdi:file-upload" className="text-lg text-primary" />
                    <h4 className="font-medium text-foreground">CSV文件上传</h4>
                  </div>
                  <input
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    className="block w-full text-sm text-foreground-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-md file:border-0
                      file:text-sm file:font-semibold
                      file:bg-content2 file:text-foreground
                      hover:file:bg-content3 cursor-pointer"
                  />
                  <p className="text-xs text-foreground-500">
                    支持上传CSV格式的数据文件进行分析
                  </p>
                </div>

                {/* 接口调用获取数据 */}
                <div className={"flex flex-col gap-4"}>
                  <div className="flex items-center gap-2">
                    <Icon icon="mdi:api" className="text-lg text-primary" />
                    <h4 className="font-medium text-foreground">接口调用获取</h4>
                  </div>
                  
                  {/* 时间范围选择 */}
                  <div className="flex items-end gap-4">
                    <div className="flex-1">
                      <I18nProvider locale="zh-CN">
                        <DateRangePicker
                          value={dateRange}
                          onChange={handleDateRangeChange}
                          size="md"
                          radius="lg"
                          className="w-full"
                        />
                      </I18nProvider>
                    </div>
                    
                    <Button
                      color="primary"
                      variant="bordered"
                      onPress={handleApiDataFetch}
                      isLoading={apiLoading}
                      startContent={<Icon icon="mdi:refresh" className="text-lg" />}
                      className="h-10"
                    >
                      获取最新数据
                    </Button>
                  </div>
                  
                  <p className="text-xs text-foreground-500">
                    通过接口自动获取指定时间范围的巡检数据
                  </p>
                </div>
              </div>

              {/* 加载状态显示 */}
              {(loading || apiLoading) && (
                <div className="mt-4 flex items-center gap-2 text-foreground-500">
                  <Icon icon="mdi:loading" className="animate-spin text-lg" />
                  <span>{loading ? "处理CSV文件中..." : "获取接口数据中..."}</span>
                </div>
              )}
            </div>

            {data && data.length > 0 && (
              <div className="mt-6">
                <Table
                  aria-label="当前数据"
                  topContent={
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-foreground">当前数据</h3>
                      <Button
                        size="sm"
                        variant="bordered"
                        onPress={() => setShowExistingData(!showExistingData)}
                        startContent={
                          <Icon 
                            icon={showExistingData ? "mdi:eye-off" : "mdi:eye"} 
                            className="text-lg" 
                          />
                        }
                        >
                          {showExistingData ? "隐藏已处理" : "显示已处理"}
                      </Button>
                    </div>
                  }
                >
                  <TableHeader columns={columns}>
                    {(column) => (
                      <TableColumn key={column.uid}>{column.name}</TableColumn>
                    )}
                  </TableHeader>
                  <TableBody items={showExistingData ? data : data.filter(item => item.isNew)}>
                    {(item) => (
                      <TableRow key={item.interface + item.traceId}>
                        {columns.map((column) => (
                          <TableCell key={column.uid}>
                            {renderCell(item, column.uid)}
                          </TableCell>
                        ))}
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            )}

            {historyData.length > 0 && (
              <div className="mt-6">
                <Table
                  aria-label="已处理记录"
                  topContent={<h3 className="text-lg font-medium mb-4 text-foreground">已处理记录</h3>}
                >
                  <TableHeader columns={historyColumns}>
                    {(column) => (
                      <TableColumn key={column.uid}>{column.name}</TableColumn>
                    )}
                  </TableHeader>
                  <TableBody items={historyData}>
                    {(item) => (
                      <TableRow key={`${item.id}`}>
                        {historyColumns.map((column) => (
                          <TableCell key={column.uid}>
                            {renderCell(item, column.uid)}
                          </TableCell>
                        ))}
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </div>
      </div>

      <Modal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)}
      >
        <ModalContent>
          <ModalHeader>处理信息</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <Select
                  size={"sm"}
                  radius={"lg"}
                  label="巡检人"
                  selectedKeys={[processInfo.processedBy || '']}
                  onSelectionChange={(key) => setProcessInfo(prev => ({ ...prev, processedBy: key.anchorKey as string }))}
                >
                  <SelectItem key="丁大伟">丁大伟</SelectItem>
                  <SelectItem key="陆洲">陆洲</SelectItem>
                  <SelectItem key="康华杰">康华杰</SelectItem>
                  <SelectItem key="汪天池">汪天池</SelectItem>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground-500 mb-1">
                  报错原因
                </label>
                <Input
                  value={processInfo.processReason}
                  onChange={(e) => setProcessInfo(prev => ({ ...prev, processReason: e.target.value }))}
                  placeholder="请输入报错原因，如无可暂不填写"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground-500 mb-1">
                  处理措施
                </label>
                <Input
                  value={processInfo.processMeasures}
                  onChange={(e) => setProcessInfo(prev => ({ ...prev, processMeasures: e.target.value }))}
                  placeholder="请输入处理措施，如无可暂不填写"
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setIsModalOpen(false)}
            >
              取消
            </Button>
            <Button
              color="primary"
              onPress={handleProcessSubmit}
            >
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

