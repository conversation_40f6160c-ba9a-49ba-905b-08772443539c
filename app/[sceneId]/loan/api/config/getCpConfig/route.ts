import prisma from "@/utils/prisma";

export async function POST(
  _req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      supplierId: scene?.supplier,
      supplierName: "度小满",
      logoUrl:
        "https://contentplatform-drcn.hihonorcdn.com/honorWallet/fb1cc21b10daf1d43ac9f1d5ff56223f_20240424025815.png",
      subTitle: "最高额度200000，年化利率7.2%起",
      maxLoan: 200000,
      apr: "7.2",
      targetUrl:
        "https://xindai.duxiaoman.com/xpage/userguide/operation.html?CH=rongyaodaoliu&fr=CeIupJva0",
      sortIndex: 2,
      suggest: 1,
      supportApi: 1,
      supportHnidVer: 100000000,
      creditVerifyList: "AGREEMENT_CREDIT,FACE_CHECK",
      supplierEnName: "<PERSON>",
      supplierContact: "95055",
      agreementReadingTime: 0,
      agreementReadingAll: 0,
      agreementExpand: 0,
      creditFailLockTime: 30,
      bindFailLockTime: 30,
      fixedTermPenaltyExplanation: "提前结清需付未还本金4%违约金",
      loanFixedTermPositionUrl:
        "https://content-test-drcn.hihonorcdn.com/honorWallet/test/loan-repaymentMethod/${versionCode}/index.html?type=dxm-aqh",
      loanBrPositionUrl:
        "https://content-test-drcn.hihonorcdn.com/honorWallet/test/loan-repaymentMethod/${versionCode}/index.html?type=dxm-lhh",
      supportSettlementCertificate: 1,
      supportReoffer: scene?.reofferInfo.supprtReoffer,
      provideIdVerficationMethod: 1,
      diversionRatio: 0,
      pollingOrder: "1,5,6",
      updateTime: 1740105186000,
      loanBrExplanation: "次日可还，按日计息，0违约金",
      repayViolateFeeExplanation: "需支付待还本金4%的违约金",
      repayViolateInterestExplanation: "提前结清需支付违约金，违约金计入利息",
      deductionEarlyMorning: 1,
      supportMultipleLoans: 0,
      apiLogoUrl:
        "https://content-test-drcn.hihonorcdn.com/honorWallet/0ce7df91188a987bde150dc45cd145ce_20240826104939.png",
      privacyNotice:
        "您提交的身份证件等信息将会用于金融机构放款评估，我们将严格遵守相关法律法规保护您的个人信息。",
      issueSettlementMethod: "0",
      settlementAllContext: "注意：将生成 1 份结清证明，含全部(% 笔)已结清借款",
      settlementPartContext:
        "注意：选择多笔借款开具证明时，只会生成 1 份结清证明",
      currentTermDisplay: 1,
      showDueTip: 1,
      unableIssueSettlementText: "暂无可开具结清证明的借据",
      allSettlementCertificateText: "证明个人所有借款已结清",
      singleSettlementCertificateText: "证明所选择的单笔或者多笔借款已结清",
      accurateLoanTrial: 0,
      supportModifyMobile: 1,
    },
  };

  return Response.json(responseBody);
}
