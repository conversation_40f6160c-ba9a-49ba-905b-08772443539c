import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      "totalAmount": 1100,
      "totalInterest": 100,
      "totalPenalty": 0,
      "totalServiceFee": 0,
      "totalViolateFee": 0,
      "totalPrincipal": 1000,
      "remark": "含本金1000.00元，利息0.65元",
      "totalOverdueFee": 0,
      "repayType": 1,
      "selectedCoupons": [],
      "usableCoupons": []
    },
  };

  return Response.json(responseBody);
}
