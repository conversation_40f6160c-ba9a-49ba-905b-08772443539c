import { LoanOrder } from "@/types";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params;

  const reqBody: { outOrderNo?: string } = await req.json();

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  let loanOrders = scene?.loanOrders as LoanOrder[];

  for (const loanOrder of loanOrders) {
    const repayPlanTerms = [];

    for (let i = 0; i < loanOrder.totalTerm; i++) {
      repayPlanTerms.push({
        termNo: i + 1,
        shouldRepayDate: loanOrder.paydate,
        termAmount: loanOrder.dueRepay / loanOrder.totalTerm,
        termPrincipal: loanOrder.dueRepay / loanOrder.totalTerm,
        termInterest: loanOrder.dueRepay / loanOrder.totalTerm,
        termFee: loanOrder.dueRepay / loanOrder.totalTerm,
        termReductionAmount: loanOrder.dueRepay / loanOrder.totalTerm,
        termPenalty: loanOrder.dueRepay / loanOrder.totalTerm,
        termPrinPenalty: loanOrder.dueRepay / loanOrder.totalTerm,
        termInterPenalty: loanOrder.dueRepay,
        overdueDays: loanOrder.dueRepay,
      });
    }

    loanOrder.repayPlanTerms = repayPlanTerms;
  }

  if (reqBody.outOrderNo) {
    loanOrders = loanOrders.filter(
      (order) => order.outOrderNo === reqBody.outOrderNo,
    );
  }

  const responseBody = {
    code: 0,
    message: "success",
    data: loanOrders,
  };

  return Response.json(responseBody);
}
