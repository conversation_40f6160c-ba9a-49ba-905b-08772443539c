import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      accessResult: 0,
      openId: "123456",
      supplier: "7",
      supplierName: "度小满",
      verifyList: ["AGREEMENT_CREDIT"],
      flowNo: "123"
    },
  };
  return Response.json(responseBody);
}
