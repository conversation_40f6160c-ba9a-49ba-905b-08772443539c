import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      status: 1,
      realName: "康康",
      ctfCode: "350521200102144019",
      mobileNo: "18851187952"
    },
  };
  return Response.json(responseBody);
}
