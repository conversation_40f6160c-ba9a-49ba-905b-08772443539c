import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      signStatus: scene.signInfo.signStatus,
      signResourceInfo:
        { signUrl: scene.signInfo.signUrl,
          signUrlValidTime: scene.signInfo.signUrlValidTime,
          contractUrl: scene.signInfo.contractUrl,
          contractName: scene.signInfo.contractName,
        },
    },
  };

  if (scene.signInfo.signStatus === 2) {
    responseBody.data.signResourceInfo.title = "已开通支付宝自动还款服务";
    responseBody.data.signResourceInfo.description = "可前往支付宝 > ‘我的’> 右上角‘设置’> ‘支付设置’ > ‘自动续费/免密支付’ 解除自动还款服务";
    responseBody.data.signResourceInfo.icon = "https://content-test-drcn.hihonorcdn.com/honorWallet/0f54b87f8f55c4289d213dc3beb79f9a_20250513113522.webp";
    responseBody.data.signResourceInfo.button = "";
    responseBody.data.signResourceInfo.bubbleText = ""
   }else {
    responseBody.data.signResourceInfo.title = "支付宝自动还款服务";
    responseBody.data.signResourceInfo.description = "还款不怕忘，到期自动还，为您的信用记录保驾护航";
    responseBody.data.signResourceInfo.icon = "https://content-test-drcn.hihonorcdn.com/honorWallet/1f2fc02d82fdf93a76f275c82fbc896d_20250513113611.webp";
    responseBody.data.signResourceInfo.button = "立即开通";
    responseBody.data.signResourceInfo.bubbleText = "享首期还款最高88元优惠";
  }

  return Response.json(responseBody);
}
