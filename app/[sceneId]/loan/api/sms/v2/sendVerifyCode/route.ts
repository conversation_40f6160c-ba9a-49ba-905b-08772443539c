import { Coupon } from "@/types";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const smsInfo = scene?.smsInfo;

  const responseBody = {
    code: 0,
    message: "success",
    data: smsInfo,
  };

  return Response.json(responseBody);
}
