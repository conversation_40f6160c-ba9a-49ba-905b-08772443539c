export async function POST() {
  const responseBody = {
    code: 0,
    message: "success",
    data: {
      applyDate: "********",
      applyTime: *************,
      effectiveDate: "********",
      loanAmount: 50000,
      status: 2,
      totalTerm: 12,
      repayMethod: 1,
      repayMethodName: "等额本息",
      outOrderNo: "8761588629345796117",
      clearTime: 0,
      paidAmount: 0,
      paidPrinAmount: 0,
      paidInterAmount: 0,
      paidFeeAmount: 0,
      paidPenalty: 0,
      bindCardNo: "7473",
      bindBankCode: "4004",
      bindBankName: "招商银行",
      couponNo: "",
      dayRate: "0.065",
      apr: "23.4",
      reductionAmount: 0,
      prePenalty: 0,
      repayPlanTerms: [
        {
          termNo: 1,
          shouldRepayDate: "********",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 3706,
          termInterest: 1007,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 3706,
          payableTermInterest: 1007,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 2,
          shouldRepayDate: "20250520",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 3811,
          termInterest: 902,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 3811,
          payableTermInterest: 902,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 3,
          shouldRepayDate: "20250620",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 3857,
          termInterest: 856,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 3857,
          payableTermInterest: 856,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 4,
          shouldRepayDate: "20250720",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 3960,
          termInterest: 753,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 3960,
          payableTermInterest: 753,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 5,
          shouldRepayDate: "20250820",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4015,
          termInterest: 698,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4015,
          payableTermInterest: 698,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 6,
          shouldRepayDate: "20250920",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4096,
          termInterest: 617,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4096,
          payableTermInterest: 617,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 7,
          shouldRepayDate: "20251020",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4196,
          termInterest: 517,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4196,
          payableTermInterest: 517,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 8,
          shouldRepayDate: "20251120",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4263,
          termInterest: 450,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4263,
          payableTermInterest: 450,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 9,
          shouldRepayDate: "20251220",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4361,
          termInterest: 352,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4361,
          payableTermInterest: 352,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 10,
          shouldRepayDate: "20260120",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4437,
          termInterest: 276,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4437,
          payableTermInterest: 276,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 11,
          shouldRepayDate: "20260220",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4713,
          termPrincipal: 4526,
          termInterest: 187,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4713,
          payableTermPrincipal: 4526,
          payableTermInterest: 187,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
        {
          termNo: 12,
          shouldRepayDate: "20260320",
          termStatus: 2,
          repayCategory: 2,
          termAmount: 4858,
          termPrincipal: 4772,
          termInterest: 86,
          termFee: 0,
          termReductionAmount: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termCharges: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermCharges: 0,
          paidTermReductionAmount: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermFee: 0,
          paidTermServiceFee: 0,
          payableTermAmount: 4858,
          payableTermPrincipal: 4772,
          payableTermInterest: 86,
          payableTermCharges: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermFee: 0,
          payableTermServiceFee: 0,
          overdueDays: 0,
          overdueAmt: 0,
          preRepay: false,
          overdue: false,
        },
      ],
      contractList: [
        {
          contractId: "7239103778546010876",
          contractName: "个人信息授权书",
        },
        {
          contractId: "7239103778546012872",
          contractName: "安心签法律声明及个人信息保护政策",
        },
        {
          contractId: "7239103778546010870",
          contractName: "个人征信授权书",
        },
        {
          contractId: "7239103778546010877",
          contractName: "度小满个人信息保护政策 ",
        },
        {
          contractId: "7239103778546010869",
          contractName: "个人征信授权书（度小满小贷）",
        },
        {
          contractId: "7239103778546012871",
          contractName: "安心签平台服务协议",
        },
        {
          contractId: "7239103778546010875",
          contractName: "度小满用户注册协议",
        },
        {
          contractId: "7239103778546010871",
          contractName: "度小满借钱服务协议",
        },
        {
          contractId: "7239103778546012873",
          contractName: "数字证书申请及使用协议",
        },
        {
          contractId: "7239103778546011091",
          contractName: "自动还款服务协议",
        },
        {
          contractId: "7239103778546011174",
          contractName: "个人授信额度合同",
        },
        {
          contractId: "7239103778546012886",
          contractName: "借款协议-重庆度小满小额贷款有限公司",
        },
        {
          contractId: "7239103778546012887",
          contractName: "借款协议-江苏银行股份有限公司",
        },
      ],
      institutionNames: "江苏银行股份有限公司",
      supplier: 1,
    },
  };

  return Response.json(responseBody);
}
