import { delay } from "@/utils/timeUtils";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  await delay(1000);

  const requestBody = await req.json();
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });
  const currentProduct = scene.productInfos.filter(
    (item) => item.repayMethod === requestBody.repayMethod,
  )[0];
  let tempDiscount = 0;

  if (currentProduct && currentProduct.tempApr) {
    tempDiscount = 10000.0;
  }

  if (
    !scene.discountAmountInfo?.isSupport ||
    scene.discountAmountInfo.isSupport === 0
  ) {
    tempDiscount = null;
  }

  let scheduleDiscount = 0;

  if (scene.productInfos.length > 1 && currentProduct.repayMethod === 4) {
    scheduleDiscount = 20000.0;
  }
  if (
    !scene.discountAmountInfo?.isSupport ||
    scene.discountAmountInfo.isSupport === 0
  ) {
    scheduleDiscount = null;
  }
  const responseBody = {
    code: 0,
    message: "success",
    data: {
      shouldRepayAmount: 10948163,
      shouldRepayPrinAmount: 9660000,
      shouldRepayInterAmount: 1288163,
      shouldRepayFeeAmount: 0,
      shouldRepayMgmAmount: 0,
      firstRepayDate: "20250420",
      lastRepayDate: "20260320",
      reductionAmount: 10000,
      annualRate: Math.floor(Math.random() * 51).toString(),
      // originalRate: Math.floor(Math.random() * 51).toString(),
      repayPlanTerms: [
        {
          termNo: 1,
          shouldRepayDate: "20250420",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 722273,
          termInterest: 188370,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 2,
          shouldRepayDate: "20250520",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 736358,
          termInterest: 174285,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 3,
          shouldRepayDate: "20250620",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 745386,
          termInterest: 165257,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 4,
          shouldRepayDate: "20250720",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 765252,
          termInterest: 145391,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 5,
          shouldRepayDate: "20250820",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 775825,
          termInterest: 134818,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 6,
          shouldRepayDate: "20250920",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 791458,
          termInterest: 119185,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 7,
          shouldRepayDate: "20251020",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 810736,
          termInterest: 99907,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 8,
          shouldRepayDate: "20251120",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 823742,
          termInterest: 86901,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 9,
          shouldRepayDate: "20251220",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 842609,
          termInterest: 68034,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 10,
          shouldRepayDate: "20260120",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 857319,
          termInterest: 53324,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 11,
          shouldRepayDate: "20260220",
          termStatus: 0,
          termAmount: 910643,
          termPrincipal: 874594,
          termInterest: 36049,
          termFee: 0,
          termReductionAmount: 0,
          amount: 910643,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
        {
          termNo: 12,
          shouldRepayDate: "20260320",
          termStatus: 0,
          termAmount: 931090,
          termPrincipal: 914448,
          termInterest: 16642,
          termFee: 0,
          termReductionAmount: 0,
          amount: 931090,
          termPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termMgntFee: 0,
          termServiceFee: 0,
        },
      ],

      discountAmountInfo: {
        tempDiscount: tempDiscount,
        scheduleDiscount: scheduleDiscount,
      },
    },
  };

  return Response.json(responseBody);
}
