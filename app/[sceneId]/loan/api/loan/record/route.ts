export async function POST(request: Request) {
  const { pageSize , currentPage} = await request.json();
  console.log(currentPage)
  if (currentPage === 3) {
    const  responseBody = {
      code: 0,
      message: "success",
      data: {
        records: [
          {
            applyDate: "20250325",
            loanAmount: "5000",
          },
        ],
      },
    };

    return Response.json(responseBody);
  }else {
  const responseBody = {
    code: 0,
    message: "success",
    data: {
      records: [
        {
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },{
          applyDate: "20250325",
          loanAmount: "5000",
        },
      ],
    },
  };

  return Response.json(responseBody);}
}
