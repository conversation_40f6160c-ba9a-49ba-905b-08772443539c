import { <PERSON>anOrder } from "@/types";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      "applyStatus": 2,
      "totalAmount": 1000000,
      "firstTermAmount": 100000,
      "repayDay": "20250612"
    },
  };

  return Response.json(responseBody);
}
