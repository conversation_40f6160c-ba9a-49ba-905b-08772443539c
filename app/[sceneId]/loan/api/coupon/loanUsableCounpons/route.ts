import { Coupon } from "@/types";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const coupons = scene?.coupons as Coupon[];

  coupons.forEach((coupon) => {
    coupon.useRules = [coupon.useRule];
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: coupons,
  };

  return Response.json(responseBody);
}
