import prisma from "@/utils/prisma";
import { delay } from "@/utils/timeUtils";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  await delay(1000);

  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      accessResult: 1,
      supplier: scene?.supplier,
      supplierName: "度小满",
      supplierLogo:
        "https://contentplatform-drcn.hihonorcdn.com/honorWallet/fb1cc21b10daf1d43ac9f1d5ff56223f_20240424025815.png",
      mobileNo: "188****7000",
      openId:
        "8cf828342879c9e78ea1841098b39744c1598e95ecd18e667b96bfbdd5ffb9a9",
      status: scene?.status,
      // 1-存在放款中借款，2-存在逾期订单不可借款，3-暂时无法支用，4-其他原因
      limitUseErrStatus: scene?.limitUseErrStatus,
      creditLimit: scene?.creditLimit,
      limitType: 1,
      remainLimit: scene?.remainLimit,
      // totalAvailableLimit: scene?.remainLimit,
      limitExpireDate: "",
      repayDay: scene?.repayDay,
      productInfos: scene?.productInfos,
      repayInfo: {
        totalAmount: 32049,
        principal: 25200,
        interest: 6849,
        mgmtFee: 0,
      },
      maxLoan: scene?.remainLimit,
      minLoan: scene?.minLoan,
      canCreditChange: false,
      dictCreditInfo: {
        creditLimit: 30000000,
        dayRate: "0.011",
        apr: "3.9",
        approvalSeconds: "30",
      },
      applyTime: 1742435277000,
      creditChange: {
        creditChangeRecordId: "999",
        limitChangeType: 1,
        rateChangeType: 2,
        oldLimit: 2000000,
        oldDayRate: "0.011%",
        oldApr: "23.2222",
        remainLimit: 3000000,
        totalAmount: 3000000,
        newDayRate: "0.001%",
        newApr: "13.9123",
      },
    },
  };

  if (scene?.limitUseErrStatus === 2) {
    responseBody.data.overdueInfo = {
      overdueAmount: 100000,
      overdueOrder: 1,
      overdueDays: 1,
      overduePenalty: 1000,
    };
  }

  return Response.json(responseBody);
}
