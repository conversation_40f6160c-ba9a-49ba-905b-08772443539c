import { LoanOrder } from "@/types";
import prisma from "@/utils/prisma";

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const { sceneId } = await params; // 直接解构

  const scene = await prisma.scene.findUnique({
    where: { id: parseInt(sceneId) },
  });

  const responseBody = {
    code: 0,
    message: "success",
    data: {
      "limitChangeType": 2,
      "rateChangeType": 2,
      "oldLimit": 100000,
      "remainLimit": 200000,
      "totalAmount": 300000,
      "tempLimitValidDays": 30,
      "tempPriceValidDays": 30,
      "oldApr": 20.0000,
      "newApr": 10.0000,
      "newDayRate": 0.0001,
    },
  };

  return Response.json(responseBody);
}
