import { NextRequest, NextResponse} from "next/server";

export async function GET(req: NextRequest) {
  const remoteUrl = 'https://content-test-drcn.hihonorcdn.com/honorWallet/22985762e4ac4943580092956fd3a193_20250725162344.html';
  const response = await fetch(remoteUrl);
  let html = await response.text();

  // 替换变量
  const repayNo = req.nextUrl.searchParams.get('repayNo') || '';
  const backUrl = `http://${process.env.LOAN_DOMAIN}/wallet-loan-web/pages/repayment/record?repayNo=${repayNo}&isFromDXM=true`;
  html = html.replace('{{back_url}}', backUrl);

  return new NextResponse(html, { headers: { "Content-Type": "text/html" } });
}