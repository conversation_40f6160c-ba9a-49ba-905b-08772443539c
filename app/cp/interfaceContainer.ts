export const interfaceContainer: Record<string, Interface> = {
  "user.bind": {
    no: "2.2",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrxik1EEPJyk62L5Cgg0idog",
    class: "credit",
    name: "准入",
  },
  "credit.info.query": {
    no: "2.3",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlraHQK2xJ87VtKBDIm62KJ8c",
    class: "credit",
    name: "额度查询",
  },
  "credit.addUrl.query": {
    no: "2.5",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#part-YsUsdIOOXoDS2yxR4bZl3gNIrvh",
    class: "credit",
    name: "增信H5链接",
  },
  "credit.apply": {
    no: "2.6",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrkjePGe5Z3TMxHH0j4lcOfd",
    class: "credit",
    name: "授信提交",
  },
  "credit.apply.status": {
    no: "2.7",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrJcGpBZtq1B4Xa2PcI9XFTe",
    class: "credit",
    name: "授信结果查询",
  },
  "loan.trial": {
    no: "2.8",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlroa2ypQeLz6JIWh5yhpfp3e",
    class: "loan",
    name: "借款试算",
  },
  "loan.verify": {
    no: "2.9",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrR7qJgyhORahfBkFTC1KNWf",
    class: "loan",
    name: "借款交易鉴权",
  },
  "loan.apply": {
    no: "2.11",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrRiARfkowGlRRCsS2Q76k9I",
    class: "loan",
    name: "借款申请",
  },
  "loan.apply.status": {
    no: "2.12",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlr6o7Xdd6j9kJSLbEJ90Tuch",
    class: "loan",
    name: "借款申请结果查询",
  },
  "loan.record": {
    no: "2.13",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrCaGwqQvp0wJCpBFoF2Hjob",
    class: "loan",
    name: "借款记录查询",
  },
  "loan.record.detail": {
    no: "2.14",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrU6tW7k27z452lXjyxQuLzg",
    class: "loan",
    name: "借款记录详情",
  },
  "repay.plan": {
    no: "2.15",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrqsQVON4wfKcNSwHaOMtdQd",
    class: "repay",
    name: "还款计划查询",
  },
  "repay.trial": {
    no: "2.16",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrHFmgy8iVyXouu64IMDrm6g",
    class: "repay",
    name: "还款试算",
  },
  "resign.check": {
    no: "2.17",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlr0nsP5Vs0OBebsipbKv16dh",
    class: "repay",
    name: "是否需要重新签约",
  },
  "repay.submit": {
    no: "2.18",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlruX5T0diHmz2U6Z4YNKCUNg",
    class: "repay",
    name: "提交主动还款",
  },
  "repay.status": {
    no: "2.19",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrZcmVzsxFkbmUmvr7dFddUc",
    class: "repay",
    name: "还款结果查询",
  },
  "repay.record": {
    no: "2.20",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlr6uQCxdI5ipWpwsCBXlg7fW",
    class: "repay",
    name: "还款记录列表",
  },
  "h5.transfer.repay": {
    no: "2.201",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlruX5T0diHmz2U6Z4YNKCUNg",
    class: "repay",
    name: "转账还款",
  },
  "settlement.order.query": {
    no: "2.22",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrQ63YI7IGJPDhd02cD2X5gh",
    class: "settlement",
    name: "查询可开具结清证明的借款订单",
  },
  "settlement.send": {
    no: "2.23",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrUEiDH68tnT12GSkZBGMZnd",
    class: "settlement",
    name: "发送结清证明到邮箱",
  },
  "bankcard.list": {
    no: "2.24",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlryzXhRci6luu6vzno98oX3c",
    class: "bankcard",
    name: "查询用户已绑定的银行卡列表",
  },
  "bankcard.support.list": {
    no: "2.25",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrRsPZ2Vv2UxXrhW4eXqPqqf",
    class: "bankcard",
    name: "查询支持的银行列表",
  },
  "bankcard.bind": {
    no: "2.26",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrIcf7nMxJUAwEX5x0qKENTg",
    class: "bankcard",
    name: "银行卡绑定",
  },
  "bankcard.sms.verify": {
    no: "2.27",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlr9xWQn5yDiM8YsDp5DuNjud",
    class: "bankcard",
    name: "银行卡绑定短信验证",
  },
  "contract.query": {
    no: "2.28",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrzAX2CEG3l4siAcU49gxRGe",
    class: "contract",
    name: "获取协议列表",
  },
  "contract.detail": {
    no: "2.29",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrOC8GfOUdEfLsKYFgaj6dzh",
    class: "contract",
    name: "获取已签协议详情",
  },
  "coupon.loan.usable": {
    no: "2.30",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrkpHYhn34pCaHbwofBjERDc",
    class: "coupon",
    name: "获取借款可用优惠券",
  },
  "coupon.list": {
    no: "2.31",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlr7qRpQ8rrTnnUeCrErzuZ00",
    class: "coupon",
    name: "用户优惠券列表查询",
  },
  "user.cancel.account": {
    no: "2.34",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrBCO2pfceMcK27E45a82aQc",
    class: "account",
    name: "用户注销",
  },
  "user.cancel.check": {
    no: "2.35",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrf3BCMzM40y6uIHF5qm7RIg",
    class: "account",
    name: "用户注销检查",
  },
  "idcard.check": {
    no: "2.36",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrvsLdleGqYvVrTfzsJ1ONRh",
    class: "idcard",
    name: "身份证识别",
  },
  "credit.apply.notify": {
    no: "3.1",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlru65Wn92tq8J4MvHUaHi9Kb",
    class: "notify",
    name: "授信结果通知",
  },
  "credit.change.notify": {
    no: "3.2",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlryPRUNeDRrZJgz6HhTsTRXe",
    class: "notify",
    name: "调额调价通知",
  },
  "loan.apply.notify": {
    no: "3.3",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrD2NJlZAxkt7i8V7sFdW9ob",
    class: "notify",
    name: "借款申请结果通知",
  },
  "repay.result.notify": {
    no: "3.4",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrbH0YEe3GojVuARaM2XhEud",
    class: "notify",
    name: "还款结果通知",
  },
  "coupon.notify": {
    no: "3.5",
    docUrl: "https://elink.e.hihonor.com/wiki/OesbwwwfJigowHkQCN3lnKxBrTb#doxlrZElw2v19DiuQTzSwRfFojc",
    class: "notify",
    name: "优惠券发放通知",
  }
}

export interface Interface {
  no: string;
  docUrl: string;
  class: string;
  name?: string;
}