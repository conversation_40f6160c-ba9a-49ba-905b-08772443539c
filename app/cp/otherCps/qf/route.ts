import { NextRequest, NextResponse } from "next/server";
import { decrypt, decryptByPrivateKey } from "@/utils/decrypt";
import { MethodName } from "@/app/cp/methodMap";
import { decryptByPrivateKeyForQf, decryptForQf } from "@/utils/qf/decrypt";
import {
  createLoanUser,
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import { RepayMethodEnum } from "@/app/cp/creditInfoQuery";
import {
  CreditApplyStatusEnum,
  CreditInfo,
  CreditRefuseCodeEnum,
  creditRefuseMsgMap,
} from "@/types/cp/creditInfo";
import { CardBandStatusEnum } from "@/types/cp/bankCard";
import {
  LoanApplyStatusEnum,
  LoanErrorCodeEnum,
  LoanErrorMessageMap,
  LoanVerifyItemEnum,
} from "@/types/cp/loanInfo";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { QF_TARGET_URL } from "@/utils/thirdUrls";

export async function POST(request: NextRequest) {
  try {
    // 获取原始请求的所有信息
    const body = await request.json();
    const headers = Object.fromEntries(request.headers);

    const key = body.key;
    const decryptKey = decryptByPrivateKey(key);
    const EncryptParams = body.params;
    const decryptParams = JSON.parse(decrypt(EncryptParams, decryptKey));
    const method: MethodName = body.method;
    const user = await getLoanUserByUserId(decryptParams.userId);

    if (method === "user.bind") {
      if (!user) {
        await createLoanUser({
          ...decryptParams,
          cp: "奇富",
          creditInfo: {
            status: {
              applyStatus: 0,
              productInfos: [
                {
                  apr: "18.25",
                  monthRate: (parseFloat("18.25") / 12).toFixed(6) || "",
                  dayRate: (parseFloat("18.25") / 365).toFixed(6) || "",
                  repayMethod: RepayMethodEnum.EqualPrincipalAndInterest,
                  earlyRepay: true,
                  termNums: [3, 6, 12],
                  isDynamicRate: false,
                },
              ],
            },
            actions: {
              creditApplyStatus: CreditApplyStatusEnum.PASSED,
              refuseCode: CreditRefuseCodeEnum.APPLY_FAILED,
              refuseMsg: creditRefuseMsgMap.get(
                CreditRefuseCodeEnum.APPLY_FAILED,
              ),
              creditLimit: 2000000,
              autoCallback: true,
              callBackDelay: 1,
            },
          },
          bankCardInfo: {
            status: {
              bankCardList: [],
            },
            action: {
              bandStatus: CardBandStatusEnum.PASSED,
            },
          },
          loanInfo: {
            status: {
              loanApplyRecords: [],
              loanOrders: [],
              repayRecords: [],
            },
            action: {
              loanApplyStatus: LoanApplyStatusEnum.SUCCESS,
              refuseCode: LoanErrorCodeEnum.LOAN_REJECTED,
              refuseMsg: LoanErrorMessageMap[LoanErrorCodeEnum.LOAN_REJECTED],
              verifyList: [LoanVerifyItemEnum.AGREEMENT_LOAN],
              autoCallback: true,
              callBackDelay: 1,
            },
          },
          couponInfo: {
            status: {
              couponList: [],
            },
          },
          clearCertificateInfo: {
            action: {
              canOpenClearCertificate: true,
              failCode: 1,
              failReason: "系统异常",
            },
          },
          createTime: BigInt(Date.now()),
        });
      }
    }

    if (method === "credit.apply") {
      const creditInfo = user!.creditInfo as unknown as CreditInfo;
      creditInfo.status.applyNo = decryptParams.applyNo;
      creditInfo.status.applyStatus = CreditApplyStatusEnum.PENDING;
      creditInfo.status.applyTime = Date.now();
      updateLoanUser(user);
    }

    // 发送请求到目标服务器
    const response = await fetch(QF_TARGET_URL, {
      method: "POST",
      headers: {
        ...headers,
      },
      body: JSON.stringify(body),
    });

    // 获取响应数据
    const data = await response.json();

    const qfKey = data.key;
    const qfDecryptKey = decryptByPrivateKeyForQf(qfKey);
    const qfEncryptParams = data.data;

    const qfDecryptParams = qfEncryptParams
      ? JSON.parse(decryptForQf(qfEncryptParams, qfDecryptKey))
      : null;

    addInterfaceLog({
      userId: user?.id!,
      interfaceName: method,
      requestData: JSON.stringify(decryptParams),
      responseData: qfDecryptParams
        ? JSON.stringify(qfDecryptParams)
        : JSON.stringify(data.code + "-" + data.desc),
    });

    // 返回响应
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    // 错误处理
    console.error("转发请求失败:", error);
    return NextResponse.json(
      {
        flag: "F",
        code: "SYSTEM_ERROR",
        msg: "系统错误",
        data: null,
      },
      { status: 500 },
    );
  }
}
