import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import { encryptRes } from "@/utils/decrypt";
import { LogoffInfo } from "@/types/cp/logoffInfo";

interface ParamType {
  userId: string;
}

interface UserCancelAccountResponse {
  logoffResult: boolean;
  logoffErrDesc?: string;
}

export async function userCancelAccount(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const logoffInfo = user.logoffInfo as unknown as LogoffInfo;
  const responseData: UserCancelAccountResponse = {
    logoffResult: logoffInfo.action.logoffResult,
    logoffErrDesc: logoffInfo.action.logoffFailReason || undefined,
  };

  if (logoffInfo.action.logoffResult) {
    logoffInfo.status.isLogoff = true;
    await updateLoanUser(user);
  }

  return encryptRes({
    code: 0,
    message: "success",
    data: responseData,
  });
}
