import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo, LoanStatusEnum } from "@/types/cp/loanInfo";
import dayjs from "dayjs";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败 */
  status?: number;
  /** 当前页码，不传则全量返回 */
  currentPage?: number;
  /** 每页条数 */
  pageSize?: number;
}

export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: LoanRecordDto[];
  /** 当前页码 */
  currentPage?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 总页数 */
  totalPage?: number;
  /** 记录总数 */
  totalCount?: number;
}

export interface LoanRecordDto {
  /** 渠道方借款订单号 */
  outOrderNo?: string;
  /** 荣耀侧借款申请订单号 */
  applyNo: string;
  /** 借款申请日期，yyyyMMdd */
  applyDate: string;
  /** 借款申请时间，毫秒 */
  applyTime: number;
  /** 借款金额，单位：分 */
  loanAmount: number;
  /** 借款状态，1-申请中，2-正常还款中，3-已逾期，4-已结清，5-借款失败 */
  status: number;
  /** 借款期数 */
  totalTerm: number;
  /** 已还款期数 */
  repayTermNum: number;
  /** 当前期次 */
  currentTerm: number;
  /** 剩余应还金额，单位：分 */
  payableAmount?: number;
  /** 借款来源 */
  loanSource?: string;
  /** 结清时间(未结清：0) */
  clearTime: number;
  /** 借据逾期天数 */
  overdueDays?: number;
  /** 借据逾期未还金额 */
  overdueAmount?: number;
}

export async function loanRecord(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 0,
      message: "success",
      data: {
        totalNum: 0,
        records: [],
      },
    });
  }

  // 过滤订单
  let loanOrders = loanInfo.status.loanOrders;
  let loanRecords = loanInfo.status.loanApplyRecords?.sort((a, b) => b.applyTime - a.applyTime);
  if (!loanRecords) {
    loanRecords = [];
  }

  // 计算分页
  const totalNum = loanRecords.length;
  let records = loanRecords;
  let currentPage = params.currentPage;
  let pageSize = params.pageSize;
  let totalPage: number | undefined;
  let totalCount: number | undefined;

  if (currentPage && pageSize) {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    records = loanRecords.slice(start, end);
    totalPage = Math.ceil(totalNum / pageSize);
    totalCount = totalNum;
  }

  const responseData: ResponseDataType = {
    totalNum,
    records: records.map((record) => {
      const loanOrder = loanOrders.find(
        (order) => order.outOrderNo === record.outOrderNo,
      );

      // 计算已还款期数
      const repayTermNum =
        loanOrder?.repayPlanTerms?.filter((term) =>
          dayjs(term.shouldRepayDate).isBefore(dayjs(), "day"),
        ).length ?? 0;

      // 计算当前期次
      const today = dayjs();
      const currentTerm =
        loanOrder?.repayPlanTerms?.findIndex((term) =>
          dayjs(term.shouldRepayDate).isSameOrAfter(today, "day"),
        ) ?? -1;

      // 计算剩余应还金额
      const payableAmount =
        loanOrder?.repayPlanTerms?.reduce(
          (sum, term) => sum + (term.termAmount || 0),
          0,
        ) ?? 0;

      // 计算逾期信息
      const overdueTerms =
        loanOrder?.repayPlanTerms?.filter((term) =>
          dayjs(term.shouldRepayDate).isBefore(today, "day"),
        ) ?? [];
      const overdueDays =
        overdueTerms.length > 0
          ? dayjs().diff(dayjs(overdueTerms[0].shouldRepayDate), "day")
          : 0;
      const overdueAmount = overdueTerms.reduce(
        (sum, term) => sum + (term.termAmount || 0),
        0,
      );

      return {
        outOrderNo: record.outOrderNo,
        applyNo: record.applyNo,
        applyDate: dayjs(record.applyTime).format("YYYYMMDD"),
        applyTime: record.applyTime,
        loanAmount: record.loanAmount,
        status: loanOrder?.status || LoanStatusEnum.FAILED,
        totalTerm: record.totalTerm,
        repayTermNum: repayTermNum || 0,
        currentTerm: currentTerm === -1 ? record.totalTerm : currentTerm + 1,
        payableAmount: payableAmount > 0 ? payableAmount : undefined,
        loanSource: loanOrder?.loanSource,
        clearTime:
          loanOrder?.status === LoanStatusEnum.CLEARED ? loanOrder.clearTime : 0,
        overdueDays: overdueDays > 0 ? overdueDays : undefined,
        overdueAmount: overdueAmount > 0 ? overdueAmount : undefined,
      };
    }),
    currentPage,
    pageSize,
    totalPage,
    totalCount,
  };

  return encryptRes({
    code: 0,
    message: "success",
    data: responseData,
  });
}