import { userBind } from "@/app/cp/userBind";
import { contractQuery } from "@/app/cp/contractQuery";
import { contractDetail } from "@/app/cp/contractDetail";
import { creditApply } from "@/app/cp/creditApply";
import { creditInfoQuery } from "@/app/cp/creditInfoQuery";
import { creditAddUrlQuery } from "@/app/cp/addUrlQuery";
import { couponList } from "@/app/cp/couponList";
import { repayPlan } from "@/app/cp/repayPlan";
import { creditApplyStatus } from "@/app/cp/creditApplyStatus";
import { loanTrial } from "@/app/cp/loanTrial";
import { bankCardList } from "@/app/cp/bankCardList";
import { bankCardBind } from "@/app/cp/bankCardBind";
import { bankCardSmsVerify } from "@/app/cp/bankCardSmsVerify";
import { loanVerify } from "@/app/cp/loanVerify";
import { loanApply } from "@/app/cp/loanApply";
import { loanApplyStatus } from "@/app/cp/loanApplyStatus";
import { loanRecordDetail } from "@/app/cp/loanRecordDetail";
import { repayTrial } from "@/app/cp/repayTrial";
import { repaySubmit } from "@/app/cp/repaySubmit";
import { resignCheck } from "@/app/cp/resignCheck";
import { smsVerify } from "@/app/cp/smsVerify";
import { smsSend } from "./smsSend";
import { repayStatus } from "./repayStatus";
import { loanRecord } from "@/app/cp/loanRecord";
import { loanUsableCoupon } from "@/app/cp/loanUsableCoupon";
import { bankcardSupportList } from "@/app/cp/bankcardSupportList";
import { repayRecord } from "@/app/cp/repayRecord";
import { settlementOrderQuery } from "@/app/cp/setttlementOrderQuery";
import { settlementSend } from "@/app/cp/settlementSend";
import { idcardCheck } from "@/app/cp/idcardCheck";
import { userCancelCheck } from "@/app/cp/userCancelCheck";
import { userCancelAccount } from "@/app/cp/userCancelAccount";
import { repayTransferSubmit } from "@/app/cp/repayTransferSubmit";

export const methodMap = {
  // 用户准入
  "user.bind": userBind,
  // 协议查询
  "contract.query": contractQuery,
  // 获取已签协议详情
  "contract.detail": contractDetail,
  // 授信提交
  "credit.apply": creditApply,
  // 用户额度查询
  "credit.info.query": creditInfoQuery,
  // 增信H5链接
  "credit.addUrl.query": creditAddUrlQuery,
  // 还款计划查询
  "repay.plan": repayPlan,
  // 授信结果查询
  "credit.apply.status": creditApplyStatus,
  // 借款试算
  "loan.trial": loanTrial,
  // 银行卡列表
  "bankcard.list": bankCardList,
  // 银行卡绑定
  "bankcard.bind": bankCardBind,
  // 银行卡绑定短信验证
  "bankcard.sms.verify": bankCardSmsVerify,
  // 交易鉴权
  "loan.verify": loanVerify,
  // 借款申请
  "loan.apply": loanApply,
  // 借款申请结果查询
  "loan.apply.status": loanApplyStatus,
  // 借款记录详情
  "loan.record.detail": loanRecordDetail,
  // 还款试算
  "repay.trial": repayTrial,
  // 是否需要重新签约
  "resign.check": resignCheck,
  // 短信发送
  "sms.code.send": smsSend,
  // 短信验证码校验
  "sms.code.verify": smsVerify,
  // 提交主动还款
  "repay.submit": repaySubmit,
  // 还款结果查询
  "repay.status": repayStatus,
  //借款记录查询
  "loan.record": loanRecord,
  // 优惠券列表
  "coupon.list": couponList,
  //获取借款可用优惠券
  "coupon.loan.usable": loanUsableCoupon,
  //查询支持的银行列表
  "bankcard.support.list": bankcardSupportList,
  //查询还款记录列表
  "repay.record": repayRecord,
  //查询可开具结清证明的借款订单
  "settlement.order.query": settlementOrderQuery,
  //发送结清证明到邮箱
  "settlement.send": settlementSend,
  // 身份证识别
  "idcard.check": idcardCheck,
  // 用户注销检查
  "user.cancel.check": userCancelCheck,
  // 用户注销
  "user.cancel.account": userCancelAccount,
  // 转账还款
  "h5.transfer.repay": repayTransferSubmit,
};

export type MethodName = keyof typeof methodMap;
