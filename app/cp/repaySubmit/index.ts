import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import {
  LoanInfo,
  LoanStatusEnum,
  RepayErrorMessageMap,
  RepayStatusEnum, RepaySubmitEnum,
  RepayTermDto,
  TermStatusEnum
} from "@/types/cp/loanInfo";
import { CreditInfo } from "@/types/cp/creditInfo";
import dayjs from "dayjs";
import { notifyRepayResult } from "@/actions/cp/notify/repayResultNotify";
import { repayRecord } from "@/app/cp/repayRecord";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 荣耀侧还款交易流水号 */
  repayNo: string;
  /** 渠道侧借款订单号 */
  outOrderNo: string;
  /** 还款金额，单位：分 */
  repayAmount: number;
  /** 优惠券ID */
  couponNo?: string;
  /** 优惠券减免金额，单位：分 */
  reductionAmount?: number;
  /** 还款类型，1-提前还款，2-正常还款 */
  repayType: number;
  /** 是否部分还款 */
  repayPart: boolean;
  /** 还款卡绑卡ID */
  bankCardId: string;
  /** 还款期次列表，主动还款必传，提前还款无需传 */
  repayTerms?: RepayTermParam[];
}

export interface RepayTermParam {
  /** 期次 */
  termNo: number;
  /** 还款金额，单位：分 */
  termAmount: number;
}

export interface ResponseDataType {
  /** 渠道方还款交易流水号（提交还款成功时返回） */
  outRepayNo?: string;
  /** 拒绝原因码 */
  refuseCode: string;
  /** 拒绝原因说明 */
  refuseMsg?: string;
}

// 防重提交缓存（仅用于模拟，生产建议用Redis等分布式缓存）
const repaySubmitCache = new Map<string, number>();

export async function repaySubmit(params: ParamType) {
  const cacheKey = params.userId;
  const now = Date.now();
  const expireMs = 3 * 1000; // 3秒

  // 检查是否短时间重复提交
  if (
    repaySubmitCache.has(cacheKey) &&
    now - repaySubmitCache.get(cacheKey)! < expireMs
  ) {
    return encryptRes({
      code: ********,
      message: "未到账单出账日",
      data: null,
    });
  }
  // 写入缓存
  repaySubmitCache.set(cacheKey, now);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: {
        refuseCode: "200004",
        refuseMsg: "无效操作",
      },
    });
  }

  // 验证主动还款的期次列表
  if (
    params.repayType === 2 &&
    (!params.repayTerms || params.repayTerms.length === 0)
  ) {
    return encryptRes({
      code: 1,
      message: "参数错误",
      data: {
        refuseCode: "200004",
        refuseMsg: "无效操作",
      },
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  let order = loanInfo.status?.loanOrders?.find(
    (order) => order.outOrderNo === params.outOrderNo,
  );
  const oldOrder = JSON.parse(JSON.stringify(order));

  if (!order) {
    return encryptRes({
      code: 1,
      message: "订单不存在",
      data: {
        refuseCode: "200004",
        refuseMsg: "无效操作",
      },
    });
  }

  let response: ResponseDataType;

  if (order.repayResultStatus === RepaySubmitEnum.Failed) {
    response = {
      refuseCode: order.refuseCode,
      refuseMsg: RepayErrorMessageMap[order.refuseCode],
    };

    // 新增还款记录
    loanInfo.status?.repayRecords?.push({
      repayNo: params.repayNo,
      outRepayNo: response.outRepayNo,
      outOrderNo: params.outOrderNo,
      repayStatus: RepayStatusEnum.Failed,
      repayResult: RepayErrorMessageMap[order.refuseCode],
      repayAmount: params.repayAmount,
      reductionAmount: params.reductionAmount,
      repayTime: Date.now(),
    });
  }

  if (
    order.repayResultStatus === RepaySubmitEnum.Success
  ) {
    response = {
      outRepayNo: "REPAY" + Date.now(),
      refuseCode: "0",
    };

    const responseRepayTerms: RepayTermDto[] = [];

    if (params.repayType && order.repayQueryResult === RepayStatusEnum.Success) {
      let repayAmount = params.repayAmount;

      params.repayTerms?.forEach((term) => {
        const repayTerm = order?.repayPlanTerms?.find(
          (t) => t.termNo === term.termNo,
        );
        if (!repayTerm) {
          return encryptRes({
            code: 1,
            message: "还款期次不存在",
            data: {
              refuseCode: "200004",
              refuseMsg: "无效操作",
            },
          });
        }

        if (!repayTerm.payableTermAmount) {
          return encryptRes({
            code: 1,
            message: "没有待还金额",
            data: {
              refuseCode: "200004",
              refuseMsg: "无效操作",
            },
          });
        }

        if (repayAmount === 0) {
          return;
        }

        if (params.repayType === 1) {
          // 提前还款时重新计算利息
          const prevTerm = order?.repayPlanTerms?.find(
            (t) => t.termNo === repayTerm.termNo - 1,
          );
          const startDate = prevTerm
            ? dayjs(prevTerm.shouldRepayDate)
            : dayjs(order.effectiveDate);
          const endDate = dayjs();

          // 如果上一期还款日期还没到，不计算利息
          if (startDate.isAfter(endDate)) {
            repayTerm.payableTermAmount = repayTerm.payableTermPrincipal;
            repayTerm.termAmount = repayTerm.payableTermPrincipal || 0;
            repayTerm.payableTermInterest = 0;
            repayTerm.termInterest = 0;
          } else {
            const days = endDate.diff(startDate, "day") + 1;
            // 计算实际利息 = 本金 * 日利率 * 天数
            repayTerm.payableTermInterest = Math.round(
              (repayTerm.payableTermPrincipal *
                parseFloat(order.dayRate) *
                days) /
                100,
            );
            repayTerm.termInterest = repayTerm.payableTermInterest || 0;

            repayTerm.payableTermAmount =
              repayTerm.payableTermPrincipal + repayTerm.payableTermInterest;
            repayTerm.termAmount = repayTerm.payableTermAmount || 0;
          }
        }

        if (repayAmount >= repayTerm.payableTermAmount) {
          repayAmount -= repayTerm.payableTermAmount;
          repayTerm.termStatus = TermStatusEnum.Settled;
          repayTerm.paidTermAmount = repayTerm.payableTermAmount;
          repayTerm.paidTermPrincipal = repayTerm.payableTermPrincipal;
          repayTerm.paidTermInterest = repayTerm.payableTermInterest;
          repayTerm.paidTermPenalty = repayTerm.payableTermPenalty;
          repayTerm.paidTermPrinPenalty = repayTerm.payableTermPrinPenalty;
          repayTerm.paidTermInterPenalty = repayTerm.payableTermInterPenalty;
          repayTerm.paidTermOverdueFee = repayTerm.payableTermOverdueFee;
          repayTerm.paidTermViolateFee = repayTerm.payableTermViolateFee;

          repayTerm.overdueDays = 0;
          repayTerm.overdue = false;
          repayTerm.overdueAmount = 0;

          repayTerm.paidTime = Date.now();
          responseRepayTerms.push({
            termNo: term.termNo,
            termAmount: repayTerm.payableTermAmount,
            termPrincipal: repayTerm.payableTermPrincipal || 0,
            termInterest: repayTerm.payableTermInterest || 0,
            termReductionAmount: params.reductionAmount || 0,
            termPenalty: repayTerm.payableTermPenalty || 0,
            termPrinPenalty: repayTerm.payableTermPrinPenalty || 0,
            termInterPenalty: repayTerm.payableTermInterPenalty || 0,
            termOverdueFee: repayTerm.payableTermOverdueFee || 0,
            termViolateFee: repayTerm.payableTermViolateFee || 0,
            termServiceFee: repayTerm.payableTermServiceFee || 0,
          });
        } else {
          repayTerm.paidTermAmount = repayAmount;
          repayTerm.paidTermPrincipal =
            ((repayTerm.payableTermPrincipal || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermInterest =
            ((repayTerm.payableTermInterest || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermPenalty =
            ((repayTerm.payableTermPenalty || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermPrinPenalty =
            ((repayTerm.payableTermPrinPenalty || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermInterPenalty =
            ((repayTerm.payableTermInterPenalty || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermOverdueFee =
            ((repayTerm.payableTermOverdueFee || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTermViolateFee =
            ((repayTerm.payableTermViolateFee || 0) * repayAmount) /
            (repayTerm.payableTermAmount || 1);
          repayTerm.paidTime = Date.now();
          responseRepayTerms.push({
            termNo: term.termNo,
            termAmount: repayAmount,
            termPrincipal: repayTerm.paidTermPrincipal || 0,
            termInterest: repayTerm.paidTermInterest || 0,
            termReductionAmount: params.reductionAmount || 0,
            termPenalty: repayTerm.paidTermPenalty || 0,
            termPrinPenalty: repayTerm.paidTermPrinPenalty || 0,
            termInterPenalty: repayTerm.paidTermInterPenalty || 0,
            termOverdueFee: repayTerm.paidTermOverdueFee || 0,
            termViolateFee: repayTerm.paidTermViolateFee || 0,
            termServiceFee: repayTerm.paidTermServiceFee || 0,
          });
        }
      });
      if (
        order.repayPlanTerms?.every(
          (term) => term.termStatus === TermStatusEnum.Settled,
        )
      ) {
        order.status = LoanStatusEnum.CLEARED;
        order.clearTime = Date.now();
      } else if (
        order.repayPlanTerms?.every(
          (term) => term.termStatus !== TermStatusEnum.Overdue,
        )
      ) {
        order.status = LoanStatusEnum.REPAYING;
      }
    }


    // 新增还款记录
    loanInfo.status?.repayRecords?.push({
      repayNo: params.repayNo,
      outRepayNo: response.outRepayNo,
      outOrderNo: params.outOrderNo,
      repayStatus: order.repayQueryResult ,
      repayAmount: params.repayAmount,
      reductionAmount: params.reductionAmount,
      repayTime: Date.now(),
      repayTerms: responseRepayTerms,
    });

    // 还款成功后恢复用户额度
    const creditInfo = user.creditInfo as unknown as CreditInfo;
    let remainingRepayAmount = params.repayAmount;

    // 先恢复临时额度
    const tempLimitInfos = creditInfo.status.tempLimitInfos || [];
    for (let i = 0; i < tempLimitInfos.length; i++) {
      const tempLimitInfo = tempLimitInfos[i];
      if (tempLimitInfo.tempLimitValidTime) {
        const tempLimitValidTime = dayjs(tempLimitInfo.tempLimitValidTime);
        if (tempLimitValidTime.isAfter(dayjs())) {
          const tempLimitToRestore = Math.min(
            (tempLimitInfo.tempCreditLimit || 0) - (tempLimitInfo.tempAvailableLimit || 0),
            remainingRepayAmount,
          );
          tempLimitInfo.tempAvailableLimit =
            (tempLimitInfo.tempAvailableLimit || 0) + tempLimitToRestore;
          remainingRepayAmount -= tempLimitToRestore;
        }
      }
    }

    // 如果临时额度恢复完还有剩余，再恢复固定额度
    if (remainingRepayAmount > 0) {
      creditInfo.status.remainLimit += remainingRepayAmount;
      // 确保可用额度不超过授信总额度
      if (creditInfo.status.remainLimit > creditInfo.status.creditLimit) {
        creditInfo.status.remainLimit = creditInfo.status.creditLimit;
      }
    }

  }

  // 如果还款结果为还款中，记录临时还款订单
  if (order.repayQueryResult === RepayStatusEnum.Repaying) {
    const repayRecord = loanInfo.status?.repayRecords?.find(
      (item) => item.repayNo === params.repayNo,
    );
    if (repayRecord) {
      repayRecord.tempRepayOrder = order;
    }
  }

  await updateLoanUser(user);

  if (
    order.repayQueryResult !== RepayStatusEnum.Repaying &&
    order.autoCallback
  ) {
    setTimeout(async () => {
      await notifyRepayResult(
        user.id || 0,
        {
          userId: params.userId,
          repayNo: params.repayNo,
          outRepayNo: response.outRepayNo!,
          loanApplyNo: order.applyNo,
          outOrderNo: params.outOrderNo,
          repaySource: "mock",
          repayStatus: order.repayResultStatus,
          repayAmount: params.repayAmount,
          repayType: params.repayType,
          reductionAmount: params.reductionAmount,
          repayTime: Date.now(),
          repayTerms: [],
        },
        "development",
      );
    }, order.callBackDelay * 1000);
  }

  return encryptRes({
    code: 0,
    message: "success",
    data: response,
  });
}