import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo, TermStatusEnum } from "@/types/cp/loanInfo";
import dayjs from "dayjs";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 渠道侧借款订单号 */
  outOrderNo: string;
  /** 还款期数, -1 表示所有期 */
  repayTerm: number;
  /** 是否部分还款 */
  repayPart: boolean;
  /** 试算金额或者本金，部分还款试算时必传 */
  repayAmount?: number;
  /** 优惠券id，用户使用优惠券时必传 */
  couponNo?: string;
  /** 部分还款试算时必传 1：本金试算，2：金额试算 */
  trialType?: number;
}

export interface ResponseDataType {
  /** 还款类型，1-提前还款，2-正常还款 */
  repayType: number;
  /** 总还款金额, 单位：分 */
  totalAmount: number;
  /** 还款利息金额, 单位：分 */
  totalInterest: number;
  /** 还款本金金额, 单位：分 */
  totalPrincipal: number;
  /** 还款逾期费, 单位：分，有逾期时必传 */
  totalOverdueFee?: number;
  /** 还款服务费, 单位：分 */
  totalServiceFee?: number;
  /** 提前结清违约金, 单位：分 */
  totalViolateFee?: number;
  /** 总罚息, 单位：分，有罚息时必传 */
  totalPenalty?: number;
  /** 优惠券减免金额, 单位：分 */
  totalReductionAmount?: number;
  /** 优惠信息说明 */
  reductionAmountDesc?: string;
  /** 还款备注信息 */
  remark?: string;
  /** 选中优惠券信息，仅限1张优惠券，有可用优惠券时必传 */
  selectedCoupons?: CouponDto[];
  /** 全部可用优惠券信息，有优惠券时必传 */
  usableCoupons?: CouponDto[];
  /** 部分还款后剩余的还款计划 */
  repayPlanTerms?: RepayPlanTermDto[];
  /** 本次还款的期次明细 */
  repayTerms: RepayTermDto[];
}

export interface CouponDto {
  /** 优惠券 ID */
  couponNo: string;
  /** 券模板 id */
  couponRuleId?: string;
  /** 优惠金额 */
  discount: string;
  /** 优惠券有效期起始时间戳 */
  effectiveTime?: number;
  /** 优惠券有效期截止时间戳 */
  invalidTime?: number;
}

export interface RepayTermDto {
  /** 期次 */
  termNo: number;
  /** 本期还款总额,单位：分 */
  termAmount: number;
  /** 本期还款本金，单位：分 */
  termPrincipal: number;
  /** 本期还款利息，单位：分 */
  termInterest: number;
  /** 本期还款优惠券减免金额，单位：分，有优惠金额时必传 */
  termReductionAmount?: number;
  /** 本期还款罚息，单位：分，有罚息时必传 */
  termPenalty?: number;
  /** 本期还款本金罚息，单位：分 */
  termPrinPenalty?: number;
  /** 本期还款利息罚息，单位：分 */
  termInterPenalty?: number;
  /** 本期还款逾期费，单位：分，有逾期罚息必传 */
  termOverdueFee?: number;
  /** 本期还款违约金，有违约金时必传 */
  termViolateFee?: number;
  /** 本期还款服务费，有服务费时必传 */
  termServiceFee?: number;
}

export interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 本期应还总额,单位：分 */
  termAmount: number;
  /** 本期应还本金，单位：分 */
  termPrincipal: number;
  /** 本期应还利息，单位：分 */
  termInterest: number;
  /** 本期优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期还款罚息，单位：分 */
  termPenalty: number;
  /** 本期还款本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期还款利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期还款逾期费，单位：分 */
  termOverdueFee: number;
  /** 本期应还违约金 */
  termViolateFee?: number;
  /** 本期应还服务费 */
  termServiceFee?: number;
}

export async function repayTrial(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 1,
      message: "未找到借款记录",
      data: null,
    });
  }

  const order = loanInfo.status.loanOrders.find(
    (order) => order.outOrderNo === params.outOrderNo,
  );

  if (!order) {
    return encryptRes({
      code: 1,
      message: "未找到指定借款记录",
      data: null,
    });
  }

  // 检查是否已结清
  if (order.status === 4) {
    // 假设4是结清状态
    return encryptRes({
      code: 201701,
      message: "本期借据已结清，请勿重复还款",
      data: null,
    });
  }

  // 检查是否有未结束的还款订单
  if (order.status === 5) {
    // 假设5是还款中状态
    return encryptRes({
      code: 201708,
      message: "存在未结束的还款订单",
      data: null,
    });
  }

  // 获取当前应还期
  if (!order.repayPlanTerms) {
    return encryptRes({
      code: 1,
      message: "还款计划不存在",
      data: null,
    });
  }
  let currentTerm =
    order.repayPlanTerms?.findIndex((term) =>
      dayjs(term.shouldRepayDate).isSameOrAfter(dayjs(), "day"),
    ) ?? -1;
  currentTerm =
    currentTerm === -1 ? order.repayPlanTerms.length : currentTerm + 1;

  // 部分还款时检查必填参数
  if (params.repayPart) {
    if (!params.repayAmount) {
      return encryptRes({
        code: 1,
        message: "部分还款试算时必传试算金额",
        data: null,
      });
    }
    if (!params.trialType) {
      return encryptRes({
        code: 1,
        message: "部分还款试算时必传试算类型",
        data: null,
      });
    }
  }

  // 计算还款金额
  let totalAmount = 0;
  let totalInterest = 0;
  let totalPrincipal = 0;
  let totalPenalty = 0;
  let totalOverdueFee = 0;
  let totalServiceFee = 0;
  let totalViolateFee = 0;
  let totalReductionAmount = 0;
  const repayTerms: RepayTermDto[] = [];

  // 是否逾期
  const isOverdue = order.repayPlanTerms?.some(
    (term) =>
      dayjs(term.shouldRepayDate).isBefore(dayjs(), "day") &&
      term.termStatus !== TermStatusEnum.Settled,
  );

  // 还本期
  if (params.repayTerm !== -1 && !isOverdue) {
    const term = order.repayPlanTerms?.find(
      (term) => term.termNo === params.repayTerm,
    );
    if (!term) {
      return encryptRes({
        code: 1,
        message: "未找到指定还款期",
        data: null,
      });
    }
    totalAmount = term.termAmount;
    totalInterest = term.termInterest;
    totalPrincipal = term.termPrincipal;
    repayTerms.push({
      termNo: term.termNo,
      termAmount: term.termAmount,
      termPrincipal: term.termPrincipal,
      termInterest: term.termInterest,
      termReductionAmount: 0,
      termPenalty: 0,
      termPrinPenalty: 0,
      termInterPenalty: 0,
      termOverdueFee: 0,
      termViolateFee: 0,
      termServiceFee: 0,
    });
  }

  // 全部结清
  if (params.repayTerm === -1 && !isOverdue) {
    order.repayPlanTerms
      ?.filter((term) => term.termStatus !== TermStatusEnum.Settled)
      .forEach((term) => {
        // 获取上一期的应还日期作为本期开始日期
        const prevTerm = order.repayPlanTerms?.find(
          (t) => t.termNo === term.termNo - 1,
        );
        const startDate = prevTerm
          ? dayjs(prevTerm.shouldRepayDate)
          : dayjs(order.effectiveDate);
        const endDate = dayjs();

        // 如果上一期还款日期还没到，不计算利息
        if (startDate.isAfter(endDate)) {
          totalAmount += term.termPrincipal;
          totalPrincipal += term.termPrincipal;
          repayTerms.push({
            termNo: term.termNo,
            termAmount: term.termPrincipal,
            termPrincipal: term.termPrincipal,
            termInterest: 0,
            termReductionAmount: 0,
            termPenalty: 0,
            termPrinPenalty: 0,
            termInterPenalty: 0,
            termOverdueFee: 0,
            termViolateFee: 0,
            termServiceFee: 0,
          });
          return;
        }

        const days = endDate.diff(startDate, "day") + 1;

        // 计算实际利息 = 本金 * 日利率 * 天数
        const actualInterest = Math.round(
          (term.termPrincipal * parseFloat(order.dayRate) * days) / 100,
        );

        totalAmount += term.termPrincipal + actualInterest;
        totalInterest += actualInterest;
        totalPrincipal += term.termPrincipal;
        repayTerms.push({
          termNo: term.termNo,
          termAmount: term.termPrincipal + actualInterest,
          termPrincipal: term.termPrincipal,
          termInterest: actualInterest,
          termReductionAmount: 0,
          termPenalty: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          termOverdueFee: 0,
          termViolateFee: 0,
          termServiceFee: 0,
        });
      });

    // 提前结清违约金
    totalViolateFee = 1000;
    totalAmount += totalViolateFee;
  }

  // 部分还款
  if (params.repayPart) {
    // 获取所有未结清的期次，按应还日期升序排列
    const unSettledTerms = order.repayPlanTerms
      ?.filter((term) => term.termStatus !== TermStatusEnum.Settled)
      .sort((a, b) => dayjs(a.shouldRepayDate).diff(dayjs(b.shouldRepayDate)));

    let remainAmount = params.repayAmount ?? 0;
    const repayTermsResult = [];

    for (const term of unSettledTerms || []) {
      const payableAmount = term.payableTermAmount || 0;
      if (remainAmount >= payableAmount) {
        // 本期全额结清
        repayTermsResult.push({
          termNo: term.termNo,
          termAmount: payableAmount,
          termPrincipal: term.payableTermPrincipal || 0,
          termInterest: term.payableTermInterest || 0,
          termReductionAmount: 0,
          termPenalty: term.payableTermPenalty || 0,
          termPrinPenalty: term.payableTermPrinPenalty || 0,
          termInterPenalty: term.payableTermInterPenalty || 0,
          termOverdueFee: term.payableTermOverdueFee || 0,
          termViolateFee: term.payableTermViolateFee || 0,
          termServiceFee: term.payableTermServiceFee || 0,
        });
        remainAmount -= payableAmount;
      } else if (remainAmount > 0) {
        // 本期按比例分摊
        const ratio = remainAmount / (payableAmount || 1);
        repayTermsResult.push({
          termNo: term.termNo,
          termAmount: remainAmount,
          termPrincipal: Math.floor((term.payableTermPrincipal || 0) * ratio),
          termInterest: Math.floor((term.payableTermInterest || 0) * ratio),
          termReductionAmount: 0,
          termPenalty: Math.floor((term.payableTermPenalty || 0) * ratio),
          termPrinPenalty: Math.floor(
            (term.payableTermPrinPenalty || 0) * ratio,
          ),
          termInterPenalty: Math.floor(
            (term.payableTermInterPenalty || 0) * ratio,
          ),
          termOverdueFee: Math.floor((term.payableTermOverdueFee || 0) * ratio),
          termViolateFee: Math.floor((term.payableTermViolateFee || 0) * ratio),
          termServiceFee: Math.floor((term.payableTermServiceFee || 0) * ratio),
        });
        remainAmount = 0;
        break;
      } else {
        break;
      }
    }

    // 汇总总金额等
    totalAmount = repayTermsResult.reduce(
      (sum, t) => sum + (t.termAmount || 0),
      0,
    );
    totalInterest = repayTermsResult.reduce(
      (sum, t) => sum + (t.termInterest || 0),
      0,
    );
    totalPrincipal = repayTermsResult.reduce(
      (sum, t) => sum + (t.termPrincipal || 0),
      0,
    );
    totalPenalty = repayTermsResult.reduce(
      (sum, t) => sum + (t.termPenalty || 0),
      0,
    );
    totalOverdueFee = repayTermsResult.reduce(
      (sum, t) => sum + (t.termOverdueFee || 0),
      0,
    );
    totalViolateFee = repayTermsResult.reduce(
      (sum, t) => sum + (t.termViolateFee || 0),
      0,
    );
    totalServiceFee = repayTermsResult.reduce(
      (sum, t) => sum + (t.termServiceFee || 0),
      0,
    );
    repayTerms.push(...repayTermsResult);
  }

  // 还全部逾期
  if (isOverdue && !params.repayPart) {
    order.repayPlanTerms
      ?.filter((term) => term.termStatus !== TermStatusEnum.Settled)
      .forEach((term) => {
        if (dayjs(term.shouldRepayDate).isBefore(dayjs(), "day")) {
          totalAmount += term.payableTermAmount || 0;
          totalInterest += term.payableTermInterest || 0;
          totalPrincipal += term.payableTermPrincipal || 0;
          totalPenalty += term.payableTermPenalty || 0;
          repayTerms.push({
            termNo: term.termNo,
            termAmount: term.termAmount,
            termPrincipal: term.termPrincipal,
            termInterest: term.termInterest,
            termReductionAmount: 0,
            termPenalty: term.termPenalty,
            termPrinPenalty: term.termPrinPenalty,
            termInterPenalty: term.termInterPenalty,
            termOverdueFee: term.termOverdueFee,
            termViolateFee: term.termViolateFee,
            termServiceFee: term.termServiceFee,
          });
        }
      });
  }

  const responseData: ResponseDataType = {
    repayType: params.repayTerm === -1 || params.repayPart ? 1 : 2, // 1-提前还款，2-正常还款， 当全部结清或者部分还款时为提前还款
    totalAmount,
    totalInterest,
    totalPrincipal,
    totalPenalty,
    totalOverdueFee,
    totalServiceFee,
    totalViolateFee,
    totalReductionAmount,
    repayTerms,
  };

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
