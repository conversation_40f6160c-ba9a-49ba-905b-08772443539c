import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { CouponStatusEnum, CouponTypeEnum } from "../couponList";
import { CouponInfo, PresetCouponTypeEnum } from "@/types/cp/couponInfo";
import { CreditInfo, RepayMethodEnum } from "@/types/cp/creditInfo";

// 选中状态枚举
export enum SelectStatusEnum {
  UNCHECKED_SELECTABLE = 0, // 未选中可选择
  UNCHECKED_UNSELECTABLE = 1, // 未选中不可选择
  CHECKED_CANCELLABLE = 2, // 选中可取消
  CHECKED_UNCANCELLABLE = 3, // 选中不可取消
}

// 请求参数类型
export interface ParamType {
  userId: string;
  loanAmount: number;
  repayMethod: RepayMethodEnum;
  totalTerm: number;
}

// 优惠券DTO
export interface LoanUsableCouponDto {
  couponNo: string; // 优惠券id
  couponType: CouponTypeEnum; // 券类型
  status: CouponStatusEnum; // 券状态
  startTime: number; // 券有效期开始时间
  endTime: number; // 券有效期结束时间
  unusableReason?: string; // 券不可用原因
  selectStatus?: SelectStatusEnum; // 选中状态
  discountAmount: number; // 优惠金额，单位分
  couponLabel?: string; // 卡券标签
  couponName: string; // 优惠券名称
  useRules: string[]; // 使用规则列表
  receiveTime: number; // 领取时间
  usedTime?: number; // 使用时间
}

// 响应结果类型
export interface ResponseDataType {
  totalNum: number;
  records: LoanUsableCouponDto[];
}

export async function loanUsableCoupon(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  // 1. 查询用户未使用的优惠券
  const couponInfo = user.couponInfo as unknown as CouponInfo;
  const unusedCoupons =
    couponInfo.status?.couponList
      ?.filter((item) => {
        return item.status === CouponStatusEnum.UNUSED;
      })
      .filter((item) => {
        const now = Date.now();
        return item.startTime! <= now && item.endTime! >= now;
      })
      .sort((a, b) => b.receiveTime - a.receiveTime) || [];

  // 2. 根据借款金额、还款方式、期数等条件筛选可用优惠券
  const usableCoupons = unusedCoupons.map((coupon) => {
    let unusableReason;
    let selectStatus = SelectStatusEnum.UNCHECKED_SELECTABLE;

    // 检查使用规则
    if (coupon.useRules) {
      for (const rule of coupon.useRules) {
        const { field, operator, value } = rule;

        if (field === "loanAmount") {
          const ruleValue = parseInt(value);
          if (
            (operator === "gt" && params.loanAmount <= ruleValue) ||
            (operator === "gte" && params.loanAmount < ruleValue) ||
            (operator === "lt" && params.loanAmount >= ruleValue) ||
            (operator === "lte" && params.loanAmount > ruleValue) ||
            (operator === "eq" && params.loanAmount !== ruleValue)
          ) {
            unusableReason = "不满足借款金额要求";
            selectStatus = SelectStatusEnum.UNCHECKED_UNSELECTABLE;
            break;
          }
        }

        if (field === "termNums") {
          const ruleValue = parseInt(value);
          if (
            (operator === "gt" && params.totalTerm <= ruleValue) ||
            (operator === "gte" && params.totalTerm < ruleValue) ||
            (operator === "lt" && params.totalTerm >= ruleValue) ||
            (operator === "lte" && params.totalTerm > ruleValue) ||
            (operator === "eq" && params.totalTerm !== ruleValue)
          ) {
            unusableReason = "不满足借款期数要求";
            selectStatus = SelectStatusEnum.UNCHECKED_UNSELECTABLE;
            break;
          }
        }

        if (field === "repayMethod") {
          if (operator === "eq" && params.repayMethod.toString() !== value) {
            unusableReason = "不满足还款方式要求";
            selectStatus = SelectStatusEnum.UNCHECKED_UNSELECTABLE;
            break;
          }
        }
      }
    }

    return {
      ...coupon,
      unusableReason,
      selectStatus,
    };
  });

  const creditInfo = user.creditInfo as unknown as CreditInfo;
  usableCoupons.forEach((coupon) => {
    if (
      coupon.presetCouponType === PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE
    ) {
      const repayMethod = params.repayMethod;
      creditInfo.status.productInfos.forEach((productInfo) => {
        if (productInfo.repayMethod === repayMethod) {
          coupon.discountAmount = Math.floor(
            ((params.loanAmount * parseFloat(productInfo.dayRate)) / 100) * 7,
          );
        }
      });
    }
  });

  const loanUsableCouponDto: LoanUsableCouponDto[] = usableCoupons.map(
    (coupon) => ({
      couponNo: coupon.couponNo!,
      couponType: coupon.couponType!,
      status: coupon.status!,
      startTime: coupon.startTime!,
      endTime: coupon.endTime!,
      unusableReason: coupon.unusableReason,
      selectStatus: coupon.selectStatus,
      discountAmount: coupon.discountAmount,
      couponName: coupon.couponName!,
      useRules: coupon.useRules!.map((rule, index) => {
        const { field, operator, value } = rule;
        let ruleText = "";

        switch (field) {
          case "loanAmount":
            const amount = parseInt(value) / 100; // 转换为元
            switch (operator) {
              case "gt":
                ruleText = `借款金额需大于${amount}元`;
                break;
              case "gte":
                ruleText = `借款金额需大于等于${amount}元`;
                break;
              case "lt":
                ruleText = `借款金额需小于${amount}元`;
                break;
              case "lte":
                ruleText = `借款金额需小于等于${amount}元`;
                break;
              case "eq":
                ruleText = `借款金额需等于${amount}元`;
                break;
            }
            break;
          case "termNums":
            switch (operator) {
              case "gt":
                ruleText = `借款期数需大于${value}期`;
                break;
              case "gte":
                ruleText = `借款期数需大于等于${value}期`;
                break;
              case "lt":
                ruleText = `借款期数需小于${value}期`;
                break;
              case "lte":
                ruleText = `借款期数需小于等于${value}期`;
                break;
              case "eq":
                ruleText = `借款期数需等于${value}期`;
                break;
            }
            break;
          case "repayMethod":
            const methodMap: Record<number, string> = {
              1: "等额本息(灵活还)",
              2: "等额本金(灵活还)",
              3: "先息后本(灵活还)",
              4: "等额本息(按期还)",
            };
            ruleText = `还款方式需为${methodMap[parseInt(value)]}`;
            break;
        }
        return `(${index + 1})${ruleText}`;
      }),
      receiveTime: coupon.receiveTime!,
      usedTime: coupon.usedTime,
    }),
  );

  const responseData: ResponseDataType = {
    totalNum: loanUsableCouponDto.length,
    records: loanUsableCouponDto,
  };

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}