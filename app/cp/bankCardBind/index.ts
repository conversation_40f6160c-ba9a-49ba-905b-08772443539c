import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import {
  BankCardBindEnum,
  BankCardInfo,
  CardBandStatusEnum,
} from "@/types/cp/bankCard";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 银行卡号 */
  bankCardNo: string;
  /** 姓名 */
  name: string;
  /** 身份证号 */
  idNo: string;
  /** 银行预留手机号 */
  mobileNo: string;
}

// 银行卡信息 DTO
interface ResponseDataType {
  /** 绑卡ID（绑卡验证成功返回） */
  bankCardId?: string;
  /** 银行名称（绑卡验证成功返回） */
  bankName?: string;
  /** 银行代码（绑卡验证成功返回） */
  bankCode?: string;
  /** 卡类型，1-借记卡，2-信用卡（绑卡验证成功返回） */
  cardType?: number;
  /** 单日限额，单位：分（绑卡验证成功返回） */
  dayLimit?: number;
  /** 单笔限额，单位：分（绑卡验证成功返回） */
  singleLimit?: number;
  /**
   * 验证结果
   * 0-成功，
   * 1-预留手机号错误，
   * 2-身份证错误，
   * 3-姓名错误，
   * 4-四要素认证失败，
   * 5-重复绑定，
   * 6-暂不支持该卡，
   * 7-获取验证码超过限制次数，
   * 8-卡号错误，
   * 9-绑卡失败，
   * 10-其他原因
   */
  verifyResult: number;
  /** 结果描述，失败必传 */
  verifyMsg?: string;
}

export async function bankCardBind(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const bankCardInfo = user.bankCardInfo as unknown as BankCardInfo;
  if (bankCardInfo.action.bandStatus === CardBandStatusEnum.PASSED) {
    const bankCardId = crypto.randomUUID();
    bankCardInfo.status.bankCardList.push({
      bankCardId: bankCardId,
      bankCardNo: params.bankCardNo.slice(-4),
      bankName: "中国工商银行",
      bankCode: "icbc",
      cardType: 1,
      dayLimit: 2000000,
      singleLimit: 2000000,
      status: BankCardBindEnum.NOT_BOUND,
    });

    updateLoanUser(user);

    responseData.verifyResult = 0;
    responseData.verifyMsg = "成功";
    responseData.bankCardId = bankCardId;
    responseData.bankName = "中国工商银行";
    responseData.bankCode = "icbc";
    responseData.cardType = 1;
    responseData.dayLimit = 2000000;
    responseData.singleLimit = 2000000;
  } else {
    responseData.verifyResult = 1;
    responseData.verifyMsg = "预留手机号错误";
  }

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
