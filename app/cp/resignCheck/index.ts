import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo } from "@/types/cp/loanInfo";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 总还款金额，单位：分 */
  totalAmount: number;
  /** 还款卡绑卡ID */
  bankCardId: string;
  /** 渠道侧借款订单号 */
  outOrderNo: string;
}

export interface ResponseDataType {
  /** 是否需要重新签约标识(0: 不需要，1：需要) */
  needResign: number;
}

export async function resignCheck(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  // 验证必填字段
  if (!params.totalAmount || !params.bankCardId || !params.outOrderNo) {
    return encryptRes({
      code: 1,
      message: "参数错误",
      data: null,
    });
  }
  const loanInfo = user.loanInfo as unknown as LoanInfo;
  const order = loanInfo.status?.loanOrders?.find(
    (order) => order.outOrderNo === params.outOrderNo,
  );
  if (!order) {
    return encryptRes({
      code: 1,
      message: "订单不存在",
      data: null,
    });
  }

  if (order.resignCheckResult === false) {
    return encryptRes({
      code: order.resignCheckErrorCode || 1,
      message: order.resignCheckErrorMsg || "unknown error",
      data: null,
    });
  }

  const response: ResponseDataType = order.needResign
    ? {
        needResign: 1,
      }
    : {
        needResign: 0,
      };

  return encryptRes({
    code: 0,
    message: "success",
    data: response,
  });
}