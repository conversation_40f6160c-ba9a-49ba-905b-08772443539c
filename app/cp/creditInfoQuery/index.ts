import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import {
  CreditApplyStatusEnum,
  CreditInfo,
  CreditStatusEnum,
} from "@/types/cp/creditInfo";
import dayjs from "dayjs";

enum StatusEnum {
  NORMAL = 1,
  PENDING = 2,
  OVERDUE = 3,
  REJECTED = 4,
}

export interface ParamType {
  userId: string;
}

export interface ResponseDataType {
  status: number; // 额度状态，1-正常，2-审核中，3-失效，4-拒绝
  creditLimit?: number; // 授信总额度，单位分，status=1时必传
  limitType?: 1; // 额度类型，1-循环额度
  remainLimit?: number; // 可用额度，单位分，status=1时必传，不包含临时额度
  limitExpireDate?: string; // 额度失效日期，格式yyyyMMdd，如果存在则返回
  repayDay?: number; // 还款日
  limitUseErrStatus?: number; // 额度使用状态，如果不是下述状态则不返回 1-存在放款中借款，2-存在逾期订单不可借款，3-暂时无法支用，4-其他原因
  limitUseErrDesc?: string; // 额度使用描述
  greyExpireTime?: number; // 额度管控截止时间，时间戳(秒)，limitUseErrStatus=4返回
  totalAvailableLimit?: number; // 总可用额度（临额+固额总和），单位分，status=1时必传
  totalCreditLimit?: number; // 总授信额度（临额+固额总和），单位分，status=1时必传
  maxLoan?: number; // 单笔借款最大金额，单位分，status=1时必传
  minLoan?: number; // 单笔借款最小金额，单位分，status=1时必传
  canCreditChange?: boolean; // 是否可提额，默认false，status=1时必传
  productInfos?: ProductInfo[]; // 产品信息， status = 1要返回
  repayInfo?: RepayInfo; // 用户待还款信息，有未结清订单时返回
  tempLimitInfo?: TempLimitInfo; // 临额相关信息，如果有就返回
  overdueInfo?: OverdueInfo; // 逾期信息，limitUseErrStatus=2要返回
  applyTime: number; // 授信申请时间，毫秒
  cancelStatus: 0 | 1; // 用户注销状态，0-正常，1-已注销
}

export interface ProductInfo {
  dayRate?: string;
  monthRate?: string;
  apr?: string;
  repayMethod: RepayMethodEnum;
  earlyRepay: boolean;
  termNums: number[];
  termPriceInfos?: TermPriceInfoDto[];
  tempDayRate?: string;
  tempApr?: string;
  tempPriceDueTime?: string;
  isDynamicRate?: boolean;
}

/** 还款方式 */
export enum RepayMethodEnum {
  /** 1-等额本息(灵活还) */
  EqualPrincipalAndInterest = 1,
  /** 2-等额本金(灵活还) */
  EqualPrincipal = 2,
  /** 3-先息后本(灵活还) */
  InterestFirst = 3,
  /** 4-等额本息(固定期限) */
  FixedTerm = 4,
}

export interface TermPriceInfoDto {
  termNum: number;
  dayRate: string;
  monthRate: string;
  apr: string;
  tempDayRate: string;
  tempApr: string;
  tempPriceDueTime: string;
}

export interface RepayInfo {
  totalAmount: number;
  principal: number;
  interest: number;
  serviceFee: number;
}

export interface TempLimitInfo {
  tempLimitValidTime: string;
  tempCreditLimit: number;
  tempAvailableLimit: number;
}

export interface OverdueInfo {
  overdueAmount: number;
  overdueOrder: number;
  overdueDays: number;
  overduePenalty: number;
}

export async function creditInfoQuery(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.REJECTED) {
    responseData.status = StatusEnum.REJECTED;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (
    creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PENDING
  ) {
    responseData.status = StatusEnum.PENDING;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PASSED) {
    const tempLimitInfos = creditInfo?.status?.tempLimitInfos;
    tempLimitInfos?.forEach((tempLimitInfo) => {
      delete tempLimitInfo.adjustTime;
    });
    let tempCreditLimit = 0;
    let tempAvailableLimit = 0;
    if (tempLimitInfos) {
      tempLimitInfos.forEach((tempLimitInfo) => {
        if (dayjs(tempLimitInfo.tempLimitValidTime).isBefore(dayjs())) {
          return;
        }
        tempCreditLimit += tempLimitInfo.tempCreditLimit;
        tempAvailableLimit += tempLimitInfo.tempAvailableLimit || 0;
      });
    }

    responseData.creditLimit = creditInfo?.status?.creditLimit;
    responseData.remainLimit = creditInfo?.status?.remainLimit;
    responseData.totalAvailableLimit =
      creditInfo?.status?.remainLimit + tempAvailableLimit;
    responseData.totalCreditLimit =
      creditInfo?.status?.creditLimit + tempCreditLimit;
    responseData.maxLoan = creditInfo?.status?.remainLimit + tempAvailableLimit;
    responseData.minLoan = 50000;
    responseData.canCreditChange = false;
    responseData.tempLimitInfo = tempLimitInfos?.[tempLimitInfos.length - 1];

    creditInfo?.status?.productInfos.forEach((productInfo) => {
      if (productInfo.isDynamicRate) {
        delete productInfo.monthRate;
        delete productInfo.apr;
        delete productInfo.dayRate;

        delete productInfo.tempApr;
        delete productInfo.tempDayRate;
      }
    });
    responseData.productInfos = creditInfo?.status?.productInfos;
    responseData.applyTime = creditInfo?.status?.applyTime;
    responseData.limitUseErrStatus = creditInfo?.status?.limitUseErrStatus;
    responseData.greyExpireTime = creditInfo?.status?.greyExpireTime;
  }
  responseData.status =
    creditInfo?.status?.creditStatus || CreditStatusEnum.NORMAL;
  responseData.cancelStatus = creditInfo?.status?.userCancelStatus;

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
