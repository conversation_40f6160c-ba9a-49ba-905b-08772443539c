import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 手机号，用户绑定时必须，其他场景使用用户绑定时的手机号 */
  mobileNo: string;
  /** 用户真实ip */
  ip?: string;
  /** 0-借款，1-还款 */
  type?: number;
}

export interface ResponseDataType {
  /** 验证码序列号，发送成功后返回 */
  serialNo: string;
}

// 验证手机号格式
function isValidMobile(mobile: string): boolean {
  return /^1[3-9]\d{9}$/.test(mobile);
}

export async function smsSend(params: ParamType) {
  // 验证手机号格式
  if (!isValidMobile(params.mobileNo)) {
    return encryptRes({
      code: 203101,
      message: "手机号错误",
      data: null,
    });
  }

  // 获取用户信息
  const user = await getLoanUserByUserId(params.userId);
  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  // 生成验证码和序列号
  const verificationCode = Math.floor(
    100000 + Math.random() * 900000,
  ).toString();

  const responseData: ResponseDataType = {
    serialNo: verificationCode,
  };

  return encryptRes({
    code: 0,
    message: "success",
    data: responseData,
  });
}