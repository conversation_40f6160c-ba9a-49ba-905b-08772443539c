import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import { ContractInfo } from "@/types/cp/contractInfo";

enum BusinessType {
  CREDIT = 1,
  BIND_CARD = 2,
  LOAN = 3,
}

export interface ParamType {
  userId: string;
  businessType: BusinessType;
}
export async function contractQuery(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const contractInfo = user.contractsInfo as unknown as ContractInfo;
  const contracts = contractInfo.status.contractList;
  const targetContracts = contracts.filter(
    (contract) =>
      contract.businessType.valueOf() === params.businessType.valueOf(),
  );

  targetContracts.forEach((contract) => {
    contract.isRead = true;
  });

  updateLoan<PERSON>ser(user);

  const res = {
    code: 0,
    message: "success",
    data: {
      totalNum: targetContracts.length,
      records: targetContracts,
    },
  };

  return encryptRes(res);
}