interface ParamType {
  /** 荣耀方用户id，必填 */
  userId: string;
  /** 荣耀方订单号，已交易鉴权未提交，且CP缓存订单未失效时必传，可选 */
  applyNo?: string;
  /** 借款金额，单位：分，必填 */
  loanAmount: number;
  /** 还款方式：1-等额本息，2-等额本金，3-先息后本，4-锁期（固定期限），必填 */
  repayMethod: number;
  /** 借款期数，必填 */
  totalTerm: number;
  /** 优惠券id，可选 */
  couponNo?: string;
  /** 0-借款试算不返回协议，1-借款试算返回协议，可选 */
  needContract?: number;
}

/** 主返回结构 */
interface ResponseDataType {
  /** 应还总额，单位：分 */
  shouldRepayAmount: number;
  /** 应还本金总额，单位：分 */
  shouldRepayPrinAmount: number;
  /** 应还利息总额，单位：分 */
  shouldRepayInterAmount: number;
  /** 应还服务费总额，单位：分 */
  shouldRepayServiceFee?: number;
  /** 首次还款日，yyyyMMdd */
  firstRepayDate: string;
  /** 最后还款日，yyyyMMdd */
  lastRepayDate: string;
  /** 优惠券减免金额，单位：分 */
  reductionAmount?: number;
  /** 限时降价优惠金额，单位：分 */
  tempDiscount?: number;
  /** 按期还优惠金额，单位：分 */
  scheduleDiscount?: number;
  /** 总优惠金额，单位：分 */
  totalDiscount?: number;
  /** 出资方 */
  stakeholders?: string;
  /** 综合年利率，示例12.95【即12.95%】（优惠后） */
  annualRate: string;
  /** 优惠前原价年利率，示例12.95【即12.95%】 */
  originalRate?: string;
  /** 还款计划 */
  repayPlanTerms: RepayPlanTermDto[];
  /** 返回借款协议 , 入参的needContract 为 1的情况下返回 */
  contract?: ContractDto[];
}

/** 还款计划明细 */
interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 本期应还总额,单位：分 (termAmount = termPrincipal + termInterest) */
  termAmount: number;
  /** 本期应还本金，单位：分 */
  termPrincipal: number;
  /** 本期应还利息，单位：分 */
  termInterest: number;
  /** 本期优惠券减免金额，单位：分 */
  termReductionAmount?: number;
  /** 本期限时降价优惠金额，单位：分 */
  termTempDiscount?: number;
  /** 本期按期还优惠金额，单位：分 */
  termScheduleDiscount?: number;
  /** 本期总优惠金额，单位：分 */
  termTotalDiscount?: number;
  /** 应还金额（总金额 - 优惠金额），单位：分 */
  amount: number;
  /** 本期应还服务费 */
  termServiceFee?: number;
}

/** 合同信息 */
interface ContractDto {
  /** 是否默认勾选，1：勾选；0：不勾选 */
  defaultChecked: number;
  /** 合同地址 */
  contractUrl: string;
  /** 合同名称 */
  contractName: string;
}
