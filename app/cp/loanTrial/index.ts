import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { CreditInfo, RepayMethodEnum } from "@/types/cp/creditInfo";
import {
  Coupon,
  CouponInfo,
  PresetCouponTypeEnum,
} from "@/types/cp/couponInfo";
import dayjs from "dayjs";
import { BusinessTypeEnum, ContractInfo } from "@/types/cp/contractInfo";
import { LoanInfo, TrialStatusEnum } from "@/types/cp/loanInfo";

export async function loanTrial(params: ParamType) {
  console.log("请求入参:", params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (loanInfo.action?.trialStatus === TrialStatusEnum.FAILED) {
    return encryptRes({
      code: loanInfo.action.trialErrorCode || 1,
      message: loanInfo.action.trialErrorMsg || "unknown error",
      data: null,
    });
  }

  let reductionAmount = 0;
  let tempDiscount = 0;
  let scheduleDiscount = 0;

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  const productInfo = creditInfo.status.productInfos.find((item) => {
    return item.repayMethod === params.repayMethod;
  });

  // 如果有使用优惠券
  let activeCoupon: Coupon | undefined;
  if (params.couponNo) {
    const couponInfo = user.couponInfo as unknown as CouponInfo;
    activeCoupon = couponInfo.status?.couponList?.find(
      (item: Coupon) => item.couponNo === params.couponNo,
    );
    if (activeCoupon) {
      if (
        activeCoupon.presetCouponType ===
        PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE
      ) {
        // 计算一下七天免息券的优惠金额
        reductionAmount = Math.floor(
          ((params.loanAmount * parseFloat(productInfo?.dayRate!)) / 100) * 7,
        );
      }
    }
  }

  // 如果有临时利率，计算优惠金额
  if (productInfo?.tempApr) {
    const baseApr = parseFloat(productInfo.apr!);
    const tempApr = parseFloat(productInfo.tempApr);
    tempDiscount = parseFloat(
      (
        (((baseApr - tempApr) / 100) * params.loanAmount * params.totalTerm) /
        12
      ).toFixed(2),
    );
  }

  if (!productInfo) {
    return encryptRes({
      code: 1,
      message: "不支持的还款方式",
      data: null,
    });
  }

  const monthRate = parseFloat(productInfo.monthRate!) / 100; // 月利率

  // 首次还款日设置为一个月后，格式为 yyyyMMdd
  responseData.firstRepayDate = dayjs(Date.now())
    .add(1, "month")
    .format("YYYYMMDD");

  // 最后还款日设置为首次还款日加上总期数，格式为 yyyyMMdd
  responseData.lastRepayDate = dayjs(Date.now())
    .add(params.totalTerm, "month")
    .format("YYYYMMDD");

  if (!productInfo.tempApr) {
    responseData.annualRate = productInfo.apr!;
  } else {
    responseData.originalRate = productInfo.apr!;
    responseData.annualRate = productInfo.tempApr;
  }

  if (productInfo.isDynamicRate) {
    // 根据还款期数动态调整利率，在原有利率基础上变化
    const baseApr = parseFloat(productInfo.apr!);
    const baseTempApr = productInfo.tempApr
      ? parseFloat(productInfo.tempApr)
      : undefined;

    switch (params.totalTerm) {
      case 3:
        if (!baseTempApr) {
          responseData.annualRate = (baseApr * 1.2).toFixed(2);
        } else {
          responseData.originalRate = (baseApr * 1.2).toFixed(2);
          responseData.annualRate = (baseTempApr * 1.2).toFixed(2);
        }
        break;
      case 6:
        if (!baseTempApr) {
          responseData.annualRate = (baseApr * 1.1).toFixed(2);
        } else {
          responseData.originalRate = (baseApr * 1.1).toFixed(2);
          responseData.annualRate = (baseTempApr * 1.1).toFixed(2);
        }
        break;
      case 12:
        // 12期保持原利率不变
        break;
      case 24:
        if (!baseTempApr) {
          responseData.annualRate = (baseApr * 0.9).toFixed(2);
        } else {
          responseData.originalRate = (baseApr * 0.9).toFixed(2);
          responseData.annualRate = (baseTempApr * 0.9).toFixed(2);
        }
        break;
      default:
        // 其他期数保持原利率不变
        break;
    }
  }

  responseData.shouldRepayPrinAmount = params.loanAmount;
  responseData.shouldRepayAmount = responseData.shouldRepayPrinAmount;
  responseData.shouldRepayInterAmount = 0;
  // 还款计划
  if (
    params.repayMethod === RepayMethodEnum.EqualPrincipalAndInterest ||
    params.repayMethod === RepayMethodEnum.FixedTerm
  ) {
    responseData.repayPlanTerms = Array.from(
      { length: params.totalTerm },
      (_, i) => {
        const termNo = i + 1;
        // 计算每月还款日期，保持日期号数相同
        const firstRepayDate = dayjs(Date.now()).add(1, "month");
        const shouldRepayDate = firstRepayDate
          .add(i, "month")
          .format("YYYYMMDD");

        // 等额本息计算
        const monthlyRate = monthRate; // 月利率
        const totalMonths = params.totalTerm;
        const principal = params.loanAmount;

        // 计算每月还款额
        const monthlyPayment =
          (principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) /
          (Math.pow(1 + monthlyRate, totalMonths) - 1);

        // 计算当前期数的本金和利息
        let remainingPrincipal = principal;
        for (let j = 0; j < i; j++) {
          const interest = remainingPrincipal * monthlyRate;
          const principalPayment = monthlyPayment - interest;
          remainingPrincipal -= principalPayment;
        }

        const termInterest = Math.round(remainingPrincipal * monthlyRate);
        responseData.shouldRepayInterAmount += termInterest;
        responseData.shouldRepayAmount += termInterest;
        const termPrincipal = Math.round(monthlyPayment - termInterest);

        // 如果使用了七天免息券，首期的前七天利息计入优惠金额
        let termReductionAmount = 0;
        if (
          activeCoupon?.presetCouponType ===
            PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE &&
          termNo === 1
        ) {
          termReductionAmount = Math.floor(
            ((params.loanAmount * parseFloat(productInfo?.dayRate!)) / 100) * 7,
          );
        }

        return {
          termNo,
          shouldRepayDate,
          termAmount: Math.round(monthlyPayment),
          termPrincipal,
          termInterest,
          termReductionAmount,
          amount: Math.round(monthlyPayment - termReductionAmount),
        };
      },
    );
  }

  // 是否存在灵活还
  const isContainFlexibleRepay = creditInfo.status.productInfos.some(
    (item) => item.repayMethod !== RepayMethodEnum.FixedTerm,
  );

  // 是否存在按期还
  const isContainFixedTermRepay = creditInfo.status.productInfos.some(
    (item) => item.repayMethod === RepayMethodEnum.FixedTerm,
  );

  if (
    isContainFlexibleRepay &&
    isContainFixedTermRepay &&
    params.repayMethod === RepayMethodEnum.FixedTerm
  ) {
    let flexibleInterest = 0;
    const flexibleProductInfo = creditInfo.status.productInfos.find(
      (item) => item.repayMethod !== RepayMethodEnum.FixedTerm,
    );

    if (
      flexibleProductInfo?.repayMethod ===
      RepayMethodEnum.EqualPrincipalAndInterest
    ) {
      const flexibleMonthRate =
        parseFloat(flexibleProductInfo?.monthRate || "0") / 100;
      const totalMonths = params.totalTerm;
      const principal = params.loanAmount;

      // 计算每月还款额
      const monthlyPayment =
        (principal *
          flexibleMonthRate *
          Math.pow(1 + flexibleMonthRate, totalMonths)) /
        (Math.pow(1 + flexibleMonthRate, totalMonths) - 1);

      // 计算每期利息和本金
      let remainingPrincipal = principal;
      for (let i = 0; i < totalMonths; i++) {
        const termInterest = Math.round(remainingPrincipal * flexibleMonthRate);
        flexibleInterest += termInterest;
        const principalPayment = Math.round(monthlyPayment - termInterest);
        remainingPrincipal -= principalPayment;
      }
    }

    if (flexibleProductInfo?.repayMethod === RepayMethodEnum.EqualPrincipal) {
      const flexibleMonthRate =
        parseFloat(flexibleProductInfo?.monthRate || "0") / 100;
      const totalMonths = params.totalTerm;
      const principal = params.loanAmount;

      // 等额本金：每期本金相同
      const monthlyPrincipal = Math.round(principal / totalMonths);
      let remainingPrincipal = principal;

      // 计算每期利息和总利息
      for (let i = 0; i < totalMonths; i++) {
        const termInterest = Math.round(remainingPrincipal * flexibleMonthRate);
        flexibleInterest += termInterest;
        remainingPrincipal -= monthlyPrincipal;
      }
    }

    if (flexibleProductInfo?.repayMethod === RepayMethodEnum.InterestFirst) {
      const flexibleMonthRate =
        parseFloat(flexibleProductInfo?.monthRate || "0") / 100;
      const totalMonths = params.totalTerm;
      const principal = params.loanAmount;

      // 先息后本：前期只还利息，最后一期还本金和利息
      for (let i = 0; i < totalMonths; i++) {
        // 每期利息 = 本金 * 月利率
        const termInterest = Math.round(principal * flexibleMonthRate);
        flexibleInterest += termInterest;
      }
    }

    // 按期还的利息总额已经在responseData.shouldRepayInterAmount中
    const fixedTermInterest = responseData.shouldRepayInterAmount;

    // 如果按期还的利息更低,给予优惠
    if (fixedTermInterest < flexibleInterest) {
      scheduleDiscount = flexibleInterest - fixedTermInterest;
    }
  }

  responseData.reductionAmount = reductionAmount;
  responseData.tempDiscount = tempDiscount;
  responseData.scheduleDiscount = scheduleDiscount;
  responseData.totalDiscount =
    reductionAmount + tempDiscount + scheduleDiscount;

  const contractsInfo = user.contractsInfo as unknown as ContractInfo;
  if (params.needContract) {
    const loanContracts = contractsInfo.status.contractList.filter(
      (item) => item.businessType === BusinessTypeEnum.LOAN,
    );

    loanContracts.forEach((item) => {
      item.isRead = true;
    });

    responseData.contract = loanContracts.map((item) => ({
      defaultChecked: item.defaultChecked,
      contractUrl: item.contractUrl || "",
      contractName: item.contractName,
    }));
  }

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
