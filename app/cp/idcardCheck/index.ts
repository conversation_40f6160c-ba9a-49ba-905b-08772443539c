import { IdCardCheckRequest, IdCardCheckResponse } from "./types";
import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo } from "@/types/cp/loanInfo";

export async function idcardCheck(params: IdCardCheckRequest) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  const responseData: IdCardCheckResponse = {
    frontImage: {
      result: loanInfo.action.idCardFrontResult,
    },
    backImage: {
      result: loanInfo.action.idCardBackResult,
    },
  };

  return encryptRes({
    code: 0,
    message: "success",
    data: responseData,
  });
}
