export interface IdCardCheckRequest {
  /**
   * 荣耀方用户id
   */
  userId: string;
  /**
   * 身份证正面图片图像数据，base64编码
   */
  faceImageContent: string;
  /**
   * 身份证反面图片图像数据，base64编码
   */
  backImageContent: string;
}

export interface IdCardImageResultDto {
  /**
   * 识别结果，0：识别失败，1：识别成功
   */
  result: number;
  /**
   * 图片状态
   * reversed_side-身份证正反面颠倒
   * non_idcard-上传的图片中不包含身份
   * blurred-身份证模糊
   * over_exposure-身份证关键字段反光或亮度过高
   * over_dark-身份证亮度过低
   * unknown-未知状态
   */
  imageStatus?: string;
  /**
   * 风险类型
   * copy-复印件
   * temporary-临时身份证
   * screen-翻拍
   * ps-PS
   * paint-涂抹
   * mosaic-马赛克
   * non_idcard-非身份证
   * unknown-其他未知情况
   */
  riskType?: string;
}

export interface IdCardCheckResponse {
  /**
   * 身份证正面识别结果
   */
  frontImage: IdCardImageResultDto;
  /**
   * 身份证反面识别结果
   */
  backImage: IdCardImageResultDto;
} 