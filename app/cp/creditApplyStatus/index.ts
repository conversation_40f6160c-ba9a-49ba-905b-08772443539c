import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { CreditApplyStatusEnum, CreditInfo } from "@/types/cp/creditInfo";

export interface ParamType {
  userId: string;
  applyNo: string;
}

// 主体授信信息
export interface ResponseDataType {
  /** 渠道方授信流水号 */
  outOrderNo: string;
  /** 授信结果，1-审核中，2-通过，3-拒绝 */
  applyStatus: number;
  /** 拒绝原因码，失败必传 */
  refuseCode?: string;
  /** 拒绝原因说明，失败必传 */
  refuseMsg?: string;
  /** 拒绝具体原因说明，失败必传 */
  refuseMsgData?: string;
  /** 授信拒绝管控期，单位：秒 */
  refuseControlTime?: number;
  /** 新老客标识，1-老客，2-新客 */
  identity?: number;
  /** 授信总额度，单位分（授信通过时返回） */
  creditLimit: number;
  /** 原授信总额度，单位分 */
  originCreditLimit?: number;
  /** 额度类型，1-循环额度 */
  limitType?: number;
  /** 可用额度，单位分（授信通过时返回） */
  remainLimit: number;
  /** 额度失效日期，格式yyyyMMdd */
  limitExpireDate?: string;
  /** 额度管控截止时间（禁止申请用信），时间戳(秒) */
  greyExpireTime?: number;
  /** 总可用额度（临额+固额总和），单位分（授信通过时返回） */
  totalAvailableLimit: number;
  /** 总授信额度（临额+固额总和），单位分（授信通过时返回） */
  totalCreditLimit: number;
  /** 日利率，示例：0.065【即0.065%】（授信通过时返回，价格最低产品为日利率时返回） */
  dayRate: string;
  /** 月利率（授信通过时返回，价格最低产品为月利率时返回） */
  monthRate: string;
  /** 年利率，示例：23.4【即23.4%】（授信通过时返回） */
  apr: string;
  /** 单笔借款最大金额，单位分，applyStatus=2时必传 */
  maxLoan?: number;
  /** 单笔借款最小金额，单位分，applyStatus=2时必传 */
  minLoan?: number;
  /** 临额相关信息，有临额临价时必传 */
  tempLimitInfo?: TempLimitInfo;
  /** 授信申请时间，毫秒 */
  applyTime: number;
}

// 临额相关信息
export interface TempLimitInfo {
  /** 临额有效期，示例：2023-09-23 23:59:59，有临额时返回 */
  tempLimitValidTime?: string;
  /** 临额授信额度，有临额时返回 */
  tempCreditLimit?: number;
  /** 临额可用额度，有临额时返回 */
  tempAvailableLimit?: number;
  /** 临价截止时间，示例：2023-09-23 23:59:59，有临价时返回 */
  tempPriceValidTime?: string;
  /** 临价日利率，示例：0.065【即0.065%】，有临价时返回 */
  tempDayRate?: string;
  /** 临价月利率，有临价时返回 */
  tempMonthRate?: string;
  /** 临价年利率，示例：23.4【即23.4%】，有临价时返回 */
  tempApr?: string;
}
export async function creditApplyStatus(params: ParamType) {
  console.log("请求入参:", params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};
  const creditInfo = user.creditInfo as unknown as CreditInfo;
  if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.REJECTED) {
    responseData.outOrderNo = creditInfo?.status?.outOrderNo || "";
    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.refuseCode = creditInfo?.status?.refuseCode;
    responseData.refuseMsg = creditInfo?.status?.refuseMsg;
    responseData.refuseMsgData = creditInfo?.status?.refuseMsgData;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (
    creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PENDING
  ) {
    responseData.outOrderNo = creditInfo?.status?.outOrderNo || "";
    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.applyTime = creditInfo?.status?.applyTime;
  } else if (creditInfo?.status?.applyStatus === CreditApplyStatusEnum.PASSED) {
    responseData.outOrderNo = creditInfo?.status?.outOrderNo || "";
    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.applyTime = creditInfo?.status?.applyTime;
    responseData.creditLimit = creditInfo?.status?.creditLimit;
    responseData.remainLimit = creditInfo?.status?.remainLimit;
    responseData.totalAvailableLimit = creditInfo?.status?.remainLimit;
    responseData.totalCreditLimit = creditInfo?.status?.creditLimit;

    creditInfo.status.productInfos.sort((a, b) => {
      return parseFloat(a.dayRate) - parseFloat(b.dayRate);
    });

    const minDayRate = Math.min(
      ...creditInfo.status.productInfos.map((item) => {
        return parseFloat(item.dayRate);
      }),
    );

    const minApr = Math.min(
      ...creditInfo.status.productInfos.map((item) => {
        return parseFloat(item.apr);
      }),
    );

    const minMonthRate = Math.min(
      ...creditInfo.status.productInfos.map((item) => {
        return parseFloat(item.monthRate);
      }),
    );

    responseData.dayRate = minDayRate.toString();
    responseData.apr = minApr.toString();
    responseData.monthRate = minMonthRate.toString();
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      ...creditInfo.status,
    },
  };

  return encryptRes(res);
}