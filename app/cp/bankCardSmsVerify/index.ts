import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import {
  BankCardBindEnum,
  BankCardInfo,
  CardBandStatusEnum,
} from "@/types/cp/bankCard";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 绑卡ID */
  bankCardId: string;
  /** 短信验证码 */
  smsCode: string;
  /** 是否重新签约标识(暂未启用) */
  resign?: boolean;
}

// 响应数据结构
export interface ResponseDataType {
  /**
   * 验证结果
   * 0-成功，
   * 1-预留手机号错误，
   * 2-身份证错误，
   * 3-姓名错误，
   * 4-四要素认证失败，
   * 5-重复绑定，
   * 6-暂不支持该卡，
   * 7-获取验证码超过限制次数，
   * 8-卡号错误，
   * 9-绑卡失败
   */
  verifyResult: number;
  /** 结果描述，失败必传 */
  verifyMsg?: string;
}

export async function bankCardSmsVerify(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const bankCardInfo = user.bankCardInfo as unknown as BankCardInfo;
  if (bankCardInfo.action.bandStatus === CardBandStatusEnum.PASSED) {
    const bankCard = bankCardInfo.status.bankCardList.find(
      (item) => item.bankCardId === params.bankCardId,
    );

    if (!bankCard) {
      responseData.verifyResult = 9;
      responseData.verifyMsg = "绑卡失败";
    } else {
      bankCard.status = BankCardBindEnum.BOUND;

      updateLoanUser(user);
      responseData.verifyResult = 0;
      responseData.verifyMsg = "成功";
    }
  } else {
    responseData.verifyResult = 1;
    responseData.verifyMsg = "预留手机号错误";
  }

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
