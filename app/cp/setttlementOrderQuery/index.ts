import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo, LoanOrderDetail, LoanStatusEnum } from "@/types/cp/loanInfo";
import dayjs from "dayjs";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 此次查询的荣耀侧订单号 */
  transNoList: string[];
}

export interface SettlementList {
  /** 外部平台交易流水号 */
  transNo: string;
  /** 是否可以开具 */
  allowCertificate: boolean;
  /** 不能开具原因码 */
  failReasonCode?: string;
  /** 不能开具原因，不可开具时必传 */
  failReason?: string;
  /** 可以开具时间yyyy-MM-dd HH:mm:ss */
  openTime?: string;
}

export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: SettlementList[];
}

export async function settlementOrderQuery(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);
  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }
  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 0,
      message: "success",
      data: { totalNum: 0, records: [] },
    });
  }
  // 只查结清订单
  const orders = loanInfo.status.loanOrders.filter(
    (order: LoanOrderDetail) =>
      order.status === LoanStatusEnum.CLEARED &&
      params.transNoList.includes(order.applyNo),
  );

  const records: SettlementList[] = orders.map((order) => {
    const allowCertificate = order.canOpenClearCertificate;
    let failReasonCode: string | undefined = undefined;
    let failReason: string | undefined = undefined;
    let openTime: string | undefined = undefined;
    if (!allowCertificate) {
      failReasonCode = order.notOpenClearCertificateCode || "NOT_ALLOWED";
      failReason = order.notOpenClearCertificateMsg || "不可开具结清证明";
      if (order.clearTime) {
        openTime = dayjs(order.clearTime).format("YYYY-MM-DD HH:mm:ss");
      }
    }
    return {
      transNo: order.applyNo,
      allowCertificate,
      failReasonCode,
      failReason,
      openTime,
    };
  });
  return encryptRes({
    code: 0,
    message: "success",
    data: { totalNum: records.length, records },
  });
}