import { encryptRes } from "@/utils/decrypt";
import { createLoanUser, getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { RepayMethodEnum } from "@/app/cp/creditInfoQuery";
import { CardBandStatusEnum } from "@/types/cp/bankCard";
import {
  IdCardVerifyResultEnum,
  LoanApplyStatusEnum,
  LoanErrorCodeEnum,
  LoanErrorMessageMap,
  LoanVerifyItemEnum
} from "@/types/cp/loanInfo";
import {
  CreditApplyStatusEnum,
  CreditRefuseCodeEnum,
  creditRefuseMsgMap,
  UseCancelStatusEnum
} from "@/types/cp/creditInfo";
import { BusinessTypeEnum, DefaultCheckedEnum, ForceReadEnum } from "@/types/cp/contractInfo";
import { getRequestContext } from "@/app/cp/route";

export interface ParamType {
  userId: string;
  realName: string;
  ctfCode: string;
  mobileNo: string;
}
export async function userBind(params: ParamType) {
  let user = await getLoanUserByUserId(params.userId);

  if (!user) {
    user = await createLoanUser({
      ...params,
      cp: "mock",
      creditInfo: {
        status: {
          applyStatus: 0,
          productInfos: [
            {
              apr: "18.25",
              monthRate: (parseFloat("18.25") / 12).toFixed(6) || "",
              dayRate: (parseFloat("18.25") / 365).toFixed(6) || "",
              repayMethod: RepayMethodEnum.EqualPrincipalAndInterest,
              earlyRepay: true,
              termNums: [3, 6, 12],
              isDynamicRate: false,
            },
          ],
          tempLimitInfos: [],
          userCancelStatus: UseCancelStatusEnum.NORMAL,
        },
        actions: {
          creditApplyStatus: CreditApplyStatusEnum.PASSED,
          refuseCode: CreditRefuseCodeEnum.APPLY_FAILED,
          refuseMsg: creditRefuseMsgMap.get(CreditRefuseCodeEnum.APPLY_FAILED),
          creditLimit: 2000000,
          autoCallback: true,
          callBackDelay: 1,
        },
      },
      bankCardInfo: {
        status: {
          bankCardList: [],
        },
        action: {
          bandStatus: CardBandStatusEnum.PASSED,
        },
      },
      loanInfo: {
        status: {
          loanApplyRecords: [],
          loanOrders: [],
          repayRecords: [],
        },
        action: {
          loanApplyStatus: LoanApplyStatusEnum.SUCCESS,
          refuseCode: LoanErrorCodeEnum.LOAN_REJECTED,
          refuseMsg: LoanErrorMessageMap[LoanErrorCodeEnum.LOAN_REJECTED],
          verifyList: [LoanVerifyItemEnum.AGREEMENT_LOAN],
          autoCallback: true,
          callBackDelay: 1,
          idCardFrontResult: IdCardVerifyResultEnum.SUCCESS,
          idCardBackResult: IdCardVerifyResultEnum.SUCCESS,
        },
      },
      couponInfo: {
        status: {
          couponList: [],
        },
      },
      clearCertificateInfo: {
        action: {
          canOpenClearCertificate: true,
          failCode: 1,
          failReason: "系统异常",
        },
      },
      contractsInfo: {
        status: {
          contractList: [
            {
              id: crypto.randomUUID(),
              businessType: BusinessTypeEnum.CREDIT,
              defaultChecked: DefaultCheckedEnum.UNCHECKED,
              forceRead: ForceReadEnum.FORCE_READ,
              contractUrl:
                "https://loancontract.ppdai.com/latest/agency/expandSearchZhengxin.html",
              isRead: false,
              contractName: "mock协议",
            },
            {
              id: crypto.randomUUID(),
              businessType: BusinessTypeEnum.LOAN,
              defaultChecked: DefaultCheckedEnum.UNCHECKED,
              forceRead: ForceReadEnum.FORCE_READ,
              contractUrl:
                "https://loancontract.ppdai.com/latest/agency/expandSearchZhengxin.html",
              isRead: false,
              contractName: "mock协议",
            },
            {
              id: crypto.randomUUID(),
              businessType: BusinessTypeEnum.BIND_CARD,
              defaultChecked: DefaultCheckedEnum.UNCHECKED,
              forceRead: ForceReadEnum.FORCE_READ,
              contractUrl:
                "https://loancontract.ppdai.com/latest/agency/expandSearchZhengxin.html",
              isRead: false,
              contractName: "mock协议",
            },
            {
              id: crypto.randomUUID(),
              businessType: BusinessTypeEnum.REOFFER,
              defaultChecked: DefaultCheckedEnum.UNCHECKED,
              forceRead: ForceReadEnum.FORCE_READ,
              contractUrl:
                "https://loancontract.ppdai.com/latest/agency/expandSearchZhengxin.html",
              isRead: false,
              contractName: "mock协议",
            },
          ],
        },
      },
      logoffInfo: {
        status: {
          isLogoff: false,
        },
        action: {
          logoffCheckResult: true,
          logoffCheckFailReason: "",
          logoffResult: true,
          logoffFailReason: "",
        },
      },
      createTime: BigInt(Date.now()),
    });
  }

  const requestContext = getRequestContext();
  if (requestContext) {
    requestContext.userId = user?.id || 0;
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      // 准入状态，1-准入成功，0-准入失败
      access: 1,
      openId: params.userId,
    },
  };

  return encryptRes(res);
}
