import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import { LoanApplyStatusEnum, LoanInfo } from "@/types/cp/loanInfo";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;

  /** 荣耀侧借款申请订单号 */
  applyNo: string;

  /** 借款金额，单位：分 */
  loanAmount: number;

  /**
   * 还款方式
   * 1-等额本息(灵活还)
   * 2-等额本金(灵活还)
   * 3-先息后本(灵活还)
   * 4-等额本息(按期还)
   */
  repayMethod: number;

  /** 借款期数 */
  totalTerm: number;

  /** 绑卡Id */
  bankCardId: string;

  /**
   * 借款用途
   * RCXF - 个人日常消费
   * ZX - 房屋装修
   * LY - 旅游出行
   * JX - 在职深造
   * JKYL - 健康医疗
   * Others - 其他消费
   */
  loanUse: string;

  /** 优惠券id（可选） */
  couponNo?: string;

  /** 联合建模模型分（Map<key,value>的json字符串，可选） */
  apiModelScoreMap?: string;

  /** 用户标签（Map<key,value>的json字符串，可选） */
  apiUserTagMap?: string;

  /**
   * 0 -未签署 ： 返回值verifyList需要下发协议项
   * 1 -已签署： 返回值verifyList不需要下发协议项
   * （可选）
   */
  sign?: number;
}

// 响应数据结构
export interface ResponseDataType {
  /** 借款申请订单号 */
  applyNo: string;

  /**
   * 状态
   * 1-等待（此状态需使用同applyNo继续调用交易鉴权）
   * 4-拒绝
   * 6-需鉴权（完成鉴权后会调用提交借款）
   */
  status: number;

  /** 拒绝原因码，status=4时必传 */
  refuseCode?: string;

  /** 拒绝原因说明，status=4时必传 */
  refuseMsg?: string;

  /** 拒绝具体原因说明，status=4时必传 */
  refuseMsgData?: string;

  /** 拒绝管控期，单位：天 */
  refuseControlDays?: number;

  /** 需验证列表，参照1.4.4借款交易鉴权项，status=6时必传 */
  verifyList?: string[];
}

export async function loanVerify(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  responseData.status = 6;
  responseData.verifyList = loanInfo.action.verifyList;

  // 创建订单申请记录
  loanInfo.status?.loanApplyRecords?.push({
    userId: params.userId,
    applyNo: params.applyNo,
    applyStatus: LoanApplyStatusEnum.INIT,
    loanAmount: params.loanAmount,
    repayMethod: params.repayMethod,
    totalTerm: params.totalTerm,
    bankCardId: params.bankCardId,
    loanUse: params.loanUse,
    couponNo: params.couponNo,
    apiModelScoreMap: params.apiModelScoreMap,
    apiUserTagMap: params.apiUserTagMap,
    applyTime: Date.now(),
  });

  await updateLoanUser(user);

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
