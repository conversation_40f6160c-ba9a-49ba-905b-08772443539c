import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { BusinessTypeEnum, ContractInfo } from "@/types/cp/contractInfo";

export interface ContractDetailParams {
  /** 荣耀方用户id */
  userId: string;
  /** 协议类型，1-URL（废弃），2-html，3-URL(支持返回多份协议)，4-pdf */
  type: number;
  /** 协议Id，传该字段返回单条协议信息 */
  contractId?: string;
  /** 渠道方借款订单号，传该字段返回已签署的用信协议 */
  outOrderNo?: string;
}

export interface ContractDetailDto {
  /** 合同名称 */
  contractName: string;
  /** 协议内容（协议地址） */
  contractContent: string;
  /** 类型，1-URL，2-html，3-pdf */
  textType: number;
}

export interface ContractDetailResponse {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: ContractDetailDto[];
}

export async function contractDetail(params: ContractDetailParams) {
  try {
    const user = await getLoanUserByUserId(params.userId);

    if (!user) {
      console.log("用户不存在");

      return encryptRes({
        code: 1,
        message: "用户不存在",
        data: null,
      });
    }

    const contractInfo = user.contractsInfo as unknown as ContractInfo;

    const contracts = contractInfo?.status?.contractList;

    let targetContracts;
    if (!contracts) {
      targetContracts = [];
    } else {
      targetContracts = contracts.filter(
        (contract) =>
          contract.businessType === BusinessTypeEnum.CREDIT && contract.isRead,
      );
      targetContracts.forEach((contract) => {
        delete contract.isRead;
      });
    }

    const res = {
      code: 0,
      message: "success",
      data: {
        totalNum: targetContracts.length,
        records: targetContracts,
      } as ContractDetailResponse,
    };

    return encryptRes(res);
  } catch (error) {
    console.error("获取已签协议详情失败:", error);
    return encryptRes({
      code: 1,
      message: "获取已签协议详情失败",
      data: null,
    });
  }
}