import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { BankCardInfo } from "@/types/cp/bankCard";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 借款订单号 (还款的时候可以传借款订单号，看三方是否需要） */
  outOrderNo?: string;
}

// 响应数据结构
export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number; // 必须

  /** 记录列表 */
  records: BankCardDto[]; // 必须
}

// 银行卡信息 DTO
export interface BankCardDto {
  /** 绑卡Id */
  bankCardId: string; // 必须

  /** 银行卡号（卡号后四位） */
  bankCardNo: string; // 必须

  /** 银行名称 */
  bankName: string; // 必须

  /** 银行代码 */
  bankCode?: string; // 可选

  /** 卡类型，1-借记卡，2-信用卡 */
  cardType?: number; // 可选

  /** 单日限额，单位：分 */
  dayLimit?: number; // 可选

  /** 单笔限额，单位：分 */
  singleLimit?: number; // 可选
}

export async function bankCardList(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const bankCardInfo = user.bankCardInfo as unknown as BankCardInfo;
  responseData.records = bankCardInfo.status.bankCardList;
  responseData.totalNum = bankCardInfo.status.bankCardList.length;

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
