import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import { BankCardInfo } from "@/types/cp/bankCard";
import {
  LoanApplyStatusEnum,
  LoanErrorCodeEnum,
  LoanErrorMessageMap,
  LoanInfo,
  LoanOrderDetail,
  LoanRecordDetail,
  LoanStatusEnum,
  RepayErrorCodeEnum,
  RepayPlanTermDto,
  RepayStatusEnum,
} from "@/types/cp/loanInfo";
import { CreditInfo, RepayMethodEnum } from "@/types/cp/creditInfo";
import dayjs from "dayjs";
import {
  notifyLoanApply,
  notifyLoanApplyParamType,
} from "@/actions/cp/notify/loanApplyNotify";
import { LoanUser } from "@/types/cp/loanUser";
import {
  Coupon,
  CouponInfo,
  PresetCouponTypeEnum,
} from "@/types/cp/couponInfo";
import { CouponStatusEnum } from "@/app/cp/couponList";

interface ParamType {
  /** 荣耀方用户id */
  userId: string;

  /** 荣耀侧借款申请订单号 */
  applyNo: string;

  /** 联系人信息，参照1.4.2联系人信息 */
  relationInfos?: RelationInfo[]; // O，交易鉴权返回BASICINFO_FILL或BASICINFO_FILL2必须

  /** 居住地 */
  residentialAddr?: string; // O，交易鉴权返回RESIDENTIAL_ADDR必须

  /** 家庭地址 */
  familyAddr?: string; // O，交易鉴权返回FAMILY_ADDR必须

  /** 教育程度 */
  education?: "1" | "2" | "3" | "4" | "5" | "6"; // O，交易鉴权返回EDUCATION必须
  // 1-研究生或以上 2-本科 3-大专 4-高中 5-中专 6-初中及以下

  /** 工作单位 */
  company?: string; // O，交易鉴权返回COMPANY必须

  /** 职业 */
  career?:
    | "01" // 党政机关/事业单位
    | "02" // 企业职员
    | "03" // 工人/服务人员
    | "04" // 生意人/个体户
    | "05" // 学生
    | "06" // 自由职业
    | "07" // 其他
    | "08" // 律师/会计师等专业技术人员
    | "09" // 金融行业从业人员
    | "10"; // 农/林/牧/渔从业人员 // O，交易鉴权返回CAREER_INCOME必须

  /** 收入 */
  income?:
    | "01" // 无稳定收入
    | "02" // 3千以下
    | "03" // 3千-5千
    | "04" // 5千-1万
    | "05" // 1万-2万
    | "06" // 2万-3万
    | "07"; // 3万以上 // O，交易鉴权返回CAREER_INCOME必须

  /** 婚姻状态 */
  marriage?: "1" | "2" | "3" | "4" | "5"; // O，交易鉴权返回MARRIAGE必须
  // 1-未婚 2-已婚 3-其他 4-离异 5-丧偶

  /** 文件信息，参照1.4.1文件信息 */
  fileInfos?: FileInfo[]; // O，交易鉴权返回FACE_CHECK或ID_CARD_OCR必须

  /** 联合建模模型分 */
  apiModelScore?: ApiModelScoreParam; // O

  /** 联合建模模型分（Map<key,value>的json字符串） */
  apiModelScoreMap?: string; // O

  /** 用户标签（Map<key,value>的json字符串） */
  apiUserTagMap?: string; // O
}

interface RelationInfo {
  /** 联系人关系 */
  relation: string;

  /** 联系人姓名 */
  name: string;

  /** 联系人手机号 */
  mobileNo: string;
}
interface FileInfo {
  /**
   * 数据类型
   * CARD_FRONT_PHOTO：身份证正面照片
   * CARD_BACK_PHOTO：身份证反面照片
   * FRONT_PHOTO：活体照片
   * FRONT_PHOTO_VIDEO：视频活体照片
   * FRONT_VIDEO：活体视频
   */
  type:
    | "CARD_FRONT_PHOTO"
    | "CARD_BACK_PHOTO"
    | "FRONT_PHOTO"
    | "FRONT_PHOTO_VIDEO"
    | "FRONT_VIDEO";

  /**
   * 具体数据
   * 照片、视频采用base64编码或url【base64编码前大小不能超过5M】
   */
  value: string;

  /** 数据标识 */
  identifier?: string;
}

interface ApiModelScoreParam {
  /** 风险分 */
  riskScore: string;

  /** 需求分 */
  demandScore: string;
}

// 响应数据结构
export interface ResponseDataType {
  /** 渠道方借款申请流水号（创建返回订单成功则订单号） */
  outOrderNo?: string;

  /** 拒绝原因码 0-表示申请提交成功 */
  refuseCode: string;

  /** 拒绝原因说明（失败必传） */
  refuseMsg?: string;

  /** 拒绝原因说明（失败必传） */
  refuseMsgData?: string;
}
const loanApplyCallBack = async (
  loanUser: LoanUser,
  loanApplyRecord: LoanRecordDetail,
  loanOrder: LoanOrderDetail,
) => {
  let loanInfoParam;

  if (loanApplyRecord.applyStatus === LoanApplyStatusEnum.SUCCESS) {
    const bindCard = loanUser.bankCardInfo.status.bankCardList.find((item) => {
      return item.bankCardNo === loanOrder.bindCardNo;
    });

    loanInfoParam = {
      repayMethod: loanOrder.repayMethod,
      loanAmount: loanOrder.loanAmount,
      effectiveDate: loanOrder.effectiveDate || "",
      dayRate: loanOrder.dayRate,
      monthRate: loanOrder.monthRate,
      apr: loanOrder.apr,
      totalTerm: loanOrder.totalTerm,
      bankCardId: bindCard?.bankCardId || "",
    };
  }

  const notifyLoanApplyParam: notifyLoanApplyParamType = {
    userId: loanApplyRecord.userId,
    applyNo: loanApplyRecord.applyNo,
    outOrderNo: loanApplyRecord.outOrderNo || "",
    applyStatus: loanApplyRecord.applyStatus,
    refuseMsg:
      loanApplyRecord.applyStatus === LoanApplyStatusEnum.FAILED
        ? loanUser.loanInfo.action.refuseMsg
        : undefined,
    refuseMsgData:
      loanApplyRecord.applyStatus === LoanApplyStatusEnum.FAILED
        ? loanUser.loanInfo.action.refuseMsg
        : undefined,
    applyTime: loanApplyRecord.applyTime || 0,

    loanInfo: loanInfoParam,
  };
  await notifyLoanApply(loanUser.id || 0, notifyLoanApplyParam, "development");
};

export async function loanApply(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const applyNo = params.applyNo;
  const loanInfo = user.loanInfo as unknown as LoanInfo;

  const loanApplyRecord = loanInfo.status?.loanApplyRecords?.find((item) => {
    return item.applyNo === applyNo;
  });

  if (!loanApplyRecord) {
    console.log("订单不存在");

    return encryptRes({
      code: 1,
      message: "申请订单不存在",
      data: null,
    });
  }
  const bindCardInfo = user.bankCardInfo as unknown as BankCardInfo;

  const bankCard = bindCardInfo.status?.bankCardList?.find((item) => {
    return item.bankCardId === loanApplyRecord?.bankCardId;
  });

  if (!bankCard) {
    console.log("绑卡信息不存在");

    return encryptRes({
      code: 1,
      message: "绑卡信息不存在",
      data: null,
    });
  }

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  const productInfo = creditInfo.status.productInfos.find((productInfo) => {
    return productInfo.repayMethod === loanApplyRecord.repayMethod;
  });

  if (!productInfo) {
    console.log("还款方式不支持");

    return encryptRes({
      code: 1,
      message: "还款方式不支持",
      data: null,
    });
  }

  let code = 0;
  let message = "success";

  // 借款失败
  if (loanInfo.action?.loanApplyStatus === LoanApplyStatusEnum.FAILED) {
    loanApplyRecord.applyStatus = LoanApplyStatusEnum.FAILED;

    if (loanInfo.action.refuseCode === LoanErrorCodeEnum.ORDER_EXPIRED) {
      code = parseInt(LoanErrorCodeEnum.ORDER_EXPIRED);
      message = LoanErrorMessageMap[LoanErrorCodeEnum.ORDER_EXPIRED];
    } else {
      responseData.refuseCode = loanInfo.action.refuseCode || "1";
      responseData.refuseMsg = loanInfo.action.refuseMsg;
      responseData.refuseMsgData = loanInfo.action.refuseMsg;

      loanApplyRecord.refuseCode = loanInfo.action.refuseCode;
      loanApplyRecord.refuseMsg = loanInfo.action.refuseMsg;
      loanApplyRecord.refuseMsgData = loanInfo.action.refuseMsg;
    }
  }

  let loanOrder: LoanOrderDetail | null = null;

  // 借款成功或审核中
  if (
    loanInfo.action?.loanApplyStatus === LoanApplyStatusEnum.SUCCESS ||
    loanInfo.action?.loanApplyStatus === LoanApplyStatusEnum.PENDING
  ) {
    // 如果使用了优惠券，则优惠券状态设置为已使用
    const couponInfo = user.couponInfo as unknown as CouponInfo;
    let activeCoupon: Coupon | undefined;
    if (loanApplyRecord.couponNo) {
      activeCoupon = couponInfo.status?.couponList?.find(
        (item: Coupon) => item.couponNo === loanApplyRecord.couponNo,
      );
      if (activeCoupon) {
        activeCoupon.status = CouponStatusEnum.USED;
        activeCoupon.usedTime = Date.now();
      }
    }

    const tempLimitInfos = creditInfo.status.tempLimitInfos || [];
    let remainingLoanAmount = loanApplyRecord.loanAmount;

    // 先扣临时额度
    for (let i = 0; i < tempLimitInfos.length; i++) {
      const tempLimitInfo = tempLimitInfos[i];
      if (tempLimitInfo.tempLimitValidTime) {
        const tempLimitValidTime = dayjs(tempLimitInfo.tempLimitValidTime);
        if (tempLimitValidTime.isAfter(dayjs())) {
          const tempLimitToDeduct = Math.min(
            tempLimitInfo.tempAvailableLimit || 0,
            remainingLoanAmount,
          );
          tempLimitInfo.tempAvailableLimit =
            (tempLimitInfo.tempAvailableLimit || 0) - tempLimitToDeduct;
          remainingLoanAmount -= tempLimitToDeduct;
        }
      }
    }

    // 如果临时额度不够,再扣固定额度
    if (remainingLoanAmount > 0) {
      creditInfo.status.remainLimit -= remainingLoanAmount;
    }

    loanApplyRecord.applyStatus = loanInfo.action?.loanApplyStatus;
    loanOrder = {
      userId: params.userId,
      applyNo: params.applyNo,
      outOrderNo: crypto.randomUUID(),
      applyDate: dayjs(Date.now()).format("YYYYMMDD"),
      applyTime: Date.now(),
      effectiveDate: dayjs(Date.now()).format("YYYYMMDD"),
      loanAmount: loanApplyRecord.loanAmount,
      status:
        loanApplyRecord.applyStatus === LoanApplyStatusEnum.SUCCESS
          ? LoanStatusEnum.REPAYING
          : LoanStatusEnum.APPLYING,
      totalTerm: loanApplyRecord.totalTerm,
      repayMethod: loanApplyRecord.repayMethod,
      clearTime: 0,
      paidAmount: 0,
      paidPrinAmount: 0,
      paidInterAmount: 0,
      paidServiceFee: 0,
      paidPenalty: 0,
      bindCardNo: bankCard.bankCardNo,
      bindBankCode: bankCard.bankCode || "",
      bindBankName: bankCard.bankName,
      dayRate: productInfo?.dayRate!,
      monthRate: productInfo?.monthRate!,
      apr: productInfo?.apr!,
      reductionAmount:
        activeCoupon?.presetCouponType ===
        PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE
          ? Math.floor(
              ((loanApplyRecord.loanAmount *
                parseFloat(productInfo?.dayRate!)) /
                100) *
                7,
            )
          : 0,
      prePenalty: 0,
      createTime: Date.now(),
      repayResultStatus: RepayStatusEnum.Success,
      autoCallback: true,
      callBackDelay: 1,
      refuseCode: RepayErrorCodeEnum.SYSTEM_ERROR,
      canOpenClearCertificate: true,
      repayQueryResult: RepayStatusEnum.Success,
    };

    if (
      productInfo.repayMethod === RepayMethodEnum.EqualPrincipalAndInterest ||
      productInfo.repayMethod === RepayMethodEnum.FixedTerm
    ) {
      const monthlyRate = Number(productInfo.monthRate) / 100;
      const totalMonths = loanOrder.totalTerm;
      const principal = loanOrder.loanAmount;

      const monthlyPayment =
        (principal * monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) /
        (Math.pow(1 + monthlyRate, totalMonths) - 1);

      const repayPlanTerms: RepayPlanTermDto[] = [];
      let remainingPrincipal = principal;

      for (let i = 0; i < totalMonths; i++) {
        const interest = remainingPrincipal * monthlyRate;
        const principalPayment = monthlyPayment - interest;
        remainingPrincipal -= principalPayment;

        const termAmount = Math.round(monthlyPayment);
        const termPrincipal = Math.round(principalPayment);
        const termInterest = Math.round(interest);

        repayPlanTerms.push({
          termNo: i + 1,
          shouldRepayDate: dayjs()
            .add(i + 1, "month")
            .format("YYYYMMDD"),
          termStatus: 2,

          // 总应还
          termAmount,
          termPrincipal,
          termInterest,
          termPenalty: 0,
          termPrinPenalty: 0,
          termInterPenalty: 0,
          termOverdueFee: 0,
          termServiceFee: 0,
          termViolateFee: 0,

          // 已还
          paidTime: 0,
          paidTermAmount: 0,
          paidTermPrincipal: 0,
          paidTermInterest: 0,
          paidTermReductionAmount: 0,
          paidTermPenalty: 0,
          paidTermPrinPenalty: 0,
          paidTermInterPenalty: 0,
          paidTermOverdueFee: 0,
          paidTermServiceFee: 0,
          paidTermViolateFee: 0,

          // 剩余应还
          payableTermAmount: termAmount,
          payableTermPrincipal: termPrincipal,
          payableTermInterest: termInterest,
          payableTermPenalty: 0,
          payableTermPrinPenalty: 0,
          payableTermInterPenalty: 0,
          payableTermOverdueFee: 0,
          payableTermServiceFee: 0,
          payableTermViolateFee: 0,

          termReductionAmount:
            activeCoupon?.presetCouponType ===
              PresetCouponTypeEnum.SEVEN_DAY_INTEREST_FREE && i === 0
              ? Math.floor(
                  ((loanOrder!.loanAmount * parseFloat(productInfo?.dayRate!)) /
                    100) *
                    7,
                )
              : 0,
          overdue: false,
        });
      }

      loanOrder.repayPlanTerms = repayPlanTerms;
      loanOrder.needResign = false;
      loanOrder.supportRepayType = [1, 2, 3, 4, 5];
      loanInfo.status.loanOrders.push(loanOrder);
      loanApplyRecord.outOrderNo = loanOrder.outOrderNo;

      responseData.outOrderNo = loanOrder.outOrderNo;
    }
  }

  if (
    loanInfo.action?.loanApplyStatus !== LoanApplyStatusEnum.PENDING &&
    loanInfo.action?.autoCallback
  ) {
    setTimeout(async () => {
      await loanApplyCallBack(user, loanApplyRecord, loanOrder);
    }, loanInfo.action?.callBackDelay * 1000);
  }

  await updateLoanUser(user);

  const res = {
    code,
    message,
    data: responseData,
  };

  return encryptRes(res);
}
