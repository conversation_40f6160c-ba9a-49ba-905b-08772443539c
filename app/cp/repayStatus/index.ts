import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { ParamType, ResponseDataType } from "./types";
import { LoanInfo, RepayStatusEnum } from "@/types/cp/loanInfo";

export async function repayStatus(params: ParamType) {
  // 验证用户是否存在
  const user = await getLoanUserByUserId(params.userId);
  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  // 查询还款记录
  const loanInfo = user.loanInfo as unknown as LoanInfo;
  const repayRecord = loanInfo.status?.repayRecords?.find(
    (item) => item.repayNo === params.repayNo,
  );

  if (!repayRecord) {
    return encryptRes({
      code: 204101,
      message: "还款记录不存在",
      data: null,
    });
  }

  // 构建响应数据
  const responseData: ResponseDataType = {
    repayNo: repayRecord.repayNo,
    outRepayNo: repayRecord.outRepayNo || "",
    repayStatus: repayRecord.repayStatus,
  };

  // 如果还款失败，添加失败原因
  if (repayRecord.repayStatus === RepayStatusEnum.Failed) {
    responseData.repayResult = repayRecord.repayResult;
  }

  // 如果还款成功，添加成功相关信息
  if (repayRecord.repayStatus === RepayStatusEnum.Success) {
    responseData.repayAmount = repayRecord.repayAmount;
    responseData.reductionAmount = repayRecord.reductionAmount || 0;
    responseData.repayTime = repayRecord.repayTime;

    responseData.repayTerms = repayRecord.repayTerms;
  }

  return encryptRes({
    code: 0,
    message: "success",
    data: responseData,
  });
}