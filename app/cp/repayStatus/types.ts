export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 荣耀侧还款交易订单号 */
  repayNo: string;
}

export interface ResponseDataType {
  /** 荣耀侧还款交易流水号 */
  repayNo: string;
  /** 渠道方还款交易订单号 */
  outRepayNo: string;
  /** 还款状态，1-还款中，2-还款成功，4-还款失败 */
  repayStatus: number;
  /** 返回失败原因，失败时必传 */
  repayResult?: string;
  /** 还款金额,单位：分，还款成功必传 */
  repayAmount?: number;
  /** 优惠券减免金额，单位：分，还款成功有优惠时必传 */
  reductionAmount?: number;
  /** 实际还款时间，毫秒，还款成功必传 */
  repayTime?: number;
  /** 还款期次明细，还款成功必传 */
  repayTerms?: RepayTermDto[];
}

export interface RepayTermDto {
  /** 期次 */
  termNo: number;
  /** 本期还款总额,单位：分 */
  termAmount: number;
  /** 本期还款本金，单位：分 */
  termPrincipal: number;
  /** 本期还款利息，单位：分 */
  termInterest: number;
  /** 本期还款优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期还款罚息 */
  termPenalty: number;
  /** 本期还款本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期还款利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期还款逾期费 */
  termOverdueFee: number;
  /** 本期还款违约金 */
  termViolateFee: number;
  /** 本期还款服务费 */
  termServiceFee: number;
  /** 逾期天数 */
  overdueDays?: number;
}