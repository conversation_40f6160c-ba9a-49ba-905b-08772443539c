import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { encryptRes } from "@/utils/decrypt";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 验证码序列号，发送成功后返回 */
  serialNo: string;
  /** 短信验证码 */
  code: string;
  /** 手机号，用户绑定时必须，其他场景使用用户绑定时的手机号 */
  mobileNo: string;
  /** 用户真实ip */
  ip?: string;
  /** 0-借款，1-还款 */
  type?: number;
}

export interface ResponseDataType {
  /** 验证结果 */
  success: boolean;
  /** 错误码 */
  errorCode?: string;
  /** 错误信息 */
  errorMsg?: string;
}
// 验证码错误码
const ERROR_CODES = {
  CODE_ERROR: "203201",
  CODE_EXPIRED: "203202",
  CODE_FREQUENT: "203203",
};

export async function smsVerify(params: ParamType) {
  // 1. 获取用户信息
  const user = await getLoanUserByUserId(params.userId);
  if (!user) {
    return {
      success: false,
      errorCode: "203201",
      errorMsg: "用户不存在",
    };
  }

  // 8. 返回成功响应
  return encryptRes({
    code: 0,
    message: "success",
    data: { success: true },
  });
}