import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { CouponInfo } from "@/types/cp/couponInfo";

// 优惠券类型枚举
export enum CouponTypeEnum {
  LOAN = 1, // 放款券
  REPAY = 2, // 还款券
}

// 优惠券状态枚举
export enum CouponStatusEnum {
  UNUSED = 1, // 未使用
  USED = 2, // 已使用
  EXPIRED = 3, // 已过期
}

// 优惠券DTO
export interface CouponDto {
  couponNo: string; // 优惠券id
  couponType: CouponTypeEnum; // 券类型
  status: CouponStatusEnum; // 券状态
  startTime: number; // 券有效期开始时间
  endTime: number; // 券有效期结束时间
  discountAmount?: number; // 优惠金额，单位分
  couponLabel?: string; // 卡券标签
  couponName: string; // 优惠券名称
  useRules: string[]; // 使用规则列表
  sendTime: number; // 优惠券发放时间
  usedTime?: number; // 优惠券使用时间
}

// 响应结果类型
export interface CouponListResponse {
  totalNum: number;
  records: CouponDto[];
}

export interface ParamType {
  userId: string;
}

export async function couponList(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const couponInfo = user.couponInfo as unknown as CouponInfo;
  const couponList = couponInfo.status?.couponList || [];

  const allCouponDtos: CouponDto[] = couponList.map((coupon) => ({
    couponNo: coupon.couponNo!,
    couponType: coupon.couponType!,
    status: coupon.status!,
    startTime: coupon.startTime!,
    endTime: coupon.endTime!,
    discountAmount: coupon.discountAmount,
    couponName: coupon.couponName!,
    useRules: coupon.useRules!.map((rule, index) => {
      const { field, operator, value } = rule;
      let ruleText = "";

      switch (field) {
        case "loanAmount":
          const amount = parseInt(value) / 100; // 转换为元
          switch (operator) {
            case "gt":
              ruleText = `借款金额需大于${amount}元`;
              break;
            case "gte":
              ruleText = `借款金额需大于等于${amount}元`;
              break;
            case "lt":
              ruleText = `借款金额需小于${amount}元`;
              break;
            case "lte":
              ruleText = `借款金额需小于等于${amount}元`;
              break;
            case "eq":
              ruleText = `借款金额需等于${amount}元`;
              break;
          }
          break;
        case "termNums":
          switch (operator) {
            case "gt":
              ruleText = `借款期数需大于${value}期`;
              break;
            case "gte":
              ruleText = `借款期数需大于等于${value}期`;
              break;
            case "lt":
              ruleText = `借款期数需小于${value}期`;
              break;
            case "lte":
              ruleText = `借款期数需小于等于${value}期`;
              break;
            case "eq":
              ruleText = `借款期数需等于${value}期`;
              break;
          }
          break;
        case "repayMethod":
          const methodMap: Record<number, string> = {
            1: "等额本息(灵活还)",
            2: "等额本金(灵活还)",
            3: "先息后本(灵活还)",
            4: "等额本息(按期还)",
          };
          ruleText = `还款方式需为${methodMap[parseInt(value)]}`;
          break;
      }
      return `(${index + 1})${ruleText}`;
    }),
    receiveTime: coupon.receiveTime!,
    sendTime: coupon.receiveTime!,
    usedTime: coupon.usedTime,
  }));

  const res = {
    code: 0,
    message: "success",
    data: {
      totalNum: allCouponDtos.length,
      records: allCouponDtos,
    } as CouponListResponse,
  };

  return encryptRes(res);
}