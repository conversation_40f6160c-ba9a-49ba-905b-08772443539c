import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";

export interface ParamType {
  /** 荣耀方用户id，必填 */
  userId: string;
  /** 借款订单号，可选 */
  outOrderNo?: string;
}

export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: BankDto[];
}

export interface BankDto {
  /** 银行名称 */
  bankName: string;
  /** 银行代码 */
  bankCode: string;
  /** 卡类型，1-借记卡，2-信用卡 */
  cardType?: number;
  /** 单日限额，单位：分 */
  dayLimit?: number;
  /** 单笔限额，单位：分 */
  singleLimit?: number;
}

export async function bankcardSupportList(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const bankList = [
    {
      bankName: "工商银行",
      bankCode: "icbc",
      cardType: 1,
      dayLimit: 2000000,
      singleLimit: 2000000,
    },
    {
      bankName: "建设银行",
      bankCode: "ccb",
      cardType: 1,
      dayLimit: 2000000,
      singleLimit: 2000000,
    },
    {
      bankName: "农业银行",
      bankCode: "abc",
      cardType: 1,
      dayLimit: 2000000,
      singleLimit: 2000000,
    },
    {
      bankName: "中国银行",
      bankCode: "boc",
      cardType: 1,
      dayLimit: 2000000,
      singleLimit: 2000000,
    },
  ];

  responseData.records = bankList;
  responseData.totalNum = bankList.length;

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}