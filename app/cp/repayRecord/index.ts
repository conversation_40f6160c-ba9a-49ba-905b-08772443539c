import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo } from "@/types/cp/loanInfo";

export interface ParamType {
  /** 荣耀方用户id，必填 */
  userId: string;
  /** 渠道方借款订单号，必填 */
  outOrderNo: string;
}

export interface RepayRecordDto {
  /** 渠道方还款交易订单号 */
  outRepayNo: string;
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 还款金额，单位：分 */
  repayAmount: number;
  /** 实际还款时间，毫秒(还款中为还款申请提交的时间，还款失败和成功为实际还款时间） */
  repayTime: number;
  /** 还款状态，1-还款中，2-还款成功，3-部分还款成功，4-还款失败 */
  repayStatus: number;
  /** 返回失败原因 */
  repayResult?: string;
}

export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: RepayRecordDto[];
}

export async function repayRecord(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {
    totalNum: 0,
    records: [],
  };

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  const repayRecords = loanInfo.status?.repayRecords || [];
  responseData.records = repayRecords
    .filter((item) => item.outOrderNo === params.outOrderNo)
    .map((item) => ({
      outRepayNo: item.outRepayNo || "",
      outOrderNo: item.outOrderNo,
      repayAmount: item.repayAmount || 0,
      repayTime: item.repayTime || 0,
      repayStatus: item.repayStatus,
      repayResult: item.repayResult,
    }));
  responseData.totalNum = responseData.records.length;

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}