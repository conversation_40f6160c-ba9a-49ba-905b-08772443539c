import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId, updateLoanUser } from "@/actions/cp/loanUserActions";
import { CreditApplyStatusEnum, CreditInfo, CreditStatusEnum } from "@/types/cp/creditInfo";
import { LoanUser } from "@/types/cp/loanUser";
import { notifyCreditApply, notifyCreditApplyParamType } from "@/actions/cp/notify/creditApplyNotify";

interface ParamType {
  userId: string;
  applyNo: string;
}

interface ResponseDataType {
  applyStatus: number;
  refuseCode?: string;
  refuseMsg?: string;
  refuseMsgData?: string;
  outOrderNo?: string;
  remainLimit?: number;
  apr?: string;
  dayRate?: string;
}
const creditApplyCallBack = async (loanUser: LoanUser) => {
  if (!loanUser?.creditInfo?.status?.productInfos) {
    return;
  }

  const minDayRate = Math.min(
    ...loanUser?.creditInfo?.status?.productInfos.map((item) => {
      return parseFloat(item.dayRate);
    }),
  );

  const minApr = Math.min(
    ...loanUser?.creditInfo?.status?.productInfos.map((item) => {
      return parseFloat(item.apr);
    }),
  );

  const minMonthRate = Math.min(
    ...loanUser?.creditInfo?.status?.productInfos.map((item) => {
      return parseFloat(item.monthRate);
    }),
  );

  const notifyCreditApplyParam: notifyCreditApplyParamType = {
    userId: loanUser?.userId || "",
    applyNo: loanUser?.creditInfo?.status?.applyNo || "",
    outOrderNo: loanUser?.creditInfo?.status?.outOrderNo || "",
    applyStatus: loanUser?.creditInfo?.status?.applyStatus || 0,
    refuseCode: loanUser?.creditInfo?.status?.refuseCode,
    refuseMsg: loanUser?.creditInfo?.status?.refuseMsg,
    refuseMsgData: loanUser?.creditInfo?.status?.refuseMsg,
    // refuseControlTime: loanUser?.creditInfo?.actions?.refuseControlTime,
    identity: 2,
    creditLimit: loanUser?.creditInfo?.status?.creditLimit || 0,
    // originCreditLimit: loanUser?.creditInfo?.actions?.originCreditLimit,
    remainLimit: loanUser?.creditInfo?.status?.remainLimit || 0,
    // limitExpireDate: loanUser?.creditInfo?.actions?.limitExpireDate,
    // greyExpireTime: loanUser?.creditInfo?.actions?.greyExpireTime,
    totalAvailableLimit: loanUser?.creditInfo?.status?.remainLimit || 0,
    totalCreditLimit: loanUser?.creditInfo?.status?.creditLimit || 0,

    dayRate: minDayRate.toString() || "",
    monthRate: minMonthRate.toString() || "",
    apr: minApr.toString() || "",
    maxLoan: loanUser?.creditInfo?.status?.remainLimit || 0,
    minLoan: 50000,
    // tempLimitInfo: loanUser?.creditInfo?.actions?.tempLimitInfo,
    applyTime: loanUser?.creditInfo?.status?.applyTime,
  };
  await notifyCreditApply(
    loanUser.id || 0,
    notifyCreditApplyParam,
    "development",
  );
};

export async function creditApply(params: ParamType) {
  console.log("请求入参:", params);

  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const responseData: ResponseDataType = {};

  const creditInfo = user.creditInfo as unknown as CreditInfo;

  if (creditInfo.actions.creditApplyStatus === CreditApplyStatusEnum.REJECTED) {
    creditInfo.status.creditStatus = CreditStatusEnum.REJECTED;
    creditInfo.status.applyNo = params.applyNo;
    creditInfo.status.applyStatus = CreditApplyStatusEnum.REJECTED;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.applyTime = Date.now();

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
    responseData.refuseCode = creditInfo?.actions?.refuseCode;
    responseData.refuseMsg = creditInfo?.actions?.refuseMsg;
  } else if (
    creditInfo?.actions?.creditApplyStatus === CreditApplyStatusEnum.PENDING
  ) {
    creditInfo.status.creditStatus = CreditStatusEnum.PENDING;
    creditInfo.status.applyNo = params.applyNo;
    creditInfo.status.applyStatus = CreditApplyStatusEnum.PENDING;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.applyTime = Date.now();
    creditInfo.actions.creditApplyStatus = CreditApplyStatusEnum.PASSED;

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
  } else {
    creditInfo.status.creditStatus = CreditStatusEnum.NORMAL;
    creditInfo.status.applyNo = params.applyNo;
    creditInfo.status.applyStatus = CreditApplyStatusEnum.PASSED;
    creditInfo.status.outOrderNo = crypto.randomUUID();
    creditInfo.status.creditLimit = creditInfo?.actions?.creditLimit;
    creditInfo.status.remainLimit = creditInfo?.actions?.creditLimit;
    creditInfo.status.applyTime = Date.now();

    responseData.applyStatus = creditInfo?.status?.applyStatus;
    responseData.outOrderNo = creditInfo?.status?.outOrderNo;
    responseData.remainLimit = creditInfo?.status?.remainLimit;

    const minApr = Math.min(
      ...creditInfo.status.productInfos.map((item) => {
        return parseFloat(item.apr);
      }),
    );
    const minDayRate = Math.min(
      ...creditInfo.status.productInfos.map((item) => {
        return parseFloat(item.dayRate);
      }),
    );
    responseData.apr = minApr.toString();
    responseData.dayRate = minDayRate.toString();
  }

  await updateLoanUser(user);

  if (creditInfo.actions.autoCallback) {
    setTimeout(async () => {
      await creditApplyCallBack(user);
    }, creditInfo.actions.callBackDelay * 1000);
  }

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}
