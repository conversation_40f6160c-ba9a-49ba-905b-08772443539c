import { encryptRes } from "@/utils/decrypt";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo, LoanOrderDetail, LoanStatusEnum } from "@/types/cp/loanInfo";
import { ClearCertificateInfo } from "@/types/cp/clearCertificateInfo";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 渠道方借款订单号列表 */
  outOrderNos: string[];
  /** 邮箱地址 */
  email: string;
}

export interface ResponseDataType {
  /** 发送结果，true-成功，false-为失败 */
  sendStatus: boolean;
  /** 发送失败原因，发送失败必传 */
  failReason?: string;
}

export async function settlementSend(params: ParamType) {
  // 获取用户信息
  const user = await getLoanUserByUserId(params.userId);
  if (!user) {
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: {
        sendStatus: false,
        failReason: "用户不存在",
      },
    });
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(params.email)) {
    return encryptRes({
      code: 203101,
      message: "邮箱格式错误",
      data: {
        sendStatus: false,
        failReason: "邮箱格式错误",
      },
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 0,
      message: "success",
      data: {
        sendStatus: false,
        failReason: "用户没有借款订单",
      },
    });
  }

  const clearCertificateInfo =
    user.clearCertificateInfo as unknown as ClearCertificateInfo;

  return encryptRes({
    code: clearCertificateInfo.action.canOpenClearCertificate
      ? 0
      : clearCertificateInfo.action.failCode!,
    message: clearCertificateInfo.action.canOpenClearCertificate
      ? "success"
      : clearCertificateInfo.action.failReason!,
    data: {
      sendStatus: clearCertificateInfo.action.canOpenClearCertificate,
      failReason: clearCertificateInfo.action.canOpenClearCertificate
        ? undefined
        : clearCertificateInfo.action.failReason!,
    },
  });
}