import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';

interface User {
  id: string;
  username: string;
  sessionId: string;
  fileId?: string;
  cursor?: {
    line: number;
    column: number;
  };
}

interface CursorPosition {
  line: number;
  column: number;
  username: string;
  userId: string;
}

interface FileChange {
  fileId: string;
  content: string;
  userId: string;
  username: string;
  timestamp: Date;
}

class WebSocketServer {
  private io: SocketIOServer;
  private users: Map<string, User> = new Map();
  private fileContents: Map<string, string> = new Map();
  private cursors: Map<string, Map<string, CursorPosition>> = new Map();

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'production' ? false : "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`用户连接: ${socket.id}`);

      // 用户加入会话
      socket.on('join-session', (data: { sessionId: string; username: string }) => {
        const user: User = {
          id: socket.id,
          username: data.username,
          sessionId: data.sessionId
        };
        
        this.users.set(socket.id, user);
        socket.join(data.sessionId);
        
        // 通知其他用户有新用户加入
        socket.to(data.sessionId).emit('user-joined', {
          userId: socket.id,
          username: data.username
        });

        // 发送当前在线用户列表
        const sessionUsers = Array.from(this.users.values())
          .filter(u => u.sessionId === data.sessionId)
          .map(u => ({ id: u.id, username: u.username }));
        
        this.io.to(data.sessionId).emit('users-updated', sessionUsers);
      });

      // 用户选择文件
      socket.on('select-file', (data: { fileId: string; content: string }) => {
        const user = this.users.get(socket.id);
        if (!user) return;

        user.fileId = data.fileId;
        this.fileContents.set(data.fileId, data.content);
        
        // 通知其他用户文件被选择
        socket.to(user.sessionId).emit('file-selected', {
          fileId: data.fileId,
          userId: socket.id,
          username: user.username
        });
      });

      // 文件内容变更
      socket.on('file-change', (data: { fileId: string; content: string }) => {
        const user = this.users.get(socket.id);
        if (!user) return;

        this.fileContents.set(data.fileId, data.content);
        
        // 广播文件变更给其他用户
        socket.to(user.sessionId).emit('file-changed', {
          fileId: data.fileId,
          content: data.content,
          userId: socket.id,
          username: user.username,
          timestamp: new Date()
        });
      });

      // 光标位置变更
      socket.on('cursor-change', (data: { fileId: string; cursor: CursorPosition }) => {
        const user = this.users.get(socket.id);
        if (!user) return;

        if (!this.cursors.has(data.fileId)) {
          this.cursors.set(data.fileId, new Map());
        }
        
        this.cursors.get(data.fileId)!.set(socket.id, data.cursor);
        
        // 广播光标位置给其他用户
        socket.to(user.sessionId).emit('cursor-changed', {
          fileId: data.fileId,
          cursor: data.cursor,
          userId: socket.id,
          username: user.username
        });
      });

      // 用户断开连接
      socket.on('disconnect', () => {
        const user = this.users.get(socket.id);
        if (user) {
          // 清理用户数据
          if (user.fileId && this.cursors.has(user.fileId)) {
            this.cursors.get(user.fileId)!.delete(socket.id);
          }
          this.users.delete(socket.id);
          
          // 通知其他用户
          socket.to(user.sessionId).emit('user-left', {
            userId: socket.id,
            username: user.username
          });

          // 更新用户列表
          const sessionUsers = Array.from(this.users.values())
            .filter(u => u.sessionId === user.sessionId)
            .map(u => ({ id: u.id, username: u.username }));
          
          this.io.to(user.sessionId).emit('users-updated', sessionUsers);
        }
        
        console.log(`用户断开连接: ${socket.id}`);
      });
    });
  }

  // 获取会话中的在线用户
  getSessionUsers(sessionId: string): User[] {
    return Array.from(this.users.values()).filter(u => u.sessionId === sessionId);
  }

  // 获取文件内容
  getFileContent(fileId: string): string | undefined {
    return this.fileContents.get(fileId);
  }

  // 获取文件的所有光标位置
  getFileCursors(fileId: string): CursorPosition[] {
    const cursors = this.cursors.get(fileId);
    return cursors ? Array.from(cursors.values()) : [];
  }
}

export default WebSocketServer; 