import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
// https://vitejs.dev/config/
const plugins = [vue()];
const config = {
  base: "/wallet-loan-web/pages",
  plugins,
  // test节点为Vitest的配置项
  test: {
    environment: "jsdom",
    watch: false,
    // reporters: "html",
    // outputFile: "./report/index.html",
  },
};
//仅处理客户端打包，该插件不支持服务端打包
if (process.argv?.includes("dist/client")) {
  config.build = {
    rollupOptions: {
      output: {
        manualChunks: {
          hnr: ['@hihonor/hnr/dist/hnr.es.min']
        },
        chunkFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'hnr') {
            return 'framework/[name]-[hash].js'; // 特定路径
          }
          return 'assets/[name]-[hash].js'; // 默认路径
        }
      }
    }
  }
}
export default defineConfig(config);
