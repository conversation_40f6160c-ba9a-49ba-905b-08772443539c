module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: ["airbnb-base", "plugin:vue/vue3-recommended", "plugin:prettier/recommended"],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["vue"],
  rules: {
    // indent: ['error', 2],
    quotes: ["error", "double"],
    semi: ["error", "always"],
    "max-len": ["error", 200],
    "no-console": "off",
    "vue/multi-word-component-names": "off",
    "vue/no-v-model-argument": "off",
    // 'no-unused-vars': 'warn',
    "import/no-absolute-path": "off",
    "import/no-unresolved": "off",
    "import/prefer-default-export": "off",
    "no-restricted-syntax": [
      "error",
      {
        selector: "CallExpression[callee.property.name=replaceAll]",
        message: "避免使用replaceAll，考虑使用replace+正则表达式以获得更好的兼容性",
      },
    ],
    // 'consistent-return': 'off',
  },
};
