import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from "pinia";
import { describe, it, expect, vi, beforeEach } from "vitest";
import useEntranceStore from "../../../src/pages/entrance/store";
import * as requestUtils from "../../../src/helpers/utils";
import * as storage from "../../../src/helpers/storage";
import * as nativeBridge from "../../../src/helpers/native-bridge";
import * as activityDialogUtils from "../../../src/helpers/activity-dialog-utils";
import { getSuggestList } from "../../../src/api/config";
import { getAdActivityListV2 } from "../../../src/api/ad";

// Mock dependencies
vi.mock("../../../src/helpers/utils");
vi.mock("../../../src/helpers/storage");
vi.mock("../../../src/helpers/configUtils");
vi.mock("../../../src/helpers/native-bridge");
vi.mock("../../../src/helpers/activity-dialog-utils");
vi.mock("../../../src/api/config");
vi.mock("../../../src/api/ad");

describe("entrance store", () => {
  let store;

  beforeEach(() => {
    // Create a fresh pinia instance and make it active
    setActivePinia(createPinia());
    // Create an instance of the store
    store = useEntranceStore();
    // Reset all mocks
    vi.clearAllMocks();
  });

  describe("state initialization", () => {
    it("should initialize with default values", () => {
      expect(store.newGusetCoupon).toBe(false);
      expect(store.isPageDisplay).toBe(true);
      expect(store.cardDisplayStatus).toBe(0);
      expect(store.isRransfer).toBe(false);
      expect(store.percentage).toBe(0);
      expect(store.userError).toBe(false);
      expect(store.pageStartTime).toBeNull();
      expect(store.screenSize).toEqual({
        screenWidth: 0,
        screenHeight: 0,
      });
    });
  });

  describe("actions", () => {
    describe("checkDialogOpenStatus", () => {
      it("should return false when no dialog is open", () => {
        store.isShowProtocolDialog = false;
        store.showAddShortcutDialog = false;
        store.showDiscountDialog = false;
        store.showCustomerServiceDialog = false;
        store.showSupplierInfoDialog = false;
        store.showWelcomeDialog = false;
        store.showMarketingDialog = false;

        expect(store.checkDialogOpenStatus()).toBe(false);
      });

      it("should return true when any dialog is open", () => {
        store.showWelcomeDialog = true;
        expect(store.checkDialogOpenStatus()).toBe(true);
      });
    });

    describe("parseFloatWithPrecision", () => {
      it("should handle values with decimal places", () => {
        expect(store.parseFloatWithPrecision("123.456")).toBe("123.456");
      });

      it("should handle values without decimal places", () => {
        expect(store.parseFloatWithPrecision("123")).toBe("123");
      });

      it("should handle undefined values", () => {
        expect(store.parseFloatWithPrecision()).toBe("100");
      });
    });

    describe("getAddShortcutDialogSilent", () => {
      it("should get add shortcut info from storage", () => {
        const mockUserId = "test-user";
        const mockData = { count: 1, lastTime: Date.now() };
        store.param = { userId: mockUserId };
        storage.getStore.mockReturnValue(mockData);

        store.getAddShortcutDialogSilent();

        expect(storage.getStore).toHaveBeenCalledWith(`U_POPUP_STORE_${mockUserId}`);
        expect(store.addShortcutInfo).toEqual(mockData);
      });
    });

    describe("setAddShortcutDialogSilent", () => {
      it("should set add shortcut info to storage", () => {
        const mockUserId = "test-user";
        const updateTime = Date.now();
        store.param = { userId: mockUserId };
        store.addShortcutInfo = { count: 0 };

        store.setAddShortcutDialogSilent(updateTime);

        expect(storage.setWithExactExpireUnit).toHaveBeenCalledWith(
          `U_POPUP_STORE_${mockUserId}`,
          { count: 1, lastTime: updateTime },
          30000,
          "D",
        );
        expect(storage.getStore).toHaveBeenCalledWith(`U_POPUP_STORE_${mockUserId}`);
      });
    });

    describe("showDiscountDialogFunction", () => {
      beforeEach(() => {
        store.isLimitIncreaseRecord = true;
        store.isRateDecreaseRecord = true;
        store.storeCreditChangeRecordId = "record-1";
        store.creditInfo = { creditChange: { creditChangeRecordId: "record-2" } };
      });

      it("should show discount dialog when conditions are met", () => {
        store.checkDialogOpenStatus = vi.fn().mockReturnValue(false);

        store.showDiscountDialogFunction();

        expect(store.clickShowDiscount).toBe(false);
        expect(store.nonClickShowDiscount).toBe(true);
        expect(store.showDiscountDialog).toBe(true);
        expect(nativeBridge.report).toHaveBeenCalled();
      });

      it("should not show discount dialog when another dialog is open", () => {
        store.checkDialogOpenStatus = vi.fn().mockReturnValue(true);

        store.showDiscountDialogFunction();

        expect(store.showDiscountDialog).toBe(false);
      });
    });

    describe("showMarketingDialogFunction", () => {
      it("should call showFloorDialog when no dialog is open", async () => {
        store.checkDialogOpenStatus = vi.fn().mockReturnValue(false);
        const mockFunctions = {};

        await store.showMarketingDialogFunction(mockFunctions);

        expect(activityDialogUtils.showFloorDialog).toHaveBeenCalledWith(
          "loan_index",
          mockFunctions,
        );
      });
    });

    describe("showWelcomeDialogFunction", () => {
      it("should show welcome dialog when conditions are met", () => {
        store.firstEnter = true;
        store.isUnknownLimitOrInvalid = true;
        store.checkDialogOpenStatus = vi.fn().mockReturnValue(false);

        store.showWelcomeDialogFunction();

        expect(store.showWelcomeDialog).toBe(true);
      });
    });

    describe("getChangeAgreement", () => {
      it("should show protocol dialog when agreements need to be signed", async () => {
        const mockResponse = {
          code: 0,
          data: {
            errorCode: "0",
            signInfo: [
              { agrType: 1493, needSign: "true", newestVersion: "1.0" },
              { agrType: 1492, needSign: "true", newestVersion: "1.0" },
            ],
          },
        };
        const mockAgreementInfo = {
          noticeInfoList: [
            { agrType: 1493, title: "User Agreement" },
            { agrType: 1492, title: "Privacy Policy" },
          ],
        };
        requestUtils.request.mockResolvedValueOnce(mockResponse);
        requestUtils.request.mockResolvedValueOnce({ data: mockAgreementInfo });
        store.param = { country: "cn", language: "zh-CN" };

        await store.getChangeAgreement();

        expect(store.isShowProtocolDialog).toBe(true);
        expect(store.isChangeUserProtocol).toBe(true);
        expect(store.isChangePrivacyProtocol).toBe(true);
      });
    });

    describe("getSuggestProducts", () => {
      it("should fetch and handle suggest products", async () => {
        const mockProducts = [{ id: 1, name: "Product 1" }];
        getSuggestList.mockResolvedValue({ data: mockProducts });

        await store.getSuggestProducts();

        expect(getSuggestList).toHaveBeenCalled();
        expect(store.suggestProducts).toEqual(mockProducts);
      });
    });

    describe("getAdActivityList", () => {
      it("should fetch and handle ad activities", async () => {
        const mockActivities = {
          loan_home_banner: { activityList: [{ id: 1 }] },
          loan_home_top_banner: { activityList: [{ id: 2 }] },
          loan_home_banner_widescreen: { activityList: [{ id: 3 }] },
        };
        getAdActivityListV2.mockResolvedValue({ data: mockActivities });

        await store.getAdActivityList();

        expect(getAdActivityListV2).toHaveBeenCalled();
        expect(store.walletBanners).toEqual(mockActivities.loan_home_banner.activityList);
        expect(store.topBanner.loan_home_top_banner).toEqual(
          mockActivities.loan_home_top_banner.activityList,
        );
        expect(store.wideScreenWalletBanners).toEqual(
          mockActivities.loan_home_banner_widescreen.activityList,
        );
      });
    });

    describe("initialServer", () => {
      it("should initialize server data", async () => {
        store.param = { language: "zh-CN" };
        store.loadTranslations = vi.fn().mockResolvedValue({});
        getSuggestList.mockResolvedValue({ data: [] });
        getAdActivityListV2.mockResolvedValue({ data: {} });

        await store.initialServer();

        expect(store.langType).toBe(0);
        expect(store.loadTranslations).toHaveBeenCalled();
        expect(getSuggestList).toHaveBeenCalled();
        expect(getAdActivityListV2).toHaveBeenCalled();
      });
    });

    describe("formatTemplateString", () => {
      it("should format string with placeholders", () => {
        const result = store.formatTemplateString("Hello %s, welcome to %s", "John", "Honor");
        expect(result).toBe("Hello John, welcome to Honor");
      });
    });

    describe("formatTemplateStringInorder", () => {
      it("should format string with numbered placeholders", () => {
        const result = store.formatTemplateStringInorder(
          "Hello %1$s, welcome to %2$s",
          "John",
          "Honor",
        );
        expect(result).toBe("Hello John, welcome to Honor");
      });
    });

    describe("loadSession", () => {
      it("should load session data from storage", () => {
        const mockUserId = "test-user";
        const mockCreditProcess = { flowNo: "123" };
        const mockLoanProcess = { applyNo: "456" };
        store.param = { userId: mockUserId };
        storage.getStore.mockImplementation((key) => {
          if (key === `U_CREDIT_PROCESS_V2_${mockUserId}`) return mockCreditProcess;
          if (key === `U_LOAN_PROCESS_${mockUserId}`) return mockLoanProcess;
          return null;
        });

        store.loadSession();

        expect(storage.del).toHaveBeenCalledWith(`U_CREDIT_PROCESS_${mockUserId}`);
        expect(store.creditProcess).toEqual(mockCreditProcess);
        expect(store.loanProcess).toEqual(mockLoanProcess);
      });
    });
  });
});
