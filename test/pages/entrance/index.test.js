// src/pages/entrance/index.spec.js
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { mount } from "@vue/test-utils";
import qs from "qs";
import { showConfirmDialog } from "@hihonor/hnr/dist/hnr.es.min";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/autoplay";
import Big from "big.js";
import lodash from "lodash";
import Index from "../../../src/pages/entrance/index.vue";
import useStore from "../../../src/pages/entrance/store";
import {
  initStore,
  request,
  amountNumberFormat,
  parseQueryStringToObj,
  storeMarketingTracking,
  isEmptyObject,
} from "../../../src/helpers/utils";
import {
  DEBOUNCE_OPTIONS,
  SET_NETWORK_EVENT,
  getUserProtocalUrl,
  getPrivacyProtocalUrl,
  getIntroductionPageUrl,
  DEBOUNCE_WAIT_MILLISECONDS,
} from "../../../src/helpers/constants";
import { creditNext } from "../../../src/helpers/next";
import {
  goto,
  back,
  hasShortcut,
  updateShortcut,
  report,
  callPhone,
} from "../../../src/helpers/native-bridge";

// Mock external dependencies
vi.mock("qs", async (importOriginal) => {
  const actualQs = await importOriginal();
  return {
    ...actualQs,
    stringify: vi.fn(), // 模拟 stringify 方法
  };
});

vi.mock("@hihonor/hnr/dist/hnr.es.min", async (importOriginal) => {
  const actualQs = await importOriginal();
  return {
    ...actualQs,
    showConfirmDialog: vi.fn(), // 模拟 stringify 方法
  };
});

vi.mock("swiper/vue", async (importOriginal) => {
  const actualQs = await importOriginal();
  return {
    ...actualQs,
    Swiper: vi.fn().mockImplementation(() => ({ template: "<div></div>" })),
    SwiperSlide: vi.fn().mockImplementation(() => ({ template: "<div></div>" })),
  };
});

vi.mock("swiper/modules", async (importOriginal) => {
  const actualQs = await importOriginal();
  return {
    ...actualQs,
    Autoplay: vi.fn(),
  };
});

vi.mock("big.js", async (importOriginal) => {
  const actualQs = await importOriginal();
  return {
    ...actualQs,
    default: vi.fn().mockImplementation((value) => ({
      toFixed: vi.fn().mockReturnThis(),
      toString: vi.fn().mockReturnValue(value.toString()),
    })),
  };
});

vi.mock("lodash", async (importOriginal) => {
  const actualLodash = await importOriginal();
  return {
    ...actualLodash,
    debounce: vi.fn((fn) => fn), // 或者你可以返回一个自定义的 debounce 实现
  };
});

vi.mock("../../../src/pages/entrance/store", async (importOriginal) => {
  const actualStore = await importOriginal();
  return {
    ...actualStore,
    default: vi.fn().mockImplementation(() => ({
      state: {
        newGusetCoupon: false,
        isPageDisplay: true,
        cardDisplayStatus: 0, // 0-鱼骨图预显示/1-正常显示/2-服务器异常
        isRransfer: false, // 跳转方法中转空白页
        percentage: 0, // 进度条百分比
        userError: false,
        pageStartTime: null,
        screenSize: {
          screenWidth: 0,
          screenHeight: 0,
        },

        translationData: null,

        // 状态列表
        isNotAccessOrLogout: null, // 是否未准入或者注销
        isAccess: null, // 是否准入成功
        isAccessFailed: null, // 是否准入失败
        isUnknownLimitOrInvalid: null, // 是否未获取额度或者额度失效
        isLimitNormal: null, // 是否额度正常
        isLimitNormalNoLoanApply: null, // 是否授信成功但无借款申请订单
        isLimitNormalLoanUnsubmitted: null, // 是否授信成功但有已填写但未提交借款订单
        isLimitNormalInsufficient: null, // 是否授信成功但可用额度不足
        isLimitNormalZero: null, // 是否授信成功但可用额度为0
        isLimitNormalLoanAppling: null, // 是否授信成功且存在放款中订单
        isRepayOverdue: null, // 是否授信成功但存在逾期订单
        isLimitNormalLoanNotAvailable: null, // 是否授信成功但额度暂不可用
        isLimitNormalLoanOtherErr: null, // 是否授信成功但受到其它原因管控
        isLimitAppling: null, // 是否授信申请审核中
        isLimitApplyFail: null, // 是否授信申请失败
        isWithoutLoan: null, // 是否无还款计划
        isRepaymentInProgress: null, // 是否有还款计划
        isRateDecrease: null, // 是否有降息
        isLimitIncrease: null, // 是否有提额
        isRateDecreaseRecord: null, // 是否有最近降息记录
        isLimitIncreaseRecord: null, // 是否有最近提额记录
        isCouponIncrease: null, // 是否有优惠券
        isLoginHandleDataAfter: false, // 登录之后处理数据之后
        isFromLogin: false, // 是否登录后返回

        // 弹窗状态
        showCustomerServiceDialog: false, // 客服弹窗
        firstEnter: false,
        showAddShortcutDialog: false, // 快捷方式弹窗
        showAddShortcutSuccessToast: false,
        showAddShortcutFailedToast: false,
        showWelcomeDialog: false, // 新客欢迎弹窗
        showDiscountDialog: false, // 调额调价弹窗
        showSupplierInfoDialog: false, // 已接入供应商信息弹窗
        clickShowDiscount: false, // 调额调价弹窗（点击弹出）状态
        nonClickShowDiscount: false, // 调额调价弹窗（落地自动弹出）状态
        showMarketingDialog: false, // 营销落地弹窗
        isChangeUserProtocol: false, // 协议弹窗状态（含用户协议）
        isChangePrivacyProtocol: false, // 协议弹窗状态（含隐私协议）
        isShowProtocolDialog: false, // 协议弹窗

        // 文本
        navigationHeader: "", // 主页顶部导航栏标题
        welcomeDialogTitle: "",
        welcomeDialogInfo: "",
        sloganInfo1: "",
        sloganInfo2: "",
        sloganInfo3: "",
        sloganHeader: "", // slogan的标题
        formattedWelcomeButtonText: "",
        sloganText1: "", // slogan展示的元素
        sloganText2: "", // slogan展示的元素
        sloganText3: "", // slogan展示的元素
        addShortcutDialogText: "", // 添加快捷方式弹窗文本
        addShortcutToastText: "", // 添加快捷方式成功的提示框文本
        addShortcutFailedToastText: "",
        formattedOverdueWarning: "", // 逾期状态下推荐位的警示文本
        formattedIntroduction: "", // 未准入下显示借钱服务介绍入口
        customerServiceDialogText: "", // 供应商客服弹窗文本
        loanProductOwner: "", // 厂商文本
        loanSupplierText: "", // 推荐位供应商文本
        loanLimitTitle: "", // 推荐位额度及逾期信息的主标题
        formattedLimitAmount: "", // 推荐位额度信息的具体数额
        formattedOverdueAmount: "", // 推荐位逾期信息的具体数额
        formattedRateTextA: "", // 推荐位利率信息文案A
        formattedRateTextB: "", // 推荐位利率信息文案B
        formattedRateTextC: "", // 推荐位利率信息文案C
        formattedRateTextD: "", // 推荐位利率信息文案D
        formattedOverdueInfo: "", // 推荐位逾期信息文案
        aboveButtonText: "", // 推荐位按钮上方文本
        formattedButtonText: "", // 推荐位主按钮文案
        cellTitleWithoutLoan: "", // 还款计划无借款时标题文案
        cellTextWithoutLoan: "", // 还款计划无借款时信息文案
        cellTitleWithLoan: "", // 还款计划有借款时标题文案
        formattedRepaymentAmount: "", // 还款计划总额的具体数额
        formattedRepaymentText: "", // 还款计划借款及逾期信息文案
        disclaimerText: "", // 首页免责声明文案
        noticeText: "", // 首页注意事项文案
        customerServiceDialogButtonText: "", // 客服弹窗按钮文本
        keepSilentCheckboxText: "", // 添加桌面弹窗不再提示选项框文本
        cancelButtonText: "", // 添加桌面弹窗取消按钮文本
        contactService: "", // 联系客服按钮文本
        addShortButtonText: "", // 添加桌面弹窗按钮文本
        suggestProductTitle: "", // 严选标题
        suggestProductText: "", // 严选文案
        suggestProductButtonText: "", // 严选按钮
        rateDiscountText: "",
        limitIncreaseText: "",
        couponText: "",
        couponText1: "",
        discountDialogTitle: "",
        discountDialogInfo: "",
        formattedDiscountButtonText: "",
        loanLimitIncreaseTitle: "",
        guide2ContinueApplyText: "",
        changeCooperativeProductText: "",
        supplierInfoDescription: "",
        supplierInfoClarSt: "",
        supplierInfoButtonText: "",
        loadingText: "",
        serverErrorText: ["", ""],
        retryText: "",
        userLogoutText: "",
        jumpOutText: "",
        increaseAmountReduceInterestText: "",

        // 持久化状态
        addShortcutInfo: null,

        // 存储
        creditInfo: {}, // 授信信息
        walletBanners: [], // 运营信息
        topBanner: {},
        currentTopBanner: [],
        suggestProducts: [],
        repayPlans: [], // 还款计划
        supplier: {}, // 供应商
        repaySupplementaryInfo: {}, // 还款计划额外信息
        signInfo: {},
        updateSignInfo: {},
        couponcomeStatus: {},
        creditProcess: null,
        loanProcess: null,
        loanOrderStatus: null,
        discountInfoList: [], // 专享调额调息信息
        storeCreditChangeRecordId: null,
        rateInfos: {},
        discountDialogBackground: "",
        cpInfo: null,
        supplierInfoList: [],
        realNameInfo: {},

        param: {
          isActivity: "1",
          location: "",
        },
        langType: 0, // 语言类型 0-中文 1-英文
        adActivityListCode: {},

        // 提额降息
        retryTimes: 0,
        addUrlTimer: null,
        increaseAmountReduceInterestData: {
          url: "",
          loading: false,
        },
        getCreditChange: true,
        canCreditChange: false,
        isValidVersion: false,
        myCouponData: {},

        creditProcessProperty: {
          emptySpaceHeight: {},
          fontSize: "var(--dp12)",
          shortTextHeight: 24,
          longTextHeight: 24,
          shortTextWidth: 0,
          shortBubbleWidth: 0,
          shortBubbleLeft: 0,
          longTextWidth: 0,
          longBubbleWidth: 0,
          longBubbleLeft: 0,
        },

        animationUtil: {
          enter2Long: {
            next: "long2Short",
            bubbleTop: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
            bubbleBottom: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          enter2Short: {
            next: "short2Long",
            bubbleTop: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleBottom: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          short2Long: {
            next: "long2Short",
            bubbleTop: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottom: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleTopText: `
          bubble-text-appear-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottomText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          long2Short: {
            next: "short2Long",
            bubbleTop: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottom: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleTopText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleBottomText: `
          bubble-text-appear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          shortDisappear: {
            next: "enterPage",
            bubbleTop: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleBottom: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          longDisappear: {
            next: "enterPage",
            bubbleTop: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
            bubbleBottom: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
        },
        animationStatus: {},
        infoConAnimationUtil: {
          infoConHide: "infoCon-hide 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          infoConShow: "infoCon-show 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConHeightTran: "loanCon-height-tran 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConShow: "loanCon-show 0.2s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConHide: "loanCon-hide 0.002s cubic-bezier(0.2,0,0.2,1) forwards",
        },
        infoConAnimationStatus: {},
      },
      actions: {
        /**
         * 弹窗弹出状态
         * @returns {boolean} 弹窗弹出状态 true-存在弹窗弹出 false-不存在弹窗弹出
         */
        checkDialogOpenStatus: vi.fn(),

        parseFloatWithPrecision: vi.fn(),

        getAddShortcutDialogSilent: vi.fn(),

        setAddShortcutDialogSilent: vi.fn(),

        showDiscountDialogFunction: vi.fn(),

        showMarketingDialogFunction: vi.fn(),

        showWelcomeDialogFunction: vi.fn(),

        getChangeAgreement: vi.fn(),

        getSuggestProducts: vi.fn(),

        getAdActivityList: vi.fn(),

        // 未登录时调用的banner接口
        getAdActivityListForGuest: vi.fn(),

        initialSupplierInfoList: vi.fn(),

        getCouponPageUrl: vi.fn(),

        initialServer: vi.fn(),

        queryApplyStatus: vi.fn(),

        checkCouponcome: vi.fn(),

        getScreenSize: vi.fn(),

        getAddUrlData: vi.fn(),

        initialClient: vi.fn(),

        handleData: vi.fn(),

        setDiscountBackground: vi.fn(),

        initialTextServer: vi.fn(),

        initialTextClient: vi.fn(),

        initialClientSide: vi.fn(),

        calculateTextWidth: vi.fn(),

        /**
         *
         * @param {*} updater       updater函数
         * @param {*} expireFuntion 过期函数
         * @param {*} expireTime    过期时间
         * @param {*} expireTimeInLocal 以本地时间为基准的过期时间
         * @param {*} updateTimerCallback 更新定时器的回调
         * @param {*} unit 时间单位，默认是毫秒
         */
        startExpireTimeUpdater: vi.fn(),

        formatTemplateString: vi.fn(),

        formatTemplateStringInorder: vi.fn(),

        loadSession: vi.fn(),

        loadWelcomeDialogSession: vi.fn(),

        /**
         * @typedef {Object} CreditCheckResult
         * @property {string} nextPage - 跳转页面
         * @property {number} accessResult - 准入结果
         */
        /**
         * 检查授信流程缓存
         * @returns {CreditCheckResult}
         */
        checkCreditStore: vi.fn(),

        loadTranslations: vi.fn(),

        handleSuppliers: vi.fn(),
      },
      getters: {
        // 添加其他必要的 getter
      },
    })),
  };
});

vi.mock("../../../src/helpers/utils", () => ({
  initStore: vi.fn().mockReturnValue({
    store: {
      initialServer: vi.fn(),
      // Add other necessary properties and methods here
    },
    data: {},
  }),
  request: vi.fn(),
  amountNumberFormat: vi.fn(),
  parseQueryStringToObj: vi.fn(),
  storeMarketingTracking: vi.fn(),
  isEmptyObject: vi.fn(),
}));

vi.mock("../../../src/helpers/constants", () => ({
  DEBOUNCE_OPTIONS: {},
  SET_NETWORK_EVENT: "set_network_event",
  getUserProtocalUrl: vi.fn(),
  getPrivacyProtocalUrl: vi.fn(),
  getIntroductionPageUrl: vi.fn(),
  DEBOUNCE_WAIT_MILLISECONDS: 300,
}));

vi.mock("../../../src/helpers/next", () => ({
  creditNext: vi.fn(),
}));

vi.mock("../../../src/helpers/native-bridge", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    regNativeEvent: vi.fn(), // 模拟 regNativeEvent 方法
    goto: vi.fn(),
    back: vi.fn(),
    hasShortcut: vi.fn(),
    updateShortcut: vi.fn(),
    report: vi.fn(),
    callPhone: vi.fn(),
    getNetworkStatus: vi.fn().mockReturnValue(true), // 假设网络状态为 true
  };
});

describe("Entrance Page", () => {
  let wrapper;
  let storeMock;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Set up default mock implementations
    storeMock = {
      state: {
        newGusetCoupon: false,
        isPageDisplay: true,
        cardDisplayStatus: 0, // 0-鱼骨图预显示/1-正常显示/2-服务器异常
        isRransfer: false, // 跳转方法中转空白页
        percentage: 0, // 进度条百分比
        userError: false,
        pageStartTime: null,
        screenSize: {
          screenWidth: 0,
          screenHeight: 0,
        },

        translationData: null,

        // 状态列表
        isNotAccessOrLogout: null, // 是否未准入或者注销
        isAccess: null, // 是否准入成功
        isAccessFailed: null, // 是否准入失败
        isUnknownLimitOrInvalid: null, // 是否未获取额度或者额度失效
        isLimitNormal: null, // 是否额度正常
        isLimitNormalNoLoanApply: null, // 是否授信成功但无借款申请订单
        isLimitNormalLoanUnsubmitted: null, // 是否授信成功但有已填写但未提交借款订单
        isLimitNormalInsufficient: null, // 是否授信成功但可用额度不足
        isLimitNormalZero: null, // 是否授信成功但可用额度为0
        isLimitNormalLoanAppling: null, // 是否授信成功且存在放款中订单
        isRepayOverdue: null, // 是否授信成功但存在逾期订单
        isLimitNormalLoanNotAvailable: null, // 是否授信成功但额度暂不可用
        isLimitNormalLoanOtherErr: null, // 是否授信成功但受到其它原因管控
        isLimitAppling: null, // 是否授信申请审核中
        isLimitApplyFail: null, // 是否授信申请失败
        isWithoutLoan: null, // 是否无还款计划
        isRepaymentInProgress: null, // 是否有还款计划
        isRateDecrease: null, // 是否有降息
        isLimitIncrease: null, // 是否有提额
        isRateDecreaseRecord: null, // 是否有最近降息记录
        isLimitIncreaseRecord: null, // 是否有最近提额记录
        isCouponIncrease: null, // 是否有优惠券
        isLoginHandleDataAfter: false, // 登录之后处理数据之后
        isFromLogin: false, // 是否登录后返回

        // 弹窗状态
        showCustomerServiceDialog: false, // 客服弹窗
        firstEnter: false,
        showAddShortcutDialog: false, // 快捷方式弹窗
        showAddShortcutSuccessToast: false,
        showAddShortcutFailedToast: false,
        showWelcomeDialog: false, // 新客欢迎弹窗
        showDiscountDialog: false, // 调额调价弹窗
        showSupplierInfoDialog: false, // 已接入供应商信息弹窗
        clickShowDiscount: false, // 调额调价弹窗（点击弹出）状态
        nonClickShowDiscount: false, // 调额调价弹窗（落地自动弹出）状态
        showMarketingDialog: false, // 营销落地弹窗
        isChangeUserProtocol: false, // 协议弹窗状态（含用户协议）
        isChangePrivacyProtocol: false, // 协议弹窗状态（含隐私协议）
        isShowProtocolDialog: false, // 协议弹窗

        // 文本
        navigationHeader: "", // 主页顶部导航栏标题
        welcomeDialogTitle: "",
        welcomeDialogInfo: "",
        sloganInfo1: "",
        sloganInfo2: "",
        sloganInfo3: "",
        sloganHeader: "", // slogan的标题
        formattedWelcomeButtonText: "",
        sloganText1: "", // slogan展示的元素
        sloganText2: "", // slogan展示的元素
        sloganText3: "", // slogan展示的元素
        addShortcutDialogText: "", // 添加快捷方式弹窗文本
        addShortcutToastText: "", // 添加快捷方式成功的提示框文本
        addShortcutFailedToastText: "",
        formattedOverdueWarning: "", // 逾期状态下推荐位的警示文本
        formattedIntroduction: "", // 未准入下显示借钱服务介绍入口
        customerServiceDialogText: "", // 供应商客服弹窗文本
        loanProductOwner: "", // 厂商文本
        loanSupplierText: "", // 推荐位供应商文本
        loanLimitTitle: "", // 推荐位额度及逾期信息的主标题
        formattedLimitAmount: "", // 推荐位额度信息的具体数额
        formattedOverdueAmount: "", // 推荐位逾期信息的具体数额
        formattedRateTextA: "", // 推荐位利率信息文案A
        formattedRateTextB: "", // 推荐位利率信息文案B
        formattedRateTextC: "", // 推荐位利率信息文案C
        formattedRateTextD: "", // 推荐位利率信息文案D
        formattedOverdueInfo: "", // 推荐位逾期信息文案
        aboveButtonText: "", // 推荐位按钮上方文本
        formattedButtonText: "", // 推荐位主按钮文案
        cellTitleWithoutLoan: "", // 还款计划无借款时标题文案
        cellTextWithoutLoan: "", // 还款计划无借款时信息文案
        cellTitleWithLoan: "", // 还款计划有借款时标题文案
        formattedRepaymentAmount: "", // 还款计划总额的具体数额
        formattedRepaymentText: "", // 还款计划借款及逾期信息文案
        disclaimerText: "", // 首页免责声明文案
        noticeText: "", // 首页注意事项文案
        customerServiceDialogButtonText: "", // 客服弹窗按钮文本
        keepSilentCheckboxText: "", // 添加桌面弹窗不再提示选项框文本
        cancelButtonText: "", // 添加桌面弹窗取消按钮文本
        contactService: "", // 联系客服按钮文本
        addShortButtonText: "", // 添加桌面弹窗按钮文本
        suggestProductTitle: "", // 严选标题
        suggestProductText: "", // 严选文案
        suggestProductButtonText: "", // 严选按钮
        rateDiscountText: "",
        limitIncreaseText: "",
        couponText: "",
        couponText1: "",
        discountDialogTitle: "",
        discountDialogInfo: "",
        formattedDiscountButtonText: "",
        loanLimitIncreaseTitle: "",
        guide2ContinueApplyText: "",
        changeCooperativeProductText: "",
        supplierInfoDescription: "",
        supplierInfoClarSt: "",
        supplierInfoButtonText: "",
        loadingText: "",
        serverErrorText: ["", ""],
        retryText: "",
        userLogoutText: "",
        jumpOutText: "",
        increaseAmountReduceInterestText: "",

        // 持久化状态
        addShortcutInfo: null,

        // 存储
        creditInfo: {}, // 授信信息
        walletBanners: [], // 运营信息
        topBanner: {},
        currentTopBanner: [],
        suggestProducts: [],
        repayPlans: [], // 还款计划
        supplier: {}, // 供应商
        repaySupplementaryInfo: {}, // 还款计划额外信息
        signInfo: {},
        updateSignInfo: {},
        couponcomeStatus: {},
        creditProcess: null,
        loanProcess: null,
        loanOrderStatus: null,
        discountInfoList: [], // 专享调额调息信息
        storeCreditChangeRecordId: null,
        rateInfos: {},
        discountDialogBackground: "",
        cpInfo: null,
        supplierInfoList: [],
        realNameInfo: {},

        param: {
          isActivity: "1",
          location: "",
        },
        langType: 0, // 语言类型 0-中文 1-英文
        adActivityListCode: {},

        // 提额降息
        retryTimes: 0,
        addUrlTimer: null,
        increaseAmountReduceInterestData: {
          url: "",
          loading: false,
        },
        getCreditChange: true,
        canCreditChange: false,
        isValidVersion: false,
        myCouponData: {},

        creditProcessProperty: {
          emptySpaceHeight: {},
          fontSize: "var(--dp12)",
          shortTextHeight: 24,
          longTextHeight: 24,
          shortTextWidth: 0,
          shortBubbleWidth: 0,
          shortBubbleLeft: 0,
          longTextWidth: 0,
          longBubbleWidth: 0,
          longBubbleLeft: 0,
        },

        animationUtil: {
          enter2Long: {
            next: "long2Short",
            bubbleTop: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
            bubbleBottom: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          enter2Short: {
            next: "short2Long",
            bubbleTop: "bubble-top-first-appear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleBottom: `
          bubble-bottom-first-appear-part1 0.2s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-bottom-first-appear-part2 0.1s cubic-bezier(0.4,0,0.2,1) 0.2s forwards,
          bubble-bottom-first-appear-part3 0.3s cubic-bezier(0.4,0,0.2,1) forwards
        `,
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          short2Long: {
            next: "long2Short",
            bubbleTop: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottom: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleTopText: `
          bubble-text-appear-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottomText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          long2Short: {
            next: "short2Long",
            bubbleTop: `
          bubble-resize-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-resize-part2 0.4s cubic-bezier(0.4,0,0.2,1) 0.3s forwards,
          bubble-resize-part3 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleBottom: `
          bubble-exchange-part1 0.7s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-exchange-part2 0.3s cubic-bezier(0.4,0,0.2,1) 0.7s forwards
        `,
            bubbleTopText: `
          bubble-text-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-disappear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleBottomText: `
          bubble-text-appear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-text-appear-part2 0.7s cubic-bezier(0.4,0,0.2,1) 0.3s forwards
        `,
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          shortDisappear: {
            next: "enterPage",
            bubbleTop: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleBottom: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
          longDisappear: {
            next: "enterPage",
            bubbleTop: `
          bubble-current-disappear-part1 0.3s cubic-bezier(0.4,0,0.2,1) forwards,
          bubble-current-disappear-part2 0.7s step-start 0.3s forwards
        `,
            bubbleBottom: "bubble-none-disappear 1s cubic-bezier(0.4,0,0.2,1) forwards",
            bubbleTopText: "",
            bubbleBottomText: "",
            bubbleTopNewTop: "",
            bubbleBottomNewTop: "",
          },
        },
        animationStatus: {},
        infoConAnimationUtil: {
          infoConHide: "infoCon-hide 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          infoConShow: "infoCon-show 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConHeightTran: "loanCon-height-tran 0.3s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConShow: "loanCon-show 0.2s cubic-bezier(0.2,0,0.2,1) forwards",
          loanConHide: "loanCon-hide 0.002s cubic-bezier(0.2,0,0.2,1) forwards",
        },
        infoConAnimationStatus: {},
      },
      actions: {
        /**
         * 弹窗弹出状态
         * @returns {boolean} 弹窗弹出状态 true-存在弹窗弹出 false-不存在弹窗弹出
         */
        checkDialogOpenStatus: vi.fn(),

        parseFloatWithPrecision: vi.fn(),

        getAddShortcutDialogSilent: vi.fn(),

        setAddShortcutDialogSilent: vi.fn(),

        showDiscountDialogFunction: vi.fn(),

        showMarketingDialogFunction: vi.fn(),

        showWelcomeDialogFunction: vi.fn(),

        getChangeAgreement: vi.fn(),

        getSuggestProducts: vi.fn(),

        getAdActivityList: vi.fn(),

        // 未登录时调用的banner接口
        getAdActivityListForGuest: vi.fn(),

        initialSupplierInfoList: vi.fn(),

        getCouponPageUrl: vi.fn(),

        initialServer: vi.fn(),

        queryApplyStatus: vi.fn(),

        checkCouponcome: vi.fn(),

        getScreenSize: vi.fn(),

        getAddUrlData: vi.fn(),

        initialClient: vi.fn(),

        handleData: vi.fn(),

        setDiscountBackground: vi.fn(),

        initialTextServer: vi.fn(),

        initialTextClient: vi.fn(),

        initialClientSide: vi.fn(),

        calculateTextWidth: vi.fn(),

        /**
         *
         * @param {*} updater       updater函数
         * @param {*} expireFuntion 过期函数
         * @param {*} expireTime    过期时间
         * @param {*} expireTimeInLocal 以本地时间为基准的过期时间
         * @param {*} updateTimerCallback 更新定时器的回调
         * @param {*} unit 时间单位，默认是毫秒
         */
        startExpireTimeUpdater: vi.fn(),

        formatTemplateString: vi.fn(),

        formatTemplateStringInorder: vi.fn(),

        loadSession: vi.fn(),

        loadWelcomeDialogSession: vi.fn(),

        /**
         * @typedef {Object} CreditCheckResult
         * @property {string} nextPage - 跳转页面
         * @property {number} accessResult - 准入结果
         */
        /**
         * 检查授信流程缓存
         * @returns {CreditCheckResult}
         */
        checkCreditStore: vi.fn(),

        loadTranslations: vi.fn(),

        handleSuppliers: vi.fn(),
      },
      getters: {
        // 添加其他必要的 getter
      },
    };
    console.log("storeMock:", storeMock); // 调试输出

    vi.mocked(useStore).mockImplementation(() => storeMock);
    vi.mocked(initStore).mockReturnValue({
      store: storeMock,
      data: {},
    });
    vi.mocked(request).mockResolvedValue({});
    vi.mocked(amountNumberFormat).mockReturnValue("");
    vi.mocked(parseQueryStringToObj).mockReturnValue({});
    vi.mocked(storeMarketingTracking).mockResolvedValue({});
    vi.mocked(isEmptyObject).mockReturnValue(false);

    wrapper = mount(Index, {
      global: {
        mocks: {
          $route: {
            query: {},
          },
        },
        provide: {
          useStore: () => storeMock, // 提供模拟的 store
        },
      },
    });
  });

  afterEach(() => {});

  describe("Imports and Dependencies", () => {
    it("should import necessary modules and functions", () => {
      expect(qs.stringify).toBeDefined();
      expect(showConfirmDialog).toBeDefined();
      expect(Swiper).toBeDefined();
      expect(SwiperSlide).toBeDefined();
      expect(Autoplay).toBeDefined();
      expect(Big).toBeDefined();
      expect(lodash.debounce).toBeDefined();
      expect(useStore).toBeDefined();
      expect(initStore).toBeDefined();
      expect(request).toBeDefined();
      expect(amountNumberFormat).toBeDefined();
      expect(parseQueryStringToObj).toBeDefined();
      expect(storeMarketingTracking).toBeDefined();
      expect(isEmptyObject).toBeDefined();
      expect(DEBOUNCE_OPTIONS).toBeDefined();
      expect(SET_NETWORK_EVENT).toBeDefined();
      expect(getUserProtocalUrl).toBeDefined();
      expect(getPrivacyProtocalUrl).toBeDefined();
      expect(getIntroductionPageUrl).toBeDefined();
      expect(DEBOUNCE_WAIT_MILLISECONDS).toBeDefined();
      expect(creditNext).toBeDefined();
      expect(goto).toBeDefined();
      expect(back).toBeDefined();
      expect(hasShortcut).toBeDefined();
      expect(updateShortcut).toBeDefined();
      expect(report).toBeDefined();
      expect(callPhone).toBeDefined();
    });
  });

  describe("Lifecycle Hooks", () => {
    it("should call initStore during onServerPrefetch", async () => {
      await wrapper.vm.onServerPrefetch();
      expect(initStore).toHaveBeenCalled();
    });

    it("should call initStore during onMounted", async () => {
      await wrapper.vm.onMounted();
      expect(initStore).toHaveBeenCalled();
    });
  });

  describe("Computed Properties", () => {
    it("should compute properties correctly", () => {});
  });

  describe("Methods", () => {
    it("should call request with correct parameters", async () => {});

    it("should call other utility methods as expected", async () => {});
  });

  describe("Watchers", () => {
    it("should react to changes in reactive data", async () => {});
  });
});

describe("RegExp: 去除多余小数点零", () => {
  const regex = /(\.0+|(\.\d*?[1-9])0+)$/;

  it("should match and remove .0 or .00 at end", () => {
    expect('1.0'.replace(regex, '$2')).toBe('1');
    expect('1.00'.replace(regex, '$2')).toBe('1');
    expect('123.0'.replace(regex, '$2')).toBe('123');
    expect('123.00'.replace(regex, '$2')).toBe('123');
  });

  it("should remove trailing zero but keep valid decimals", () => {
    expect('1.10'.replace(regex, '$2')).toBe('1.1');
    expect('1.1200'.replace(regex, '$2')).toBe('1.12');
    expect('123.4500'.replace(regex, '$2')).toBe('123.45');
  });

  it("should not change if no trailing zero", () => {
    expect('1.12'.replace(regex, '$2')).toBe('1.12');
    expect('123.123'.replace(regex, '$2')).toBe('123.123');
    expect('100'.replace(regex, '$2')).toBe('100');
    expect('1.'.replace(regex, '$2')).toBe('1.');
  });

  it("should not change if not at end", () => {
    expect('1.00abc'.replace(regex, '$2')).toBe('1.00abc');
    expect('1.10abc'.replace(regex, '$2')).toBe('1.10abc');
  });
});
