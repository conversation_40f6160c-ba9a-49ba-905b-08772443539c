import { jest, describe, expect, beforeEach, it } from "vitest";
import { mount } from "@vue/test-utils";
import { nextTick } from "vue";
import Clearoff from "../../../../src/pages/my/clearoff/index.vue";

describe("Clearoff.vue", () => {
  let wrapper;

  const mockData = {
    param: {
      userId: "user123",
    },
    supportSettlementCertificate: 1,
    supplierName: "测试供应商",
    supplierContact: "************",
    loanInfo: {
      data: {
        totalNum: 2,
        records: [
          {
            outOrderNo: "order1",
            applyDate: "2023-01-01",
            loanAmount: "1000000",
            clearTime: 1672502400000,
          },
          {
            outOrderNo: "order2",
            applyDate: "2023-01-02",
            loanAmount: "2000000",
            clearTime: 1672588800000,
          },
        ],
      },
    },
  };

  const mockCompanyInfo = {
    supplierId: 1,
    supplierName: "测试供应商",
    supplierContact: "************",
    settlementTimeExplanation: "结清证明说明",
    singleSettlementCertificateText: "单笔结清证明说明",
    allSettlementCertificateText: "全部结清证明说明",
    issueSettlementMethod: "0", // 0-多选 1-单选
    unableIssueSettlementText: "无法开具结清证明",
  };

  beforeEach(() => {
    // Mock window.matchMedia
    window.matchMedia = jest.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
    }));

    wrapper = mount(Clearoff, {
      global: {
        stubs: {
          "hnr-nav-bar": true,
          "hnr-loading": true,
          "hnr-button": true,
          "hnr-dialog": true,
          "hnr-field": true,
          "hnr-card": true,
          "hnr-checkbox": true,
          "icsvg-public-back-filled": true,
          "all-click-page": true,
          "more-click-page": true,
          "single-click-page": true,
          "card-cell": true,
        },
        mocks: {
          back: jest.fn(),
          goto: jest.fn(),
          callPhone: jest.fn(),
          showDialog: jest.fn(),
          report: jest.fn(),
          request: jest.fn().mockResolvedValue({
            code: 0,
            data: { sendStatus: true },
          }),
          getCurrentCpInfo: jest.fn().mockResolvedValue(mockCompanyInfo),
          getStore: jest.fn().mockImplementation((key) => {
            if (key === "U_CLEAROFF_PAGE_ID_user123") return "1";
            return "";
          }),
          setWithExactExpireUnit: jest.fn(),
          del: jest.fn(),
          setNetwork: jest.fn(),
          store: {
            initial: jest.fn(),
            dateFormat: jest.fn().mockImplementation((date) => date),
            addCommas: jest.fn().mockImplementation((amount) => (amount / 100).toFixed(2)),
          },
        },
      },
    });

    // 设置组件数据
    wrapper.vm.data = mockData;
    wrapper.vm.store.initial = jest.fn();
  });

  it("正确渲染初始页面结构", () => {
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("开具结清证明");
    expect(wrapper.find(".introduce div").text()).toBe("选择开具证明的内容");
    expect(wrapper.findAll("card-cell-stub").length).toBe(2);
  });

  it("点击全部开具按钮进入全部开具页面", async () => {
    await wrapper.findAll("hnr-button-stub")[0].trigger("click");
    expect(wrapper.vm.setLocalMethod).toHaveBeenCalledWith(2);
    expect(wrapper.vm.setLocalPageId).toHaveBeenCalledWith(2);
  });

  it("点击单笔开具按钮进入单笔开具页面", async () => {
    await wrapper.findAll("hnr-button-stub")[1].trigger("click");
    expect(wrapper.vm.setLocalMethod).toHaveBeenCalledWith(1);
    expect(wrapper.vm.setLocalPageId).toHaveBeenCalledWith(2);
  });

  it("无记录时显示提示弹窗", async () => {
    wrapper.vm.data.loanInfo.data.records = [];
    await nextTick();
    await wrapper.findAll("hnr-button-stub")[0].trigger("click");
    expect(wrapper.vm.show).toBe(true);
  });

  it("全部开具页面显示正确", async () => {
    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "2";
    await nextTick();

    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("全部借款结清证明");
    expect(wrapper.find("all-click-page-stub").exists()).toBe(true);
  });

  it("多选开具页面显示正确", async () => {
    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "1";
    await nextTick();

    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("单笔借款结清证明");
    expect(wrapper.find("more-click-page-stub").exists()).toBe(true);
  });

  it("单选开具页面显示正确", async () => {
    wrapper.vm.companyInfo.issueSettlementMethod = "1";
    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "1";
    await nextTick();

    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("单笔借款结清证明");
    expect(wrapper.find("single-click-page-stub").exists()).toBe(true);
  });

  it("邮箱格式验证", () => {
    expect(wrapper.vm.IsEmail("<EMAIL>")).toBe(true);
    expect(wrapper.vm.IsEmail("invalid-email")).toBe(false);
  });

  it("开具证明功能 - 成功", async () => {
    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "2";
    wrapper.vm.emailAddress = "<EMAIL>";
    wrapper.vm.loanListChecked = mockData.loanInfo.data.records;
    await nextTick();

    await wrapper.vm.openDocument();

    expect(wrapper.vm.request).toHaveBeenCalledWith(
      "/loan/api/settlement/send",
      {
        encryptedParams: {
          outOrderNos: ["order1", "order2"],
          email: "<EMAIL>",
        },
      },
      { mock: false, isHideOffline: true },
    );

    expect(wrapper.vm.successPage).toBe(true);
    expect(wrapper.vm.localPageId).toBe("3");
  });

  it("开具证明功能 - 失败", async () => {
    wrapper.vm.request.mockResolvedValueOnce({
      code: 1,
      message: "开具失败",
    });

    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "2";
    wrapper.vm.emailAddress = "<EMAIL>";
    wrapper.vm.loanListChecked = mockData.loanInfo.data.records;
    await nextTick();

    await wrapper.vm.openDocument();

    expect(wrapper.vm.showFailDialog).toBe(true);
    expect(wrapper.vm.showFailDialogText).toBe("开具失败");
  });

  it("网络未连接时显示设置网络", async () => {
    wrapper.vm.isOnLine = false;
    wrapper.vm.localPageId = "2";
    wrapper.vm.holdClickState = "2";
    wrapper.vm.emailAddress = "<EMAIL>";
    wrapper.vm.loanListChecked = mockData.loanInfo.data.records;
    await nextTick();

    await wrapper.vm.openDocument();

    expect(wrapper.vm.showFailDialog).toBe(true);
    expect(wrapper.vm.btnText).toBe("设置网络");
  });

  it("成功页面显示正确", async () => {
    wrapper.vm.localPageId = "3";
    await nextTick();

    expect(wrapper.find(".success-title").text()).toBe("申请成功");
    expect(wrapper.find(".bottom-success-main-button").exists()).toBe(true);
  });

  it("点击完成跳转到记录页面", async () => {
    wrapper.vm.localPageId = "3";
    await nextTick();

    await wrapper.find(".bottom-success-main-button").trigger("click");
    expect(wrapper.vm.goto).toHaveBeenCalledWith("/wallet-loan-web/pages/my/clearHistory");
  });

  it("不支持开具证明时显示正确", async () => {
    wrapper.vm.data.supportSettlementCertificate = 0;
    await nextTick();

    expect(wrapper.find(".content-line1").text()).toContain("无法直接为您提供开具结清证明服务");
    expect(wrapper.find(".call-supplier-contact").text()).toBe("************");
  });

  it("点击客服电话调用拨号功能", async () => {
    wrapper.vm.data.supportSettlementCertificate = 0;
    await nextTick();

    await wrapper.find(".call-supplier-contact").trigger("click");
    expect(wrapper.vm.callPhone).toHaveBeenCalledWith("************");
  });

  it("返回按钮功能 - 从详情返回列表", async () => {
    wrapper.vm.localPageId = "2";
    await nextTick();

    await wrapper.find("icsvg-public-back-filled-stub").trigger("click");
    expect(wrapper.vm.setLocalPageId).toHaveBeenCalledWith("1");
    expect(wrapper.vm.del).toHaveBeenCalled();
  });

  it("返回按钮功能 - 从成功页返回", async () => {
    wrapper.vm.localPageId = "3";
    await nextTick();

    await wrapper.find("icsvg-public-back-filled-stub").trigger("click");
    expect(wrapper.vm.setLocalPageId).toHaveBeenCalledWith("2");
    expect(wrapper.vm.isClearOff).toBe(true);
    expect(wrapper.vm.successPage).toBe(false);
  });

  it("组件卸载时清理存储", async () => {
    await wrapper.vm.onUnmounted();
    expect(wrapper.vm.del).toHaveBeenCalledWith("U_MY_CLEAROFF_ISSUCCESS_user123");
  });
});
