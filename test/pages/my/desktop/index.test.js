import { jest, describe, expect, beforeEach, it } from "vitest";
import { mount } from "@vue/test-utils";
import { nextTick } from "vue";
import Desktop from "../../../../src/pages/my/desktop/index.vue";

describe("Desktop.vue", () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(Desktop, {
      global: {
        stubs: {
          "hnr-nav-bar": true,
          "hnr-card": true,
          "hnr-cell": true,
          "hnr-button": true,
          "hnr-icon": true,
          "icsvg-public-back-filled": true,
        },
        mocks: {
          back: jest.fn(),
          goto: jest.fn(),
          showToast: jest.fn(),
          hasShortcut: jest.fn().mockResolvedValue(false),
          updateShortcut: jest.fn().mockResolvedValue(true),
          del: jest.fn(),
        },
      },
    });

    wrapper.vm.store = {
      initial: jest.fn(),
      loadSession: jest.fn(),
      param: {
        userId: "user123",
      },
      setAddShortcutDialogSilent: jest.fn(),
    };
  });

  it("正确渲染页面标题和基本结构", () => {
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("管理中心");
    expect(wrapper.find(".home-content").exists()).toBe(true);
  });

  it("渲染添加到桌面功能项", () => {
    const cell = wrapper.find("hnr-cell-stub");
    expect(cell.attributes("title")).toBe("添加到桌面");
    expect(wrapper.find("hnr-button-stub").text()).toBe("添加");
  });

  it("点击返回按钮触发back方法", async () => {
    await wrapper.find("icsvg-public-back-filled-stub").trigger("click");
    expect(wrapper.vm.back).toHaveBeenCalled();
  });

  it("点击添加到桌面功能项", async () => {
    const addBtn = wrapper.find(".home-content span");
    await addBtn.trigger("click");

    expect(wrapper.vm.hasShortcut).toHaveBeenCalled();
    await nextTick();

    expect(wrapper.vm.updateShortcut).toHaveBeenCalledWith(true);
    expect(wrapper.vm.del).toHaveBeenCalledWith("U_POPUP_STORE_user123");
    expect(wrapper.vm.store.setAddShortcutDialogSilent).toHaveBeenCalled();
    expect(wrapper.vm.goto).toHaveBeenCalledWith("/");
  });

  it("已存在快捷方式时显示提示", async () => {
    wrapper.vm.hasShortcut = jest.fn().mockResolvedValue(true);
    const addBtn = wrapper.find(".home-content span");
    await addBtn.trigger("click");

    expect(wrapper.vm.showToast).toHaveBeenCalledWith({
      message: "桌面已存在借钱服务快捷方式",
      position: "bottom",
    });
    expect(wrapper.vm.updateShortcut).not.toHaveBeenCalled();
  });

  it("计算属性deskSvg返回false", () => {
    expect(wrapper.vm.deskSvg).toBe(false);
  });

  it("页面加载时初始化数据", () => {
    expect(wrapper.vm.store.initial).toHaveBeenCalled();
    expect(wrapper.vm.store.loadSession).toHaveBeenCalled();
  });

  it("添加失败时不跳转", async () => {
    wrapper.vm.updateShortcut = jest.fn().mockResolvedValue(false);
    const addBtn = wrapper.find(".home-content span");
    await addBtn.trigger("click");

    expect(wrapper.vm.goto).not.toHaveBeenCalled();
  });

  it("渲染右侧图标", () => {
    const icons = wrapper.findAll("hnr-icon-stub");
    expect(icons.length).toBe(3); // 返回图标、更多图标、右侧箭头
    expect(icons[1].attributes("name")).toBe("/icsvg_public_toolbar_more12.svg");
    expect(icons[2].attributes("name")).toBe("/my/right.svg");
  });

  it("已添加状态显示正确", async () => {
    wrapper.vm.deskSvg = true;
    await nextTick();
    expect(wrapper.find("hnr-button-stub").text()).toBe("已添加");
  });
});
