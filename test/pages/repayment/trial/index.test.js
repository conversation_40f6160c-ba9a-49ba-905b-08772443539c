import { mount } from '@vue/test-utils'
import RepaymentTrial from '../../../../src/pages/repayment/trial/index.vue'
import { nextTick } from 'vue'

describe('RepaymentTrial.vue', () => {
  let wrapper

  const mockData = {
    param: {
      userId: 'user123',
      outOrderNo: 'order123',
      repayTerm: '1',
      allTerm: '12',
      isdelay: 'false',
      delaydays: '0',
      repayAmount: '1200000', // 12000.00元
      RepayDate: '2023-12-31',
      random: 'true'
    },
    trialinfo: {
      totalAmount: '1200000',
      totalPrincipal: '1000000',
      totalInterest: '200000',
      totalServiceFee: '0',
      totalPenalty: '0',
      totalDiscount: '10000',
      reductionAmountDesc: '优惠说明'
    },
    earlyRepayDays: 5,
    repayday: '2023-12-31',
    btnloading: false,
    bankinfo: {
      bankCardId: 'card123',
      bankCardNo: '****1234'
    },
    showcards: false,
    repayViolateFeeExplanation: '服务费说明'
  }

  beforeEach(() => {
    wrapper = mount(RepaymentTrial, {
      global: {
        stubs: {
          'hnr-nav-bar': true,
          'hnr-cell-group': true,
          'hnr-divider': true,
          'hnr-button': true,
          'hnr-dialog': true,
          'hnr-loading': true,
          'hnr-chips': true,
          'hnr-bubble-tip': true,
          'hnr-icon': true,
          'icsvg-public-back-filled': true,
          'my-bank': true,
          'no-ssr': true
        },
        mocks: {
          $route: {
            query: {
              isdelay: 'false'
            }
          },
          showToast: jest.fn(),
          report: jest.fn(),
          request: jest.fn().mockResolvedValue({ code: 0, data: { status: 0, needResign: 'false', serialNo: 'serial123' } }),
          getCurrentCpInfo: jest.fn().mockResolvedValue({ repayViolateFeeExplanation: '服务费说明' })
        }
      }
    })

    // 设置组件数据
    wrapper.vm.data = mockData
    wrapper.vm.store = {
      initial: jest.fn()
    }
  })

  it('正确渲染页面标题和基本结构', () => {
    expect(wrapper.find('hnr-nav-bar-stub').attributes('title')).toBe('还款明细')
    expect(wrapper.find('.infotitle1').text()).toContain('1/12')
    expect(wrapper.find('.infotitle2').text()).toBe('￥12,000.00')
  })

  it('正常还款期数显示正确', async () => {
    // 测试正常还款显示
    expect(wrapper.find('.infotitle1').text()).toContain('1/12')
    expect(wrapper.find('.infotitle3').text()).toContain('提前5天还款')
    
    // 测试逾期还款显示
    wrapper.vm.data.param.isdelay = 'true'
    wrapper.vm.data.param.delaydays = '3'
    await nextTick()
    expect(wrapper.find('.infotitle4').text()).toContain('已逾期3天')
    expect(wrapper.find('.infotitle4').classes()).toContain('infotitle4')
  })

  it('还款明细显示正确', () => {
    const cells = wrapper.findAll('.cellLine')
    expect(cells.length).toBe(5) // 本金、利息、服务费、优惠券
    
    expect(cells[0].find('.cellLeft').text()).toBe('还款本金')
    expect(cells[0].find('.cellRight').text()).toBe('￥10,000.00')
    
    expect(cells[1].find('.cellLeft').text()).toBe('利息')
    expect(cells[1].find('.cellRight').text()).toContain('￥2,000.00')
    expect(cells[1].find('hnr-chips-stub').exists()).toBe(true)
    
    expect(cells[3].find('.cellLeft').text()).toBe('优惠券')
    expect(cells[3].find('.cellRight').text()).toBe('-￥100.00')
  })

  it('服务费说明气泡显示正确', async () => {
    wrapper.vm.showViolateFeeBubble = true
    await nextTick()
    
    const serviceFeeCell = wrapper.findAll('.cellLine')[2]
    expect(serviceFeeCell.find('.cellLeft').text()).toContain('服务费')
    expect(serviceFeeCell.find('img').exists()).toBe(true)
    expect(wrapper.vm.data.repayViolateFeeExplanation).toBe('服务费说明')
  })

  it('银行卡选择功能正确', async () => {
    const bankInfo = { 
      bankCardId: 'newCard123', 
      bankCardNo: '****5678' 
    }
    wrapper.vm.bankinfo(bankInfo)
    await nextTick()
    
    expect(wrapper.vm.data.bankinfo).toEqual(bankInfo)
  })

  it('确认还款按钮状态正确', () => {
    const button = wrapper.find('.subbtn')
    expect(button.text()).toContain('确认还款（￥12,000.00）')
    expect(button.attributes('disabled')).toBeUndefined()
    
    // 测试禁用状态
    wrapper.vm.data.bankinfo.bankCardId = null
    wrapper.vm.data.istoday = true
    await nextTick()
    expect(button.attributes('disabled')).toBeDefined()
  })

  it('提交还款逻辑正确', async () => {
    // 模拟API成功响应
    wrapper.vm.submit()
    await nextTick()
    
    expect(wrapper.vm.data.btnloading).toBe(true)
    expect(wrapper.vm.request).toHaveBeenCalledWith('/loan/api/repay/v2/sendVerifyCode', {
      totalAmount: '1200000',
      bankCardId: 'card123',
      outOrderNo: 'order123'
    })
    
    // 验证跳转参数
    await new Promise(resolve => setTimeout(resolve, 0))
    expect(wrapper.vm.goto).toBeUndefined() // 需要mock goto方法
  })

  it('处理API错误情况', async () => {
    // 模拟API错误响应
    wrapper.vm.request.mockResolvedValueOnce({ 
      code: 1, 
      message: '操作频繁' 
    })
    
    await wrapper.vm.submit()
    expect(wrapper.vm.data.btnloading).toBe(false)
    expect(wrapper.vm.showToast).toHaveBeenCalledWith({
      message: '操作频繁',
      position: 'bottom'
    })
  })

  it('页面加载时清理存储', async () => {
    await wrapper.vm.onMounted()
    expect(wrapper.vm.del).toBeUndefined() // 需要mock del方法
    expect(wrapper.vm.report).toHaveBeenCalledWith('wallet_page_view', {
      page_name: 'repayment_trial_page'
    })
  })

  it('返回事件处理正确', () => {
    wrapper.find('icsvg-public-back-filled-stub').vm.$emit('click')
    expect(wrapper.vm.back).toBeUndefined() // 需要mock back方法
  })

  it('加载状态显示正确', async () => {
    wrapper.vm.data.btnloading = true
    await nextTick()
    
    const dialog = wrapper.find('hnr-dialog-stub')
    expect(dialog.attributes('show')).toBeDefined()
    expect(wrapper.find('.loading-text').text()).toBe('正在提交')
  })
})
