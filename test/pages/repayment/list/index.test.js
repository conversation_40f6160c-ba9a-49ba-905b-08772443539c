import { mount } from "@vue/test-utils";
import RepaymentList from "../../../../src/index.vue";
import { nextTick } from "vue";

describe("RepaymentList.vue", () => {
  let wrapper;

  const mockData = {
    param: { type: "0", from: "normal" },
    loanlist: [
      {
        outOrderNo: "order123",
        paydate: "2023-01-15",
        loanAmount: 1000000,
        duerepay: 1200000,
        outstandingAmt: "2023-02-15",
        loanOverDueDays: 0,
        repayPlanTerms: [{ termNo: 1 }],
        currentTerm: 1,
      },
    ],
    loaninfo: [
      {
        outOrderNo: "order456",
        applyDate: "2023-01-10",
        loanAmount: 500000,
      },
    ],
    totalAmount: 1200000,
    loading: false,
    errorStatus: false,
    deductionEarlyMorning: 1,
    currentTermDisplay: true,
  };

  beforeEach(() => {
    wrapper = mount(RepaymentList, {
      global: {
        stubs: {
          "hnr-nav-bar": true,
          "hnr-segmented-picker": true,
          "hnr-segmented-picker-item": true,
          "hnr-chips": true,
          "hnr-button": true,
          "hnr-list": true,
          "hnr-icon": true,
          "no-ssr": true,
          "reloading-component": true,
          "resource-position": true,
          "icsvg-public-back-filled": true,
        },
      },
    });

    // 设置组件数据
    wrapper.vm.data = mockData;
    wrapper.vm.store = {
      signStatus: 0,
      isOnResume: false,
      signResourceInfo: {},
      clearRecordPageInfo: {
        loading: false,
        finished: false,
      },
      queryclear: jest.fn(),
      initial: jest.fn(),
      getResourcePositionInfo: jest.fn(),
      getOrderStatus: jest.fn().mockReturnValue("未结清"),
      handleTodoRepaymentReport: jest.fn(),
    };
  });

  it("正确渲染页面标题和基本结构", () => {
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("我的借款");
    expect(wrapper.find(".segmented-box").exists()).toBe(true);
  });

  it("切换标签页功能正常", async () => {
    const tabs = wrapper.findAll("hnr-segmented-picker-item-stub");
    expect(tabs.length).toBe(2);

    // 模拟点击"已结清"标签
    await wrapper.vm.onClickTab(1);
    expect(wrapper.vm.tabActive).toBe(1);
    expect(wrapper.vm.store.queryclear).toHaveBeenCalled();
  });

  it("正确渲染待还款列表", () => {
    wrapper.vm.tabActive = 0;
    expect(wrapper.find(".infotitle1").text()).toContain("共1笔借款");
    expect(wrapper.find(".infotitle2").text()).toContain("12,000.00");
    expect(wrapper.findAll(".card").length).toBe(1);
  });

  it("正确渲染已结清列表", async () => {
    wrapper.vm.tabActive = 1;
    await nextTick();
    expect(wrapper.findAll(".card").length).toBe(1);
    expect(wrapper.find(".title1").text()).toContain("2023-01-10 借款");
  });

  it("金额格式化显示正确", () => {
    const formatted = wrapper.vm.amountNumberFormat(1234567);
    expect(formatted).toBe("12,345.67");
  });

  it("导航按钮功能正常", () => {
    // 测试返回按钮
    wrapper.vm.onClickLeft();
    expect(wrapper.vm.data.param.type).toBe("0");

    // 测试跳转到详情页
    const gotoDetailSpy = jest.spyOn(wrapper.vm, "gotoDetail");
    wrapper.vm.gotoDetail("order123", "未结清");
    expect(gotoDetailSpy).toHaveBeenCalledWith("order123", "未结清");
  });

  it("网络状态显示正确", async () => {
    wrapper.vm.isOnLine = false;
    await nextTick();
    expect(wrapper.find(".emptyct").text()).toBe("网络未连接");
  });

  it("无数据状态显示正确", async () => {
    wrapper.vm.data.loanlist = [];
    wrapper.vm.data.loaninfo = [];
    await nextTick();
    expect(wrapper.find(".emptyct").text()).toBe("暂无待还借款");

    wrapper.vm.tabActive = 1;
    await nextTick();
    expect(wrapper.find(".emptyct").text()).toBe("暂无已结清借款");
  });

  it("逾期状态显示正确", async () => {
    wrapper.vm.data.loanlist[0].loanOverDueDays = 5;
    wrapper.vm.data.loanlist[0].loanOverDueAmount = 100000;
    await nextTick();
    expect(wrapper.find(".overwarn").text()).toContain("存在逾期金额");
  });

  it("自动还款提示显示正确", () => {
    expect(wrapper.find(".container2").text()).toContain("凌晨");
  });
});
