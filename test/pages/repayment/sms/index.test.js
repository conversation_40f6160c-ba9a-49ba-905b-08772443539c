import { mount } from "@vue/test-utils";
import RepaymentSms from "../../../../src/pages/repayment/sms/index.vue";
import { nextTick } from "vue";

describe("RepaymentSms.vue", () => {
  let wrapper;

  const mockData = {
    param: {
      userId: "user123",
      bankCardId: "card123",
      bankCardNo: "****1234",
      totalAmount: "1200000", // 12000.00元
      repayType: "1",
      needResign: "false",
      repayPart: "false",
      outOrderNo: "order123",
      serialNo: "serial123",
      couponNo: "coupon123",
      reductionAmount: "10000",
    },
    phonenumber: "138****1234",
    countdown: 0,
    smscode: "",
    smserror: "",
    issend: false,
    sendmessage: "请输入发送至手机号 138****1234的验证码",
    queryresult: {
      repayStatus: undefined,
      repayResult: "",
    },
  };

  beforeEach(() => {
    wrapper = mount(RepaymentSms, {
      global: {
        stubs: {
          "hnr-nav-bar": true,
          "hnr-field": true,
          "hnr-button": true,
          "hnr-card": true,
          "hnr-divider": true,
          "hnr-loading": true,
          "icsvg-public-back-filled": true,
          "credit-adjust-incentive-card": true,
          "coupon-incentive-card": true,
        },
        mocks: {
          $route: {
            query: {
              needResign: "false",
            },
          },
        },
      },
    });

    // 设置组件数据
    wrapper.vm.data = mockData;
    wrapper.vm.store = {
      initial: jest.fn(),
      sendVerifyCode: jest.fn(),
      Polling: jest.fn(),
      isLoading: false,
      ignoreSmsCode: false,
      queryresult: {},
      remainLimit: 50000,
      nextamt: 100000,
      lastdate: "2023-12-31",
      changeType: 0,
      usableCoupons: [],
      creditInfo: {},
      loanOrderStatus: {},
      totalAvailableLimit: 50000,
    };
  });

  it("正确渲染页面标题和基本结构", () => {
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("短信验证");
    expect(wrapper.find(".head1").text()).toBe("输入短信验证码");
    expect(wrapper.find(".head2").text()).toContain("138****1234");
  });

  it("验证码发送逻辑正确", async () => {
    // 测试获取验证码按钮
    wrapper.vm.startCountdown();
    expect(wrapper.vm.store.sendVerifyCode).toHaveBeenCalled();
    expect(wrapper.vm.data.countdown).toBe(59);
    expect(wrapper.vm.data.sendmessage).toContain("已发送至手机号");
  });

  it("验证码输入和提交逻辑正确", async () => {
    // 模拟输入验证码
    wrapper.vm.data.smscode = "1234";
    await wrapper.vm.submit();

    // 验证API调用
    expect(wrapper.vm.request).toBeUndefined(); // 需要mock request方法

    // 验证状态更新
    expect(wrapper.vm.data.issend).toBe(true);
    expect(wrapper.vm.store.Polling).toHaveBeenCalled();
  });

  it("还款状态显示正确", async () => {
    // 测试成功状态
    wrapper.vm.data.issend = true;
    wrapper.vm.data.queryresult.repayStatus = 2; // 成功
    await nextTick();
    expect(wrapper.find(".suchead1").text()).toBe("还款成功");

    // 测试处理中状态
    wrapper.vm.data.queryresult.repayStatus = 1; // 处理中
    await nextTick();
    expect(wrapper.find(".suchead1").text()).toBe("还款处理中");

    // 测试失败状态
    wrapper.vm.data.queryresult.repayStatus = 99; // 失败
    wrapper.vm.data.queryresult.repayResult = "还款失败原因";
    await nextTick();
    expect(wrapper.find(".suchead1").text()).toBe("还款失败");
    expect(wrapper.find(".errmessage").text()).toContain("还款失败原因");
  });

  it("下期还款信息显示正确", async () => {
    wrapper.vm.data.issend = true;
    wrapper.vm.data.queryresult.repayStatus = 2; // 成功
    await nextTick();

    expect(wrapper.find(".cellLine1 .cellLeft").text()).toBe("下期还款金额");
    expect(wrapper.find(".cellLine1 .cellRight").text()).toBe("￥1,000.00");
    expect(wrapper.find(".cellLine2 .cellLeft").text()).toBe("下期还款时间");
    expect(wrapper.find(".cellLine2 .cellRight").text()).toBe("2023-12-31");
  });

  it("完成和再借一笔按钮显示正确", async () => {
    wrapper.vm.data.issend = true;
    wrapper.vm.data.queryresult.repayStatus = 2; // 成功
    await nextTick();

    const buttons = wrapper.findAll(".subbtn");
    expect(buttons.length).toBe(2);
    expect(buttons[0].text()).toBe("完成");
    expect(buttons[1].text()).toBe("再借一笔");
  });

  it("调额降息卡片显示逻辑正确", async () => {
    wrapper.vm.data.issend = true;
    wrapper.vm.data.queryresult.repayStatus = 2; // 成功
    wrapper.vm.store.changeType = 1; // 有调额
    await nextTick();

    expect(wrapper.find("credit-adjust-incentive-card-stub").exists()).toBe(true);
  });

  it("优惠券卡片显示逻辑正确", async () => {
    wrapper.vm.data.issend = true;
    wrapper.vm.data.queryresult.repayStatus = 2; // 成功
    wrapper.vm.store.usableCoupons = [{ id: 1 }]; // 有可用优惠券
    await nextTick();

    expect(wrapper.find("coupon-incentive-card-stub").exists()).toBe(true);
  });

  it("返回首页逻辑正确", () => {
    // 测试成功状态返回
    wrapper.vm.data.queryresult.repayStatus = 2;
    wrapper.vm.gotohome();
    expect(wrapper.vm.data.issend).toBe(true);

    // 测试失败状态返回
    wrapper.vm.data.queryresult.repayStatus = 99;
    wrapper.vm.gotohome();
    expect(wrapper.vm.data.issend).toBe(false);
  });

  it("倒计时功能正确", () => {
    // 测试倒计时开始
    wrapper.vm.getLastCountdown();
    expect(wrapper.vm.data.countdown).toBe(59);

    // 测试倒计时结束
    wrapper.vm.data.countdown = 1;
    wrapper.vm.getLastCountdown();
    setTimeout(() => {
      expect(wrapper.vm.data.countdown).toBe(0);
    }, 1000);
  });
});
