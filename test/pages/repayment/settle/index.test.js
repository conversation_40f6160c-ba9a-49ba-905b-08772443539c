import { mount } from "@vue/test-utils";
import RepaymentSettle from "../../../../src/pages/repayment/settle/index.vue";
import { nextTick } from "vue";

describe("RepaymentSettle.vue", () => {
  let wrapper;

  const mockData = {
    param: {
      isdelay: "false",
      userId: "user123",
      outOrderNo: "order123",
      period: "3期",
      delaydays: 0,
      repayAmount: "1000000",
    },
    wholeamt: 1200000, // 12000.00元
    trialinfo: {
      totalAmount: 1200000,
      totalPrincipal: 1000000,
      totalInterest: 200000,
      totalViolateFee: 50000,
      totalServiceFee: 30000,
      totalDiscount: 10000,
    },
    repayamt: "",
    clickflag: false,
    termloading: false,
    amterror: "",
    isall: false,
    btnloading: false,
    bankcardid: "card123",
    bankCardNo: "****1234",
  };

  beforeEach(() => {
    wrapper = mount(RepaymentSettle, {
      global: {
        stubs: {
          "hnr-nav-bar": true,
          "hnr-field": true,
          "hnr-button": true,
          "hnr-cell-group": true,
          "hnr-divider": true,
          "hnr-bubble-tip": true,
          "hnr-icon": true,
          "hnr-dialog": true,
          "hnr-loading": true,
          "icsvg-public-back-filled": true,
          "my-bank": true,
        },
        mocks: {
          $route: {
            query: {
              isdelay: "false",
            },
          },
        },
      },
    });

    // 设置组件数据
    wrapper.vm.data = mockData;
    wrapper.vm.store = {
      prepayTrial: jest.fn(),
      repayall: jest.fn(),
      loanOrderDetail: {
        repayMethod: 4,
      },
    };
  });

  it("正确渲染页面标题和基本结构", () => {
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("提前还款");
    expect(wrapper.find(".describe").exists()).toBe(true);
  });

  it("金额计算和显示正确", () => {
    expect(wrapper.vm.showamt).toBe("12000.00");
    expect(wrapper.vm.realamt).toBe("（￥12,000.00）");
  });

  it("输入金额验证逻辑正确", async () => {
    // 测试无效金额
    wrapper.vm.data.repayamt = "abc";
    await wrapper.vm.custinput();
    expect(wrapper.vm.data.amterror).toBe("金额有误");

    // 测试金额大于待还金额
    wrapper.vm.data.repayamt = "13000";
    await wrapper.vm.custinput();
    expect(wrapper.vm.data.amterror).toBe("还款金额不能大于待还金额");

    // 测试有效金额
    wrapper.vm.data.repayamt = "5000";
    await wrapper.vm.custinput();
    expect(wrapper.vm.data.amterror).toBe("");
  });

  it('"还全部"按钮功能正常', async () => {
    await wrapper.vm.repayall();
    expect(wrapper.vm.data.repayamt).toBe("12000.00");
    expect(wrapper.vm.data.clickflag).toBe(true);
    expect(wrapper.vm.store.repayall).toHaveBeenCalled();
  });

  it("确认还款按钮状态正确", () => {
    // 初始状态应为禁用
    expect(wrapper.vm.comfireButtonDisabled).toBe(true);

    // 输入有效金额后应启用
    wrapper.vm.data.repayamt = "5000";
    expect(wrapper.vm.comfireButtonDisabled).toBe(false);

    // 有错误时应禁用
    wrapper.vm.data.amterror = "金额有误";
    expect(wrapper.vm.comfireButtonDisabled).toBe(true);
  });

  it("提交还款逻辑正确", async () => {
    // 模拟有效输入
    wrapper.vm.data.repayamt = "5000";
    wrapper.vm.data.bankcardid = "card123";

    // 模拟API响应
    const mockResponse = { code: 0, data: { status: 0, serialNo: "123", needResign: false } };
    wrapper.vm.request = jest.fn().mockResolvedValue(mockResponse);

    await wrapper.vm.submit();

    // 验证API调用
    expect(wrapper.vm.request).toHaveBeenCalledWith("/loan/api/repay/v2/sendVerifyCode", {
      totalAmount: 1200000,
      bankCardId: "card123",
      outOrderNo: "order123",
    });

    // 验证状态更新
    expect(wrapper.vm.data.btnloading).toBe(false);
  });

  it("逾期还款状态显示正确", async () => {
    wrapper.vm.data.param.isdelay = "true";
    wrapper.vm.data.param.delaydays = 5;
    await nextTick();

    expect(wrapper.find(".describe").text()).toContain("已逾期5天");
    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("还部分逾期");
  });

  it("全部结清状态显示正确", async () => {
    wrapper.vm.data.isall = true;
    await nextTick();

    expect(wrapper.find("hnr-nav-bar-stub").attributes("title")).toBe("全部结清");
    expect(wrapper.find(".infotitle2").text()).toContain("12,000.00");
  });

  it("金额格式化函数正确", () => {
    const formatted = wrapper.vm.amountNumberFormat("1234.56");
    expect(formatted).toBe("1,234.56");
  });

  it("输入过滤函数正确", () => {
    expect(wrapper.vm.checkstrlong("123.456")).toBe("123.45");
    expect(wrapper.vm.checkstrlong("abc")).toBe("");
  });
});
