import { mount } from "@vue/test-utils";
import RepaymentDetail from "../../../../src/pages/repayment/detail/index.vue";
import { nextTick } from "vue";

describe("RepaymentDetail.vue", () => {
  let wrapper;

  const mockData = {
    isLoading: false,
    isOverdue: false,
    overdueDays: 0,
    loanDate: "20230115",
    loanPrincipal: 1000000, // 代表10000.00元
    status: 2,
    totalInstallmentAmount: 1200000,
    installmentInterest: 200000,
    serviceFee: 50000,
    repayPlanTerms: [
      {
        termNo: 1,
        termStatus: 1,
        termAmount: 100000,
        termPrincipal: 80000,
        termInterest: 20000,
        shouldRepayDate: "20230215",
        paidTermAmount: 100000,
      },
      {
        termNo: 2,
        termStatus: 2,
        termAmount: 100000,
        termPrincipal: 80000,
        termInterest: 20000,
        shouldRepayDate: "20230315",
      },
    ],
    agreementList: [
      {
        contractId: "1",
        contractName: "借款合同",
      },
    ],
  };

  beforeEach(() => {
    wrapper = mount(RepaymentDetail, {
      global: {
        stubs: {
          "hnr-loading": true,
          "hnr-nav-bar": true,
          "hnr-notify": true,
          "hnr-divider": true,
          "hnr-bubble-tip": true,
          "hnr-icon": true,
          "hnr-popup": true,
          "hnr-cell-group": true,
          "hnr-cell": true,
          "hnr-sub-title": true,
          "hnr-index-anchor": true,
          "no-ssr": true,
          "contract-detail-popup": true,
          "pdf-preview": true,
        },
      },
    });

    // 设置组件数据
    wrapper.vm.data = mockData;
  });

  it("正确渲染借款基本信息", () => {
    expect(wrapper.find(".detail-header-date").text()).toContain("2023年1月15日借款");
    expect(wrapper.find(".detail-header-money").text()).toBe("￥10,000.00");
    expect(wrapper.find(".item-right").text()).toBe("未结清");
  });

  it("正确格式化金额显示", () => {
    const addCommas = wrapper.vm.addCommas;
    expect(addCommas(100000)).toBe("1,000.00");
    expect(addCommas(1234567)).toBe("12,345.67");
  });

  it("正确计算罚息显示", () => {
    const getPenalty = wrapper.vm.getPenalty;
    expect(getPenalty({ termPrinPenalty: 100, termInterPenalty: 200 })).toBe(true);
    expect(getPenalty({ termPenalty: 300 })).toBe(true);
    expect(getPenalty({})).toBe(false);
  });

  it('切换"更多明细"显示状态', async () => {
    expect(wrapper.vm.data.isShowMore).toBe(false);
    await wrapper.find(".item-right.light-color").trigger("click");
    expect(wrapper.vm.data.isShowMore).toBe(true);
  });

  it("正确渲染还款计划", () => {
    const planItems = wrapper.findAll(".plan-item");
    expect(planItems.length).toBe(2);
    expect(planItems[0].find(".plan-item-left-top-termNo").text()).toBe("1/2期");
    expect(planItems[0].find(".plan-label-end").text()).toBe("已还清");
  });

  it("逾期状态显示正确", async () => {
    wrapper.vm.data.isOverdue = true;
    wrapper.vm.data.overdueDays = 5;
    await nextTick();
    expect(wrapper.find(".item-right-overdue").text()).toContain("已逾期 5 天");
  });

  it("合同弹窗交互正常", async () => {
    await wrapper.vm.showContractPopup();
    expect(wrapper.vm.data.isShowContract).toBe(true);

    await wrapper.vm.closeContractPopup();
    expect(wrapper.vm.data.isShowContract).toBe(false);
  });

  it("正确解析合同URL类型", async () => {
    await wrapper.vm.parseContractUrl("http://example.com/contract.pdf");
    expect(wrapper.vm.contracttype).toBe(1);
  });
});
