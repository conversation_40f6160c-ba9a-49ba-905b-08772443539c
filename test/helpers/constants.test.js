import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  DEBOUNCE_OPTIONS,
  DEBOUNCE_WAIT_MILLISECONDS,
  USER_PROTOCAL_CODE,
  PRIVACY_PROTOCAL_CODE,
  STORE_VERSION_CODE,
  MATERIAL_VERSION_CODE,
  LOAN_APPLY_ERROR_CODE_MAP,
  LOAN_VERIFY_ERROR_CODE_MAP,
  domainMap,
  getUserProtocalUrl,
  getPrivacyProtocalUrl,
  getIntroductionPageUrl,
  getCouponUseRuleUrl,
  versionCodeMapUrl,
  urlMap,
  getCurrentStore,
  setCurrentStore,
  getAxiosInstance,
  setAxiosInstance,
  supplierIdMap,
} from "../../src/helpers/constants";

describe("constants", () => {
  describe("static constants", () => {
    it("should have correct DEBOUNCE_OPTIONS", () => {
      expect(DEBOUNCE_OPTIONS).toEqual({
        leading: true,
        trailing: false,
        maxWait: 3000,
      });
    });

    it("should have correct DEBOUNCE_WAIT_MILLISECONDS", () => {
      expect(DEBOUNCE_WAIT_MILLISECONDS).toBe(1000);
    });

    it("should have correct protocol codes", () => {
      expect(USER_PROTOCAL_CODE).toBe(1493);
      expect(PRIVACY_PROTOCAL_CODE).toBe(1492);
    });

    it("should have correct version codes", () => {
      expect(STORE_VERSION_CODE).toBe("905");
      expect(MATERIAL_VERSION_CODE).toBe("908");
    });

    it("should have correct error code maps", () => {
      expect(LOAN_APPLY_ERROR_CODE_MAP).toEqual({
        LOAN_EXPIRED: 171013,
      });
      expect(LOAN_VERIFY_ERROR_CODE_MAP).toEqual({
        TRIAL_EXPIRED: 171013,
      });
    });

    it("should have correct urlMap", () => {
      expect(urlMap).toEqual({
        loan_index: "/wallet-loan-web/pages/index",
        credit_realname: "/wallet-loan-web/pages/credit/realname",
        credit_realname_activity: "/wallet-loan-web/pages/credit/realname",
        credit_sign: "/wallet-loan-web/pages/credit/sign",
        credit_result: "/wallet-loan-web/pages/credit/result",
        loan_calc: "/wallet-loan-web/pages/loan/calc",
        loan_sign: "/wallet-loan-web/pages/loan/sign",
        loan_infoform: "/wallet-loan-web/pages/loan/infoform",
        loan_sms: "/wallet-loan-web/pages/loan/sms",
        sdk_credit_face: "/wallet-loan-web/pages/credit/detection",
        sdk_loan_face_confirm: "/wallet-loan-web/pages/loan/detection",
      });
    });

    it("should have correct supplierIdMap", () => {
      expect(supplierIdMap).toEqual({
        dxm: 1,
        jd: 5,
        lx: 6,
        qf: 7,
        ppd: 8,
      });
    });
  });

  describe("URL generation functions", () => {
    beforeEach(() => {
      // Mock window.location
      delete window.location;
      window.location = { host: "wallet-web-drcn.cloud.honor.com" };
    });

    describe("getUserProtocalUrl", () => {
      it("should return production URL for production domain", () => {
        window.location.host = "wallet-web-drcn.cloud.honor.com";
        const result = getUserProtocalUrl();
        expect(result).toBe(
          "https://agreement.itsec.honor.com/asm/agrFile/getHtmlFile?agrNo=1493&country=cn&branchId=0&langCode=zh-cn",
        );
      });

      it("should return test URL for test domain", () => {
        window.location.host = "wallet-web-test-drcn.cloud.honor.com";
        const result = getUserProtocalUrl();
        expect(result).toBe(
          "https://agreement-sit.test.honor.com/asm/agrFile/getHtmlFile?agrNo=1493&country=cn&branchId=0&langCode=zh-cn",
        );
      });

      it("should include theme parameter when specified", () => {
        const result = getUserProtocalUrl("dark");
        expect(result).toContain("&themeName=dark");
      });
    });

    describe("getPrivacyProtocalUrl", () => {
      it("should return production URL for production domain", () => {
        window.location.host = "wallet-web-drcn.cloud.honor.com";
        const result = getPrivacyProtocalUrl();
        expect(result).toBe(
          "https://agreement.itsec.honor.com/asm/agrFile/getHtmlFile?agrNo=1492&country=cn&branchId=0&langCode=zh-cn",
        );
      });

      it("should return test URL for test domain", () => {
        window.location.host = "wallet-web-test-drcn.cloud.honor.com";
        const result = getPrivacyProtocalUrl();
        expect(result).toBe(
          "https://agreement-sit.test.honor.com/asm/agrFile/getHtmlFile?agrNo=1492&country=cn&branchId=0&langCode=zh-cn",
        );
      });
    });

    describe("getIntroductionPageUrl", () => {
      it("should return production URL for production domain", () => {
        window.location.host = "wallet-web-drcn.cloud.honor.com";
        const result = getIntroductionPageUrl();
        expect(result).toBe(
          "https://contentplatform-drcn.hihonorcdn.com/honorWallet/loan-productIntroduce/908/index.html",
        );
      });

      it("should return test URL for test domain", () => {
        window.location.host = "wallet-web-test-drcn.cloud.honor.com";
        const result = getIntroductionPageUrl();
        expect(result).toBe(
          "https://content-test-drcn.hihonorcdn.com/honorWallet/test/loan-productIntroduce/908/index.html",
        );
      });

      it("should return development URL for development domain", () => {
        window.location.host = "wallet-web-dev-drcn.cloud.honor.com";
        const result = getIntroductionPageUrl();
        expect(result).toBe(
          "https://content-test-drcn.hihonorcdn.com/honorWallet/dev/loan-productIntroduce/908/index.html",
        );
      });
    });

    describe("getCouponUseRuleUrl", () => {
      it("should return production URL for production domain", () => {
        window.location.host = "wallet-web-drcn.cloud.honor.com";
        const result = getCouponUseRuleUrl();
        expect(result).toBe(
          "https://contentplatform-drcn.hihonorcdn.com/honorWallet/loan-couponUseRule/908/index.html",
        );
      });

      it("should return test URL for test domain", () => {
        window.location.host = "wallet-web-test-drcn.cloud.honor.com";
        const result = getCouponUseRuleUrl();
        expect(result).toBe(
          "https://content-test-drcn.hihonorcdn.com/honorWallet/test/loan-couponUseRule/908/index.html",
        );
      });
    });
  });

  describe("versionCodeMapUrl", () => {
    it("should replace version code placeholder in URL", () => {
      const url = "https://example.com/path/${versionCode}/file.html";
      const result = versionCodeMapUrl(url);
      expect(result).toBe("https://example.com/path/908/file.html");
    });

    it("should insert version code when no placeholder exists", () => {
      const url = "https://example.com/path/file.html";
      const result = versionCodeMapUrl(url);
      expect(result).toBe("https://example.com/path/908/file.html");
    });
  });

  describe("store management functions", () => {
    it("getCurrentStore should return window.__INITIAL_STATE__ when available", () => {
      const mockState = { param: { userId: "test-user" } };
      window.__INITIAL_STATE__ = mockState;
      expect(getCurrentStore()).toEqual(mockState);
      delete window.__INITIAL_STATE__;
    });

    it("getCurrentStore should return currentStore when window.__INITIAL_STATE__ is not available", () => {
      const mockState = { param: { userId: "test-user" } };
      setCurrentStore(mockState);
      expect(getCurrentStore()).toEqual(mockState);
    });

    it("setCurrentStore should update currentStore", () => {
      const mockState = { param: { userId: "test-user" } };
      setCurrentStore(mockState);
      expect(getCurrentStore()).toEqual(mockState);
    });
  });

  describe("axios instance management", () => {
    it("getAxiosInstance should return null by default", () => {
      expect(getAxiosInstance()).toBeNull();
    });

    it("setAxiosInstance should update axios instance", () => {
      const mockInstance = { get: vi.fn(), post: vi.fn() };
      setAxiosInstance(mockInstance);
      expect(getAxiosInstance()).toEqual(mockInstance);
    });
  });

  describe("domainMap", () => {
    it("should correctly map domains to environments", () => {
      expect(domainMap).toEqual({
        "card-dev-drcn.wallet.hihonorcloud.com": "development",
        "wallet-web-dev-drcn.cloud.honor.com": "development",
        "card-test-drcn.wallet.hihonorcloud.com": "test",
        "wallet-web-test-drcn.cloud.honor.com": "test",
        "card-sit-drcn.wallet.hihonorcloud.com": "test",
        "wallet-card-uat-drcn.hihonorcloud.com": "production",
        "wallet-api-uat-drcn.cloud.honor.com": "production",
        "card-drcn.wallet.hihonorcloud.com": "production",
        "wallet-web-uat-drcn.hihonorcloud.com": "production",
        "wallet-web-drcn.cloud.honor.com": "production",
      });
    });
  });
});
