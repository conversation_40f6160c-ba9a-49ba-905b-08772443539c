import { describe, it, expect, vi, beforeEach } from "vitest";
import * as hnr from "@hihonor/hnr/dist/hnr.es.min";
import { loanNext, creditNext } from "../../src/helpers/next";
import * as nativeBridge from "../../src/helpers/native-bridge";
import * as storage from "../../src/helpers/storage";
import * as requestUtils from "../../src/helpers/utils";
import * as constants from "../../src/helpers/constants";

// Mock all dependencies
vi.mock("@hihonor/hnr/dist/hnr.es.min");
vi.mock("../../src/helpers/native-bridge");
vi.mock("../../src/helpers/storage");
vi.mock("../../src/helpers/utils");
vi.mock("../../src/helpers/configUtils");
vi.mock("../../src/helpers/retention-dialog-utils");
vi.mock("../../src/helpers/constants");

describe("next.js", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    window.nextLoadingStatus = vi.fn();
  });

  describe("loanNext", () => {
    it("should handle loanStorage with applyStatus", async () => {
      const userId = "test-user";
      const mockStorage = {
        applyStatus: "SUCCESS",
        applyMessage: "Loan approved",
      };
      storage.getStore.mockReturnValue(mockStorage);
      const callback = vi.fn();

      await loanNext(userId, callback);

      expect(storage.del).toHaveBeenCalledWith(`U_LOAN_PROCESS_${userId}`);
      expect(nativeBridge.gotoWithOptions).toHaveBeenCalledWith({
        url: "/wallet-loan-web/pages/loan/result?applyCode=SUCCESS&applyMessage=Loan approved",
        allowBack: false,
      });
      expect(callback).toHaveBeenCalled();
    });

    it("should handle FACE_CHECK step when user is not authenticated", async () => {
      const userId = "test-user";
      const mockStorage = {
        steps: ["FACE_CHECK", "AGREEMENT"],
        [constants.urlMap.loan_sign]: "/loan/sign",
      };
      storage.getStore.mockImplementation((key) => {
        if (key === `U_LOAN_PROCESS_${userId}`) return mockStorage;
        return null;
      });
      requestUtils.request.mockResolvedValue({ data: { status: 0 } });

      await loanNext(userId);

      expect(hnr.showConfirmDialog).toHaveBeenCalled();
      expect(nativeBridge.goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
    });

    it("should handle successful loan application", async () => {
      const userId = "test-user";
      const applyNo = "APP-123";
      const mockStorage = {
        steps: ["INFOS", "SMS"],
        infos: { marriage: "已婚", education: "本科" },
        applyNo,
        dictMap: {
          marriage: [{ name: "marriage", value: "已婚" }],
          education: [{ name: "education", value: "本科" }],
        },
      };
      storage.getStore.mockImplementation((key) => {
        if (key === `U_LOAN_PROCESS_${userId}`) return mockStorage;
        if (key === `U_LOAN_PROCESS_APPLYING_${userId}`) return false;
        return null;
      });
      requestUtils.request.mockResolvedValue({
        code: 0,
        data: { status: 4, refuseMsg: "Rejected" },
      });
      constants.LOAN_APPLY_ERROR_CODE_MAP = { LOAN_EXPIRED: 123 };

      await loanNext(userId);

      expect(requestUtils.request).toHaveBeenCalledWith("/loan/api/loan/apply", {
        encryptedParams: { applyNo, ...mockStorage.infos },
      });
      expect(nativeBridge.report).toHaveBeenCalled();
    });
  });

  describe("creditNext", () => {
    it("should redirect to realname page when no realInfo", async () => {
      const userId = "test-user";
      storage.getStore.mockReturnValue({});

      const result = await creditNext(userId);

      expect(nativeBridge.goto).toHaveBeenCalledWith("/wallet-loan-web/pages/credit/realname");
      expect(result).toBe(true);
    });

    it("should handle FACE_CHECK step for credit", async () => {
      const userId = "test-user";
      const mockStorage = {
        verifyList: ["FACE_CHECK", "AGREEMENT_CREDIT"],
        realInfo: "encrypted-data",
      };
      storage.getStore.mockReturnValue(mockStorage);
      requestUtils.request.mockResolvedValue({ data: { status: 1, realName: "Test User" } });
      nativeBridge.liveDetection.mockResolvedValue(true);
      nativeBridge.getLiveImage.mockResolvedValue({ fileInfos: [] });

      const result = await creditNext(userId);

      expect(nativeBridge.liveDetection).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it("should handle successful credit application", async () => {
      const userId = "test-user";
      const mockStorage = {
        verifyList: [],
        info: "encrypted-data",
        reActive: 1,
      };
      storage.getStore.mockReturnValue(mockStorage);
      nativeBridge.getLiveImage.mockResolvedValue({ fileInfos: [] });
      requestUtils.request.mockResolvedValue({
        code: 0,
        data: {
          applyStatus: 2,
          applyNo: "CREDIT-123",
          remainLimit: "10000",
          apr: "0.1",
          dayRate: "0.01",
        },
      });

      const callback = vi.fn();
      const result = await creditNext(userId, {}, callback);

      expect(requestUtils.request).toHaveBeenCalledWith("/loan/api/credit/apply", {
        encryptedParams: expect.objectContaining({ reActive: 1 }),
      });
      expect(nativeBridge.goto).toHaveBeenCalled();
      expect(callback).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });
});
