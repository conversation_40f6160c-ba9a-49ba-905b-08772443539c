import { describe, it, expect } from "vitest";
import loanApiCreditInfo from "../../mock/loan_api_credit_info.json";

describe("loan_api_credit_info.json", () => {
  it("should have correct data structure", () => {
    expect(loanApiCreditInfo).toHaveProperty("data");
    expect(loanApiCreditInfo.data).toHaveProperty("code", 0);
    expect(loanApiCreditInfo.data).toHaveProperty("message", "success");
    expect(loanApiCreditInfo.data).toHaveProperty("data");
  });

  describe("credit info data", () => {
    const creditData = loanApiCreditInfo.data.data;

    it("should have correct supplier info", () => {
      expect(creditData).toHaveProperty("supplier", 7);
      expect(creditData).toHaveProperty("supplierName", "奇富借条");
      expect(creditData.supplierLogo).toMatch(/^https:\/\/.*\.svg$/);
    });

    it("should have correct user info", () => {
      expect(creditData).toHaveProperty("mobileNo", "138****4001");
      expect(creditData.openId).toHaveLength(64);
      expect(creditData).toHaveProperty("status", 0);
      expect(creditData).toHaveProperty("cancelStatus", 0);
    });

    it("should have correct credit limits", () => {
      expect(creditData).toHaveProperty("creditLimit", 11000000);
      expect(creditData).toHaveProperty("limitType", 1);
      expect(creditData).toHaveProperty("remainLimit", 11000000);
      expect(creditData).toHaveProperty("totalAvailableLimit", 11000000);
      expect(creditData).toHaveProperty("totalCreditLimit", 11000000);
    });

    it("should have correct loan range", () => {
      expect(creditData).toHaveProperty("maxLoan", 20000000);
      expect(creditData).toHaveProperty("minLoan", 50000);
    });

    describe("product info", () => {
      const productInfo = creditData.productInfos[0];

      it("should have correct product info structure", () => {
        expect(Array.isArray(creditData.productInfos)).toBe(true);
        expect(productInfo).toHaveProperty("dayRate", "0.0493");
        expect(productInfo).toHaveProperty("apr", "17.9945");
        expect(productInfo).toHaveProperty("repayMethod", 1);
        expect(productInfo).toHaveProperty("repayMethodName", "等额本息");
        expect(productInfo).toHaveProperty("earlyRepay", true);
      });

      it("should have correct term numbers", () => {
        expect(Array.isArray(productInfo.termNums)).toBe(true);
        expect(productInfo.termNums).toEqual([6, 12]);
      });
    });

    it("should have correct dict credit info", () => {
      expect(creditData.dictCreditInfo).toHaveProperty("creditLimit", 30000000);
      expect(creditData.dictCreditInfo).toHaveProperty("dayRate", "0.011");
      expect(creditData.dictCreditInfo).toHaveProperty("apr", "3.9");
      expect(creditData.dictCreditInfo).toHaveProperty("approvalSeconds", "30");
    });

    it("should have correct timestamp", () => {
      expect(creditData.applyTime).toBe(1751621191000);
      expect(creditData).toHaveProperty("reActiveFail", true);
    });
  });
});
