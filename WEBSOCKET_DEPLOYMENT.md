# WebSocket服务器独立部署指南

## 概述

WebSocket服务器已经独立为一个单独的项目，可以独立部署和管理。

## 项目结构

```
wallet-loan-toolbox/
├── websocket-server/          # 独立的WebSocket服务器
│   ├── server.js             # 主服务器文件
│   ├── package.json          # 依赖配置
│   ├── Dockerfile            # Docker配置
│   ├── docker-compose.yml    # Docker Compose配置
│   ├── env.example           # 环境变量示例
│   └── README.md             # 服务器文档
├── hooks/useCollaboration.ts # 客户端WebSocket连接
└── ...                       # 其他项目文件
```

## 部署步骤

### 1. 启动WebSocket服务器

#### 方法一：本地启动

```bash
cd websocket-server
npm install
cp env.example .env
npm run dev
```

#### 方法二：Docker启动

```bash
cd websocket-server
docker-compose up -d
```

#### 方法三：直接运行

```bash
cd websocket-server
npm install
npm start
```

### 2. 启动主应用

```bash
# 在项目根目录
npm run dev
```

### 3. 验证连接

1. 检查WebSocket服务器状态：
   ```
   http://localhost:3001/health
   ```

2. 查看连接统计：
   ```
   http://localhost:3001/stats
   ```

## 环境配置

### WebSocket服务器环境变量

在`websocket-server/.env`文件中配置：

```env
PORT=3001
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

### 主应用环境变量

在主项目的`.env.local`文件中配置：

```env
NEXT_PUBLIC_WS_URL=http://localhost:3001
```

## 生产环境部署

### 1. WebSocket服务器部署

#### 使用Docker

```bash
# 构建镜像
docker build -t websocket-server ./websocket-server

# 运行容器
docker run -d \
  --name websocket-server \
  -p 3001:3001 \
  -e ALLOWED_ORIGINS=https://your-domain.com \
  websocket-server
```

#### 使用Docker Compose

```bash
cd websocket-server
docker-compose -f docker-compose.yml up -d
```

#### 直接部署

```bash
cd websocket-server
npm ci --only=production
npm start
```

### 2. 主应用部署

按照Next.js的标准部署流程进行。

### 3. 负载均衡配置

如果使用Nginx，配置示例：

```nginx
# WebSocket服务器
upstream websocket {
    server localhost:3001;
}

server {
    listen 80;
    server_name your-domain.com;

    # 主应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # WebSocket连接
    location /socket.io/ {
        proxy_pass http://websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和维护

### 健康检查

```bash
# 检查服务器状态
curl http://localhost:3001/health

# 查看连接统计
curl http://localhost:3001/stats
```

### 日志查看

```bash
# Docker日志
docker logs websocket-server

# 直接运行的日志
# 日志会输出到控制台
```

### 重启服务

```bash
# Docker重启
docker restart websocket-server

# Docker Compose重启
docker-compose restart

# 直接重启
# 停止进程后重新启动
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查WebSocket服务器是否启动
   - 确认端口3001未被占用
   - 检查防火墙设置

2. **CORS错误**
   - 确认`ALLOWED_ORIGINS`配置正确
   - 检查域名是否在允许列表中

3. **客户端连接问题**
   - 确认`NEXT_PUBLIC_WS_URL`环境变量设置正确
   - 检查网络连接

### 调试步骤

1. 检查WebSocket服务器状态
2. 查看服务器日志
3. 检查客户端连接日志
4. 验证环境变量配置

## 扩展功能

### 集群部署

如果需要支持多实例，可以：

1. 使用Redis适配器
2. 配置负载均衡器
3. 使用PM2进行进程管理

### 持久化存储

如果需要持久化用户数据，可以：

1. 添加数据库连接
2. 实现数据持久化逻辑
3. 配置数据备份策略

## 安全考虑

1. **CORS配置**: 严格限制允许的源域名
2. **环境变量**: 不要在代码中硬编码敏感信息
3. **网络安全**: 使用HTTPS/WSS协议
4. **访问控制**: 实现用户认证和授权
5. **速率限制**: 防止恶意连接和攻击 