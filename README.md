# 1 概述

荣耀钱包借贷前端项目是基于 VUE3+Pinia+Express 的服务端渲染（SSR）多页面（MPA）项目，

- 采用服务端渲染的原因是希望将页面尽快的呈现给用户
- 多页应用的状态管理相对简单，且不像单页应用在落地页需要加载较大的单页状态处理代码。
- 页面跳转和 APP 配合，采用新开 webview 的方式，多页更适合些

## 1.1 主要模块

![img.jpg](README/module.jpg)
<BR>
每个页面都有各自的 vue 文件和对应状态管理文件（Pinia Store）
Pinia 被认为是 Vuex 的演进版本，相关资料可以参考 Pinia 中文文档<https://pinia.web3doc.top/introduction.html>

## 1.2 页面主要流程流程

```mermaid
sequenceDiagram

    participant App Native
    participant App H5
    participant Web服务(nodejs)
    participant 后端服务
    participant 账号APP

    App Native ->> 账号APP: 1 获取登录信息
    App Native ->> App H5: 2 写入登录信息,客户端信息（httpcookie）   
    App H5 ->> Web服务(nodejs): 3 携带cookie，发起页面url访问
    Web服务(nodejs) ->> 后端服务: 4 Nodejs将cookie转为后端识别的header
    后端服务 ->> Web服务(nodejs): 5 返回数据
    Web服务(nodejs) ->> Web服务(nodejs): 6 web服务完成html渲染,下发html与后端数据
    Web服务(nodejs) ->> App H5: 7 下发html与后端数据
    App H5 ->> App H5: 8 客户端挂载组件 
```

# 2 开发指南

## 2.1 目录结构

```
├─components         //公共组件目录
├─helpers            //工具类方法
└─pages              //页面目录
    ├─example        //服务端渲染示例，包含点击
    └─notfound       //无需后端接口的页面示例
```

## 2.2 页面开发步骤

在 pages 目录下新增对应的页面目录，并创建空白的 vue 和 store 文件，如果页面简单不需要 Store 文件，则 Store 文件写法为, pagename 根据页面名称指定，否则按照 Pinia 定义 Store 的写法完成即可，参见 <https://pinia.web3doc.top/core-concepts/>

```JavaScript
import { defineStore } from "pinia";
export default defineStore(pagename, {});
```

Vue 文件 Script 段 script 采用了 Vue3 的 setup 写法，如下：

```JavaScript
//页面vue文件
import { onServerPrefetch } from "vue";
import useStore from "./store";
import { initStore } from "../../helpers/utils";

const { store, data } = initStore(useStore);

//服务端执行，用与服务端渲染获取初始化数据
onServerPrefetch(store.initial);

//store.js
import { defineStore } from 'pinia';
import { request } from '../../helpers/utils';

export default defineStore('example', {
  state: () => {
    return {
      //页面初始数据
      param: {}, //如果需要接受公共参数，这个一定要写
    };
  },
  actions: {
    async initial() {
      //对应onServerPrefetch 注册的方法，这里通过this.param可以获取页面的quertString
      const data = await Promise.all([
        request('/config/api/getEntry', {}),
        request('/config/api/getEntry2', {}),
      ]);
      this.$patch({entryInfo: data[0]}); //由于初始化没有设置entryInfo，所以此处要用$patch方法
    },
  },
});
```

以上可以参见 src/pages/example 和 src/pages/notfound
页面文件创建好后，在 src/helpers/router-helper 文件中添加路由处理

## 2.3 建议使用 ES2021 的 Optional Chain 使代码简洁，并且避免 undefined 错误

```JavaScript
const info = { name: "info", friend: { name: "lucy" } }

// es11 可选链的写法 

console.log(info?.friend?.name) 

// 原来的写法，如果info下面的friend属性没有

if ( info.friend && info.friend.name ) {

    console.log( info.friend.name )

}

```

# 3 调试指南

# 3.1 npm 启动命令

首次启动需执行 npm install<BR>
本地开发启动: npm run dev<BR>
测试环境 or 生产环境启动（连接生产 or 测试接口），需先执行<font color="red">npm run build</font>生产运行态文件<BR>
DEV环境启动: npm run fat<BR>
测试环境启动: npm run uat<BR>
生产环境启动: npm run prod<BR>

# 3.2 调试准备

确保本地网络可以直连对应的环境，域名分别为<BR>
DEV 环境（开发调测阶段使用）:card-dev-drcn.wallet.hihonorcloud.com<BR>
TEST 环境: card-test-drcn.wallet.hihonorcloud.com<BR>
PROD 环境: card-drcn.wallet.hihonorcloud.com<BR>
客户端状态：<BR>
实际运行时，安卓侧会在初始化 webview 时写入 cookie，所以浏览器调测时需要写入如下代码<BR>

```JavaScript
document.cookie='wallet_deviceInfo={"versionCode":"8.0.7.900","packageName":"com.hihonor.id","deviceModel":"FMN-AN00","appFingerprint":"EDE02A47F935EC8990EF9CD1DEE9D0E9353ED612DAD85900966F79A972EBE071","country":"CN","language":"zh-CN"}; path=/; expires=Tue, 01 Jan 2030 00:00:00 GMT';
document.cookie='wallet_userInfo={"userId":"260086000252244259","accessToken":"CgB6e3x9EnoMX53uykdYsI8bnO0rNu+HGK0Xiy9bEv+8xo8iPKz\/oIWr3PBBzCzRNvlEqwMqhz5r08+wrxp5WXkoU\/RACwMHfbWfoN\/b\/k8WdMy2g3GGrU1GjPgcp+4zxVU=","deviceId":""};; path=/; expires=Tue, 01 Jan 2030 00:00:00 GMT';
```

其中 w-sessionId 是登录态 auth，每次调测是需要找安卓侧相关同学要一个可以调试的 sessionId

# 3.3 网络请求

无论是前端还是后端通过 request 方法调用 url 格式为/{serviceName}/api/{interface}
参见 src\pages\example\store.js

# 4 云龙流水线地址

https://clouddragon.ipd.hihonor.com/pipeline/details/2551287119d14b1e8f99ab881879bfe1?serviceId=9fa5006c41894734bbd91e7ebeba8a41&serviceMenu=true

# 5 调试
## 5.1 Nodejs 服务端调试
dev 启动 。浏览器地址栏输入 chrome://inspect(chrome 浏览器) 或者 edge://inspect (edge 浏览器) 可以看到下图
![img.jpg](README/2024-02-08_162532.jpg)
![img.jpg](README/2024-02-08_162654.jpg)

## 5.2 安卓webview调测
找安卓同事提供一个开启webview debug开关的包，并保证自己的PC有USB debug权限，测试机连上PC机，打开测试机USB调试开关
打开edge://inspect (edge 浏览器)  ，可以识别到所连接的测试机

## 5.2 Pinia devtool
安装vue devtool扩展
![img.jpg](README/vuetool.jpg)
调试时可以看到并编辑Pinia缓存
![img.jpg](README/pinia.jpg)

# 6 Mock
## 6.1 Mock数据存储位置
```
└─mock
```
## 6.2 修改和使用Mock数据
调用request方法时传入第三个形参（options）为`{mock: true}`
```JavaScript
import { request } from '../../helpers/utils';

const data = await Promise.all([
    request('/config/api/getEntry', {}, {mock: true}), //对应的mock数据存放于 ./mock/config_api_getEntry.json
    request('/config/api/getEntry2', {}, {mock: true}), //对应的mock数据存放于 ./mock/config_api_getEntry2.json
  ]);
```
修改mock文件夹下对应的json文件，模拟服务端接口返回的响应结果
```json
// ./mock/loan_api_credit_info.json
// 模拟/loan/api/credit/info接口返回的响应结果
{
  "success": true,
  "data": {
    "supplier": 1,
    "creditLimit": 150000,
    "remainLimit": 123456,
    "status": 0,
    "productInfos": [
      {
        "dayRate": "0.048",
        "apr": "17.5"
      },
      {
        "dayRate": "0.05",
        "apr": "18.25"
      }
    ],
    "dictCreditInfoDto": {
      "creditLimit": 200000,
      "dayRate": "0.02",
      "apr": "7.3"
    }
  }
}
```
