{"name": "wallet-loan-toolbox", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix", "prisma": "npx prisma migrate dev --name init"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@heroui/react": "2.7.5", "@heroui/system": "2.4.12", "@heroui/theme": "2.4.12", "@monaco-editor/react": "^4.7.0", "@prisma/client": "6.5.0", "@react-aria/ssr": "3.9.7", "@react-aria/visually-hidden": "3.8.20", "@types/lodash": "4.17.17", "axios": "1.9.0", "clsx": "2.1.1", "csv-parse": "^5.6.0", "dayjs": "1.11.13", "diff": "8.0.2", "file-saver": "^2.0.5", "framer-motion": "11.13.1", "intl-messageformat": "^10.5.0", "jszip": "3.10.1", "lodash": "4.17.21", "lucide-react": "^0.525.0", "monaco-editor": "^0.52.2", "next": "15.2.4", "next-themes": "0.4.4", "react": "18.3.1", "react-dom": "18.3.1", "react-syntax-highlighter": "15.6.1", "recharts": "^2.15.3", "ws": "^8.18.3", "zustand": "5.0.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@iconify/react": "^5.2.0", "@next/eslint-plugin-next": "15.0.4", "@react-types/shared": "3.25.0", "@types/diff": "^7.0.2", "@types/file-saver": "^2.0.7", "@types/node": "20.5.7", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.0.4", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.49", "prettier": "3.3.3", "prisma": "^6.5.0", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "0.3.0", "tailwindcss": "3.4.16", "typescript": "5.6.3"}}