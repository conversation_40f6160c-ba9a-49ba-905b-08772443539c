{"name": "vite-vue-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "cross-env DEF_ENV_TYPE=development node --inspect server", "build": "npm run build:client && npm run build:server", "build:client": "vite build --ssrManifest --outDir dist/client && node ./build/index.js", "build:server": "vite build --ssr src/entry-server.js --outDir dist/server", "prod": "nohup pm2-runtime app.json --env prod > /dev/null 2>&1", "sit": "nohup pm2-runtime app.json --env sit > /dev/null 2>&1", "pre": "nohup pm2-runtime app.json --env pre > /dev/null 2>&1", "uat": "nohup pm2-runtime app.json --env uat > /dev/null 2>&1", "fat": "nohup pm2-runtime app.json --env fat > /dev/null 2>&1", "stop": "pm2 stop server", "preview": "cross-env NODE_ENV=test DEF_ENV_TYPE=fat node server.js", "lint": "eslint src/**/*.{js,vue}", "fix": "eslint --fix src/**/*.{js,vue}", "prettier": "prettier --write src/**/*.{js,vue}", "prepare": "husky", "lint-staged": "lint-staged", "test": "vitest", "test:report": "vitest --reporter=html"}, "dependencies": {"@babel/preset-env": "7.26.0", "@hihonor/hnr": "3.9.0-beta.8", "@hihonor/hnr-js-sdk": "1.0.5", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-node-resolve": "15.3.0", "@vue-office/pdf": "2.0.2", "axios": "1.8.4", "big.js": "6.2.1", "body-parser": "1.20.3", "compression": "1.7.4", "cookie-parser": "1.4.6", "dayjs": "1.11.13", "express": "4.21.2", "express-formidable": "1.2.0", "hpp": "0.2.3", "html-minifier-terser": "7.2.0", "lodash": "4.17.21", "log4js": "6.9.1", "lottie-web": "5.12.2", "node-forge": "1.3.1", "pinia": "2.2.1", "qs": "6.14.0", "request-ip": "3.3.0", "send": "1.1.0", "serve-static": "1.16.2", "sirv": "2.0.4", "swiper": "11.1.1", "ua-parser-js": "1.0.37", "vue": "3.3.13", "vue-demi": "0.14.6", "vue-no-ssr": "1.1.1", "vue3-touch-events": "4.1.8"}, "devDependencies": {"@pinia/testing": "0.1.5", "@types/lodash": "4.17.0", "@vitejs/plugin-vue": "4.5.2", "@vitest/ui": "2.0.5", "@vue/test-utils": "2.4.6", "cross-env": "7.0.3", "eslint": "8.56.0", "eslint-config-airbnb-base": "15.0.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-vue": "9.21.1", "glob": "10.3.10", "husky": "9.0.11", "jsdom": "24.1.1", "lint-staged": "15.2.7", "prettier": "3.3.2", "sass": "1.78.0", "vite": "5.0.10", "vite-plugin-cdn-import": "^1.0.1", "vitest": "2.0.5", "rollup": "4.39.0"}}