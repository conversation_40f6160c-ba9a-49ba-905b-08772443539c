/*
TODO
  1 log4j集成（50%），剩余搬迁appdender&category
  2 待提供高保真
*/
import fs from "node:fs/promises";
import hpp from "hpp";
import express from "express";
import v8 from "v8";
// import { Cron } from 'croner';
import cookieParser from "cookie-parser";
import formidable from "express-formidable";
import requestIp from "request-ip";

// Constants
const isProduction = ["production", "uat", "fat", "sit", "pre"].includes(process.env.DEF_ENV_TYPE);
const port = process.env.PORT || 8080;
const base = "/wallet-loan-web/pages";
const apibase = "/wallet-loan-web/api";
const uribase = "/wallet-loan-web/uri";
const redirectBase = "/wallet-loan-web/redirect";
let whiteList = [];
const apmSdkVersion = "*********";

// Cached production assets
const templateHtml = isProduction ? await fs.readFile("./dist/client/index.html", "utf-8") : "";
const imgTemplateHtml = await fs.readFile(
  isProduction ? "./dist/client/img_preview.html" : "./public/img_preview.html",
  "utf-8",
);

const ssrManifest = isProduction
  ? JSON.parse(await fs.readFile("./dist/client/.vite/ssr-manifest.json", "utf-8"))
  : undefined;

// Create http server
const app = express();
app.use(cookieParser());
app.use(formidable());
app.use(hpp());
app.use(requestIp.mw());
// Add Vite or respective production middlewares
let vite;
if (!isProduction) {
  const { createServer } = await import("vite");
  vite = await createServer({
    server: { middlewareMode: true },
    appType: "custom",
    base,
  });
  app.use(vite.middlewares);
} else {
  const sirv = (await import("sirv")).default;

  // framework路径 - 长期缓存
  app.use(
    `${base}/framework`,
    sirv("./dist/client/framework", {
      etag: true,
      gzip: false,
      maxAge: 31536000,
    }),
  );
  // 其他静态资源
  app.use(base, sirv("./dist/client", { etag: true, extensions: [], gzip: false, maxAge: 864500 }));
}
let entryModule;
if (isProduction) {
  // eslint-disable-next-line import/extensions
  entryModule = await import("./dist/server/entry-server.js");
} else {
  entryModule = await vite.ssrLoadModule("/src/entry-server.js");
}
const { render, forwardReq, queryWhiteList, logInterface, logRun, buildContextStore, redirectReq } =
  entryModule;

async function getHeapSnapshot(filepath) {
  if (filepath) {
    v8.writeHeapSnapshot(filepath, {
      exposeInternals: true,
      exposeNumericValues: true,
    });
  } else {
    v8.writeHeapSnapshot();
  }
}
function getCurrentFormattedDate() {
  const now = new Date(); // 创建一个Date对象表示当前时间
  const year = now.getFullYear(); // 获取年份
  const month = now.getMonth() + 1; // 获取月份，月份从0开始，所以加1
  const day = now.getDate(); // 获取日
  const hours = now.getHours(); // 获取小时
  const minutes = now.getMinutes(); // 获取分钟
  return `${year}${month.toString().padStart(2, "0")}${day.toString().padStart(2, "0")}${hours.toString().padStart(2, "0")}${minutes.toString().padStart(2, "0")}`;
}

process.on("SIGUSR1", () => {
  getHeapSnapshot(
    `/opt/hihonor/logs/wallet-loan-web/heap_${getCurrentFormattedDate()}_PID${process.pid}.heapsnapshot`,
  );
});

// Serve HTML
app.use("*", async (req, res) => {
  const context = buildContextStore(req);
  global.asyncLocalStorage.run({ req, res, context }, async () => {
    res.setHeader("traceId", context.logContext.traceId);
    const start = new Date().getTime();
    let flag = "T";
    let message;
    const protocol = isProduction ? "https" : "http";
    const redirectHref = encodeURIComponent(`${protocol}://${req.headers.host}${req.originalUrl}`);
    try {
      if (req.originalUrl.startsWith(base)) {
        const url = req.baseUrl.startsWith(base) ? req.baseUrl.replace(base, "") : req.baseUrl;
        let template;
        if (isProduction) {
          template = templateHtml;
        } else {
          // Always read fresh template in development
          template = await fs.readFile("./index.html", "utf-8");
          template = await vite.transformIndexHtml(url, template);
        }

        const rendered = await render(url, ssrManifest);

        if (rendered?.store?.$state?.redirectUrl) {
          res.redirect(302, `${rendered.store.$state.redirectUrl}`);
          return;
        }

        // 处理状态码
        if (rendered.status && rendered.status !== 1201) {
          res.redirect(
            302,
            `${protocol}://${req.headers.host}${base}/notfound?href=${redirectHref}`,
          );
          return;
        }
        let footer = "";
        // 添加测试环境VConsole
        if (process.env.DEF_ENV_TYPE !== "production" && !url.includes("pdfView2Frame")) {
          footer = `<script src="https://contentplatform-drcn.hihonorcdn.com/honorWallet/vconsole/vconsole.min.js" async 
            onload="var vConsole = new window.VConsole();console.log('vConsole lauched!')"></script>`;
        }
        footer = `${footer}<input type=hidden value="${process.env.DEF_ENV_TYPE}" />`;
        const apmSettings = {
          url:
            process.env.DEF_ENV_TYPE === "production"
              ? `https://dapm-sdk.yun.honor.com/${apmSdkVersion}/dapm-vue-sdk.js`
              : `https://dapm-sdk-1311258067.cos.ap-beijing.myqcloud.com/${apmSdkVersion}/dapm-vue-sdk-uat.js`,
          dsn:
            process.env.DEF_ENV_TYPE === "production"
              ? "https://dapm.yun.honor.com/dapm-collector"
              : "https://uem-uat.test.hihonor.com/dapm-collector",
          projectId:
            process.env.DEF_ENV_TYPE === "production"
              ? "c27d74ee0ebe416babb77f858a34b8f4"
              : "d73cde8ae8154b88822cdc39bbfdf277",
          envType: process.env.DEF_ENV_TYPE === "production" ? "production" : "development",
          token: process.env.apmToken,
        };
        footer = `${footer}<script>window.__apmSettings = ${JSON.stringify(apmSettings)}</script>`;
        const html = template
          .replace("<!--app-title-->", rendered.title ?? "借钱")
          .replace("<!--app-head-->", rendered.head ?? "")
          .replace("<!--app-html-->", rendered.html ?? "")
          .replace("<!--app-data-->", rendered.data ?? "")
          .replace("<!--app-footer-->", footer ?? "");
        message = html;
        res.status(200).set({ "Content-Type": "text/html" }).end(html);
      } else if (req.originalUrl.startsWith(uribase)) {
        let uri = req.query?.href;
        let isSupportIframeSrcUrl = req.query?.isSupportIframeSrcUrl;

        if (uri) {
          const uRL = new URL(uri);
          uRL.search = "";
          const uriWithoutSearch = uRL.toString();
          const fullDomain = uRL.origin;
          // domain存在，whiteList中不存在domain(包括whiteList为空)，再查询一次whiteList
          if (fullDomain && !whiteList.includes(fullDomain)) {
            whiteList = await queryWhiteList();
          }
          // domain存在，再次判断whiteList内是否存在domain
          if (fullDomain && whiteList.includes(fullDomain)) {
            const uriType = uriWithoutSearch.split(".").pop().toLowerCase();
            if (["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"].includes(uriType)) {
              const message = imgTemplateHtml.replace("{uri}", uri);
              res.status(200).set({ "Content-Type": "text/html" }).end(message);
              return;
            }
            const response = await fetch(uri);
            if (isSupportIframeSrcUrl) {
              message = await response.text();
              const darkModeStyle = `<meta name="color-scheme" content="dark light">
              <style>span {color: var(--hnr-text-color-primary) !important;}</style>
              <style>ul * {color: var(--hnr-text-color-primary) !important;}</style>
              <style>body {background-color: var(--hnr-color-card-background) !important;}</style>
              <style>body * {background-color: var(--hnr-color-card-background) !important;}</style>
              <style>@media (prefers-color-scheme: dark){img {filter: invert(1) hue-rotate(180deg) !important;}}</style>`;
              // 在协议页，规避三方页面问题插入深色模式适配标签
              message = message.replace(/<head>/i, `<head><base href="${uri}">`);
              message = message.replace(/color: (black|#384369|#002A3C)[ !important;]/gi, "");

              if (message?.includes("<head>")) {
                message = message.replace("<head>", `<head>${darkModeStyle}`);
              } else if (message?.includes("<html>")) {
                message = message.replace("<html>", `<html>${darkModeStyle}`);
              } else {
                message = `<meta name="color-scheme" content="dark light">${darkModeStyle}${message}`;
              }
              if (typeof message === "string") message = message.replace(/background:white/g, "");
              res.status(200).set({ "Content-Type": "text/html" }).end(message);
            } else {
              const arrayBuffer = await response.arrayBuffer();
              const buffer = Buffer.from(arrayBuffer);
              res.status(200).set("Content-Type", "application/octet-stream").end(buffer);
            }
          } else {
            message = "Not Found";
            res.status(200).set({ "Content-Type": "text/html" }).end(message);
          }
        } else {
          message = "Not Found";
          res.status(200).set({ "Content-Type": "text/html" }).end(message);
        }
      } else if (req.originalUrl.startsWith(apibase)) {
        const data = await forwardReq();
        if (data?.agwError === 1) {
          flag = "F";
        }
        message = JSON.stringify(data);
        res.send(data);
      } else if (req.originalUrl.startsWith(redirectBase)) {
        await redirectReq();
      } else {
        message = "Not Found";
        res.status(200).set({ "Content-Type": "text/html" }).end(message);
      }
    } catch (e) {
      flag = "F";
      vite?.ssrFixStacktrace(e);
      message = e.message;
      if (isProduction) {
        res.redirect(302, `${protocol}://${req.headers.host}${base}/notfound?href=${redirectHref}`);
      } else {
        res.status(500).end(e.stack);
      }
      logRun(e.stack, "error");
    } finally {
      const cost = new Date().getTime() - start;
      logInterface(cost, flag, res.statusCode, message);
      global.asyncLocalStorage.exit(() => {});
    }
  });
});

// Start http server
app.listen(port, async () => {
  whiteList = await queryWhiteList();
  console.log(`Server started at http://localhost:${port}${base}/index`);
});

app.set("x-powered-by", false);

process.on("uncaughtException", (err) => {
  logRun(err.stack, "error");
});

process.on("unhandledRejection", (err) => {
  logRun(err.stack, "error");
});
