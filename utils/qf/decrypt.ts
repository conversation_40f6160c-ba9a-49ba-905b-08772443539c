import * as crypto from "crypto";
import { EncryptResponse, Response } from "@/app/cp/types";
import { createSign, createVerify } from "node:crypto";

export const privateKey = `************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;
const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAhtkvL1WMYJjtSPkDBlgr
M0d0EQS7KRr90v1Azu+U8IQtlMBJ3lwVq7sI4bIGrze05azIG2qFgK4sSZDMY7x0
z05x+miQJ7PL/Dz3CTQ7j6iaqzY7u08XEYHuGgkTovWDhT+ozUfcB8i8BwjJYIM8
eIr2VpUJAINTdUF+gTvLPZlYtYPn1EM0HdrOJDXmGeCZLpWHyOtM90br6cv4hREO
NWpCAe/AyBTi7Qe7ZeDbkPE+x5QEwxPQbduYhAcF84pz+VkkoGeIhHchQSfRp+Iy
9yPc+wiO66VGhskcrRI13PBIIId4NJHojIaPRKm3WFwZpkmLD3e1xrTnqtigtj40
pEURuiHaxmnlTlqRiR7uMFZ4lMFfs2tS0IPMJcZqo9vFea3nhHGbhu5pQtYNWiku
vFdRjtsUKNMmCXXsdCGbqJxVD3WK01vSXno9PEMRgVHdlTg4czenXNSjKAzCK2A5
99r524AvbgrHL2BoIqeFPy19xZi1vfmMrcElS5lNL84tAgMBAAE=
-----END PUBLIC KEY-----
`;

export const privateKeyShort =
  "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";
// 解密逻辑
export function decryptByPrivateKeyForQf(encryptedKey: string): string {
  // Base64解码密文
  const aesKey = Buffer.from(encryptedKey, "base64");

  // 使用私钥进行解密
  const decryptedBuffer = crypto.privateDecrypt(
    {
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: "sha256",
    },
    aesKey,
  );

  // 转换为字符串
  return decryptedBuffer.toString("utf-8");
}

export function encryptRequestForQf(params: any, method: string): string {
  const aesKeyBuffer = crypto.randomBytes(16);
  const aesKeyHexString = aesKeyBuffer.toString("hex");

  const encryptedParams = encryptData(JSON.stringify(params), aesKeyHexString);
  const key = generateAesKey(aesKeyHexString);

  const r = {
    appId: "mock",
    flowNo: crypto.randomUUID().replaceAll("-", ""),
    version: "1.0.0",
    method,
    params: encryptedParams,
    key,
    timestamp: Date.now(),
  };

  return JSON.stringify({
    ...r,
    sign: signData(r),
  });
}

export function encryptRes(res: Response): EncryptResponse {
  const aesKeyBuffer = crypto.randomBytes(16);
  const aesKeyHexString = aesKeyBuffer.toString("hex"); // 这个步骤其实在加密时不需要，Buffer可以直接用于加密

  const r = {
    code: res.code,
    desc: res.message,
    timestamp: Date.now(),
    data: encryptData(JSON.stringify(res.data), aesKeyHexString),
    key: generateAesKey(aesKeyHexString),
  };

  return {
    ...r,
    sign: signData(r),
  };
}

const SPLIT_CHAR = ":";
const KEY_ALGORITHM = "aes-128-gcm";
const IV_LENGTH = 16;

/**
 * AES GCM 解密
 *
 * @param text 密文，格式如 nonce:content
 * @param key 密钥，16位字符串
 * @returns 解密后的明文
 * @throws Error 如果解密失败或格式不匹配
 */
export function decryptForQf(text: string, key: string): string {
  if (!text || !key) {
    return "";
  }

  // 检查格式
  const keys = text.split(SPLIT_CHAR);
  if (keys.length !== 2) {
    console.error("decrypt error, text not match");
    throw new Error("decrypt error, text not match");
  }

  // 转换密钥和 IV
  const keyBytes = Buffer.from(key, "hex");
  const iv = Buffer.from(keys[0], "hex");
  const encryptedContent = Buffer.from(keys[1], "hex");

  if (iv.length !== IV_LENGTH) {
    throw new Error("Invalid IV length");
  }

  // 解密
  const decipher = crypto.createDecipheriv(KEY_ALGORITHM, keyBytes, iv);
  decipher.setAuthTag(encryptedContent.slice(-16)); // 设置认证标签
  const encryptedData = encryptedContent.slice(0, -16); // 去掉认证标签部分

  let decrypted = decipher.update(encryptedData, undefined, "utf8");
  decrypted += decipher.final("utf8");

  return decrypted;
}

/**
 * AES GCM 加密
 *
 * @param text 明文
 * @param key 密钥，16位字符串（十六进制编码）
 * @returns 加密后的密文，格式如 nonce:content（nonce 和 content 均为十六进制）
 * @throws Error 如果加密失败或输入无效
 */
export function encryptData(text: string, key: string): string {
  if (!text || !key) {
    return "";
  }

  try {
    // 转换密钥
    const keyBytes = Buffer.from(key, "hex");
    if (keyBytes.length !== 16) {
      throw new Error("Invalid key length, expected 16 bytes");
    }

    // 生成随机 IV
    const iv = crypto.randomBytes(IV_LENGTH);

    // 创建加密器
    const cipher = crypto.createCipheriv(KEY_ALGORITHM, keyBytes, iv);

    // 加密数据
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");

    // 获取认证标签
    const authTag = cipher.getAuthTag().toString("hex");

    // 拼接密文和认证标签
    const encryptedContent = encrypted + authTag;

    // 返回格式：nonce:content
    return `${iv.toString("hex")}${SPLIT_CHAR}${encryptedContent}`;
  } catch (error) {
    console.error("encrypt error", error);
    throw new Error("Encryption failed");
  }
}

export function generateAesKey(aesKeyHexString: string): string {
  const encryptedBuffer = crypto.publicEncrypt(
    {
      key: publicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: "sha256",
    },
    aesKeyHexString,
  );
  return encryptedBuffer.toString("base64");
}

// 加签函数
function signData(data: Record<string, any>): string {
  const keys = Object.keys(data)
    .filter((key) => key !== "sign")
    .sort();

  const signString = keys
    .map((key) => `${key.trim()}=${data[key].toString().trim()}`)
    .join("&");

  const signer = createSign("RSA-SHA256"); // SHA256 哈希算法
  signer.update(signString);
  signer.end();

  return signer.sign(
    {
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
      saltLength: 32,
    },
    "base64",
  );
}

export function verifySign(input: Record<string, any>): boolean {
  const keys = Object.keys(input)
    .filter((key) => key !== "sign")
    .sort();

  const signString = keys
    .map((key) => `${key.trim()}=${input[key].toString().trim()}`)
    .join("&");

  const signature = input["sign"];

  const verifier = createVerify("RSA-SHA256");
  verifier.update(signString);
  verifier.end();

  return verifier.verify(
    {
      key: publicKey,
      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
      saltLength: 32,
    },
    signature,
    "base64",
  );
}