// -----------------------------------------------------
// STEP 1: 定义从数据库获取的新数据类型
// -----------------------------------------------------

/**
 * @description 代表数据库中 "AttributedLine" 表的一行。
 * 这是我们实现 'git blame' 功能的基础。
 */
export interface DbLine {
  content: string;
  modifiedByName: string | null;
}

/**
 * @description 代表从数据库查询出的一个完整文件对象。
 * 它包含了原始文件内容和所有带归属信息的行。
 * 这个类型将作为 `buildFileTree` 函数的输入。
 */
export interface DbFile {
  id: string; // 文件的数据库唯一 ID，用于更新操作
  path: string;
  originalContent: string; // 原始文件内容，用于 Diff 对比视图的左侧
  lines: DbLine[]; // 包含当前所有行及其修改人信息的数组
}


// -----------------------------------------------------
// STEP 2: 扩展您提供的现有类型接口
// -----------------------------------------------------
// 您原始的类型定义保持不变，我们在此基础上扩展

// 基础文件类型，与您提供的保持一致
export interface FileInfo {
  path: string;
  content: string; // 注意：这里的 content 将代表 "当前" 的内容
  type: 'file';
}

// 基础目录类型，与您提供的保持一致
export interface DirectoryInfo {
  name: string;
  path: string;
  children: FileSystemNode[];
  type: 'directory';
}

/**
 * @description [修改] 扩展 `FileInfoNode` 以包含所有我们需要的功能。
 * 它继承了您定义的 FileInfo，并添加了 `git blame` 和数据持久化所需的字段。
 */
export interface FileInfoNode extends FileInfo {
  name: string;
  // --- 新增字段 ---
  id: string;                      // 文件的数据库 ID，用于发起更新请求
  originalContent: string;         // 原始内容，用于 Diff 编辑器的左侧面板
  lines: DbLine[];                 // 行级归属信息，用于 Blame Gutter 组件
  isModified: boolean;             // 标记文件是否被修改，用于在 TreeView 中高亮显示
}

// DirectoryInfoNode 保持不变
export interface DirectoryInfoNode extends DirectoryInfo {}

// 联合类型，现在 FileInfoNode 包含了更丰富的信息
export type FileSystemNode = FileInfoNode | DirectoryInfoNode;


// -----------------------------------------------------
// STEP 3: 适配 `buildFileTree` 函数
// -----------------------------------------------------

/**
 * [修改] 将文件列表构建成树形结构。
 * @param rootName - 树的根节点名称 (例如，ZIP文件名)。
 * @param files - 从数据库获取的 `DbFile[]` 数组，包含了所有必要信息。
 * @returns 根目录节点，其子节点现在是功能增强的 `FileInfoNode`。
 */
export function buildFileTree(rootName: string, files: DbFile[]): DirectoryInfoNode {
  const root: DirectoryInfoNode = {
    name: rootName, // 使用传入的 rootName
    type: 'directory',
    path: '', // 根路径为空
    children: [],
  };

  files.forEach(file => {
    // [新增] 从行的数组动态生成当前文件内容
    const currentContent = file.lines && Array.isArray(file.lines) 
      ? file.lines.map(l => l && typeof l.content === 'string' ? l.content : '').join('\n')
      : '';

    // [新增] 计算文件是否被修改
    const isModified = file.originalContent !== currentContent;

    // 您原有的路径拆分逻辑，保持不变
    const parts = file.path.split('/').filter(p => p);
    let currentNode: DirectoryInfoNode = root;

    parts.forEach((part, index) => {
      const isLastPart = index === parts.length - 1;
      const currentPath = parts.slice(0, index + 1).join('/');

      if (isLastPart) {
        // [修改] 这是文件节点。
        // 我们用从 `DbFile` 中提取的所有信息来构建增强的 `FileInfoNode`。
        currentNode.children.push({
          //--- 基础字段 ---
          type: 'file',
          name: part,
          path: file.path,
          content: currentContent, // 'content' 字段现在是动态生成的当前内容

          //--- 增强字段 ---
          id: file.id,
          originalContent: file.originalContent,
          lines: file.lines,
          isModified: isModified,
        });
      } else {
        // 您原有的目录查找和创建逻辑，保持不变
        let dirNode = currentNode.children.find(
          (child): child is DirectoryInfoNode => child.name === part && child.type === 'directory'
        );

        if (!dirNode) {
          dirNode = {
            name: part,
            type: 'directory',
            path: currentPath,
            children: [],
          };
          currentNode.children.push(dirNode);
        }
        currentNode = dirNode;
      }
    });

    // 为子节点排序，使目录始终排在文件前面，更美观（可选）
    root.children.sort((a, b) => {
      if (a.type === b.type) return a.name.localeCompare(b.name);
      return a.type === 'directory' ? -1 : 1;
    });
  });

  return root;
}
