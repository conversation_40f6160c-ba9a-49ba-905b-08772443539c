import * as crypto from "crypto";
import { createSign } from "node:crypto";

export const privateKey = `************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAgTkR114G/DgR9aKApsa5
oql9V8euSWxKvS6FOm2qrfRDedSE53/5OkD8UtW0imXJ79M0OjFzBJ/vbWQNotSi
2QXYGejoTuOuHUs4lVVhRe41IIWronQQ4ImuRn40M+IGwKhAqGDGuSUZUzzt8oni
OKalbvhieNoEH+k69ZC/l842ayIgtvHKgwtFa0BMPAQCfsGfdhSwVfFJ+YwMzzPA
QfDFgjKLhf7A0LtnncxCcCCb4J9Y5GXfpduy2WdcI8xxPI3Mt6v8H2NEwvWWMFg+
laNAcysuHqQNuW6r+Wu2e1I91rvXGm3vDqka6ynOG3LrNcofbk4zmcDL8p4BM+2U
WYcEZi0y1oZB8JiOL38LBOyzrMQ0hrZlmbGP1nY3OjnhcXvdRSUn1yFhyDwcq3oT
cesnWqVefyejz+QcZYazqeNerws2mnotXPogvxuae35BqGJdtoEH06Q+pniKpejK
W0tqMMLsPInnrPLEJaZEGGXTogM+FI4GVeAqvIS+1lBvAgMBAAE=
-----END PUBLIC KEY-----
`;

export function encryptRequest(params: any, method: string): string {
  const aesKeyBuffer = crypto.randomBytes(16);
  const aesKeyHexString = aesKeyBuffer.toString("hex");

  const encryptedParams = encryptData(JSON.stringify(params), aesKeyHexString);
  const key = generateAesKey(aesKeyHexString);

  const r = {
    appId: "mock",
    flowNo: crypto.randomUUID().replaceAll("-", ""),
    version: "1.0.0",
    method,
    params: encryptedParams,
    key,
    timestamp: Date.now(),
  };

  return JSON.stringify({
    ...r,
    sign: signData(r),
  });
}

/**
 * AES GCM 加密
 *
 * @param text 明文
 * @param key 密钥，16位字符串（十六进制编码）
 * @returns 加密后的密文，格式如 nonce:content（nonce 和 content 均为十六进制）
 * @throws Error 如果加密失败或输入无效
 */
export function encryptData(text: string, key: string): string {
  if (!text || !key) {
    return "";
  }

  try {
    // 转换密钥
    const keyBytes = Buffer.from(key, "hex");
    if (keyBytes.length !== 16) {
      throw new Error("Invalid key length, expected 16 bytes");
    }

    // 生成随机 IV
    const iv = crypto.randomBytes(16);

    // 创建加密器
    const cipher = crypto.createCipheriv("aes-128-gcm", keyBytes, iv);

    // 加密数据
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");

    // 获取认证标签
    const authTag = cipher.getAuthTag().toString("hex");

    // 拼接密文和认证标签
    const encryptedContent = encrypted + authTag;

    // 返回格式：nonce:content
    return `${iv.toString("hex")}:${encryptedContent}`;
  } catch (error) {
    console.error("encrypt error", error);
    throw new Error("Encryption failed");
  }
}

export function generateAesKey(aesKeyHexString: string): string {
  const encryptedBuffer = crypto.publicEncrypt(
    {
      key: publicKey,
      padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
      oaepHash: "sha256",
    },
    aesKeyHexString,
  );
  return encryptedBuffer.toString("base64");
}

// 加签函数
function signData(data: Record<string, any>): string {
  const keys = Object.keys(data)
    .filter((key) => key !== "sign")
    .sort();

  const signString = keys
    .map((key) => `${key.trim()}=${data[key].toString().trim()}`)
    .join("&");

  const signer = createSign("RSA-SHA256"); // SHA256 哈希算法
  signer.update(signString);
  signer.end();

  return signer.sign(
    {
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
      saltLength: 32,
    },
    "base64",
  );
}