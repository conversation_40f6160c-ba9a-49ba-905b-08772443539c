import React, { useState } from "react";
import { Button, Form, Input, Card, CardBody, Card<PERSON>ooter, Tabs, Tab } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useTheme } from "next-themes";

import { useUserStore } from "@/store/user";
import { systems } from "@/types/system";

interface SimpleLoginPromptProps {
  onLoginSuccess: () => void;
}

export default function SimpleLoginPrompt({
  onLoginSuccess,
}: SimpleLoginPromptProps) {
  const login = useUserStore((state) => state.loginUser);
  const { theme, setTheme } = useTheme();
  const [username, setUsername] = useState<string>("");
  const [chooseSystem, setChooseSystem] = useState<string>("0");
  const [selectedCategory, setSelectedCategory] = useState<string>("mock");

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (username.trim()) {
      await login({ username: username, chooseSystem: chooseSystem });
      onLoginSuccess();
    } else {
      alert("请输入一个用户名");
    }
  };

  const handleSystemLogin = async (systemKey: string) => {
    if (username.trim()) {
      await login({ username: username, chooseSystem: systemKey });
      onLoginSuccess();
    } else {
      alert("请输入一个用户名");
    }
  };

  const systemDescriptions = {
    "0": "用于云测环境的模拟系统，支持完整的业务流程测试和验证",
    "1": "用于三方对接的模拟系统，支持与外部系统的集成测试",
    "2": "用于日常巡检接口报错数据分析和已处理记录管理",
    "3": "配置变更文件追踪"
  };

  return (
    <div className="flex h-screen w-full items-center justify-center bg-gradient-to-br from-background via-background/95 to-background relative overflow-hidden">
      {/* 背景图案 */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/images/grid.svg')] bg-center"></div>
      </div>
      
      {/* 柔和光效 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[800px] h-[800px] bg-primary/10 rounded-full mix-blend-multiply filter blur-3xl"></div>
      </div>

      <div className="flex w-full max-w-lg flex-col gap-4 rounded-large px-8 pb-10 pt-6 bg-content1/10 backdrop-blur-sm shadow-lg relative z-10 border border-divider">
        <div className="flex items-center justify-between pb-2">
          <div className="flex items-center gap-2">
            <span className="text-2xl">🚀</span>
            <p className="text-3xl font-semibold text-foreground">
              小贷工具库
            </p>
          </div>
          <Icon
            icon={theme === 'dark' ? "solar:sun-linear" : "solar:moon-linear"}
            className="text-foreground-500 hover:text-foreground cursor-pointer transition-colors"
            width={24}
            height={24}
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          />
        </div>

        <Form
          className="flex flex-col gap-4"
          validationBehavior="native"
          onSubmit={handleSubmit}
        >
          <Input
            isRequired
            label="用户名"
            labelPlacement="outside"
            name="username"
            type="text"
            placeholder="推荐使用工号"
            variant="bordered"
            autoComplete="username"
            onValueChange={(username) => setUsername(username)}
          />

          <Tabs 
            selectedKey={selectedCategory}
            onSelectionChange={(key) => setSelectedCategory(key as string)}
            className="w-full"
          >
            <Tab key="mock" title="Mock工具">
              <div className="grid grid-cols-2 gap-4">
                {systems.filter(system => system.category === 'mock').map((system) => (
                  <Card
                    key={system.key}
                    onPress={() => setChooseSystem(String(system.key))}
                  >
                    <CardBody>
                      <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                      <p className="mt-2 text-sm text-foreground-500">
                        {systemDescriptions[String(system.key) as keyof typeof systemDescriptions]}
                      </p>
                    </CardBody>
                    <CardFooter className="flex flex-col gap-2">
                      <Button 
                        className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                        onPress={() => handleSystemLogin(String(system.key))}
                        color="primary"
                        variant="light"
                      >
                        选择此系统登录
                      </Button>
                      {system.tutorialUrl && (
                        <Button
                          className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                          onPress={() => {
                            window.open(system.tutorialUrl, '_blank');
                          }}
                          variant="light"
                        >
                          使用教程
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </Tab>
            <Tab key="inspection" title="巡检工具">
              <div className="grid grid-cols-2 gap-4">
                {systems.filter(system => system.category === 'inspection').map((system) => (
                  <Card
                    key={system.key}
                    onPress={() => setChooseSystem(String(system.key))}
                  >
                    <CardBody>
                      <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                      <p className="mt-2 text-sm text-foreground-500">
                        {systemDescriptions[String(system.key) as keyof typeof systemDescriptions]}
                      </p>
                    </CardBody>
                    <CardFooter className="flex flex-col gap-2">
                      <Button 
                        className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                        onPress={() => handleSystemLogin(String(system.key))}
                        color="primary"
                        variant="light"
                      >
                        选择此系统登录
                      </Button>
                      {system.tutorialUrl && (
                        <Button
                          className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                          onPress={() => {
                            window.open(system.tutorialUrl, '_blank');
                          }}
                          variant="light"
                        >
                          使用教程
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </Tab>

            <Tab key="fileChange" title="配置变更">
              <div className="grid grid-cols-2 gap-4">
                {systems.filter(system => system.category === 'fileChange').map((system) => (
                  <Card
                    key={system.key}
                    onPress={() => setChooseSystem(String(system.key))}
                  >
                    <CardBody>
                      <h3 className="text-xl font-semibold text-foreground">{system.label}</h3>
                      <p className="mt-2 text-sm text-foreground-500">
                        {systemDescriptions[String(system.key) as keyof typeof systemDescriptions]}
                      </p>
                    </CardBody>
                    <CardFooter className="flex flex-col gap-2">
                      <Button 
                        className="w-full text-primary bg-primary/10 hover:bg-primary/20 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                        onPress={() => handleSystemLogin(String(system.key))}
                        color="primary"
                        variant="light"
                      >
                        选择此系统登录
                      </Button>
                      {system.tutorialUrl && (
                        <Button
                          className="w-full text-foreground-600 bg-default-100 hover:bg-default-200 rounded-xl py-2 px-4 transition-colors cursor-pointer"
                          onPress={() => {
                            window.open(system.tutorialUrl, '_blank');
                          }}
                          variant="light"
                        >
                          使用教程
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
              </Tab>
          </Tabs>

          {/* 移除底部登录按钮 */}
        </Form>
      </div>
    </div>
  );
}
