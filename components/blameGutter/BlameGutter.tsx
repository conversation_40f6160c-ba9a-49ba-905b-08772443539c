import React, { type FC } from 'react';
import { type DbLine } from '@/utils/file-compare'; // 导入我们定义的行类型

/**
 * @description BlameGutter 组件的 Props 定义
 * @property {DbLine[]} lines - 包含内容和修改人信息的行数组
 */
interface BlameGutterProps {
  lines: DbLine[];
}

export const BlameGutter = React.forwardRef<HTMLDivElement, BlameGutterProps>(
  ({ lines }, ref) => {
    return (
      // 1. 将父组件传递过来的 ref 附加到这个 div 上
      // 2. 修改 CSS：
      //    - `overflow-y-auto`: 允许内容垂直滚动
      //    - `scrollbar-hide`: 这是一个自定义类或 Tailwind 插件，用于隐藏滚动条。
      //      如果不起作用，请使用下面的内联样式或全局CSS。
      //      这是为了让元素可滚动（以便 scrollTop 生效），但用户看不到滚动条。
      <div
        ref={ref}
        className="w-[100px] flex-shrink-0 select-none overflow-y-auto bg-gray-100 dark:bg-gray-800 border-r border-gray-300 dark:border-gray-600 pointer-events-none scrollbar-hide"
      >
        {lines.map((line, index) => {
          const displayName = line.modifiedByName || '-';
          const isOriginal = !line.modifiedByName;

          return (
            <div
              key={index}
              className={`
                flex items-center h-[19px] px-2 text-xs font-mono
                whitespace-nowrap overflow-hidden text-ellipsis
                ${isOriginal
                ? 'text-gray-400 dark:text-gray-500'
                : 'text-gray-500 dark:text-gray-400'
              }
              `}
              title={line.modifiedByName ?? 'Original line'}
            >
              {displayName}
            </div>
          );
        })}
      </div>
    );
  }
);

// 为 forwardRef 组件设置 displayName，便于React DevTools调试
BlameGutter.displayName = 'BlameGutter';
