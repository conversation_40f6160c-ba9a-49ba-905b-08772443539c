import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Button,
  Modal,
  useDisclosure,
  ModalContent,
  Input,
  Select,
  SelectItem,
  NumberInput,
  DatePicker,
} from "@heroui/react";
import { <PERSON>dalB<PERSON>, ModalFooter, ModalHeader } from "@heroui/modal";
import { getLocalTimeZone } from "@internationalized/date";

import { DeleteIcon } from "@heroui/shared-icons";
import { Coupon, couponStatuses, couponTypes } from "@/types";
import dayjs from "dayjs";

const columns = [
  { name: "优惠券编号", uid: "couponNo" },
  { name: "优惠券名称", uid: "couponName" },
  { name: "状态", uid: "status" },
  { name: "有效开始时间", uid: "startTime" },
  { name: "有效结束时间", uid: "endTime" },
  { name: "优惠金额", uid: "discountAmount" },
  { name: "不可用原因", uid: "unusableReason" },
  { name: "使用规则", uid: "useRule" },
  { name: "操作", uid: "actions" },
];

export default function CouponTable(props: {
  coupons?: Coupon[];
  setCoupons: (coupons: Coupon[]) => void;
}) {
  const [newCoupon, setNewCoupon] = useState<Coupon>({
    couponNo: "",
    couponName: "",
    couponType: 1,
    status: 1,
  });
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const createCoupon = () => {
    props.setCoupons([...(props.coupons || []), newCoupon]);
    onClose();
  };


  function deleteCoupon(couponNo: string) {
    // 从props.coupons移除couponNo
    // 这里的props.coupons是一个引用类型，所以直接修改会影响到父组件的状态
    // 所以需要使用setCoupons来更新状态
    // 这里的prevCoupons是一个函数，返回值是新的状态


    props.setCoupons(
      props.coupons.filter((coupon) => coupon.couponNo !== couponNo)
    );
  }
  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "couponType":
        return (
          <div>{couponTypes.find((type) => type.key === cellValue)?.label}</div>
        );
      case "status":
        return (
          <Select
            className="max-w-xs w-24"
            label="状态"
            defaultSelectedKeys={[cellValue?.toString()]}
            onSelectionChange={(key) => {
              item[columnKey] = parseInt(key.anchorKey as string);
            }}
          >
            {couponStatuses.map((status) => (
              <SelectItem key={status.key}>{status.label}</SelectItem>
            ))}
          </Select>
        );
      case "startTime":
        return (
          <div>
            {cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );

    case "endTime":
      return (
        <div>
          {cellValue
            ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
            : ""}
        </div>
      );
      case "useRule":
        return (
          <Input
            defaultValue={cellValue}
            onValueChange={(value) => {
              item[columnKey] = value;
            }}
          />
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除" >
              <span className="text-lg text-danger cursor-pointer active:opacity-50" onClick={() => deleteCoupon(item.couponNo)}>
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>优惠券</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table className={"col-span-full"} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.coupons}>
          {(item) => (
            <TableRow key={item.couponNo}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的优惠券
              </ModalHeader>
              <ModalBody>
                <Input
                  label="优惠券编号"
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      couponNo: value,
                    });
                  }}
                />
                <Input
                  label="优惠券名称"
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      couponName: value,
                    });
                  }}
                />
                <Select
                  className="max-w-xs"
                  label="状态"
                  onSelectionChange={(key) => {
                    setNewCoupon({
                      ...newCoupon,
                      status: parseInt(key.anchorKey as string),
                    });
                  }}
                >
                  {couponStatuses.map((status) => (
                    <SelectItem key={status.key}>{status.label}</SelectItem>
                  ))}
                </Select>

                <DatePicker
                  label="有效开始时间"
                  onChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      startTime: value?.toDate(getLocalTimeZone()).getTime(),
                    });
                  }}
                />

                <DatePicker
                  label="有效结束时间"
                  onChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      endTime: value?.toDate(getLocalTimeZone()).getTime(),
                    });
                  }}
                />

                <NumberInput
                  className="max-w-xs"
                  label="优惠金额"
                  minValue={100}
                  step={100}
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      discountAmount: value,
                    });
                  }}
                />

                <Input
                  label="不可用原因"
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      unusableReason: value,
                    });
                  }}
                />

                <Input
                  label="使用规则"
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      useRule: value,
                    });
                  }}
                />
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createCoupon}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
