import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Button,
  Modal,
  useDisclosure,
  ModalContent,
  Input,
  Select,
  SelectItem,
  NumberInput,
  DatePicker,
  addToast,
} from "@heroui/react";
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader } from "@heroui/modal";
import { getLocalTimeZone } from "@internationalized/date";

import { DeleteIcon } from "@heroui/shared-icons";
import dayjs from "dayjs";
import {
  Coupon,
  CouponRule,
  CouponStatusEnum,
  couponStatusMap,
  CouponTypeEnum,
  couponTypeMap, presetCoupons
} from "@/types/cp/couponInfo";
import { I18nProvider } from "@react-aria/i18n";
import { LoanUser } from "@/types/cp/loanUser";
import { updateLoanUser } from "@/actions/cp/loanUserActions";
import { notifyCoupon } from "@/actions/cp/notify/couponNotify";

const columns = [
  { name: "优惠券名称", uid: "couponName" },
  { name: "优惠券类型", uid: "couponType" },
  { name: "状态", uid: "status" },
  { name: "有效开始时间", uid: "startTime" },
  { name: "有效结束时间", uid: "endTime" },
  { name: "使用规则", uid: "useRules" },
  { name: "操作", uid: "actions" },
];

export default function CouponTable(props: {
  coupons?: Coupon[];
  setCoupons: (coupons: Coupon[]) => void;
  loanUser: LoanUser;
}) {
  const [newCoupon, setNewCoupon] = useState<Coupon>({
    couponNo: crypto.randomUUID(),
    couponBatchNo: crypto.randomUUID(),
    couponName: '',
    couponType: CouponTypeEnum.LOAN,
    status: CouponStatusEnum.UNUSED,
    startTime: 0,
    endTime: 0,
    discountAmount: 0,
    unusableReason: '',
    useRules: [],
    receiveTime: Date.now(),
    usedTime: 0,
  });
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const createCoupon =async () => {
    if (!newCoupon.couponName || !newCoupon.couponType || !newCoupon.startTime || !newCoupon.endTime) {
      addToast({
        title: "提示",
        description: "请填写所有必填字段",
        color: "warning"
      });
      return;
    }
    newCoupon.couponNo = crypto.randomUUID();
    newCoupon.couponBatchNo = crypto.randomUUID();
    newCoupon.receiveTime = Date.now();

    props.setCoupons([...(props.coupons || []), newCoupon]);

    onClose();
    props.loanUser = {
      ...props.loanUser,
      couponInfo: {
        status: {
          couponList: [...(props.loanUser.couponInfo?.status?.couponList || []), newCoupon]
        }
      }
    };
    await updateLoanUser(props.loanUser);
    addToast({
      title: "新增成功",
      color: "success",
    });

    setTimeout(() => {
      notifyCoupon(props.loanUser.id || 0,
        {
          userId: props.loanUser.userId,
          couponNo: newCoupon.couponNo!,
          couponBatchNo: newCoupon.couponBatchNo!,
          couponType: newCoupon.couponType!,
          supplier: "mock",
          startTime: newCoupon.startTime!,
          endTime: newCoupon.endTime!,
          couponName: newCoupon.couponName!
        }, "development");
    }, 1000);
  };


  function deleteCoupon(couponNo: string) {
    if (!props.coupons) return;
    props.setCoupons(
      props.coupons.filter((coupon) => coupon.couponNo !== couponNo)
    );
  }
  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "couponType":
        return (
          <div>{couponTypeMap.get(cellValue)}</div>
        );
      case "status":
        return (
          <div>{couponStatusMap.get(cellValue)}</div>
        );
      case "startTime":
        return (
          <div>
            {cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );
      case "endTime":
        return (
          <div>
            {cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );
      case "useRules":
        return (
          <div className="flex flex-col gap-1">
            {cellValue?.map((rule: CouponRule, index: number) => {
              const operatorMap = {
                gt: '>',
                gte: '>=',
                lt: '<',
                lte: '<=',
                eq: '='
              };
              const fieldMap = {
                loanAmount: '借款金额',
                termNums: '借款期数',
                repayMethod: '还款方式'
              };
              const repayMethodMap = {
                '1': '等额本息',
                '2': '等额本金',
                '3': '先息后本'
              };
              
              let value = rule.value;
              if (rule.field === 'repayMethod') {
                value = repayMethodMap[rule.value as keyof typeof repayMethodMap] || rule.value;
              }
              
              return (
                <div key={index} className="text-sm">
                  {fieldMap[rule.field]} {operatorMap[rule.operator]} {value}
                </div>
              );
            })}
          </div>
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除" >
              <span className="text-lg text-danger cursor-pointer active:opacity-50" onClick={() => deleteCoupon(item.couponNo)}>
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>优惠券</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table className={"col-span-full"} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.coupons}>
          {(item) => (
            <TableRow key={item.couponNo}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的优惠券
              </ModalHeader>
              <ModalBody>
                <Select
                  className="w-full"
                  label="选择预设优惠券"
                  placeholder="选择预设优惠券"
                  onSelectionChange={(key) => {
                    const selectedCoupon = presetCoupons.find(c => c.couponNo === key.anchorKey);
                    if (selectedCoupon) {
                      setNewCoupon({
                        ...newCoupon,
                        presetCouponType: selectedCoupon.presetCouponType,
                        couponName: selectedCoupon.couponName,
                      });
                    }
                  }}
                  isRequired
                >
                  {presetCoupons.map((coupon) => (
                    <SelectItem key={coupon.couponNo}>{coupon.couponName}</SelectItem>
                  ))}
                </Select>

                <Input
                  label="优惠券名称"
                  value={newCoupon.couponName}
                  onValueChange={(value) => {
                    setNewCoupon({
                      ...newCoupon,
                      couponName: value,
                    });
                  }}
                  isRequired
                />

                <Select
                  className="max-w-xs"
                  label="优惠券类型"
                  selectedKeys={[newCoupon.couponType?.toString()]}
                  onSelectionChange={(key) => {
                    setNewCoupon({
                      ...newCoupon,
                      couponType: parseInt(key.anchorKey as string),
                    });
                  }}
                  isRequired
                >
                  {Array.from(couponTypeMap.keys()).map((type) => (
                    <SelectItem key={type}>{couponTypeMap.get(type)}</SelectItem>
                  ))}
                </Select>

                <I18nProvider locale="zh-CN">
                  <DatePicker
                    label="有效开始时间"
                    onChange={(value) => {
                      if (value) {
                        setNewCoupon({
                          ...newCoupon,
                          startTime: value.toDate(getLocalTimeZone()).getTime(),
                        });
                      }
                    }}
                    isRequired
                  />
                </I18nProvider>

                <I18nProvider locale="zh-CN">
                  <DatePicker
                    label="有效结束时间"
                    onChange={(value) => {
                      if (value) {
                        setNewCoupon({
                          ...newCoupon,
                          endTime: value.toDate(getLocalTimeZone()).getTime(),
                        });
                      }
                    }}
                    isRequired
                  />
                </I18nProvider>

                <div className="flex flex-col gap-2">
                  <Select
                    className="max-w-xs"
                    label="添加规则"
                    onSelectionChange={(key) => {
                      const [field, operator] = (key.anchorKey as string).split('_') as [CouponRule['field'], CouponRule['operator']];
                      setNewCoupon({
                        ...newCoupon,
                        useRules: [...(newCoupon.useRules || []), { field, operator, value: '' }],
                      });
                    }}
                  >
                    <SelectItem key="loanAmount_gt">借款金额 &gt;</SelectItem>
                    <SelectItem key="loanAmount_gte">借款金额 &gt;=</SelectItem>
                    <SelectItem key="loanAmount_lt">借款金额 &lt;</SelectItem>
                    <SelectItem key="loanAmount_lte">借款金额 &lt;=</SelectItem>
                    <SelectItem key="loanAmount_eq">借款金额 =</SelectItem>
                    <SelectItem key="termNums_gt">借款期数 &gt;</SelectItem>
                    <SelectItem key="termNums_gte">借款期数 &gt;=</SelectItem>
                    <SelectItem key="termNums_lt">借款期数 &lt;</SelectItem>
                    <SelectItem key="termNums_lte">借款期数 &lt;=</SelectItem>
                    <SelectItem key="termNums_eq">借款期数 =</SelectItem>
                    <SelectItem key="repayMethod_eq">还款方式 =</SelectItem>
                  </Select>

                  {newCoupon.useRules?.map((rule: CouponRule, index) => (
                    <div key={index} className="flex gap-2 items-end">
                      <div className="flex-1">
                        {rule.field === 'repayMethod' ? (
                          <Select
                            className="w-full"
                            label="还款方式="
                            selectedKeys={[rule.value]}
                            onSelectionChange={(key) => {
                              const newRules = [...(newCoupon.useRules || [])];
                              newRules[index] = { ...rule, value: key.anchorKey as string };
                              setNewCoupon({
                                ...newCoupon,
                                useRules: newRules,
                              });
                            }}
                          >
                            <SelectItem key="1">等额本息(灵活还)</SelectItem>
                            <SelectItem key="2">等额本金(灵活还)</SelectItem>
                            <SelectItem key="3">先息后本(灵活还)</SelectItem>
                            <SelectItem key="4">等额本息(按期还)</SelectItem>
                          </Select>
                        ) : (
                          <NumberInput
                            label={`${rule.field === 'loanAmount' ? '借款金额(单位: 分)' : '借款期数'} ${
                              rule.operator === 'gt' ? '>' :
                              rule.operator === 'gte' ? '>=' :
                              rule.operator === 'lt' ? '<' :
                              rule.operator === 'lte' ? '<=' : '='
                            }`}
                            value={parseInt(rule.value) || 0}
                            onValueChange={(value) => {
                              const newRules = [...(newCoupon.useRules || [])];
                              newRules[index] = { ...rule, value: value.toString() };
                              setNewCoupon({
                                ...newCoupon,
                                useRules: newRules,
                              });
                            }}
                            minValue={0}
                            maxValue={rule.field === 'loanAmount' ? 1000000 : 36}
                            step={rule.field === 'loanAmount' ? 100 : 1}
                          />
                        )}
                      </div>

                      <Button
                        color="danger"
                        variant="flat"
                        size="sm"
                        onPress={() => {
                          const newRules = newCoupon.useRules?.filter((_, i) => i !== index);
                          setNewCoupon({
                            ...newCoupon,
                            useRules: newRules,
                          });
                        }}
                      >
                        删除
                      </Button>
                    </div>
                  ))}
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createCoupon}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
