import React from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Button, Chip
} from "@heroui/react";

import { loanStatuses, repayMethods} from "@/types";
import { LoanOrderDetail, LoanStatusEnum } from "@/types/cp/loanInfo";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { RightIcon } from "@/components/icons";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";

dayjs.extend(isSameOrAfter);
const columns = [
  { name: "荣耀侧订单号", uid: "applyNo" },
  { name: "起息日期", uid: "effectiveDate" },
  { name: "借款金额", uid: "loanAmount" },
  { name: "借款期数", uid: "totalTerm" },
  { name: "还款方式", uid: "repayMethod" },
  { name: "当前期数", uid: "currentTerm" },
  { name: "借款状态", uid: "status" },
  { name: "创建时间", uid: "createTime" },
  { name: "操作", uid: "actions" },
];

export default function LoanOrderTable(props: {
  userId: number;
  loanOrders?: LoanOrderDetail[];
}) {
  const router = useRouter();

  const getOrderStatusColor = (status: number | undefined) => {
    switch (status) {
      case LoanStatusEnum.APPLYING:
        return "warning";
      case LoanStatusEnum.REPAYING:
        return "primary";
      case LoanStatusEnum.OVERDUE:
        return "danger";
      case LoanStatusEnum.CLEARED:
        return "success";
      case LoanStatusEnum.FAILED:
        return "danger";
      default:
        return "default";
    }
  };
  const renderCell = React.useCallback((loanOrder: LoanOrderDetail, columnKey: keyof LoanOrderDetail | "currentTerm" | "actions") => {
    const cellValue = columnKey === "actions" || columnKey === "currentTerm" ? undefined : loanOrder[columnKey];

    switch (columnKey) {
      case "status":
        return (
          <Chip
            className="capitalize border-none gap-1"
            color={getOrderStatusColor(parseInt(cellValue))}
            size="sm"
            variant="flat"
          >
            {loanStatuses.find((status) => status.key === cellValue)?.label}
          </Chip>
        );

        case "effectiveDate":
        return (
          <div>
            {typeof cellValue === 'string' || typeof cellValue === 'number'
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );
      case "repayMethod":
        return (
          
          <div>
            {repayMethods.find((method) => method.key === cellValue)?.label}
          </div>
        );

      case "currentTerm":
        return (
          <div>
            {(() => {
              if (!loanOrder.repayPlanTerms) return 0;
              
              const today = dayjs();
              
              const currentTerm = loanOrder.repayPlanTerms?.findIndex((term) =>
                dayjs(term.shouldRepayDate).isSameOrAfter(today, "day")
              ) ?? -1;
              return currentTerm === -1 ? loanOrder.repayPlanTerms.length : currentTerm + 1;
            })()}
          </div>
        );
      case "createTime":
        return (
          <div>
            {dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip content="查看详情">
              <Button
                isIconOnly
                variant="light"
                onPress={() => {router.push(`/pages/cp/loan-user/${props.userId}/loan-order/${loanOrder.applyNo}`)}}
              >
                <RightIcon />
              </Button>
            </Tooltip>
          </div>
        );
      default:
        return cellValue ? String(cellValue) : "";
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>借款订单</div>
    </div>
  );

  return (
    <>
      <Table className={"col-span-full"} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.loanOrders}>
          {(item) => (
            <TableRow key={item.outOrderNo}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey as keyof LoanOrderDetail)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}
