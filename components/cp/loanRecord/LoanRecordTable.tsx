import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tooltip,
  Button,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Select,
  SelectItem, addToast
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { loanApplyStatuses, loanStatuses, repayMethods } from "@/types";
import {
  LoanApplyStatusEnum,
  LoanRecordDetail,
  LoanErrorCodeEnum,
  LoanErrorMessageMap,
  LoanInfo, LoanStatusEnum
} from "@/types/cp/loanInfo";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { RightIcon } from "@/components/icons";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import { getLoanUserById, updateLoanUser } from "@/actions/cp/loanUserActions";
import { LoanUser } from "@/types/cp/loanUser";
import { Coupon, CouponInfo, CouponStatusEnum } from "@/types/cp/couponInfo";

dayjs.extend(isSameOrAfter);
const columns = [
  { name: "荣耀侧订单号", uid: "applyNo" },
  { name: "借款金额", uid: "loanAmount" },
  { name: "还款方式", uid: "repayMethod" },
  { name: "申请状态", uid: "applyStatus" },
  { name: "借款状态", uid: "orderStatus" },
  { name: "申请日期", uid: "applyTime" },
  { name: "操作", uid: "actions" },
];
export default function LoanRecordTable(props: {
  userId: number;
  loanUser: LoanUser;
  loanRecords?: LoanRecordDetail[];
  onRefresh?: () => void;
}) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<LoanRecordDetail | null>(null);

  // 审批结果
  const [approveResult, setApproveResult] = useState<LoanApplyStatusEnum>(LoanApplyStatusEnum.SUCCESS);
  // 拒绝原因
  const [refuseReason, setRefuseReason] = useState<LoanErrorCodeEnum>(LoanErrorCodeEnum.LOAN_REJECTED);

  const getApplyStatusColor = (status: number | undefined) => {
    switch (status) {
      case LoanApplyStatusEnum.PENDING:
        return "primary";
      case LoanApplyStatusEnum.FAILED:
        return "danger";
      case LoanApplyStatusEnum.SUCCESS:
        return "success";
      default:
        return "default";
    }
  };


  const getOrderStatusColor = (status: number | undefined) => {
    switch (status) {
      case LoanStatusEnum.APPLYING:
        return "warning";
      case LoanStatusEnum.REPAYING:
        return "primary";
      case LoanStatusEnum.OVERDUE:
        return "danger";
      case LoanStatusEnum.CLEARED:
        return "success";
      case LoanStatusEnum.FAILED:
        return "danger";
      default:
        return "default";
    }
  };

  const renderCell = React.useCallback((loanRecord: LoanRecordDetail, columnKey: string) => {
    const cellValue = loanRecord[columnKey];

    switch (columnKey) {
      case "repayMethod":
        return (

          <div>
            {repayMethods.find((method) => method.key === cellValue)?.label}
          </div>
        );

      case "applyStatus":
        return (
          <Chip
            className="capitalize border-none gap-1"
            color={getApplyStatusColor(parseInt(cellValue))}
            size="sm"
            variant="flat"
          >
            {loanApplyStatuses.find((status) => status.key === cellValue)?.label}
          </Chip> 
        );
      case "orderStatus":
        const loanInfo = props.loanUser.loanInfo as unknown as LoanInfo;
        const loanOrder = loanInfo.status?.loanOrders.find((item) => {
          return item.applyNo === loanRecord.applyNo;
        });
        return (
          <Chip
            className="capitalize border-none gap-1"
            color={getOrderStatusColor(parseInt(loanOrder?.status))}
            size="sm"
            variant="flat"
          >
            {loanStatuses.find((status) => status.key === loanOrder?.status)?.label}
          </Chip>
        );
      case "applyTime":
        return (
          <div>
            {dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')}
          </div>
        );

      case "actions":
        return loanRecord.applyStatus === LoanApplyStatusEnum.PENDING ? (
          <div >
            <Tooltip content="审批">
              <Button
                isIconOnly
                variant="light"
                color="primary"
                onPress={() => {
                  setSelectedRecord(loanRecord);
                  setIsModalOpen(true);
                }}
              >
                <Icon icon="mdi:check-circle-outline" width="20" height="20" />
              </Button>
            </Tooltip>
            <Tooltip content="查看订单详情">
              <Button
                isIconOnly
                variant="light"
                onPress={() => {
                  router.push(`/pages/cp/loan-user/${props.userId}/loan-order/${loanRecord.applyNo}`)
                }}
              >
                <RightIcon />
              </Button>
            </Tooltip>
          </div>
        ) : loanRecord.applyStatus === LoanApplyStatusEnum.SUCCESS ? (
          <div>
            <Tooltip content="查看订单详情">
              <Button
                isIconOnly
                variant="light"
                onPress={() => {
                  router.push(`/pages/cp/loan-user/${props.userId}/loan-order/${loanRecord.applyNo}`)
                }}
              >
                <RightIcon />
              </Button>
            </Tooltip>
          </div>
        ) : null;
      default:
        return cellValue ? String(cellValue) : "";
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>借款记录</div>
    </div>
  );

  return (
    <>
      <Table className={"col-span-full"} topContent={topContent} >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              align={column.uid === "actions" ? "center" : "start"}
              key={column.uid}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.loanRecords}>
          {(item) => (
            <TableRow key={item.applyNo}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey as keyof LoanRecordDetail)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalContent>
          <ModalHeader>借款审批</ModalHeader>
          <ModalBody>
            <div className="grid gap-4">
              <Select
                label="审批结果"
                defaultSelectedKeys={[LoanApplyStatusEnum.SUCCESS.toString()]}
                onSelectionChange={(key) => {
                  setApproveResult(parseInt(key.anchorKey as string));
                }}
              >
                <SelectItem key={LoanApplyStatusEnum.SUCCESS}>
                  {loanApplyStatuses.find(status => status.key === LoanApplyStatusEnum.SUCCESS)?.label}
                </SelectItem>
                <SelectItem key={LoanApplyStatusEnum.FAILED}>
                  {loanApplyStatuses.find(status => status.key === LoanApplyStatusEnum.FAILED)?.label}
                </SelectItem>
              </Select>

              {approveResult === LoanApplyStatusEnum.FAILED && (
                <Select
                  label="拒绝原因"
                  defaultSelectedKeys={[LoanErrorCodeEnum.LOAN_REJECTED]}
                  onSelectionChange={(key) => {
                    setRefuseReason(key.anchorKey as LoanErrorCodeEnum);
                  }}
                >
                  {Object.entries(LoanErrorCodeEnum).map(([key, value]) => (
                    <SelectItem key={value}>
                      {LoanErrorMessageMap[value]}
                    </SelectItem>
                  ))}
                </Select>)}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={() => setIsModalOpen(false)}>
              取消
            </Button>
            <Button color="primary" onPress={async () => {
              // 如果审批失败要将优惠券的状态设置为未使用
              if (approveResult === LoanApplyStatusEnum.FAILED) {
                const couponInfo = props.loanUser.couponInfo as unknown as CouponInfo;
                if (couponInfo.status?.couponList) {
                  const coupon = couponInfo.status?.couponList?.find(
                    (item: Coupon) => item.couponNo === selectedRecord?.couponNo,
                  );
                  if (coupon) {
                    coupon.status = CouponStatusEnum.UNUSED;
                    coupon.usedTime = 0;
                  }
                }
              }
              if (selectedRecord) {
                selectedRecord.applyStatus = approveResult;
                if (approveResult === LoanApplyStatusEnum.FAILED) {
                  selectedRecord.refuseCode = refuseReason;
                  selectedRecord.refuseMsg = LoanErrorMessageMap[refuseReason];
                  selectedRecord.refuseMsgData = LoanErrorMessageMap[refuseReason];
                }
              }

              const loanUser =await getLoanUserById(props.userId);
              const loanInfo = loanUser?.loanInfo as unknown as LoanInfo;

              const loanOrder = loanInfo.status?.loanOrders.find((item) => {
                return item.applyNo === selectedRecord?.applyNo;
              });
              if (loanOrder) {
                loanOrder.status = approveResult === LoanApplyStatusEnum.SUCCESS ? LoanStatusEnum.REPAYING : LoanStatusEnum.FAILED;
              }
              const updatedLoanInfo = {
                ...loanInfo,
                status: {
                  ...loanInfo.status,
                  loanApplyRecords: loanInfo.status.loanApplyRecords.map((item) =>
                    item.applyNo === selectedRecord?.applyNo ? selectedRecord : item,
                  ),
                  loanOrders: loanInfo.status.loanOrders.map((item) =>
                    item.applyNo === selectedRecord?.applyNo ? loanOrder : item,
                  ),
                },
              };
              const updatedLoanUser = {
                ...loanUser,
                loanInfo: updatedLoanInfo,
              };

              await updateLoanUser(updatedLoanUser)
              setIsModalOpen(false);
              addToast({
                title: "审批成功",
                color: "success",
              });
              props.onRefresh?.();
            }}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
