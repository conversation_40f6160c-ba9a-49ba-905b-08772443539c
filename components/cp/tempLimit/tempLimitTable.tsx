import React, { useState } from "react";
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell
} from "@heroui/react";
import { TempLimitInfo } from "@/types/cp/creditInfo";
import dayjs from "dayjs";


// 表格列定义
const columns = [
  { name: "临时额度", uid: "tempLimit" },
  { name: "临额有效期", uid: "tempLimitValidTime" },
  { name: "调额时间", uid: "adjustTime" }
];

export default function TempLimitTable(props: {
  tempLimitInfos?: TempLimitInfo[];
}) {
  // 格式化金额显示（分转元）
  const formatAmount = (amount?: number) => {
    if (amount === undefined) return "-";
    return (amount / 100).toFixed(2) + "元";
  };

  // 渲染表格单元格
  const renderCell = React.useCallback((item: TempLimitInfo, columnKey: string) => {
    switch (columnKey) {
      case "tempLimit":
        return formatAmount(item.tempCreditLimit);
      case "tempLimitValidTime":
        return item.tempLimitValidTime || "-";
      case "adjustTime":
        return item.adjustTime ? dayjs(item.adjustTime).format("YYYY-MM-DD HH:mm:ss") : "-";
      default:
        return item[columnKey as keyof TempLimitInfo] || "-";
    }
  }, []);


  // 表格顶部内容
  const topContent = (
    <div className="flex justify-between items-center">
      <div>调额调价记录</div>
    </div>
  );

  return (
    <>
      <Table
        className={"col-span-full"}
        topContent={topContent}
        aria-label="调额调价记录表格"
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align="start"
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.tempLimitInfos || []}>
          {(item) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey.toString())}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}
