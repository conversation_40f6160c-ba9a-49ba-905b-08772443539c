import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Tooltip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Select,
  SelectItem, addToast
} from "@heroui/react";
import {
  LoanApplyStatusEnum, LoanInfo,
  LoanRecordDetail,
  RepayErrorCodeEnum,
  RepayErrorMessageMap,
  RepayRecord,
  RepayStatusEnum
} from "@/types/cp/loanInfo";
import { CreditInfo } from "@/types/cp/creditInfo";
import dayjs from "dayjs";
import { Icon } from "@iconify/react";
import React, { useState } from "react";
import { LoanUser } from "@/types/cp/loanUser";
import { updateLoanUser } from "@/actions/cp/loanUserActions";
import { notifyRepayResult } from "@/actions/cp/notify/repayResultNotify";

interface Props {
  records: RepayRecord[];
  loanUser: LoanUser;
  onRefresh?: () => void;
}

const repayStatusMap = {
  1: "还款中",
  2: "还款成功",
  4: "还款失败",
};

export default function RepayRecordTable({ records, loanUser, onRefresh }: Props) {
  const [selectedRecord, setSelectedRecord] = useState<RepayRecord | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [approveResult, setApproveResult] = useState<RepayStatusEnum>(RepayStatusEnum.Success);
  const [refuseReason, setRefuseReason] = useState<RepayErrorCodeEnum>(RepayErrorCodeEnum.SYSTEM_ERROR);

  return (
    <>
      <Table aria-label="还款记录" topContent={<div className={"text-xl"}>还款记录</div>}>
      <TableHeader>
        <TableColumn>还款流水号</TableColumn>
        <TableColumn>渠道方还款订单号</TableColumn>
        <TableColumn>渠道方借款订单号</TableColumn>
        <TableColumn>还款状态</TableColumn>
        <TableColumn>还款金额(元)</TableColumn>
        <TableColumn>优惠券减免金额(元)</TableColumn>
        <TableColumn>还款时间</TableColumn>
        <TableColumn>失败原因</TableColumn>
        <TableColumn>操作</TableColumn>
      </TableHeader>
      <TableBody>
        {records.map((record) => (
          <TableRow key={record.repayNo}>
            <TableCell>{record.repayNo}</TableCell>
            <TableCell>{record.outRepayNo}</TableCell>
            <TableCell>{record.outOrderNo}</TableCell>
            <TableCell>{repayStatusMap[record.repayStatus]}</TableCell>
            <TableCell>{record.repayAmount ? (record.repayAmount / 100).toFixed(2) : "-"}</TableCell>
            <TableCell>{record.reductionAmount ? (record.reductionAmount / 100).toFixed(2) : "-"}</TableCell>
            <TableCell>{record.repayTime ? dayjs(record.repayTime).format("YYYY-MM-DD HH:mm:ss") : "-"}</TableCell>
            <TableCell>{record.repayResult || "-"}</TableCell>
            <TableCell>
              {record.repayStatus === RepayStatusEnum.Repaying && (
              <Tooltip content="审批">
                <Button
                  isIconOnly
                  variant="light"
                  color="primary"
                  onPress={() => {
                    setSelectedRecord(record);
                    setIsModalOpen(true);
                  }}
                >
                  <Icon icon="mdi:check-circle-outline" width="20" height="20" />
                </Button>
              </Tooltip>)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
      <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
        <ModalContent>
          <ModalHeader>还款审批</ModalHeader>
          <ModalBody>
            <div className="grid gap-4">
              <Select
                label="审批结果"
                defaultSelectedKeys={[RepayStatusEnum.Success.toString()]}
                onSelectionChange={(key) => {
                  setApproveResult(parseInt(key.anchorKey as string));
                }}
              >
                <SelectItem key={RepayStatusEnum.Success}>
                  还款成功
                </SelectItem>
                <SelectItem key={RepayStatusEnum.Failed}>
                  还款失败
                </SelectItem>
              </Select>

              {approveResult === RepayStatusEnum.Failed && (
                <Select
                  label="拒绝原因"
                  defaultSelectedKeys={[RepayErrorCodeEnum.SYSTEM_ERROR]}
                  onSelectionChange={(key) => {
                    setRefuseReason(key.anchorKey as RepayErrorCodeEnum);
                  }}
                >
                  {Object.entries(RepayErrorCodeEnum).map(([key, value]) => (
                    <SelectItem key={value}>
                      {RepayErrorMessageMap[value]}
                    </SelectItem>
                  ))}
                </Select>)}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={() => setIsModalOpen(false)}>
              取消
            </Button>
            <Button color="primary" onPress={async () => {
              if (selectedRecord) {
                selectedRecord.repayStatus = approveResult;
                if (approveResult === RepayStatusEnum.Failed) {
                  selectedRecord.repayResult = RepayErrorMessageMap[refuseReason];
                }

                const loanInfo = loanUser.loanInfo as unknown as LoanInfo;
                const updatedLoanInfo = {
                  ...loanInfo,
                  status: {
                    ...loanInfo.status,
                    repayRecords: loanInfo.status.repayRecords.map((item) =>
                      item.repayNo === selectedRecord?.repayNo ? selectedRecord : item,
                    ),
                    loanOrders: loanInfo.status.loanOrders.map((item) => {
                      if (item.applyNo === selectedRecord?.tempRepayOrder?.applyNo && approveResult === RepayStatusEnum.Success) {
                        return selectedRecord.tempRepayOrder;
                      }
                      return item;
                    }),
                  },
                };

                // 审批成功时恢复用户额度
                let updatedLoanUser = {
                  ...loanUser,
                  loanInfo: updatedLoanInfo,
                };

                if (approveResult === RepayStatusEnum.Success) {
                  const creditInfo = loanUser.creditInfo as unknown as CreditInfo;
                  let remainingRepayAmount = selectedRecord.repayAmount || 0;

                  // 先恢复临时额度
                  const tempLimitInfos = creditInfo.status.tempLimitInfos || [];
                  for (let i = 0; i < tempLimitInfos.length; i++) {
                    const tempLimitInfo = tempLimitInfos[i];
                    if (tempLimitInfo.tempLimitValidTime) {
                      const tempLimitValidTime = dayjs(tempLimitInfo.tempLimitValidTime);
                      if (tempLimitValidTime.isAfter(dayjs())) {
                        const tempLimitToRestore = Math.min(
                          (tempLimitInfo.tempCreditLimit || 0) - (tempLimitInfo.tempAvailableLimit || 0),
                          remainingRepayAmount,
                        );
                        tempLimitInfo.tempAvailableLimit =
                          (tempLimitInfo.tempAvailableLimit || 0) + tempLimitToRestore;
                        remainingRepayAmount -= tempLimitToRestore;
                      }
                    }
                  }

                  // 如果临时额度恢复完还有剩余，再恢复固定额度
                  if (remainingRepayAmount > 0) {
                    creditInfo.status.remainLimit += remainingRepayAmount;
                    // 确保可用额度不超过授信总额度
                    if (creditInfo.status.remainLimit > creditInfo.status.creditLimit) {
                      creditInfo.status.remainLimit = creditInfo.status.creditLimit;
                    }
                  }

                  updatedLoanUser = {
                    ...updatedLoanUser,
                    creditInfo: creditInfo,
                  };
                }

                await updateLoanUser(updatedLoanUser);
              }


              setIsModalOpen(false);

              addToast({
                title: "审批成功",
                color: "success",
              });

              onRefresh?.();

              if (
                selectedRecord?.tempRepayOrder?.autoCallback
              ) {
                setTimeout(async () => {
                  await notifyRepayResult(
                    loanUser.id || 0,
                    {
                      userId: loanUser.userId,
                      repayNo: selectedRecord?.repayNo || "",
                      outRepayNo: selectedRecord?.outRepayNo || "",
                      loanApplyNo: selectedRecord?.tempRepayOrder?.applyNo || "",
                      outOrderNo: selectedRecord?.outOrderNo || "",
                      repaySource: "mock",
                      repayStatus: approveResult,
                      repayAmount: selectedRecord?.repayAmount || 0,
                      repayType: selectedRecord?.tempRepayOrder?.repayMethod || 0,
                      reductionAmount: selectedRecord?.reductionAmount || 0,
                      repayTime: Date.now(),
                      repayTerms: [],
                    },
                    "development",
                  );
                }, selectedRecord?.tempRepayOrder?.callBackDelay * 1000);
            }}}>
              确认
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}