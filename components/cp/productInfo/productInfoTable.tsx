import React, { useState } from "react";
import {
  Button,
  Checkbox,
  DatePicker,
  Input,
  Modal,
  ModalContent,
  NumberInput,
  Select,
  SelectItem,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalHeader } from "@heroui/modal";

import { repayMethods } from "@/types";
import { ProductInfo } from "@/app/cp/creditInfoQuery";
import { DeleteIcon } from "@heroui/shared-icons";
import { CalendarDate, DateValue } from "@internationalized/date";
import { I18nProvider } from "@react-aria/i18n";
import { RepayMethodEnum } from "@/types/cp/creditInfo";
import { Icon } from "@iconify/react";

const columns = [
  { name: "还款方式", uid: "repayMethod" },
  { name: "年利率（%）", uid: "apr" },
  { name: "临价年利率（%）", uid: "tempApr" },
  { name: "临价截止时间", uid: "tempPriceDueTime" },
  { name: "期数列表", uid: "termNums" },
  { name: "是否为动态利率", uid: "isDynamicRate" },
  { name: "操作", uid: "actions" },
];

export default function ProductInfoTable(props: {
  productInfos?: ProductInfo[];
  setProductInfos: (productInfos: ProductInfo[]) => void;
}) {
  const [newProductInfo, setNewProductInfo] = useState<ProductInfo>({
    earlyRepay: false,
    termNums: [3, 6, 12],
    apr: "",
    dayRate: "",
    repayMethod: repayMethods[0].key,
    tempApr: "",
    tempDayRate: "",
  });
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const createProductInfo = () => {
    if (props.productInfos) {
      props.setProductInfos([...props.productInfos, newProductInfo]);
      onClose();
    }
  };

  function deleteProductInfo(repayMethod: number) {
    if (props.productInfos) {
      props.setProductInfos(
        props.productInfos.filter((item) => item.repayMethod !== repayMethod),
      );
    }
  }

  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "earlyRepay":
        return <div>{cellValue ? "是" : "否"}</div>;
      case "termNums":
        return <div>{cellValue.join(",")}</div>;
      case "repayMethod":
        return (
          <Select
            size={"lg"}
            className="max-w-xs"
            defaultSelectedKeys={[cellValue.toString()]}
            onSelectionChange={(key) => {
              item.repayMethod = parseInt(key.anchorKey as string);
            }}
          >
            {repayMethods
              .filter(
                (method) =>
                  // 保留当前行的还款方式，过滤掉其他行已存在的还款方式
                  method.key === cellValue ||
                  !props.productInfos?.some(
                    (item) => item.repayMethod === method.key,
                  ),
              )
              .map((method) => (
                <SelectItem key={method.key}>{method.label}</SelectItem>
              ))}
          </Select>
        );
      case "apr":
        return (
          <NumberInput
            className="max-w-xs"
            defaultValue={cellValue}
            formatOptions={{
              style: "percent",
            }}
            label={columns.find((col) => col.uid === columnKey)?.name}
            minValue={0}
            isRequired
            onValueChange={(value) => {
              if (!value) {
                return; // 不允许为空，直接返回
              }
              const aprValue = parseFloat(value.toString());
              item[columnKey] = value.toString();
              // 年利率除以365得到日利率，保留4位小数
              item.dayRate = (aprValue / 365).toFixed(4);
              // 年利率除以12得到月利率，保留4位小数
              item.monthRate = (aprValue / 12).toFixed(4);
            }}
          />
        );
      case "tempApr":
        return (
          <NumberInput
            className="max-w-xs"
            defaultValue={cellValue}
            label={columns.find((col) => col.uid === columnKey)?.name}
            onValueChange={(value) => {
              if (!value) {
                item[columnKey] = null;
                item.tempDayRate = null;
                item.tempMonthRate = null;
              } else {
                const tempAprValue = parseFloat(value.toString());
                item[columnKey] = value.toString();
                // 临价年利率除以365得到临价日利率，保留4位小数
                item.tempDayRate = (tempAprValue / 365).toFixed(4);
                // 临价年利率除以12得到临价月利率，保留4位小数
                item.tempMonthRate = (tempAprValue / 12).toFixed(4);
              }
            }}
          />
        );
      case "tempPriceDueTime":
        return (
          <I18nProvider locale="zh-CN">
            <DatePicker
              className="max-w-xs"
              defaultValue={
                cellValue
                  ? new CalendarDate(
                      parseInt(cellValue.split(" ")[0].split("-")[0]),
                      parseInt(cellValue.split(" ")[0].split("-")[1]),
                      parseInt(cellValue.split(" ")[0].split("-")[2]),
                    )
                  : undefined
              }
              label="临价截止时间"
              onChange={(date: DateValue | null) => {
                if (date) {
                  item[columnKey] =
                    `${date.year}-${String(date.month).padStart(2, "0")}-${String(date.day).padStart(2, "0")} 00:00:00`;
                } else {
                  item[columnKey] = undefined;
                }
              }}
            />
          </I18nProvider>
        );
      case "isDynamicRate":
        return (
          <Switch
            isDisabled={item.repayMethod !== RepayMethodEnum.FixedTerm}
            defaultSelected={cellValue}
            onChange={() => {
              item[columnKey] = !cellValue;
            }}
          />
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除">
              <span
                className="text-lg text-danger cursor-pointer active:opacity-50"
                onClick={() => deleteProductInfo(item.repayMethod)}
              >
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>还款方式</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table
        selectionMode={"single"}
        className={"col-span-full"}
        topContent={topContent}
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.productInfos}>
          {(item) => (
            <TableRow key={item.repayMethod}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的还款方式
              </ModalHeader>
              <ModalBody>
                <Select
                  className="w-full"
                  label="还款方式"
                  onSelectionChange={(key) => {
                    const repayMethod = parseInt(key.anchorKey as string);
                    setNewProductInfo({
                      ...newProductInfo,
                      repayMethod,
                    });
                  }}
                >
                  {repayMethods
                    .filter(
                      (method) =>
                        !props.productInfos?.some(
                          (item) => item.repayMethod === method.key,
                        ),
                    )
                    .map((status) => (
                      <SelectItem key={status.key}>{status.label}</SelectItem>
                    ))}
                </Select>
                <Input
                  label="年利率"
                  variant="bordered"
                  onValueChange={(value) => {
                    const dayRate = value
                      ? (parseFloat(value) / 365).toFixed(6)
                      : "";
                    const monthRate = value
                      ? (parseFloat(value) / 12).toFixed(6)
                      : "";
                    setNewProductInfo({
                      ...newProductInfo,
                      apr: value,
                      monthRate: monthRate,
                      dayRate: dayRate,
                    });
                  }}
                />
                <Input
                  label="临价年利率"
                  variant="bordered"
                  onValueChange={(value) => {
                    const tempDayRate = value
                      ? (parseFloat(value) / 365).toFixed(6)
                      : "";
                    const tempMonthRate = value
                      ? (parseFloat(value) / 12).toFixed(6)
                      : "";
                    setNewProductInfo({
                      ...newProductInfo,
                      tempApr: value,
                      tempMonthRate: tempMonthRate,
                      tempDayRate: tempDayRate,
                    });
                  }}
                />
                {newProductInfo.tempApr && (
                  <I18nProvider locale="zh-CN">
                    <DatePicker
                      className="max-w-xs"
                      label="临价截止时间"
                      onChange={(date: DateValue | null) => {
                        if (date) {
                          setNewProductInfo({
                            ...newProductInfo,
                            tempPriceDueTime: `${date.year}-${String(date.month).padStart(2, "0")}-${String(date.day).padStart(2, "0")} 00:00:00`,
                          });
                        } else {
                          setNewProductInfo({
                            ...newProductInfo,
                            tempPriceDueTime: undefined,
                          });
                        }
                      }}
                    />
                  </I18nProvider>
                )}

                <div className={"flex gap-2 items-center"}>
                  <Switch
                    isDisabled={newProductInfo.repayMethod !== RepayMethodEnum.FixedTerm}
                    isSelected={newProductInfo.isDynamicRate}
                    onChange={() =>
                      setNewProductInfo({
                        ...newProductInfo,
                        isDynamicRate: !newProductInfo.isDynamicRate,
                      })
                    }
                  >是否为动态利率</Switch>
                  <Tooltip
                    content={
                      <div className="w-36 px-1 py-2">
                        <div className="text-tiny">
                          动态利率表示该还款方式的年利率会根据期数的切换而变化，仅支持等额本息(按期还)
                        </div>
                      </div>
                    }
                  >
                    <Icon icon="mdi:question-mark-circle" />
                  </Tooltip>
                </div>


                <div>还款期数</div>
                <div className="flex gap-4">
                  <Checkbox
                    isSelected={newProductInfo.termNums.includes(3)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 3],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 3,
                          ),
                        });
                      }
                    }}
                  >
                    3期
                  </Checkbox>
                  <Checkbox
                    defaultSelected
                    isSelected={newProductInfo.termNums.includes(6)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 6],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 6,
                          ),
                        });
                      }
                    }}
                  >
                    6期
                  </Checkbox>
                  <Checkbox
                    defaultSelected
                    isSelected={newProductInfo.termNums.includes(12)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 12],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 12,
                          ),
                        });
                      }
                    }}
                  >
                    12期
                  </Checkbox>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createProductInfo}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
