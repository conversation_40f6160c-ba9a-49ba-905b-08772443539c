import { TempRateInfo } from "@/types/cp/creditInfo";
import dayjs from "dayjs";
import React from "react";
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@heroui/react";

export default function TempRatetable(props: {
  tempRateInfos?: TempRateInfo[];
}) {
  const columns = [
    { name: "临价年利率", uid: "tempAdjustRate" },
    { name: "临价截止时间", uid: "tempLimitValidTime" },
    { name: "调价时间", uid: "adjustTime" },
  ];

  const renderCell = React.useCallback((item: TempRateInfo, columnKey: keyof TempRateInfo) => {
    const cellValue = item[columnKey];
    switch (columnKey) {
      case "tempAdjustRate":
        return <div>{cellValue ? `${cellValue}%` : ""}</div>;
      case "tempLimitValidTime":
        return (
          <div>
            {cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );
      case "adjustTime":
        return (
          <div>
            {cellValue
              ? dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss')
              : ""}
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>临价记录</div>
    </div>
  );

  return (
    <>
      <Table
        className={"col-span-full"}
        topContent={topContent}
        aria-label="临价记录表格"
      >
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align="start"
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.tempRateInfos || []}>
          {(item) => (
            <TableRow key={item.id}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey.toString())}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}
