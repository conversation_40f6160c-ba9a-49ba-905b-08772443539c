import {
  addToast,
  Button,
  Input,
  Modal,
  ModalBody,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import React from "react";
import {
  BusinessTypeEnum,
  Contract,
  ContractInfo,
  DefaultCheckedEnum,
  ForceReadEnum,
} from "@/types/cp/contractInfo";
import { DeleteIcon } from "@heroui/shared-icons";
import { LoanUser } from "@/types/cp/loanUser";
import { updateLoanUser } from "@/actions/cp/loanUserActions";

export default function ContractTable(props: {
  contracts?: Contract[];
  loanUser: LoanUser;
  title: string;
  onUpdate?: () => void;
}) {
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const [newContract, setNewContract] = React.useState<Contract>({
    id: "",
    contractName: "",
    defaultChecked: DefaultCheckedEnum.UNCHECKED,
    forceRead: ForceReadEnum.FORCE_READ,
    contractUrl: "",
    contractContent: "",
    businessType: 1,
  });

  const columns = [
    { name: "协议名称", uid: "contractName" },
    { name: "协议地址", uid: "contractUrl" },
    { name: "协议内容", uid: "contractContent" },
    { name: "是否已阅读", uid: "isRead" },
    { name: "操作", uid: "actions" },
  ];

  const createContract =async () => {
    if (!newContract.contractName) {
      addToast({
        title: "提示",
        description: "请填写合同名称",
        color: "warning",
      });
      return;
    }
    newContract.id = crypto.randomUUID();

    switch (props.title) {
      case "授信协议":
        newContract.businessType = BusinessTypeEnum.CREDIT;
        break;
      case "绑卡协议":
        newContract.businessType = BusinessTypeEnum.BIND_CARD;
        break;
      case "借款协议":
        newContract.businessType = BusinessTypeEnum.LOAN;
        break;
      case "用信补录协议":
        newContract.businessType = BusinessTypeEnum.REOFFER;
        break;
      default:
        break;
    }

    if (!props.loanUser.contractsInfo) {
      props.loanUser.contractsInfo = {
        status: {
          contractList: [],
        },
      };
    }
    if (!props.loanUser.contractsInfo.status.contractList) {
      props.loanUser.contractsInfo.status.contractList = [];
    }
    props.loanUser.contractsInfo.status.contractList.push(newContract);
    await updateLoanUser(props.loanUser);
    addToast({
      title: "新增成功",
      color: "success",
    });
    onClose();
    props.onUpdate?.();
  };

  const deleteContract =async (contractId: string) => {
    const contractInfo = props.loanUser.contractsInfo as unknown as ContractInfo
    contractInfo.status.contractList = contractInfo.status.contractList.filter((item) => item.id !== contractId);
    await updateLoanUser(props.loanUser);
    addToast({
      title: "删除成功",
      color: "success",
    });
    props.onUpdate?.();
  };

  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "defaultChecked":
        return <div>{cellValue ? "是" : "否"}</div>;
      case "forceRead":
        return <div>{cellValue ? "是" : "否"}</div>;
      case "isRead":
        return <div>{cellValue ? "是" : "否"}</div>;
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除">
              <span className="text-lg text-danger cursor-pointer active:opacity-50" onClick={() => deleteContract(item.id)}>
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>{props.title}</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={"start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.contracts}>
          {(item) => (
            <TableRow key={item.contractName}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的协议
              </ModalHeader>
              <ModalBody>
                <Input
                  isRequired
                  label="协议名称"
                  onValueChange={(value) => {
                    setNewContract({
                      ...newContract,
                      contractName: value,
                    });
                  }}
                />
                <div className="text-sm text-gray-500 mb-2">
                  地址和内容选其一填写即可，如果都填以内容为准，地址示例：https://loancontract.ppdai.com/latest/agency/expandSearchZhengxin.html
                </div>
                <Input
                  label="协议地址"
                  onValueChange={(value) => {
                    setNewContract({
                      ...newContract,
                      contractUrl: value,
                    });
                  }}
                />
                <Input
                  label="协议内容"
                  onValueChange={(value) => {
                    setNewContract({
                      ...newContract,
                      contractContent: value,
                    });
                  }}
                />
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createContract}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
