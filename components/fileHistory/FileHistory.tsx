import React, { useState, useEffect } from 'react';
import { getFileModificationHistory } from '@/actions/fileChange/fileChange';
import { Clock, User, FileText, ChevronDown, ChevronRight, Calendar } from 'lucide-react';
import { addToast } from '@heroui/react';

interface ModificationRecord {
  lineNumber: number;
  content: string;
  modifiedAt: Date;
}

interface UserModification {
  userName: string;
  modifications: ModificationRecord[];
  totalModifications: number;
  lastModified: Date | null;
}

interface FileHistoryData {
  file: {
    id: string;
    path: string;
    originalContent: string;
    createdAt: Date;
    updatedAt: Date;
    uploadSession: {
      id: string;
      name: string;
    };
  };
  history: UserModification[];
  totalModifications: number;
}

interface FileHistoryProps {
  fileId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

const FileHistory: React.FC<FileHistoryProps> = ({ fileId, isOpen, onClose }) => {
  const [historyData, setHistoryData] = useState<FileHistoryData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedUsers, setExpandedUsers] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (fileId && isOpen) {
      loadFileHistory(fileId);
    }
  }, [fileId, isOpen]);

  const loadFileHistory = async (id: string) => {
    setIsLoading(true);
    try {
      const data = await getFileModificationHistory(id);
      if (data) {
        setHistoryData(data);
      } else {
        addToast({
          title: "获取历史记录失败",
          color: "danger",
        });
      }
    } catch (error) {
      console.error('加载文件历史记录失败:', error);
      addToast({
        title: "获取历史记录失败",
        color: "danger",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleUserExpansion = (userName: string) => {
    const newExpanded = new Set(expandedUsers);
    if (newExpanded.has(userName)) {
      newExpanded.delete(userName);
    } else {
      newExpanded.add(userName);
    }
    setExpandedUsers(newExpanded);
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;
    return formatDate(date);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Clock className="text-blue-500" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                文件修改历史
              </h2>
              {historyData && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {historyData.file.path}
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">加载历史记录中...</span>
            </div>
          ) : historyData ? (
            <div className="space-y-6">
              {/* 文件信息摘要 */}
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <FileText className="text-gray-500" size={16} />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">文件信息</span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">创建时间:</span>
                    <p className="text-gray-900 dark:text-gray-100">{formatDate(historyData.file.createdAt)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">最后更新:</span>
                    <p className="text-gray-900 dark:text-gray-100">{formatDate(historyData.file.updatedAt)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">总修改数:</span>
                    <p className="text-gray-900 dark:text-gray-100">{historyData.totalModifications} 行</p>
                  </div>
                </div>
              </div>

              {/* 修改历史列表 */}
              {historyData.history.length > 0 ? (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                    <User className="text-blue-500" size={20} />
                    修改记录
                  </h3>
                  
                  {historyData.history.map((userMod) => (
                    <div key={userMod.userName} className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                      <div 
                        className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                        onClick={() => toggleUserExpansion(userMod.userName)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                            {userMod.userName.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-gray-100">{userMod.userName}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              修改了 {userMod.totalModifications} 行
                              {userMod.lastModified && (
                                <span className="ml-2">· {formatRelativeTime(userMod.lastModified)}</span>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {expandedUsers.has(userMod.userName) ? (
                            <ChevronDown className="text-gray-400" size={20} />
                          ) : (
                            <ChevronRight className="text-gray-400" size={20} />
                          )}
                        </div>
                      </div>

                      {expandedUsers.has(userMod.userName) && (
                        <div className="border-t border-gray-200 dark:border-gray-600">
                          <div className="p-4 space-y-3">
                            {userMod.modifications.map((mod, index) => (
                              <div key={index} className="flex items-start gap-3 p-3 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                                <div className="flex-shrink-0 w-12 text-right">
                                  <span className="text-sm font-mono text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                    {mod.lineNumber}
                                  </span>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2 mb-1">
                                    <Calendar className="text-gray-400" size={14} />
                                    <span className="text-xs text-gray-500 dark:text-gray-400">
                                      {formatDate(mod.modifiedAt)}
                                    </span>
                                  </div>
                                  <pre className="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 p-2 rounded overflow-x-auto">
                                    {mod.content}
                                  </pre>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Clock className="mx-auto text-gray-400 mb-4" size={48} />
                  <p className="text-gray-500 dark:text-gray-400">暂无修改记录</p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">无法加载历史记录</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileHistory; 