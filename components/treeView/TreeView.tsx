'use client';

import { useState, type FC } from 'react';
import type { FileSystemNode, FileInfoNode } from '@/utils/file-compare';
import { FileText, ChevronRight } from 'lucide-react';

// 组件 Props 定义
interface TreeViewProps {
  root: FileSystemNode;
  onFileSelect: (file: FileInfoNode) => void;
  modifiedFiles: string[];
}

interface TreeNodeProps {
  node: FileSystemNode;
  onFileSelect: (file: FileInfoNode) => void;
  modifiedFiles: string[];
}

interface FileNodeProps {
  file: FileInfoNode;
  onFileSelect: (file: FileInfoNode) => void;
  isModified: boolean;
}

// 文件节点组件
const FileNode: FC<FileNodeProps> = ({ file, onFileSelect, isModified }) => {
  return (
    <div
      className="flex items-center pl-10 pr-2 py-1.5 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-r-md"
      onClick={() => onFileSelect(file)}
    >
      <FileText size={16} className="mr-2.5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
      <span className={`text-sm truncate ${
        isModified
          ? 'text-orange-600 dark:text-orange-400 font-semibold'
          : 'text-gray-800 dark:text-gray-200'
      }`}>
        {file.name}
      </span>
    </div>
  );
};

// 目录节点组件
const DirectoryNode: FC<{ name: string; children: React.ReactNode }> = ({ name, children }) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div>
      <div
        className="flex items-center pl-4 pr-2 py-1.5 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded-r-md"
        onClick={() => setIsOpen(!isOpen)}
      >
        <ChevronRight size={18} className={`mr-2 text-gray-500 dark:text-gray-400 transition-transform ${isOpen ? 'rotate-90' : ''}`} />
        <span className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">{name}</span>
      </div>
      {isOpen && <div className="border-l border-gray-200 dark:border-gray-600 ml-[22px]">{children}</div>}
    </div>
  );
};

// 递归的树节点组件
const TreeNode: FC<TreeNodeProps> = ({ node, onFileSelect, modifiedFiles }) => {
  if (node.type === 'directory') {
    return (
      <DirectoryNode name={node.name}>
        {node.children.map(child => (
          <TreeNode key={child.name} node={child} onFileSelect={onFileSelect} modifiedFiles={modifiedFiles} />
        ))}
      </DirectoryNode>
    );
  }

  const isModified = modifiedFiles?.includes(node.path) || false;
  return <FileNode file={node} onFileSelect={onFileSelect} isModified={isModified} />;
};

// 导出的主组件
export const TreeView: FC<TreeViewProps> = ({ root, onFileSelect, modifiedFiles }) => {
  return (
    <div className="py-2">
      {root.children.map(node => (
        <TreeNode key={node.name} node={node} onFileSelect={onFileSelect} modifiedFiles={modifiedFiles} />
      ))}
    </div>
  );
};
