import React, { useEffect, useState } from 'react';
import { addToast } from '@heroui/react';
import { User, FileText, Edit3 } from 'lucide-react';

interface User {
  id: string;
  username: string;
}

interface FileChange {
  fileId: string;
  content: string;
  userId: string;
  username: string;
  timestamp: Date;
}

interface CollaborationNotificationProps {
  onUserJoined?: (user: User) => void;
  onUserLeft?: (user: User) => void;
  onFileChange?: (change: FileChange) => void;
}

export const CollaborationNotification: React.FC<CollaborationNotificationProps> = ({
  onUserJoined,
  onUserLeft,
  onFileChange
}) => {
  const [notifications, setNotifications] = useState<Array<{
    id: string;
    type: 'user-joined' | 'user-left' | 'file-changed';
    message: string;
    timestamp: Date;
  }>>([]);

  useEffect(() => {
    // 用户加入通知
    if (onUserJoined) {
      const handleUserJoined = (user: User) => {
        const message = `${user.username} 加入了编辑`;
        addToast({
          title: "用户加入",
          description: message,
          color: "success",
        });
        
        setNotifications(prev => [...prev, {
          id: `${user.id}-joined`,
          type: 'user-joined',
          message,
          timestamp: new Date()
        }]);
      };
      
      // 这里应该连接到实际的WebSocket事件
      // 暂时用空函数占位
    }

    // 用户离开通知
    if (onUserLeft) {
      const handleUserLeft = (user: User) => {
        const message = `${user.username} 离开了编辑`;
        addToast({
          title: "用户离开",
          description: message,
          color: "warning",
        });
        
        setNotifications(prev => [...prev, {
          id: `${user.id}-left`,
          type: 'user-left',
          message,
          timestamp: new Date()
        }]);
      };
    }

    // 文件变更通知
    if (onFileChange) {
      const handleFileChange = (change: FileChange) => {
        const message = `${change.username} 修改了文件`;
        addToast({
          title: "文件变更",
          description: message,
          color: "primary",
        });
        
        setNotifications(prev => [...prev, {
          id: `${change.userId}-${change.timestamp.getTime()}`,
          type: 'file-changed',
          message,
          timestamp: change.timestamp
        }]);
      };
    }
  }, [onUserJoined, onUserLeft, onFileChange]);

  // 清理旧通知
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      setNotifications(prev => 
        prev.filter(notification => 
          now.getTime() - notification.timestamp.getTime() < 5000
        )
      );
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return null; // 这个组件只负责显示通知，不需要渲染UI
}; 