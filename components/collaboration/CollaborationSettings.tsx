import React, { useState } from 'react';
import { Switch, Slider } from '@heroui/react';

interface CollaborationSettingsProps {
  isCollaborationEnabled: boolean;
  onToggleCollaboration: (enabled: boolean) => void;
  notificationSensitivity: number; // 0-100, 0=关闭通知, 100=所有通知
  onNotificationSensitivityChange: (value: number) => void;
}

export const CollaborationSettings: React.FC<CollaborationSettingsProps> = ({
  isCollaborationEnabled,
  onToggleCollaboration,
  notificationSensitivity,
  onNotificationSensitivityChange
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const getSensitivityLabel = (value: number) => {
    if (value === 0) return '关闭通知';
    if (value <= 25) return '低敏感度';
    if (value <= 50) return '中等敏感度';
    if (value <= 75) return '高敏感度';
    return '最高敏感度';
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        title="协作编辑设置"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <span className="hidden sm:inline">设置</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 p-4">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
            协作编辑设置
          </h3>
          
          <div className="space-y-4">
            {/* 协作编辑开关 */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  启用协作编辑
                </h4>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  允许多人同时编辑文件
                </p>
              </div>
              <Switch
                isSelected={isCollaborationEnabled}
                onValueChange={onToggleCollaboration}
                size="sm"
              />
            </div>

            {/* 通知敏感度 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  通知敏感度
                </h4>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {getSensitivityLabel(notificationSensitivity)}
                </span>
              </div>
              <Slider
                size="sm"
                step={25}
                color="primary"
                showSteps={true}
                maxValue={100}
                minValue={0}
                value={notificationSensitivity}
                onChange={onNotificationSensitivityChange}
                className="max-w-md"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                控制文件变更通知的显示频率
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}; 