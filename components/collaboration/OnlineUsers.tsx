import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Chip } from '@heroui/react';
import { ModernAvatar } from './ModernAvatar';

interface OnlineUser {
  id: string;
  username: string;
}

interface OnlineUsersProps {
  users: OnlineUser[];
  currentUser?: string;
}

export const OnlineUsers: React.FC<OnlineUsersProps> = ({ users, currentUser }) => {
  if (users.length === 0) {
    return null;
  }

  return (
    <Card className="p-3 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
      <div className="flex items-center gap-3">

        <div className="flex items-center gap-2">
           {users.slice(0, 3).map((user) => (
             <Tooltip key={user.id} content={user.username}>
               <div className="flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200 cursor-pointer">
                 <ModernAvatar 
                   username={user.username === currentUser ? '你' : user.username}
                   isOnline={true} 
                   size="sm" 
                 />
               </div>
             </Tooltip>
           ))}
           
           {users.length > 3 && (
             <Tooltip content={users.slice(3).map(u => u.username).join(', ')}>
               <Chip
                 color="secondary"
                 variant="flat"
                 size="sm"
                 className="cursor-pointer transition-all duration-200 hover:scale-105"
               >
                 +{users.length - 3}
               </Chip>
             </Tooltip>
           )}
         </div>
      </div>
    </Card>
  );
}; 