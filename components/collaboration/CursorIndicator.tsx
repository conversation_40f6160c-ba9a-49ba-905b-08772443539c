import React, { useEffect, useState } from 'react';
import { Card } from '@heroui/react';
import { ModernAvatar } from './ModernAvatar';

interface CursorPosition {
  line: number;
  column: number;
  username: string;
  userId: string;
  fileId?: string;
}

interface CursorIndicatorProps {
  cursors: CursorPosition[];
  currentUser?: string;
  position?: 'top-right' | 'inline';
  currentFileId?: string;
}

export const CursorIndicator: React.FC<CursorIndicatorProps> = ({ cursors, currentUser, position = 'top-right', currentFileId }) => {
  const [visibleCursors, setVisibleCursors] = useState<CursorPosition[]>([]);

  useEffect(() => {
    // 过滤掉当前用户的光标，并去重
    // 只有当当前文件ID存在时，才显示其他用户的光标
    const otherCursors = cursors
      .filter(cursor => cursor.username !== currentUser)
      .reduce((unique, cursor) => {
        const exists = unique.find(item => item.username === cursor.username);
        if (!exists) {
          unique.push(cursor);
        }
        return unique;
      }, [] as CursorPosition[]);
    
    // 只有当有当前文件ID时，才显示光标指示器
    if (currentFileId) {
      // 只显示与当前文件ID匹配的光标
      const fileSpecificCursors = otherCursors.filter(cursor => {
        // 检查光标是否属于当前文件
        return cursor.fileId === currentFileId;
      });
      setVisibleCursors(fileSpecificCursors);
    } else {
      setVisibleCursors([]);
    }
  }, [cursors, currentUser, currentFileId]);

  if (visibleCursors.length === 0) {
    return null;
  }

       return (
    <div className={position === 'top-right' ? "absolute top-1 right-1 z-10" : "inline-block"}>
      <div className={position === 'top-right' ? "flex flex-col gap-1" : "flex items-center gap-2"}>
        {visibleCursors.map((cursor) => (
                     <Card
             key={cursor.userId}
             className={position === 'top-right' 
               ? "p-2 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 cursor-pointer"
               : "p-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer"
             }
           >
                            <div className="flex items-center gap-2">
                 <ModernAvatar 
                   username={cursor.username} 
                   isOnline={true} 
                   size="sm" 
                 />
                 <div className="flex-1 min-w-0">
                   {position === 'top-right' ? (
                     <>
                       <div className="flex items-center gap-1">
                         <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                           {cursor.username}
                         </span>
                       </div>
                       <div className="text-xs text-gray-500 dark:text-gray-400">
                         行 {typeof cursor.line === 'number' ? cursor.line + 1 : '?'}, 列 {typeof cursor.column === 'number' ? cursor.column + 1 : '?'}
                       </div>
                     </>
                                       ) : (
                      <div className="flex flex-col">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                          {cursor.username}
                        </span>
                        <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                          正在修改该文件
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          行 {typeof cursor.line === 'number' ? cursor.line + 1 : '?'}, 列 {typeof cursor.column === 'number' ? cursor.column + 1 : '?'}
                        </span>
                      </div>
                    )}
                 </div>
               </div>
           </Card>
        ))}
      </div>
    </div>
  );
}; 