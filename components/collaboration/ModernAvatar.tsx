import React from 'react';

interface ModernAvatarProps {
  username: string;
  isOnline?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const getColorByUsername = (username: string) => {
  const colors = [
    'from-blue-400 to-purple-500',
    'from-green-400 to-blue-500',
    'from-purple-400 to-pink-500',
    'from-yellow-400 to-orange-500',
    'from-pink-400 to-red-500',
    'from-indigo-400 to-purple-500',
    'from-teal-400 to-green-500',
    'from-orange-400 to-red-500',
  ];
  
  const index = username.charCodeAt(0) % colors.length;
  return colors[index];
};

const getSizeClasses = (size: 'sm' | 'md' | 'lg') => {
  switch (size) {
    case 'sm':
      return 'w-8 h-8 text-xs';
    case 'md':
      return 'w-10 h-10 text-sm';
    case 'lg':
      return 'w-12 h-12 text-base';
    default:
      return 'w-8 h-8 text-xs';
  }
};

export const ModernAvatar: React.FC<ModernAvatarProps> = ({ 
  username, 
  isOnline = true, 
  size = 'sm' 
}) => {
  const colorGradient = getColorByUsername(username);
  const sizeClasses = getSizeClasses(size);
  const initial = username.charAt(0).toUpperCase();

  return (
    <div className="relative">
      <div 
        className={`${sizeClasses} rounded-full bg-gradient-to-br ${colorGradient} flex items-center justify-center text-white font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105`}
      >
        {initial}
      </div>
             {isOnline && (
         <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 rounded-full border-2 border-white dark:border-gray-800 shadow-sm"></div>
       )}
    </div>
  );
}; 