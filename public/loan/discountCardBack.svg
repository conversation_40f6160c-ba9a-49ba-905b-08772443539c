<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="130" height="122" viewBox="0 0 130 122"><defs><linearGradient x1="0.5" y1="0" x2="0.5" y2="1" id="master_svg0_2_04308"><stop offset="0%" stop-color="#FFFFFF" stop-opacity="1"/><stop offset="50.40983557701111%" stop-color="#FFFFFF" stop-opacity="0.9876543283462524"/><stop offset="69.87704634666443%" stop-color="#FFFFFF" stop-opacity="0"/></linearGradient><mask id="master_svg1_1_62672/2_02231" style="mask-type:alpha" maskUnits="objectBoundingBox"><g><path d="M0 0C0 0 0 0 0 0L118 0C124.62741699796952 0 130 5.3725830020304794 130 12L130 122C130 122 130 122 130 122L0 122C0 122 0 122 0 122Z" fill="url(#master_svg0_2_04308)" fill-opacity="0.7" class="path-fill"/></g></mask><linearGradient x1="-0.0020913018379360437" y1="1" x2="1" y2="0" id="master_svg2_2_02210"><stop offset="0%" stop-color="#FDE5DA" stop-opacity="1"/><stop offset="100%" stop-color="#F9D7C1" stop-opacity="1"/></linearGradient>
    <style>
        .themed-opacity-group {
            opacity: 0.7; /* 恢复原来的值作为默认值 */
        }
        @media (prefers-color-scheme: dark) {
            .themed-opacity-group {
            opacity: 0.05; /* 深色模式下的值 - 请根据需要调整 */
            }
        }
    </style>
</defs><g style="opacity:0.699999988079071;" mask="url(#master_svg1_1_62672/2_02231)"><g transform="matrix(0.7071067690849304,-0.7071067690849304,0.7071067690849304,0.7071067690849304,-19.313808789564064,46.107689747499535)"><path d="M154,46.36767578125L46,46.36767578125L46,154.36767578125L154,154.36767578125L154,46.36767578125ZM47,153.36767578125L47,47.36767578125L153,47.36767578125L153,153.36767578125L47,153.36767578125ZM150,50.36767578125L50,50.36767578125L50,150.36767578125L150,150.36767578125L150,50.36767578125ZM51,149.36767578125L51,51.36767578125L149,51.36767578125L149,149.36767578125L51,149.36767578125ZM146,54.36767578125L54,54.36767578125L54,146.36767578125L146,146.36767578125L146,54.36767578125ZM55,145.36767578125L55,55.36767578125L145,55.36767578125L145,145.36767578125L55,145.36767578125ZM142,58.36767578125L58,58.36767578125L58,142.36767578125L142,142.36767578125L142,58.36767578125ZM59,141.36767578125L59,59.36767578125L141,59.36767578125L141,141.36767578125L59,141.36767578125ZM62,62.36767578125L138,62.36767578125L138,138.36767578125L62,138.36767578125L62,62.36767578125ZM63,63.36767578125L63,137.36767578125L137,137.36767578125L137,63.36767578125L63,63.36767578125ZM66,66.36767578125L134,66.36767578125L134,134.36767578125L66,134.36767578125L66,66.36767578125ZM67,67.36767578125L67,133.36767578125L133,133.36767578125L133,67.36767578125L67,67.36767578125ZM70,70.36767578125L130,70.36767578125L130,130.36767578125L70,130.36767578125L70,70.36767578125ZM71,71.36767578125L71,129.36767578125L129,129.36767578125L129,71.36767578125L71,71.36767578125ZM74,74.36767578125L126,74.36767578125L126,126.36767578125L74,126.36767578125L74,74.36767578125ZM75,75.36767578125L75,125.36767578125L125,125.36767578125L125,75.36767578125L75,75.36767578125ZM78,78.36767578125L122,78.36767578125L122,122.36767578125L78,122.36767578125L78,78.36767578125ZM79,79.36767578125L79,121.36767578125L121,121.36767578125L121,79.36767578125L79,79.36767578125ZM82,82.36767578125L118,82.36767578125L118,118.36767578125L82,118.36767578125L82,82.36767578125ZM83,83.36767578125L83,117.36767578125L117,117.36767578125L117,83.36767578125L83,83.36767578125Z" fill-rule="evenodd" fill="url(#master_svg2_2_02210)" class="themed-opacity-group"/></g></g></svg>