<?xml version="1.0" encoding="UTF-8"?>
<svg width="72px" height="48px" viewBox="0 0 72 48" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>用信_上传身份证_3照片模糊</title>
    <defs>
        <rect id="path-1" x="0" y="0" width="72" height="48" rx="4"></rect>
        <filter x="-6.2%" y="-12.8%" width="112.4%" height="125.6%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="1" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="21.完善资料_上传身份证" transform="translate(-184.000000, -526.000000)">
            <g id="编组-3" transform="translate(24.000000, 491.000000)">
                <g id="用信_上传身份证_3照片模糊" transform="translate(160.000000, 35.000000)">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <use id="蒙版" fill="#FFFFFF" xlink:href="#path-1"></use>
                    <g id="身份证-正面" transform="translate(8.000000, 8.000000)" fill-rule="nonzero">
                        <path d="M56,29.1509677 C56,30.72 54.5545379,32 52.7360533,32 L3.26394669,32 C1.49208989,32 0.046627792,30.72 0,29.1509677 L0,2.84903226 C0,1.27999998 1.4454621,0 3.26394669,0 L52.7360533,0 C54.5079101,0 55.9533722,1.27999998 56,2.84903226 L56,29.1509677 L56,29.1509677 Z" id="路径" fill="#F1F4FF"></path>
                        <path d="M4.01793722,13.9799425 L7.8429797,13.9799425 L7.8429797,15.82905 L4.01793722,15.82905 L4.01793722,13.9799425 Z M4.01793722,4.64635206 L7.8429797,4.64635206 L7.8429797,6.53948599 L4.01793722,6.53948599 L4.01793722,4.64635206 L4.01793722,4.64635206 Z M4.01793722,9.31314729 L7.8429797,9.31314729 L7.8429797,11.1622548 L4.01793722,11.1622548 L4.01793722,9.31314729 L4.01793722,9.31314729 Z M9.2423855,9.31314729 L20.8108067,9.31314729 L20.8108067,11.1622548 L9.2423855,11.1622548 L9.2423855,9.31314729 L9.2423855,9.31314729 Z M52.2331839,19.048728 C52.2331839,19.4531492 51.8655625,19.7901669 51.4104122,19.7901669 L36.6180274,19.7901669 C36.1628771,19.7901669 35.7952557,19.4531492 35.7952557,19.048728 C35.7952557,19.048728 35.7952557,16.3694373 38.0009841,15.4089368 C39.4014466,14.7854541 38.5086518,15.2909806 40.2242183,14.6674979 C41.9397849,14.0440152 41.9923022,13.8249536 41.9923021,13.8249536 L42.0098079,12.3757776 C42.0098079,12.3757776 41.3620941,11.9376545 41.1695305,10.5727328 C40.7668975,10.6738382 40.6443571,10.1514607 40.6093454,9.83129394 C40.5918397,9.51112715 40.3817703,8.51692489 40.8719322,8.6011793 C40.7668975,7.92714389 40.6968744,7.33736295 40.7318859,7.01719611 C40.8544264,5.90503772 42.079831,4.72547579 43.9529496,4.64122137 C46.1586779,4.72547579 47.033967,5.88818686 47.1565075,7.01719611 C47.1915191,7.3373629 47.1214959,7.94399475 47.0164612,8.6011793 C47.5066231,8.51692489 47.2965537,9.51112709 47.2615421,9.83129394 C47.2440364,10.1683116 47.1039901,10.6738382 46.7013572,10.5727328 C46.5087936,11.9376545 45.8610797,12.3589266 45.8610797,12.3589266 L45.8785855,13.8081028 C45.8785855,13.8081028 46.1061606,14.0103134 47.8217271,14.6337961 C49.5372936,15.2572788 48.6444988,14.7854541 50.0449613,15.4089368 C52.2331839,16.3694373 52.2331839,19.048728 52.2331839,19.048728 L52.2331839,19.048728 Z M9.2423855,4.64635206 L20.8108067,4.64635206 L20.8108067,6.53948599 L9.2423855,6.53948599 L9.2423855,4.64635206 Z M15.3840516,26.2424955 L32.7833303,26.2424955 L32.7833303,28.0916031 L15.3840516,28.0916031 L15.3840516,26.2424955 Z M9.2423855,13.9799425 L20.8108067,13.9799425 L20.8108067,15.82905 L9.2423855,15.82905 L9.2423855,13.9799425 Z M4.01793722,26.2424955 L14.2335995,26.2424955 L14.2335995,28.0916031 L4.01793722,28.0916031 L4.01793722,26.2424955 Z" id="形状" fill="#E5EAFB" filter="url(#filter-3)"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>