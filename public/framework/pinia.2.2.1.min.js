/*!
 * pinia v2.2.1
 * (c) 2024 <PERSON>
 * @license MIT
 */
var Pinia=function(t,e){"use strict";let n;const i=t=>n=t,s=Symbol();function o(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var r;t.MutationType=void 0,(r=t.MutationType||(t.MutationType={})).direct="direct",r.patchObject="patch object",r.patchFunction="patch function";const c="undefined"!=typeof window;const a=()=>{};function u(t,n,i,s=a){t.push(n);const o=()=>{const e=t.indexOf(n);e>-1&&(t.splice(e,1),s())};return!i&&e.getCurrentScope()&&e.onScopeDispose(o),o}function p(t,...e){t.slice().forEach((t=>{t(...e)}))}const f=t=>t(),h=Symbol(),l=Symbol();function d(t,n){t instanceof Map&&n instanceof Map?n.forEach(((e,n)=>t.set(n,e))):t instanceof Set&&n instanceof Set&&n.forEach(t.add,t);for(const i in n){if(!n.hasOwnProperty(i))continue;const s=n[i],r=t[i];t[i]=o(r)&&o(s)&&t.hasOwnProperty(i)&&!e.isRef(s)&&!e.isReactive(s)?d(r,s):s}return t}const y=Symbol(),v=new WeakMap;const{assign:$}=Object;function _(n,s,r={},c,_,b){let j;const S=$({actions:{}},r),m={deep:!0};let O,g,R,P=[],V=[];const w=c.state.value[n];let M;function A(i){let s;O=g=!1,"function"==typeof i?(i(c.state.value[n]),s={type:t.MutationType.patchFunction,storeId:n,events:R}):(d(c.state.value[n],i),s={type:t.MutationType.patchObject,payload:i,storeId:n,events:R});const o=M=Symbol();e.nextTick().then((()=>{M===o&&(O=!0)})),g=!0,p(P,s,c.state.value[n])}b||w||(e.isVue2?e.set(c.state.value,n,{}):c.state.value[n]={}),e.ref({});const k=b?function(){const{state:t}=r,e=t?t():{};this.$patch((t=>{$(t,e)}))}:a;const T=(t,e="")=>{if(h in t)return t[l]=e,t;const s=function(){i(c);const e=Array.from(arguments),o=[],r=[];let a;p(V,{args:e,name:s[l],store:E,after:function(t){o.push(t)},onError:function(t){r.push(t)}});try{a=t.apply(this&&this.$id===n?this:E,e)}catch(t){throw p(r,t),t}return a instanceof Promise?a.then((t=>(p(o,t),t))).catch((t=>(p(r,t),Promise.reject(t)))):(p(o,a),a)};return s[h]=!0,s[l]=e,s},x={_p:c,$id:n,$onAction:u.bind(null,V),$patch:A,$reset:k,$subscribe(i,s={}){const o=u(P,i,s.detached,(()=>r())),r=j.run((()=>e.watch((()=>c.state.value[n]),(e=>{("sync"===s.flush?g:O)&&i({storeId:n,type:t.MutationType.direct,events:R},e)}),$({},m,s))));return o},$dispose:function(){j.stop(),P=[],V=[],c._s.delete(n)}};e.isVue2&&(x._r=!1);const E=e.reactive(x);c._s.set(n,E);const C=(c._a&&c._a.runWithContext||f)((()=>c._e.run((()=>(j=e.effectScope()).run((()=>s({action:T})))))));for(const t in C){const i=C[t];if(e.isRef(i)&&(!e.isRef(W=i)||!W.effect)||e.isReactive(i))b||(!w||(I=i,e.isVue2?v.has(I):o(I)&&I.hasOwnProperty(y))||(e.isRef(i)?i.value=w[t]:d(i,w[t])),e.isVue2?e.set(c.state.value[n],t,i):c.state.value[n][t]=i);else if("function"==typeof i){const n=T(i,t);e.isVue2?e.set(C,t,n):C[t]=n,S.actions[t]=i}}var I,W;return e.isVue2?Object.keys(C).forEach((t=>{e.set(E,t,C[t])})):($(E,C),$(e.toRaw(E),C)),Object.defineProperty(E,"$state",{get:()=>c.state.value[n],set:t=>{A((e=>{$(e,t)}))}}),e.isVue2&&(E._r=!0),c._p.forEach((t=>{$(E,j.run((()=>t({store:E,app:c._a,pinia:c,options:S}))))})),w&&b&&r.hydrate&&r.hydrate(E.$state,w),O=!0,g=!0,E}let b="Store";function j(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]=function(){return t(this.$pinia)[n]},e)),{}):Object.keys(e).reduce(((n,i)=>(n[i]=function(){const n=t(this.$pinia),s=e[i];return"function"==typeof s?s.call(this,n):n[s]},n)),{})}const S=j;return t.PiniaVuePlugin=function(t){t.mixin({beforeCreate(){const t=this.$options;if(t.pinia){const e=t.pinia;if(!this._provided){const t={};Object.defineProperty(this,"_provided",{get:()=>t,set:e=>Object.assign(t,e)})}this._provided[s]=e,this.$pinia||(this.$pinia=e),e._a=this,c&&i(e)}else!this.$pinia&&t.parent&&t.parent.$pinia&&(this.$pinia=t.parent.$pinia)},destroyed(){delete this._pStores}})},t.acceptHMRUpdate=function(t,e){return()=>{}},t.createPinia=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let o=[],r=[];const c=e.markRaw({install(t){i(c),e.isVue2||(c._a=t,t.provide(s,c),t.config.globalProperties.$pinia=c,r.forEach((t=>o.push(t))),r=[])},use(t){return this._a||e.isVue2?o.push(t):r.push(t),this},_p:o,_a:null,_e:t,_s:new Map,state:n});return c},t.defineStore=function(t,o,r){let c,a;const u="function"==typeof o;function p(t,r){const p=e.hasInjectionContext();(t=t||(p?e.inject(s,null):null))&&i(t),(t=n)._s.has(c)||(u?_(c,o,a,t):function(t,n,s){const{state:o,actions:r,getters:c}=n,a=s.state.value[t];let u;u=_(t,(function(){a||(e.isVue2?e.set(s.state.value,t,o?o():{}):s.state.value[t]=o?o():{});const n=e.toRefs(s.state.value[t]);return $(n,r,Object.keys(c||{}).reduce(((n,o)=>(n[o]=e.markRaw(e.computed((()=>{i(s);const n=s._s.get(t);if(!e.isVue2||n._r)return c[o].call(n,n)}))),n)),{}))}),n,s,0,!0)}(c,a,t));return t._s.get(c)}return"string"==typeof t?(c=t,a=u?r:o):(a=t,c=t.id),p.$id=c,p},t.disposePinia=function(t){t._e.stop(),t._s.clear(),t._p.splice(0),t.state.value={},t._a=null},t.getActivePinia=()=>e.hasInjectionContext()&&e.inject(s)||n,t.mapActions=function(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]=function(...e){return t(this.$pinia)[n](...e)},e)),{}):Object.keys(e).reduce(((n,i)=>(n[i]=function(...n){return t(this.$pinia)[e[i]](...n)},n)),{})},t.mapGetters=S,t.mapState=j,t.mapStores=function(...t){return t.reduce(((t,e)=>(t[e.$id+b]=function(){return e(this.$pinia)},t)),{})},t.mapWritableState=function(t,e){return Array.isArray(e)?e.reduce(((e,n)=>(e[n]={get(){return t(this.$pinia)[n]},set(e){return t(this.$pinia)[n]=e}},e)),{}):Object.keys(e).reduce(((n,i)=>(n[i]={get(){return t(this.$pinia)[e[i]]},set(n){return t(this.$pinia)[e[i]]=n}},n)),{})},t.setActivePinia=i,t.setMapStoreSuffix=function(t){b=t},t.skipHydrate=function(t){return e.isVue2?v.set(t,1)&&t:Object.defineProperty(t,y,{})},t.storeToRefs=function(t){if(e.isVue2)return e.toRefs(t);{t=e.toRaw(t);const n={};for(const i in t){const s=t[i];(e.isRef(s)||e.isReactive(s))&&(n[i]=e.toRef(t,i))}return n}},t}({},Vue);
