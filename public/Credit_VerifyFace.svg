<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>授信_验证人脸</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="授信_验证人脸">
            <rect id="矩形" x="0" y="0" width="64" height="64"></rect>
            <path d="M32,0 C49.673112,0 64,14.326888 64,32 C64,49.673112 49.673112,64 32,64 C14.326888,64 0,49.673112 0,32 C0,14.326888 14.326888,0 32,0 Z M32,4 C16.536027,4 4,16.536027 4,32 C4,40.1934511 7.51926107,47.5649347 13.1286457,52.6853134 L24.0759643,47.7946332 C24.9888335,47.3868333 25.5766398,46.4804546 25.5766398,45.4806391 L25.5766398,44.7394295 C25.5766398,43.7395294 25.2554791,42.7660679 24.6604756,41.9624691 L22.6909562,39.3024789 C20.999906,37.0185831 19.8740045,34.3857214 19.3822863,31.618502 C20.674719,31.7062079 22.0597731,31.7794706 23.5146547,31.83774 L23.6136509,32.2147733 C24.0782145,33.9007401 24.8513576,35.4982921 25.9056726,36.9222271 L27.875192,39.5822173 C28.9801985,41.0746151 29.5766398,42.8824721 29.5766398,44.7394295 L29.5766398,45.4806391 C29.5766398,48.058452 28.0611054,50.3953581 25.707465,51.446784 L16.7149029,55.4637427 C21.1097495,58.3325485 26.3601477,60 32,60 C37.5841649,60 42.7865285,58.3653145 47.1546643,55.54837 L38.5617695,51.4860995 C36.2391484,50.3883054 34.757782,48.0496313 34.757782,45.4806391 L34.757782,44.7445209 C34.757782,42.8834821 35.3568448,41.0718727 36.4663388,39.5777212 L38.43984,36.9200146 C39.5731703,35.393763 40.3615873,33.6560929 40.7687823,31.8261981 C42.2195791,31.765725 43.5979167,31.6903071 44.8813459,31.6010337 C44.4465968,34.3752254 43.3461863,37.0221612 41.6512753,39.3046914 L39.6777741,41.962398 C39.0803543,42.7669411 38.757782,43.7424231 38.757782,44.7445209 L38.757782,45.4806391 C38.757782,46.5026251 39.3470931,47.4329869 40.2710687,47.8697068 L50.7298977,52.8135621 C56.4215102,47.6884206 60,40.2621108 60,32 C60,16.536027 47.463973,4 32,4 Z M32.4463334,25.3337624 C44.0223483,25.356036 53.3333333,26.2427457 53.3333333,27.3333333 C53.3333333,28.4379028 43.7820747,29.3333333 32,29.3333333 C20.2179253,29.3333333 10.6666667,28.4379028 10.6666667,27.3333333 C10.6666667,26.2287638 20.2179253,25.3333333 32,25.3333333 Z M31.8728129,9.62426249 C38.7708767,9.62426249 44.4523743,15.042957 44.7788222,21.9332922 L44.831982,23.0622145 C43.5774353,22.9754693 42.2323472,22.9018955 40.8182356,22.8425949 L40.783304,22.1225904 C40.5579197,17.3654057 36.6353335,13.6242625 31.8728129,13.6242625 C27.1282449,13.6242625 23.2623333,17.4330066 23.1916443,22.1770492 L23.180973,22.8426166 C21.7705718,22.9017654 20.42854,22.9751306 19.1761858,23.0616462 L19.1920882,22.11746 C19.2953448,15.187755 24.9423428,9.62426249 31.8728129,9.62426249 Z" id="形状结合" fill="#256FFF" fill-rule="nonzero"></path>
        </g>
    </g>
</svg>