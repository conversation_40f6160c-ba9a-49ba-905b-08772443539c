@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?rrfjyt');
  src:  url('fonts/icomoon.eot?rrfjyt#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?rrfjyt') format('truetype'),
    url('fonts/icomoon.woff?rrfjyt') format('woff'),
    url('fonts/icomoon.svg?rrfjyt#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-addBank:before {
  content: "\e900";
  color: #256fff;
}
.icon-arrow-down:before {
  content: "\e901";
}
.icon-arrow-pull:before {
  content: "\e902";
}
.icon-arrow-right:before {
  content: "\e903";
}
.icon-arrow-up:before {
  content: "\e904";
}
.icon-CancellationOfDuxiaoman_CancellationInstructions:before {
  content: "\e905";
  color: #ff9700;
}
.icon-credit_arrow_right:before {
  content: "\e906";
}
.icon-credit_realname_confidentiality:before {
  content: "\e907";
  color: #256fff;
}
.icon-credit_realname_fail:before {
  content: "\e908";
  color: #ea3c37;
}
.icon-Credit_RealnameAuthentication_ConfidentialityIcon:before {
  content: "\e909";
  color: #256fff;
}
.icon-Credit_Result_Examine:before {
  content: "\e90a";
}
.icon-Credit_Result_Lose:before {
  content: "\e90b";
  color: #ea3c37;
}
.icon-Credit_Result_Successful:before {
  content: "\e90c";
  color: #15c252;
}
.icon-Credit_Result_Wait:before {
  content: "\e90d";
  color: #ffb84d;
}
.icon-Credit_VerifyFace:before {
  content: "\e90e";
  color: #256fff;
}
.icon-GeneralStatus_GrayUnderReview:before {
  content: "\e90f";
}
.icon-GeneralStatus_GreenSuccess:before {
  content: "\e910";
  color: #15c252;
}
.icon-GeneralStatus_RedFailed:before {
  content: "\e911";
  color: #ea3c37;
}
.icon-GeneralStatus_YellowProcessing:before {
  content: "\e912";
  color: #ffb84d;
}
.icon-help:before {
  content: "\e913";
}
.icon-Homepage_Back:before {
  content: "\e914";
  color: #fff;
}
.icon-Homepage_CouponLabel:before {
  content: "\e915";
  color: #f46629;
}
.icon-Homepage_CustomerService:before {
  content: "\e916";
  color: #fff;
}
.icon-Homepage_ServiceNotSupported:before {
  content: "\e917";
}
.icon-Homepage_Setting:before {
  content: "\e918";
  color: #fff;
}
.icon-icon:before {
  content: "\e919";
}
.icon-IssuanceOfSettlementCertificate_NoApplicationRecordYet:before {
  content: "\e91a";
}
.icon-Loan_AddBankCard_InformationSecurity:before {
  content: "\e91b";
  color: #256fff;
}
.icon-Loan_RepaymentPlan_Calendar:before {
  content: "\e91c";
  color: #256fff;
}
.icon-My_Add2Desktop:before {
  content: "\e91d";
}
.icon-My_AgreementAuthorization:before {
  content: "\e91e";
}
.icon-My_ExpandBusiness:before {
  content: "\e91f";
}
.icon-My_HelpCustomerService:before {
  content: "\e920";
}
.icon-My_IssuanceOfSettlementCertificate:before {
  content: "\e921";
}
.icon-My_LoanApplicationProgress:before {
  content: "\e922";
}
.icon-My_More:before {
  content: "\e923";
}
.icon-My_MyCoupon:before {
  content: "\e924";
}
.icon-MyCoupons_NoCouponsYet:before {
  content: "\e925";
}
.icon-MyLoan_NoLoanYet:before {
  content: "\e926";
}
.icon-no-record:before {
  content: "\e927";
}
.icon-right:before {
  content: "\e928";
}
.icon-tip:before {
  content: "\e929";
  color: #f5031c;
}
.icon-UniversalIcon_CollapseArrowDown:before {
  content: "\e92a";
}
.icon-UniversalIcon_CollapseArrowUp:before {
  content: "\e92b";
}
.icon-UniversalIcon_ListRightArrow:before {
  content: "\e92c";
}
.icon-UniversalIcon_SlidablePanelDropDown-Arrow:before {
  content: "\e92d";
}
.icon-UseCredit_NoSearchResults:before {
  content: "\e92e";
}
.icon-UseCredit_UploadIDCard_1Standard .path1:before {
  content: "\e92f";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_1Standard .path2:before {
  content: "\e930";
  margin-left: -1.5em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_1Standard .path3:before {
  content: "\e931";
  margin-left: -1.5em;
  color: rgb(229, 234, 251);
}
.icon-UseCredit_UploadIDCard_2MissingBorder .path1:before {
  content: "\e932";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_2MissingBorder .path2:before {
  content: "\e933";
  margin-left: -1.5em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_2MissingBorder .path3:before {
  content: "\e934";
  margin-left: -1.5em;
  color: rgb(229, 234, 251);
}
.icon-UseCredit_UploadIDCard_3BlurryPhoto .path1:before {
  content: "\e935";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_3BlurryPhoto .path2:before {
  content: "\e936";
  margin-left: -1.5em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_3BlurryPhoto .path3:before {
  content: "\e937";
  margin-left: -1.5em;
  color: rgb(229, 234, 251);
}
.icon-UseCredit_UploadIDCard_4StrongFlash .path1:before {
  content: "\e938";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_4StrongFlash .path2:before {
  content: "\e939";
  margin-left: -1.5em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_4StrongFlash .path3:before {
  content: "\e93a";
  margin-left: -1.5em;
  color: rgb(229, 234, 251);
}
.icon-UseCredit_UploadIDCard_4StrongFlash .path4:before {
  content: "\e93b";
  margin-left: -1.5em;
  color: rgb(255, 191, 0);
}
.icon-UseCredit_UploadIDCard_NationalEmblemPage .path1:before {
  content: "\e93c";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_NationalEmblemPage .path2:before {
  content: "\e93d";
  margin-left: -1.58203125em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_PortraitPage .path1:before {
  content: "\e93e";
  color: rgb(255, 255, 255);
}
.icon-UseCredit_UploadIDCard_PortraitPage .path2:before {
  content: "\e93f";
  margin-left: -1.58203125em;
  color: rgb(241, 244, 255);
}
.icon-UseCredit_UploadIDCard_UploadIcon:before {
  content: "\e940";
  color: #256fff;
}
.icon-vite:before {
  content: "\e941";
}
.icon-vue .path1:before {
  content: "\e942";
  color: rgb(65, 184, 131);
}
.icon-vue .path2:before {
  content: "\e943";
  margin-left: -1.029296875em;
  color: rgb(65, 184, 131);
}
.icon-vue .path3:before {
  content: "\e944";
  margin-left: -1.029296875em;
  color: rgb(53, 73, 94);
}
.icon-Help:before {
  content: "\e945";
}
.icon-Loan_AddBankCard_InformationSecurity1:before {
  content: "\e946";
  color: #256fff;
}
.icon-Loan_RepaymentPlan_Calendar1:before {
  content: "\e947";
  color: #256fff;
}
.icon-noinfos:before {
  content: "\e948";
}
.icon-detail-logo:before {
  content: "\e949";
}
.icon-detail-logo-deep:before {
  content: "\e94a";
  color: #fff;
}
.icon-logoff:before {
  content: "\e94b";
  color: #ff9700;
}
.icon-myAddDesk:before {
  content: "\e94c";
}
.icon-myApplicationStatus:before {
  content: "\e94d";
}
.icon-myCoupon:before {
  content: "\e94e";
}
.icon-myEmpower:before {
  content: "\e94f";
}
.icon-myExtendedBusiness:before {
  content: "\e950";
}
.icon-myHelp:before {
  content: "\e951";
}
.icon-myMore:before {
  content: "\e952";
}
.icon-myNoCoupons:before {
  content: "\e953";
}
.icon-mySettlementCertificate:before {
  content: "\e954";
}
.icon-norecord:before {
  content: "\e955";
}
.icon-right1:before {
  content: "\e956";
}
.icon-success:before {
  content: "\e957";
  color: #15c252;
}
.icon-date:before {
  content: "\e958";
  color: #256fff;
}
.icon-dealing:before {
  content: "\e959";
  color: #ffb84d;
}
.icon-fail:before {
  content: "\e95a";
  color: #ea3c37;
}
.icon-helps:before {
  content: "\e95b";
}
.icon-jq:before {
  content: "\e95c";
  color: #256fff;
}
.icon-load:before {
  content: "\e95d";
}
.icon-rightIcon:before {
  content: "\e95e";
}
.icon-starIcon:before {
  content: "\e95f";
}
.icon-success1:before {
  content: "\e960";
  color: #15c252;
}
.icon-upIcon:before {
  content: "\e961";
}
.icon-useLoad:before {
  content: "\e962";
}
.icon-Homepage_Back1:before {
  content: "\e963";
  color: #fff;
}
.icon-Homepage_CouponLabel1:before {
  content: "\e964";
  color: #f46629;
}
.icon-Homepage_CustomerService1:before {
  content: "\e965";
  color: #fff;
}
.icon-Homepage_ServiceNotSupported1:before {
  content: "\e966";
}
.icon-Homepage_Setting1:before {
  content: "\e967";
  color: #fff;
}
