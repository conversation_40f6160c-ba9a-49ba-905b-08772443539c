<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>银行转账</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        body {
            background-color: #f5f6fa;
            color: #333;
            line-height: 1.5;
        }

        .header {
            padding: 16px;
            display: flex;
            align-items: center;
            background: #fff;
        }

        .back-btn {
            width: 24px;
            height: 24px;
            background: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23333"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>') no-repeat center;
            border: none;
            background-size: contain;
        }

        .container {
            padding: 16px;
        }

        .transfer-info {
            background: #fff;
            border-radius: 12px;
            padding: 20px 16px;
            margin-bottom: 16px;
        }

        .transfer-notice {
            background: #fff7e6;
            color: #666;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 16px;
        }

        .transfer-amount {
            font-size: 24px;
            font-weight: bold;
            margin: 16px 0;
        }

        .account-info {
            font-size: 14px;
            color: #666;
            line-height: 2;
        }

        .copy-btn {
            color: #4080ff;
            border: none;
            background: none;
            padding: 4px 8px;
            font-size: 14px;
        }

        .faq-section {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
        }

        .faq-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .faq-title {
            font-size: 16px;
            font-weight: 500;
        }

        .more-link {
            color: #999;
            font-size: 14px;
            text-decoration: none;
        }

        .faq-item {
            margin-bottom: 16px;
        }

        .faq-question {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 16px;
            display: flex;
            gap: 12px;
            background: #fff;
        }

        .btn {
            flex: 1;
            height: 44px;
            border-radius: 22px;
            border: none;
            font-size: 16px;
        }

        .btn-outline {
            border: 1px solid #4080ff;
            background: #fff;
            color: #4080ff;
        }

        .btn-primary {
            background: #4080ff;
            color: #fff;
        }
    </style>
</head>
<body>
    <header class="header">
        <button class="back-btn" onclick="history.back()"></button>
    </header>

    <div class="container">
        <div class="transfer-notice">
            请在13:16前，通过银行APP完成转账
        </div>

        <div class="transfer-info">
            <div class="transfer-amount">
                ¥ 268,000.00
            </div>
            <div class="account-info">
                户名：度小满科技（度小满支付专用账户）
                <button class="copy-btn" onclick="copyText('度小满科技（度小满支付专用账户）')">复制</button>
                <br>
                账号：6210 5000 0512 8158 14
                <button class="copy-btn" onclick="copyText('**************** 14')">复制</button>
                <br>
                银行：上海银行（天津西青支行）
                <button class="copy-btn" onclick="copyText('上海银行（天津西青支行）')">复制</button>
            </div>
        </div>

        <div class="faq-section">
            <div class="faq-header">
                <span class="faq-title">常见问题</span>
                <a href="#" class="more-link">更多问题 ›</a>
            </div>
            <div class="faq-item">
                <div class="faq-question">
                    向重庆度小满小额贷款公司转账安全吗？
                    <span style="color: #999;">›</span>
                </div>
            </div>
            <div class="faq-item">
                <div class="faq-question">
                    银行转账支付方式支付有额度吗？
                    <span style="color: #999;">›</span>
                </div>
            </div>
            <div class="faq-item">
                <div class="faq-question">
                    银行转账成功后，放款会立即成功吗？
                    <span style="color: #999;">›</span>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-buttons">
        <button class="btn btn-outline" onclick="checkTransferResult()">查看转账结果</button>
        <button class="btn btn-primary" onclick="goBankApp()">去银行APP转账</button>
    </div>

    <script>
        function copyText(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('复制成功');
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        function checkTransferResult() {
            // 实现查看转账结果的逻辑
            alert('查看转账结果');
        }

        function goBankApp() {
            // 通过scheme拉起招商银行APP
            const scheme = 'cmbmobilebank://';
            window.location.href = scheme;
            
            // 如果3秒后还在当前页面，说明可能没有安装招商银行APP
            setTimeout(() => {
                if (document.hidden || document.webkitHidden) {
                    return;
                }
                // 未安装APP，跳转到下载页
                if (confirm('您可能未安装招商银行APP，是否前往下载？')) {
                    window.location.href = 'https://itunes.apple.com/cn/app/id318708001'; // iOS
                    // Android用户可以跳转到应用市场
                    if (/android/i.test(navigator.userAgent)) {
                        window.location.href = 'market://details?id=cmb.pb';
                    }
                }
            }, 3000);
        }
    </script>
</body>
</html>
